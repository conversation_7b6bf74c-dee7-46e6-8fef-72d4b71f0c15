# REF-VEAX 套利机器人项目架构理解文档

## 📁 项目整体结构概览

这是一个复杂的NEAR生态套利机器人项目，包含主程序和多个分支程序，采用模块化架构设计。

```
ref-veax-new/
├── 📂 主程序 (REF-VEAX套利机器人)
│   ├── src/                          # 主程序源码
│   │   ├── index.ts                  # 程序入口
│   │   ├── arbitrageBot.ts           # 套利机器人主逻辑
│   │   ├── services/                 # 核心服务层
│   │   ├── config/                   # 配置管理
│   │   └── types/                    # 类型定义
│   ├── package.json                  # 主程序依赖
│   └── README.md                     # 主程序说明
│
├── 📂 可复用模块 (NEAR DeFi SDK)
│   ├── reusable-modules/             # 高度模块化的DeFi工具包
│   │   ├── index.ts                  # SDK主入口
│   │   ├── types/                    # 完整类型定义系统
│   │   ├── config/                   # 网络配置和代币定义
│   │   ├── near-utils/               # NEAR通用工具
│   │   ├── veax/                     # VEAX DEX模块
│   │   ├── ref-finance/              # REF Finance模块
│   │   ├── jumbo-exchange/           # Jumbo Exchange模块
│   │   └── examples/                 # 使用示例
│
├── 📂 分支程序1 (REF内部套利)
│   ├── ref-internal-arbitrage/       # REF V1↔V2内部套利
│   │   ├── main.ts                   # 程序入口
│   │   ├── refInternalArbitrageBot.ts # 套利机器人
│   │   ├── services/                 # 独立服务实现
│   │   └── config/                   # 内部套利配置
│
├── 📂 分支程序2 (REF-Jumbo套利)
│   ├── ref-jumbo/                    # REF-Jumbo套利系统
│   │   ├── main.ts                   # 程序入口
│   │   ├── refJumboArbitrageBot.ts   # 套利机器人
│   │   ├── services/                 # Jumbo相关服务
│   │   └── config/                   # 交易对配置
│
└── 📂 研究项目
    ├── ref-sdk-research/             # REF SDK研究和对比
    │   ├── test-ref-sdk.ts           # SDK功能测试
    │   └── compare-results.ts        # API vs SDK对比
    └── 📄 开发文档/                   # 大量技术文档
        ├── REF_Finance_完整开发指南.md
        ├── 三档位动态金额交易系统.md
        └── RayNear套利总结.md
```

## 🎯 核心功能模块分析

### 1. 主程序 - REF-VEAX套利机器人

**核心职责**: 在REF Finance和VEAX DEX之间执行自动化套利交易

**关键特性**:
- ✅ 双系统并行报价 (REF V1 Smart Router + DCL v2)
- ✅ 智能套利机会检测
- ✅ 三档位动态交易金额系统
- ✅ 完整的风险管理和错误处理
- ✅ 执行锁机制防止并发冲突

**核心服务依赖**:
```typescript
// 主要服务组件
- refQuoteService: REF Finance统一报价服务
- VeaxQuoteService: VEAX DEX报价服务  
- RefExecutionServiceCorrect: REF交易执行服务
- VeaxExecutionService: VEAX交易执行服务
- NearWrapService: NEAR包装/解包服务
- AutoBalanceManager: 自动余额管理
- dynamicAmountManager: 动态金额管理
```

### 2. 可复用模块 - NEAR DeFi SDK

**核心职责**: 提供高度模块化的NEAR DeFi开发工具包

**架构设计**:
- 🔧 **完全模块化**: 每个功能都是独立模块，可按需使用
- 🔧 **类型安全**: 完整的TypeScript类型定义
- 🔧 **精度修复**: 使用NEAR官方parseNearAmount避免精度损失
- 🔧 **错误检测**: 智能检测FunctionCallError和ExecutionError
- 🔧 **生产就绪**: 经过实际套利机器人验证的代码

**模块结构**:
```typescript
// 核心模块导出
export const VEAX = {
  QuoteService: VeaxQuoteService,
  ExecutionService: VeaxExecutionService
};

export const REF = {
  QuoteService: RefQuoteService,
  quoteService: refQuoteService, // 单例实例
  V1Router: V1SmartRouterService,
  ExecutionService: RefExecutionService
};

export const NEAR = {
  WrapService: NearWrapService,
  AutoBalanceManager: AutoBalanceManager
};
```

### 3. 分支程序 - REF内部套利

**核心职责**: 监控REF Finance V1和DCL v2系统之间的价格差异

**套利逻辑**:
```
方向1: V1 → V2
- 第一步: V1 (100 NEAR → A USDT)
- 第二步: V2 (A USDT → B NEAR)  
- 利润: B - 100

方向2: V2 → V1
- 第一步: V2 (100 NEAR → A USDT)
- 第二步: V1 (A USDT → B NEAR)
- 利润: B - 100
```

**独立性设计**: 不依赖reusable-modules，拥有独立的RefQuoteService实现

### 4. 分支程序 - REF-Jumbo套利

**核心职责**: 监控REF Finance和Jumbo Exchange之间的价格差异

**技术特点**:
- 🔄 复用主程序的REF Finance服务
- 🔄 独立实现Jumbo Exchange集成
- 🔄 采用与REF-VEAX相同的架构模式

## 🔗 代码依赖关系图

```mermaid
graph TD
    A[主程序 src/] --> B[reusable-modules/]
    C[ref-jumbo/] --> A
    C --> B
    D[ref-internal-arbitrage/] --> E[独立实现]
    F[ref-sdk-research/] --> G[REF官方SDK]
    
    B --> H[VEAX模块]
    B --> I[REF Finance模块]  
    B --> J[NEAR工具模块]
    B --> K[Jumbo Exchange模块]
    
    A --> L[套利机器人逻辑]
    A --> M[动态金额管理]
    A --> N[风险管理系统]
```

## 🎯 关键技术特性

### 1. 精度处理系统
- **问题**: JavaScript浮点数精度限制导致大数值精度丢失
- **解决**: 使用NEAR官方parseNearAmount()和字符串精确计算
- **效果**: 完全消除精度损失，从37万亿wei误差降至0

### 2. 错误检测系统  
- **FunctionCallError检测**: 智能识别合约执行错误
- **具体错误类型**: E22(存款不足)、E76(滑点过大)等
- **网络错误处理**: 超时、503等网络问题的智能重试

### 3. 代币注册强制要求
- **核心规则**: NEAR协议账户接收FT代币前必须先注册
- **自动化**: 程序启动时自动检查和注册所有配置代币
- **安全性**: 防止资金永久损失的关键机制

### 4. 套利执行流程优化
- **监控与执行分离**: 监控用预期金额，执行用实际金额重新报价
- **动态重新报价**: 解决执行阶段的金额不匹配问题
- **执行锁机制**: 防止并发交易冲突

## 📊 项目规模统计

| 组件 | 文件数量 | 核心功能 | 状态 |
|------|----------|----------|------|
| 主程序 | ~50+ | REF-VEAX套利 | ✅ 生产运行 |
| 可复用模块 | ~30+ | DeFi工具包 | ✅ 稳定版本 |
| REF内部套利 | ~15+ | V1↔V2套利 | 🔄 开发中 |
| REF-Jumbo套利 | ~20+ | REF-Jumbo套利 | 🔄 开发中 |
| SDK研究 | ~10+ | 技术研究 | 📚 研究完成 |
| 文档 | ~15+ | 技术文档 | 📝 持续更新 |

## 🚀 部署和运行方式

### 主程序运行
```bash
npm install
npm run start          # 生产模式
npm run dev            # 开发模式  
```

### 分支程序运行
```bash
# REF内部套利
cd ref-internal-arbitrage
npm install
npx ts-node main.ts

# REF-Jumbo套利  
cd ref-jumbo
npm install
npx ts-node main.ts
```

### PM2生产部署
```bash
pm2 start ecosystem.config.js
pm2 logs arbitrage-bot
pm2 monit
```

## 📋 下一步开发计划

1. **完善REF内部套利执行功能**
2. **优化REF-Jumbo套利系统**  
3. **扩展更多DEX支持**
4. **完善监控和告警系统**
5. **优化性能和稳定性**

## 🔧 核心服务详细分析

### REF Finance服务架构

#### 1. 报价服务 (refQuoteService)
```typescript
// 双系统并行报价架构
class RefQuoteService {
  async getBestQuote(params: QuoteParams): Promise<QuoteResult> {
    // 并行调用V1 Smart Router和DCL v2
    const [v1Result, dclv2Result] = await Promise.allSettled([
      v1SmartRouter.getV1Quote(params),
      dclv2Contract.getDCLv2Quote(params)
    ]);

    // 智能选择最优报价
    return this.selectBestQuote(v1Result, dclv2Result);
  }
}
```

**关键特性**:
- 🔄 V1 Smart Router: 支持多跳路径，API调用
- 🔄 DCL v2: 直接合约调用，4个费用等级 (0.01%, 0.04%, 0.2%, 1%)
- 🔄 智能选择: 自动选择输出金额最大的报价

#### 2. 执行服务 (RefExecutionServiceCorrect)
```typescript
// 修复了金额不匹配问题的执行服务
class RefExecutionServiceCorrect {
  async executeV1Swap(quoteResult, inputTokenId, inputAmount, minOutputAmount) {
    // 🔧 关键修复：使用实际输入金额而不是Smart Router返回的金额
    const swapActions = this.buildCorrectV1SwapActions(
      quoteResult.rawResponse.result_data,
      minOutputAmount,
      inputAmount  // 传递实际的输入金额
    );

    // 调用输入代币合约的ft_transfer_call
    const result = await this.account.functionCall({
      contractId: inputTokenId,
      methodName: 'ft_transfer_call',
      args: {
        receiver_id: 'v2.ref-finance.near',
        amount: inputAmount,
        msg: JSON.stringify({ force: 0, actions: swapActions })
      }
    });
  }
}
```

### VEAX DEX服务架构

#### 1. 报价服务 (VeaxQuoteService)
```typescript
class VeaxQuoteService {
  static async getQuote(tokenIn: string, tokenOut: string, amountIn: string) {
    // 直接调用VEAX合约的get_quote方法
    const result = await account.viewFunction({
      contractId: 'v1.veax.near',
      methodName: 'get_quote',
      args: { token_in: tokenIn, token_out: tokenOut, amount_in: amountIn }
    });

    return {
      success: true,
      outputAmount: result.amount_out,
      priceImpact: result.price_impact,
      poolExists: result.pool_exists
    };
  }
}
```

#### 2. 执行服务 (VeaxExecutionService)
```typescript
class VeaxExecutionService {
  async executeSwap(tokenIn, tokenOut, amountIn, minAmountOut, slippage) {
    // 调用输入代币合约的ft_transfer_call
    const result = await this.account.functionCall({
      contractId: tokenIn.id,
      methodName: 'ft_transfer_call',
      args: {
        receiver_id: 'v1.veax.near',
        amount: amountIn,
        msg: JSON.stringify({
          method: 'swap_exact_in',
          params: {
            token_out: tokenOut.id,
            min_amount_out: minAmountOut
          }
        })
      }
    });
  }
}
```

### Jumbo Exchange服务架构

#### 1. 报价服务 (JumboQuoteService)
```typescript
class JumboQuoteService {
  static async getQuote(tokenIn: string, tokenOut: string, amountIn: string) {
    // 使用本地AMM计算获取Jumbo价格
    const pools = await this.getPools();
    const relevantPool = pools.find(pool =>
      pool.token_symbols.includes(tokenIn) &&
      pool.token_symbols.includes(tokenOut)
    );

    // 本地计算输出金额
    return this.calculateOutputAmount(relevantPool, tokenIn, tokenOut, amountIn);
  }
}
```

## 🎯 套利算法核心逻辑

### 1. 套利机会检测算法
```typescript
class ArbitrageBot {
  async detectArbitrageOpportunities(pair: TradingPairConfig) {
    // 并行获取两个DEX的报价
    const [refQuote, veaxQuote] = await Promise.all([
      refQuoteService.getBestQuote({
        tokenIn: pair.tokenA,
        tokenOut: pair.tokenB,
        amountIn: pair.tradeAmount
      }),
      VeaxQuoteService.getQuote(
        pair.tokenA.id,
        pair.tokenB.id,
        this.toWei(pair.tradeAmount, pair.tokenA.decimals)
      )
    ]);

    // 计算套利利润
    const opportunities = [];

    // REF → VEAX 方向
    if (refQuote.success && veaxQuote.success) {
      const refToVeaxProfit = this.calculateArbitrageProfit(
        refQuote, veaxQuote, 'ref-to-veax'
      );
      if (refToVeaxProfit > pair.minProfitThreshold) {
        opportunities.push({
          direction: 'ref-to-veax',
          profit: refToVeaxProfit,
          // ...
        });
      }
    }

    return opportunities;
  }
}
```

### 2. 动态金额管理系统
```typescript
class DynamicAmountManager {
  // 三档位动态交易金额系统
  getTradeAmount(pair: TradingPairConfig, profit: number): string {
    if (profit >= pair.highProfitThreshold) {
      return pair.highAmount;    // 高利润 -> 大金额
    } else if (profit >= pair.mediumProfitThreshold) {
      return pair.mediumAmount;  // 中利润 -> 中金额
    } else {
      return pair.lowAmount;     // 低利润 -> 小金额
    }
  }
}
```

### 3. 风险管理系统
```typescript
class ArbitrageBot {
  async riskManagement(tokenIn: any, tokenOut: any, amount: string) {
    console.log(`🚨 启动风险管理: 紧急卖出 ${amount} ${tokenIn.symbol}`);

    try {
      // 尝试通过REF Finance紧急卖出
      const emergencyQuote = await refQuoteService.getBestQuote({
        tokenIn: tokenIn,
        tokenOut: tokenOut,
        amountIn: this.fromWei(amount, tokenIn.decimals)
      });

      if (emergencyQuote.success) {
        const result = await this.refExecutionService.executeSwap(
          emergencyQuote,
          tokenIn.id,
          amount,
          '1', // 极低的最小输出，确保能够执行
          0.05  // 5%滑点
        );

        if (result.success) {
          console.log(`✅ 风险管理成功: 紧急卖出完成`);
        }
      }
    } catch (error) {
      console.error(`❌ 风险管理失败:`, error);
    }
  }
}
```

## 📊 性能优化特性

### 1. 报价查询优化
- **减少75%的RPC调用**: 通过智能缓存和批量查询
- **并行查询**: V1和DCL v2系统并行调用
- **池子预筛选**: 只查询高流动性池子

### 2. 执行锁机制
```typescript
class ArbitrageBot {
  private isExecutingTrade: boolean = false;

  async executeArbitrage(opportunity: ArbitrageOpportunity) {
    if (this.isExecutingTrade) {
      console.log(`⚠️ 已有交易在执行中，跳过套利机会`);
      return;
    }

    this.isExecutingTrade = true;
    try {
      // 执行套利交易
      await this.performArbitrageTrade(opportunity);
    } finally {
      this.isExecutingTrade = false;
    }
  }
}
```

### 3. 自动余额管理
```typescript
class AutoBalanceManager {
  async start() {
    setInterval(async () => {
      if (!this.isTrading) {  // 只在非交易期间检查
        await this.checkAndManageBalance();
      }
    }, 30 * 60 * 1000); // 每30分钟检查一次
  }

  async checkAndManageBalance() {
    const nearBalance = await this.getNearBalance();
    const wNearBalance = await this.getWNearBalance();

    if (nearBalance < this.config.minNearBalance && wNearBalance > this.config.reserveAmount) {
      // 自动解包1 NEAR
      await this.nearWrapService.unwrapNear('1');
    }
  }
}
```

---

*本文档基于项目代码分析生成，反映当前项目架构状态*
