/**
 * Jumbo Exchange 集成模块主入口
 * 提供统一的对外接口和服务管理
 */

import { Account } from 'near-api-js';
import { jumboPoolManager } from './pool-manager';
import { JumboQuoteService, jumboQuoteService } from './quote-service';
import JumboExecutionService from './execution-service';
import {
  JumboQuoteResult,
  JumboSwapRequest,
  JumboSwapResult,
  JumboPoolInfo,
  JumboTargetPool,
  JumboError,
  JumboErrorType
} from './types';
import { 
  JUMBO_TARGET_POOLS,
  getPoolConfig,
  isTargetPool
} from './config';
import { createLogger, getCurrentTimestamp } from './utils';

const logger = createLogger('JumboExchange');

// ============================================================================
// 主要导出类
// ============================================================================

/**
 * Jumbo Exchange 主服务类
 */
export class JumboExchange {
  private quoteService: JumboQuoteService;
  private executionService?: JumboExecutionService;
  private isInitialized: boolean = false;

  constructor(nearAccount?: Account) {
    this.quoteService = nearAccount ?
      new JumboQuoteService(nearAccount) :
      jumboQuoteService;

    // 只有提供了 NEAR 账户才能执行交易
    if (nearAccount) {
      this.executionService = new JumboExecutionService(nearAccount);
    }
  }

  // ============================================================================
  // 初始化方法
  // ============================================================================

  /**
   * 初始化服务 - 优化版本（移除不必要的缓存）
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Jumbo Exchange service...');

      // 简化初始化：只验证目标池子配置
      const targetPools = JUMBO_TARGET_POOLS.filter(pool => pool.isActive);
      logger.info(`Configured ${targetPools.length} target pools:`);

      for (const pool of targetPools) {
        logger.info(`  - Pool ${pool.poolId}: ${pool.baseSymbol}-${pool.quoteSymbol}`);
      }

      this.isInitialized = true;
      logger.info('Jumbo Exchange service initialized successfully');

    } catch (error) {
      logger.error('Failed to initialize Jumbo Exchange service:', error);
      throw new JumboError(
        JumboErrorType.API_ERROR,
        'Initialization failed',
        error
      );
    }
  }

  /**
   * 检查服务是否已初始化
   */
  private ensureInitialized(): void {
    if (!this.isInitialized) {
      throw new JumboError(
        JumboErrorType.API_ERROR,
        'Service not initialized. Call initialize() first.'
      );
    }
  }

  // ============================================================================
  // 报价方法
  // ============================================================================

  /**
   * 获取所有重点池子的报价
   */
  async getAllQuotes(): Promise<{ [poolId: number]: JumboQuoteResult }> {
    this.ensureInitialized();
    return this.quoteService.getAllQuotes();
  }

  /**
   * 获取 NEAR-AURORA 报价 (Pool 0)
   */
  async getNearAuroraQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearAuroraQuote(amount);
  }

  /**
   * 获取 NEAR-USDT.e 报价 (Pool 1) - 主要套利目标
   */
  async getNearUsdtQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearUsdtQuote(amount);
  }

  /**
   * 获取 NEAR-LINEAR 报价 (Pool 231) - 最大流动性
   */
  async getNearLinearQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearLinearQuote(amount);
  }

  /**
   * 获取 NEAR-HAPI 报价 (Pool 6)
   */
  async getNearHapiQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearHapiQuote(amount);
  }

  /**
   * 获取 NEAR-UMINT 报价 (Pool 184)
   */
  async getNearUmintQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearUmintQuote(amount);
  }

  /**
   * 获取 NEAR-CHICA 报价 (Pool 274)
   */
  async getNearChicaQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearChicaQuote(amount);
  }

  /**
   * 获取 NEAR-1MIL 报价 (Pool 294)
   */
  async getNear1MilQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNear1MilQuote(amount);
  }

  /**
   * 获取 NEAR-NearX 报价 (Pool 266)
   */
  async getNearNearxQuote(amount: string = '4'): Promise<JumboQuoteResult> {
    this.ensureInitialized();
    return this.quoteService.getNearNearxQuote(amount);
  }

  // ============================================================================
  // 池子信息方法
  // ============================================================================

  /**
   * 获取池子信息
   */
  async getPoolInfo(poolId: number): Promise<JumboPoolInfo | null> {
    this.ensureInitialized();
    return jumboPoolManager.getPoolInfo(poolId);
  }

  /**
   * 获取所有重点池子配置
   */
  getTargetPools(): JumboTargetPool[] {
    return JUMBO_TARGET_POOLS.filter(pool => pool.isActive);
  }

  /**
   * 检查池子是否活跃
   */
  async isPoolActive(poolId: number): Promise<boolean> {
    this.ensureInitialized();
    return jumboPoolManager.isPoolActive(poolId);
  }

  /**
   * 获取池子统计信息
   */
  getPoolStats() {
    this.ensureInitialized();
    return jumboPoolManager.getPoolStats();
  }

  // ============================================================================
  // 交易执行方法
  // ============================================================================

  /**
   * 执行交换交易
   */
  async executeSwap(request: JumboSwapRequest): Promise<JumboSwapResult> {
    this.ensureInitialized();

    if (!this.executionService) {
      throw new JumboError(
        JumboErrorType.CONTRACT_ERROR,
        'Execution service not available. Please provide a NEAR account in constructor.'
      );
    }

    return this.executionService.executeSwap(request);
  }

  /**
   * 检查用户是否已注册
   */
  async checkUserRegistration(): Promise<boolean> {
    this.ensureInitialized();

    if (!this.executionService) {
      throw new JumboError(
        JumboErrorType.CONTRACT_ERROR,
        'Execution service not available. Please provide a NEAR account in constructor.'
      );
    }

    return this.executionService.checkUserRegistration();
  }

  /**
   * 注册用户
   */
  async registerUser(): Promise<void> {
    this.ensureInitialized();

    if (!this.executionService) {
      throw new JumboError(
        JumboErrorType.CONTRACT_ERROR,
        'Execution service not available. Please provide a NEAR account in constructor.'
      );
    }

    return this.executionService.registerUser();
  }

  // ============================================================================
  // 缓存管理方法
  // ============================================================================

  /**
   * 刷新池子缓存
   */
  async refreshCache(): Promise<void> {
    await jumboPoolManager.refreshCache();
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    jumboPoolManager.clearCache();
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus() {
    return jumboPoolManager.getCacheStatus();
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 获取服务状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      timestamp: getCurrentTimestamp(),
      targetPools: this.getTargetPools().length,
      cache: this.getCacheStatus()
    };
  }

  /**
   * 健康检查 - 优化版本
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }

      // 尝试获取一个报价来验证服务状态
      const quote = await this.getNearUsdtQuote('1');
      return quote.success;

    } catch (error) {
      logger.error('Health check failed:', error);
      return false;
    }
  }
}

// ============================================================================
// 便捷函数导出
// ============================================================================

/**
 * 创建 Jumbo Exchange 实例
 */
export function createJumboExchange(nearAccount?: Account): JumboExchange {
  return new JumboExchange(nearAccount);
}

/**
 * 快速获取所有报价（无需初始化实例）
 */
export async function getJumboQuotes(): Promise<{ [poolId: number]: JumboQuoteResult }> {
  const jumbo = new JumboExchange();
  await jumbo.initialize();
  return jumbo.getAllQuotes();
}

/**
 * 快速获取 NEAR-USDT 报价（主要套利目标）
 */
export async function getJumboNearUsdtQuote(amount: string = '4'): Promise<JumboQuoteResult> {
  const jumbo = new JumboExchange();
  await jumbo.initialize();
  return jumbo.getNearUsdtQuote(amount);
}

// ============================================================================
  // 类型和配置导出
// ============================================================================

export * from './types';
export * from './config';
export { jumboPoolManager } from './pool-manager';
export { jumboQuoteService } from './quote-service';
export { default as JumboExecutionService } from './execution-service';

// ============================================================================
// 默认导出
// ============================================================================

export default JumboExchange;
