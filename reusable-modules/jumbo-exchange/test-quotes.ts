/**
 * Jumbo Exchange 报价测试脚本
 * 用于测试所有重点池子的报价功能
 */

import { JumboExchange } from './index';
import { createLogger } from './utils';

const logger = createLogger('JumboTest');

/**
 * 测试所有池子的报价
 */
async function testAllQuotes() {
  try {
    logger.info('Starting Jumbo Exchange quote test...');
    
    // 创建 Jumbo Exchange 实例
    const jumbo = new JumboExchange();
    
    // 初始化服务
    await jumbo.initialize();
    logger.info('Service initialized successfully');
    
    // 获取服务状态
    const status = jumbo.getStatus();
    logger.info('Service status:', status);
    
    // 测试单个报价
    logger.info('\n=== Testing Individual Quotes ===');
    
    // 1. NEAR-USDT.e (Pool 1) - 主要套利目标
    try {
      const nearUsdtQuote = await jumbo.getNearUsdtQuote('4');
      logger.info(`Pool 1 (NEAR-USDT.e): ${nearUsdtQuote.amountIn} NEAR -> ${nearUsdtQuote.amountOut} USDT.e`);
      logger.info(`  Success: ${nearUsdtQuote.success}, Method: ${nearUsdtQuote.method}`);
      if (nearUsdtQuote.priceImpact) {
        logger.info(`  Price Impact: ${nearUsdtQuote.priceImpact}%`);
      }
    } catch (error) {
      logger.error('Pool 1 quote failed:', error instanceof Error ? error.message : String(error));
    }
    
    // 2. NEAR-LINEAR (Pool 231) - 最大流动性
    try {
      const nearLinearQuote = await jumbo.getNearLinearQuote('4');
      logger.info(`Pool 231 (NEAR-LINEAR): ${nearLinearQuote.amountIn} NEAR -> ${nearLinearQuote.amountOut} LINEAR`);
      logger.info(`  Success: ${nearLinearQuote.success}, Method: ${nearLinearQuote.method}`);
    } catch (error) {
      logger.error('Pool 231 quote failed:', error instanceof Error ? error.message : String(error));
    }

    // 3. NEAR-HAPI (Pool 6)
    try {
      const nearHapiQuote = await jumbo.getNearHapiQuote('4');
      logger.info(`Pool 6 (NEAR-HAPI): ${nearHapiQuote.amountIn} NEAR -> ${nearHapiQuote.amountOut} HAPI`);
      logger.info(`  Success: ${nearHapiQuote.success}, Method: ${nearHapiQuote.method}`);
    } catch (error) {
      logger.error('Pool 6 quote failed:', error instanceof Error ? error.message : String(error));
    }

    // 4. NEAR-UMINT (Pool 184)
    try {
      const nearUmintQuote = await jumbo.getNearUmintQuote('4');
      logger.info(`Pool 184 (NEAR-UMINT): ${nearUmintQuote.amountIn} NEAR -> ${nearUmintQuote.amountOut} UMINT`);
      logger.info(`  Success: ${nearUmintQuote.success}, Method: ${nearUmintQuote.method}`);
    } catch (error) {
      logger.error('Pool 184 quote failed:', error instanceof Error ? error.message : String(error));
    }

    // 5. NEAR-CHICA (Pool 274)
    try {
      const nearChicaQuote = await jumbo.getNearChicaQuote('4');
      logger.info(`Pool 274 (NEAR-CHICA): ${nearChicaQuote.amountIn} NEAR -> ${nearChicaQuote.amountOut} CHICA`);
      logger.info(`  Success: ${nearChicaQuote.success}, Method: ${nearChicaQuote.method}`);
    } catch (error) {
      logger.error('Pool 274 quote failed:', error instanceof Error ? error.message : String(error));
    }

    // 6. NEAR-1MIL (Pool 294)
    try {
      const near1MilQuote = await jumbo.getNear1MilQuote('4');
      logger.info(`Pool 294 (NEAR-1MIL): ${near1MilQuote.amountIn} NEAR -> ${near1MilQuote.amountOut} 1MIL`);
      logger.info(`  Success: ${near1MilQuote.success}, Method: ${near1MilQuote.method}`);
    } catch (error) {
      logger.error('Pool 294 quote failed:', error instanceof Error ? error.message : String(error));
    }

    // 7. NEAR-NearX (Pool 266)
    try {
      const nearNearxQuote = await jumbo.getNearNearxQuote('4');
      logger.info(`Pool 266 (NEAR-NearX): ${nearNearxQuote.amountIn} NEAR -> ${nearNearxQuote.amountOut} NearX`);
      logger.info(`  Success: ${nearNearxQuote.success}, Method: ${nearNearxQuote.method}`);
    } catch (error) {
      logger.error('Pool 266 quote failed:', error instanceof Error ? error.message : String(error));
    }
    
    // 测试批量报价
    logger.info('\n=== Testing Batch Quotes ===');
    const allQuotes = await jumbo.getAllQuotes();
    
    logger.info('Batch quote results:');
    for (const [poolId, quote] of Object.entries(allQuotes)) {
      if (quote.success) {
        logger.info(`  Pool ${poolId}: ${quote.amountIn} -> ${quote.amountOut} (${quote.method})`);
      } else {
        logger.error(`  Pool ${poolId}: FAILED - ${quote.error}`);
      }
    }
    
    // 获取池子统计
    logger.info('\n=== Pool Statistics ===');
    const stats = jumbo.getPoolStats();
    logger.info('Pool stats:', stats);
    
    // 健康检查
    logger.info('\n=== Health Check ===');
    const isHealthy = await jumbo.healthCheck();
    logger.info(`Health check: ${isHealthy ? 'PASSED' : 'FAILED'}`);
    
    logger.info('\nJumbo Exchange quote test completed successfully!');
    
  } catch (error) {
    logger.error('Test failed:', error);
    throw error;
  }
}

/**
 * 测试池子信息获取
 */
async function testPoolInfo() {
  try {
    logger.info('\n=== Testing Pool Info ===');
    
    const jumbo = new JumboExchange();
    await jumbo.initialize();
    
    // 测试获取重点池子信息
    const targetPools = jumbo.getTargetPools();
    logger.info(`Found ${targetPools.length} target pools`);
    
    for (const poolConfig of targetPools) {
      try {
        const poolInfo = await jumbo.getPoolInfo(poolConfig.poolId);
        if (poolInfo) {
          logger.info(`Pool ${poolConfig.poolId} (${poolConfig.name}):`);
          logger.info(`  Liquidity: $${poolInfo.liquidity}`);
          logger.info(`  Base: ${poolInfo.base_symbol} (${poolInfo.base_liquidity})`);
          logger.info(`  Quote: ${poolInfo.quote_symbol} (${poolInfo.quote_liquidity})`);
          
          const isActive = await jumbo.isPoolActive(poolConfig.poolId);
          logger.info(`  Active: ${isActive}`);
        } else {
          logger.warn(`Pool ${poolConfig.poolId} info not found`);
        }
      } catch (error) {
        logger.error(`Failed to get info for pool ${poolConfig.poolId}:`, error instanceof Error ? error.message : String(error));
      }
    }
    
  } catch (error) {
    logger.error('Pool info test failed:', error);
    throw error;
  }
}

/**
 * 主测试函数
 */
async function main() {
  try {
    await testPoolInfo();
    await testAllQuotes();
  } catch (error) {
    logger.error('Main test failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testAllQuotes, testPoolInfo };
