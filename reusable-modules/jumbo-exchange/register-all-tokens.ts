/**
 * 批量注册所有 Jumbo Exchange 相关代币
 * 包括 Jumbo Exchange 本身和所有配置的代币合约
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import { Account, connect, keyStores } from 'near-api-js';
import { parseNearAmount } from 'near-api-js/lib/utils/format';
import { TOKEN_ADDRESSES, JUMBO_CONFIG } from './config';
import { createLogger, withRetry } from './utils';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../../.env') });

const logger = createLogger('TokenRegistration');

/**
 * NEAR 网络配置
 */
function getNearConfig(networkId: string) {
  if (networkId === 'mainnet') {
    return {
      networkId: 'mainnet',
      nodeUrl: 'https://rpc.mainnet.near.org',
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org'
    };
  } else {
    return {
      networkId: 'testnet',
      nodeUrl: 'https://rpc.testnet.near.org',
      walletUrl: 'https://wallet.testnet.near.org',
      helperUrl: 'https://helper.testnet.near.org'
    };
  }
}

/**
 * 初始化 NEAR 账户
 */
async function initializeAccount(): Promise<Account> {
  const accountId = process.env.NEAR_ACCOUNT_ID;
  const privateKey = process.env.NEAR_PRIVATE_KEY;
  const networkId = process.env.NEAR_NETWORK_ID || 'mainnet';

  if (!accountId || !privateKey) {
    throw new Error('请在 .env 文件中设置 NEAR_ACCOUNT_ID 和 NEAR_PRIVATE_KEY');
  }

  const keyStore = new keyStores.InMemoryKeyStore();
  const keyPair = require('near-api-js').utils.KeyPair.fromString(privateKey);
  await keyStore.setKey(networkId, accountId, keyPair);

  const config = {
    ...getNearConfig(networkId),
    keyStore
  };

  const near = await connect(config);
  return await near.account(accountId);
}

/**
 * 检查代币注册状态
 */
async function checkTokenRegistration(account: Account, tokenAddress: string): Promise<boolean> {
  try {
    const result = await account.viewFunction({
      contractId: tokenAddress,
      methodName: 'storage_balance_of',
      args: {
        account_id: account.accountId
      }
    });
    
    return result !== null;
  } catch (error) {
    logger.warn(`Failed to check ${tokenAddress} registration:`, error);
    return false;
  }
}

/**
 * 注册到代币合约
 */
async function registerToToken(account: Account, tokenAddress: string): Promise<boolean> {
  try {
    await withRetry(async () => {
      return await account.functionCall({
        contractId: tokenAddress,
        methodName: 'storage_deposit',
        args: {
          account_id: account.accountId,
          registration_only: true
        },
        gas: BigInt('***************'),
        attachedDeposit: BigInt(parseNearAmount('0.00125') || '0')
      });
    });
    
    logger.info(`✅ Successfully registered to ${tokenAddress}`);
    return true;
  } catch (error) {
    logger.error(`❌ Failed to register to ${tokenAddress}:`, error);
    return false;
  }
}

/**
 * 批量注册所有代币
 */
async function registerAllTokens() {
  logger.info('🚀 开始批量注册所有代币');
  logger.info('='.repeat(60));

  try {
    // 初始化账户
    const account = await initializeAccount();
    logger.info(`📡 账户: ${account.accountId}`);

    // 获取所有需要注册的合约地址
    const contractsToRegister = [
      // Jumbo Exchange 主合约
      JUMBO_CONFIG.contractAddress,
      // 所有代币合约
      ...Object.values(TOKEN_ADDRESSES)
    ];

    logger.info(`📋 需要注册的合约数量: ${contractsToRegister.length}`);
    
    let successCount = 0;
    let skipCount = 0;
    let failCount = 0;

    for (let i = 0; i < contractsToRegister.length; i++) {
      const contractAddress = contractsToRegister[i];
      const progress = `[${i + 1}/${contractsToRegister.length}]`;
      
      logger.info(`\n${progress} 检查合约: ${contractAddress}`);

      // 检查是否已注册
      const isRegistered = await checkTokenRegistration(account, contractAddress);
      
      if (isRegistered) {
        logger.info(`✅ ${progress} 已注册: ${contractAddress}`);
        skipCount++;
        continue;
      }

      // 执行注册
      logger.info(`🔄 ${progress} 注册中: ${contractAddress}`);
      const success = await registerToToken(account, contractAddress);
      
      if (success) {
        successCount++;
      } else {
        failCount++;
      }

      // 添加延迟避免过快请求
      if (i < contractsToRegister.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // 总结结果
    logger.info('\n📊 注册结果总结:');
    logger.info(`✅ 新注册成功: ${successCount}`);
    logger.info(`⏭️  已注册跳过: ${skipCount}`);
    logger.info(`❌ 注册失败: ${failCount}`);
    logger.info(`📋 总计: ${contractsToRegister.length}`);

    if (failCount === 0) {
      logger.info('\n🎉 所有代币注册完成！');
    } else {
      logger.warn(`\n⚠️  有 ${failCount} 个合约注册失败，请检查日志`);
    }

  } catch (error) {
    logger.error('❌ 批量注册失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await registerAllTokens();
  } catch (error) {
    logger.error('程序失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行注册
if (require.main === module) {
  main().catch(console.error);
}

export { registerAllTokens };
