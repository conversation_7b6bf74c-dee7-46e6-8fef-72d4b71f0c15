/**
 * Jumbo Exchange 交易测试脚本
 * 测试 Pool 1 (NEAR-USDT.e) 的实际交易执行
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../../.env') });
import { Account, connect, keyStores, Near } from 'near-api-js';
import { parseNearAmount } from 'near-api-js/lib/utils/format';
import { JumboExchange } from './index';
import JumboExecutionService from './execution-service';
import { 
  toWei, 
  fromWei, 
  getTokenDecimals, 
  calculateMinAmountOut,
  createLogger 
} from './utils';

const logger = createLogger('JumboSwapTest');

/**
 * NEAR 网络配置
 */
function getNearConfig(networkId: string) {
  if (networkId === 'mainnet') {
    return {
      networkId: 'mainnet',
      nodeUrl: 'https://rpc.mainnet.near.org',
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org',
      explorerUrl: 'https://nearblocks.io'
    };
  } else {
    return {
      networkId: 'testnet',
      nodeUrl: 'https://rpc.testnet.near.org',
      walletUrl: 'https://wallet.testnet.near.org',
      helperUrl: 'https://helper.testnet.near.org',
      explorerUrl: 'https://testnet.nearblocks.io'
    };
  }
}

/**
 * 初始化 NEAR 连接
 */
async function initializeNear(accountId: string, privateKey: string, networkId: string = 'mainnet'): Promise<Account> {
  const keyStore = new keyStores.InMemoryKeyStore();
  const keyPair = require('near-api-js').utils.KeyPair.fromString(privateKey);
  await keyStore.setKey(networkId, accountId, keyPair);

  const config = {
    ...getNearConfig(networkId),
    keyStore
  };

  const near = await connect(config);
  return await near.account(accountId);
}

/**
 * 测试 Jumbo Exchange 交易
 */
async function testJumboSwap() {
  try {
    logger.info('🚀 开始测试 Jumbo Exchange 交易');
    logger.info('='.repeat(60));

    // 从环境变量获取账户信息
    const accountId = process.env.NEAR_ACCOUNT_ID;
    const privateKey = process.env.NEAR_PRIVATE_KEY;
    const networkId = process.env.NEAR_NETWORK_ID || 'mainnet';

    if (!accountId || !privateKey) {
      throw new Error('请在 .env 文件中设置 NEAR_ACCOUNT_ID 和 NEAR_PRIVATE_KEY');
    }

    logger.info(`账户: ${accountId}`);
    logger.info(`网络: ${networkId}`);

    // 初始化 NEAR 连接
    logger.info('\n📡 初始化 NEAR 连接...');
    const account = await initializeNear(accountId, privateKey, networkId);
    logger.info('NEAR 连接初始化成功');

    // 初始化 Jumbo Exchange
    logger.info('\n🔧 初始化 Jumbo Exchange...');
    const jumbo = new JumboExchange(account);
    await jumbo.initialize();
    logger.info('Jumbo Exchange 初始化成功');

    // 创建执行服务
    const executionService = new JumboExecutionService(account);

    // 测试参数
    const testAmount = '0.001'; // 0.001 NEAR
    const poolId = 1; // NEAR-USDT.e 池子
    const tokenIn = 'wrap.near';
    const tokenOut = 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near'; // USDT.e
    const slippage = 1.0; // 1% 滑点

    logger.info('\n📊 测试参数:');
    logger.info(`  池子: Pool ${poolId} (NEAR-USDT.e)`);
    logger.info(`  输入: ${testAmount} NEAR`);
    logger.info(`  输出: USDT.e`);
    logger.info(`  滑点: ${slippage}%`);

    // 获取报价
    logger.info('\n💰 获取报价...');
    const quote = await jumbo.getNearUsdtQuote(testAmount);
    
    if (!quote.success) {
      throw new Error(`获取报价失败: ${quote.error}`);
    }

    logger.info(`报价结果:`);
    logger.info(`  输入: ${quote.amountIn} NEAR`);
    logger.info(`  预期输出: ${quote.amountOut} USDT.e`);
    logger.info(`  价格影响: ${quote.priceImpact}%`);
    logger.info(`  方法: ${quote.method}`);

    // 计算最小输出金额
    const minAmountOut = calculateMinAmountOut(quote.amountOutWei!, slippage);
    const minAmountOutReadable = fromWei(minAmountOut, getTokenDecimals(tokenOut));
    
    logger.info(`  最小输出: ${minAmountOutReadable} USDT.e (${slippage}% 滑点保护)`);

    // 检查余额
    logger.info('\n💳 检查账户余额...');
    try {
      const nearBalance = await account.getAccountBalance();
      const availableBalance = parseFloat(nearBalance.available) / 1e24;
      logger.info(`NEAR 余额: ${availableBalance.toFixed(6)} NEAR`);
      
      if (availableBalance < parseFloat(testAmount)) {
        throw new Error(`余额不足: 需要 ${testAmount} NEAR，但只有 ${availableBalance.toFixed(6)} NEAR`);
      }
    } catch (error) {
      logger.warn('无法获取余额信息:', error);
    }

    // 构建交易请求
    const swapRequest = {
      poolId,
      tokenIn,
      tokenOut,
      amountIn: quote.amountInWei,
      minAmountOut,
      slippage
    };

    logger.info('\n🔄 准备执行交易...');
    logger.info('交易请求:', {
      poolId: swapRequest.poolId,
      tokenIn: swapRequest.tokenIn,
      tokenOut: swapRequest.tokenOut,
      amountIn: `${quote.amountIn} NEAR (${swapRequest.amountIn} wei)`,
      minAmountOut: `${minAmountOutReadable} USDT.e (${swapRequest.minAmountOut} wei)`,
      slippage: `${swapRequest.slippage}%`
    });

    // 确认执行
    logger.info('\n⚠️  即将执行真实交易！');
    logger.info('请确认以下信息:');
    logger.info(`  - 将花费: ${testAmount} NEAR`);
    logger.info(`  - 预期获得: ~${quote.amountOut} USDT.e`);
    logger.info(`  - 最少获得: ${minAmountOutReadable} USDT.e`);
    logger.info(`  - 池子: Jumbo Exchange Pool ${poolId}`);
    
    // 这里可以添加用户确认逻辑
    logger.info('\n⏳ 执行交易中...');

    // 执行交易
    const result = await executionService.executeSwap(swapRequest);

    // 显示结果
    logger.info('\n📋 交易结果:');
    if (result.success) {
      logger.info('✅ 交易成功!');
      logger.info(`  交易哈希: ${result.transactionHash}`);
      logger.info(`  输入金额: ${result.inputAmount} NEAR`);
      logger.info(`  输出金额: ${result.outputAmount} USDT.e`);
      if (result.actualSlippage) {
        logger.info(`  实际滑点: ${result.actualSlippage}%`);
      }
      if (result.gasUsed) {
        logger.info(`  Gas 消耗: ${result.gasUsed}`);
      }
      
      // 计算实际价格
      const actualPrice = parseFloat(result.outputAmount!) / parseFloat(result.inputAmount!);
      const expectedPrice = parseFloat(quote.amountOut!) / parseFloat(quote.amountIn);
      const priceDiff = ((actualPrice - expectedPrice) / expectedPrice * 100).toFixed(2);
      
      logger.info(`  预期价格: ${expectedPrice.toFixed(6)} USDT.e/NEAR`);
      logger.info(`  实际价格: ${actualPrice.toFixed(6)} USDT.e/NEAR`);
      logger.info(`  价格差异: ${priceDiff}%`);
      
      if (networkId === 'mainnet') {
        logger.info(`  浏览器链接: https://nearblocks.io/txns/${result.transactionHash}`);
      } else {
        logger.info(`  浏览器链接: https://testnet.nearblocks.io/txns/${result.transactionHash}`);
      }
    } else {
      logger.error('❌ 交易失败!');
      logger.error(`  错误信息: ${result.error}`);
    }

    logger.info('\n🎉 测试完成!');

  } catch (error) {
    logger.error('❌ 测试失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await testJumboSwap();
  } catch (error) {
    logger.error('主程序失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testJumboSwap };
