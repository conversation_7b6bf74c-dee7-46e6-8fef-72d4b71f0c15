/**
 * Jumbo Exchange 池子管理器
 * 负责管理池子信息的获取、缓存和更新
 */

import { 
  IJumboPoolManager, 
  JumboPoolInfo, 
  JumboPoolsResponse, 
  JumboTargetPool,
  JumboPoolCache,
  JumboError,
  JumboErrorType
} from './types';
import { 
  JUMBO_CONFIG, 
  JUMBO_TARGET_POOLS, 
  buildApiUrl, 
  API_ENDPOINTS,
  getPoolConfig,
  isTargetPool
} from './config';

/**
 * Jumbo Exchange 池子管理器实现
 */
export class JumboPoolManager implements IJumboPoolManager {
  private cache: JumboPoolCache;
  private isUpdating: boolean = false;

  constructor() {
    this.cache = {
      pools: new Map(),
      lastUpdated: 0,
      isValid: false
    };
  }

  // ============================================================================
  // 公共方法
  // ============================================================================

  /**
   * 获取指定池子信息
   */
  async getPoolInfo(poolId: number): Promise<JumboPoolInfo | null> {
    try {
      // 检查缓存是否有效
      if (!this.isCacheValid()) {
        await this.refreshCache();
      }

      const poolInfo = this.cache.pools.get(poolId);
      if (!poolInfo) {
        console.warn(`Pool ${poolId} not found in cache`);
        return null;
      }

      return poolInfo;
    } catch (error) {
      console.error(`Error getting pool info for ${poolId}:`, error);
      throw new JumboError(
        JumboErrorType.POOL_NOT_FOUND,
        `Failed to get pool info for pool ${poolId}`,
        error
      );
    }
  }

  /**
   * 获取所有池子信息
   */
  async getAllPools(): Promise<JumboPoolsResponse> {
    try {
      const apiUrl = buildApiUrl(API_ENDPOINTS.POOLS);
      console.log(`Fetching pools from: ${apiUrl}`);

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const pools = await response.json() as JumboPoolsResponse;
      console.log(`Successfully fetched ${Object.keys(pools).length} pools`);

      return pools;
    } catch (error) {
      console.error('Error fetching pools from API:', error);
      throw new JumboError(
        JumboErrorType.API_ERROR,
        'Failed to fetch pools from Jumbo API',
        error
      );
    }
  }

  /**
   * 获取重点池子配置
   */
  getTargetPools(): JumboTargetPool[] {
    return JUMBO_TARGET_POOLS.filter(pool => pool.isActive);
  }

  /**
   * 刷新缓存
   */
  async refreshCache(): Promise<void> {
    if (this.isUpdating) {
      console.log('Cache update already in progress, waiting...');
      // 等待当前更新完成
      while (this.isUpdating) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return;
    }

    this.isUpdating = true;
    
    try {
      console.log('Refreshing Jumbo pools cache...');
      const allPools = await this.getAllPools();
      
      // 清空现有缓存
      this.cache.pools.clear();
      
      // 只缓存重点池子
      const targetPoolIds = JUMBO_TARGET_POOLS.map(p => p.poolId);
      
      for (const [key, poolInfo] of Object.entries(allPools)) {
        const poolId = parseInt(poolInfo.pool_id);
        
        if (targetPoolIds.includes(poolId)) {
          this.cache.pools.set(poolId, poolInfo);
          console.log(`Cached pool ${poolId} (${poolInfo.base_symbol}-${poolInfo.quote_symbol}): $${poolInfo.liquidity}`);
        }
      }

      // 更新缓存状态
      this.cache.lastUpdated = Date.now();
      this.cache.isValid = true;
      
      console.log(`Cache refreshed successfully. Cached ${this.cache.pools.size} target pools.`);
      
    } catch (error) {
      console.error('Failed to refresh cache:', error);
      this.cache.isValid = false;
      throw error;
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 检查池子是否活跃
   */
  isPoolActive(poolId: number): boolean {
    const config = getPoolConfig(poolId);
    if (!config || !config.isActive) {
      return false;
    }

    const poolInfo = this.cache.pools.get(poolId);
    if (!poolInfo) {
      return false;
    }

    // 检查流动性是否满足最小要求
    const liquidity = parseFloat(poolInfo.liquidity);
    return liquidity >= config.minLiquidity;
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    if (!this.cache.isValid) {
      return false;
    }

    const now = Date.now();
    const cacheAge = now - this.cache.lastUpdated;
    
    return cacheAge < JUMBO_CONFIG.cacheTimeout;
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 获取池子统计信息
   */
  getPoolStats() {
    const stats = {
      totalCachedPools: this.cache.pools.size,
      activePools: 0,
      totalLiquidity: 0,
      lastUpdated: this.cache.lastUpdated,
      cacheValid: this.cache.isValid
    };

    for (const [poolId, poolInfo] of this.cache.pools) {
      if (this.isPoolActive(poolId)) {
        stats.activePools++;
      }
      stats.totalLiquidity += parseFloat(poolInfo.liquidity || '0');
    }

    return stats;
  }

  /**
   * 获取池子详细信息（用于调试）
   */
  getPoolDetails(poolId: number) {
    const poolInfo = this.cache.pools.get(poolId);
    const config = getPoolConfig(poolId);
    
    if (!poolInfo || !config) {
      return null;
    }

    return {
      config,
      info: poolInfo,
      isActive: this.isPoolActive(poolId),
      liquidityUSD: parseFloat(poolInfo.liquidity),
      baseReserve: poolInfo.base_liquidity,
      quoteReserve: poolInfo.quote_liquidity
    };
  }

  /**
   * 强制清除缓存
   */
  clearCache(): void {
    this.cache.pools.clear();
    this.cache.lastUpdated = 0;
    this.cache.isValid = false;
    console.log('Jumbo pools cache cleared');
  }

  /**
   * 获取缓存状态
   */
  getCacheStatus() {
    return {
      isValid: this.cache.isValid,
      lastUpdated: this.cache.lastUpdated,
      cacheAge: Date.now() - this.cache.lastUpdated,
      poolCount: this.cache.pools.size,
      isUpdating: this.isUpdating
    };
  }
}

// ============================================================================
// 导出单例实例
// ============================================================================

/** Jumbo 池子管理器单例 */
export const jumboPoolManager = new JumboPoolManager();
