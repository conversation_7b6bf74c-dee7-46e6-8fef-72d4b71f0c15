# Jumbo Exchange 集成开发文档

## 📋 项目概述

本文档记录 Jumbo Exchange 集成到现有套利系统的完整开发过程，包括池子信息、API 集成、报价计算和交易执行。

## 🎯 重点池子信息

### 高流动性池子列表 (>1000 USD)

| Pool ID | 交易对 | 流动性 (USD) | 基础代币 | 报价代币 | 备注 |
|---------|--------|-------------|----------|----------|------|
| 1 | NEAR-USDT.e | 5,566 | wrap.near | dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near | **主要套利目标** |
| 231 | NEAR-LINEAR | 113,618 | wrap.near | linear-protocol.near | **最大流动性** |
| 6 | NEAR-HAPI | 6,499 | wrap.near | d9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near | 高流动性 |
| 184 | NEAR-UMINT | 3,736 | wrap.near | e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near | 中等流动性 |
| 274 | CHICA-NEAR | 3,213 | token.bocachica_mars.near | wrap.near | 中等流动性 |
| 294 | 1MIL-NEAR | 6,046 | a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near | wrap.near | 高流动性 |
| 266 | NearX-NEAR | 1,533 | v2-nearx.stader-labs.near | wrap.near | 中等流动性 |

### 合约信息
- **主合约地址**: `v1.jumbo_exchange.near`
- **价格服务 API**: `https://price-service.jumbo.exchange`
- **报价方法**: `get_return(pool_id, token_in, amount_in, token_out)`

## 🚀 开发计划

### Phase 1: 基础架构 ✅
- [x] 创建开发文档
- [x] 设计模块架构
- [x] 实现类型定义 (types.ts)
- [x] 实现配置管理 (config.ts)
- [x] 实现池子信息管理 (pool-manager.ts)
- [x] 实现工具函数 (utils.ts)

### Phase 2: 报价系统 ✅
- [x] 实现本地 AMM 计算
- [x] 实现合约直接调用
- [x] 实现报价服务 (quote-service.ts)
- [x] 创建主入口文件 (index.ts)
- [x] 创建测试脚本 (test-quotes.ts)

### Phase 3: 交易执行 ✅
- [x] 实现交易执行服务 (execution-service.ts)
- [x] 实现交易构建和提交
- [x] 实现结果解析和验证
- [x] 实现用户注册检查
- [x] 完整的错误处理机制

### Phase 4: 套利集成
- [ ] 集成到现有监控系统
- [ ] 三方价格比较
- [ ] 套利机会检测
- [ ] 自动执行逻辑

## 📁 模块架构设计

```
reusable-modules/jumbo-exchange/
├── JUMBO_DEVELOPMENT.md          # 开发文档
├── index.ts                      # 主入口文件
├── types.ts                      # 类型定义
├── config.ts                     # 配置管理
├── pool-manager.ts               # 池子信息管理
├── quote-service.ts              # 报价服务
├── execution-service.ts          # 交易执行服务
└── utils.ts                      # 工具函数
```

## 🔧 技术要点

### API 集成
- **池子数据**: `GET https://price-service.jumbo.exchange/pools`
- **代币价格**: `GET https://price-service.jumbo.exchange/token-prices`
- **实时更新**: 定期获取最新流动性数据

### AMM 计算公式
```typescript
// Uniswap 风格 AMM 公式
function calculateOutput(amountIn: bigint, reserveIn: bigint, reserveOut: bigint): bigint {
  const fee = 997n; // 0.3% 手续费
  const amountInWithFee = amountIn * fee;
  const numerator = amountInWithFee * reserveOut;
  const denominator = (reserveIn * 1000n) + amountInWithFee;
  return numerator / denominator;
}
```

### 合约调用
```typescript
// 直接调用合约获取报价
const quote = await nearConnection.viewFunction(
  'v1.jumbo_exchange.near',
  'get_return',
  {
    pool_id: 1,
    token_in: 'wrap.near',
    amount_in: amountIn,
    token_out: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near'
  }
);
```

## 📝 开发日志

### 2024-12-23
- ✅ 完成池子信息调研和筛选
- ✅ 确定 7 个重点池子 (ID: 1, 231, 6, 184, 274, 294, 266)
- ✅ 创建开发文档和架构设计
- ✅ 完成基础架构开发

#### 已完成的模块:
1. ✅ **types.ts** - 完整的 TypeScript 类型定义
2. ✅ **config.ts** - 配置管理和常量定义
3. ✅ **pool-manager.ts** - 池子信息管理和缓存
4. ✅ **utils.ts** - AMM 计算和工具函数
5. ✅ **quote-service.ts** - 报价服务实现
6. ✅ **execution-service.ts** - 交易执行服务
7. ✅ **index.ts** - 主入口文件和统一接口
8. ✅ **test-quotes.ts** - 报价功能测试脚本
9. ✅ **test-swap.ts** - 完整交易测试脚本
10. ✅ **test-simple-swap.ts** - 简化交易测试脚本

#### 报价服务特性:
- ✅ 支持 7 个重点池子的报价获取
- ✅ 智能报价方法选择（本地计算 -> 合约调用）
- ✅ 固定测试金额 4 的便捷方法
- ✅ 批量报价获取功能
- ✅ 完整的错误处理和重试机制

#### 交易执行特性:
- ✅ 支持 NEAR 代币的 ft_transfer_call 交易
- ✅ 自动用户注册检查和注册
- ✅ 完整的交易结果解析
- ✅ 滑点保护和最小输出计算
- ✅ 详细的交易日志和错误处理

### 下一步计划
1. ✅ 完成交易执行功能开发
2. ✅ **测试成功**: 0.001 NEAR -> 0.001904 USDT.e 交易成功执行
3. 🔄 集成到现有套利监控系统
4. 实现 Jumbo vs REF 价格比较

#### 测试结果 (2024-12-23):
- ✅ **交易成功**: 修复 msg 格式后交易正常执行
- ✅ **交易哈希**: `96GEeqse3mfyS91ZvPYxWUNjbTjoPjG4hEsJwXmD6hRv`
- ✅ **输入**: 0.001 NEAR
- ✅ **实际输出**: 0.001985 USDT.e (比预期更好!)
- ✅ **价格**: 1.985 USDT.e/NEAR (预期 1.924，实际更优)
- ✅ **自动包装**: 成功将 NEAR 包装为 wNEAR
- ✅ **msg 格式修复**: 解决了 E28 错误
- ⚠️ **存储问题**: 需要为输出代币注册存储

## 🎯 成功指标

- [ ] 能够准确获取 7 个重点池子的实时数据
- [ ] 本地 AMM 计算与合约调用结果误差 < 0.1%
- [ ] 报价响应时间 < 500ms
- [ ] 成功集成到现有套利监控系统
- [ ] 能够检测到 REF ↔ VEAX ↔ JUMBO 三方套利机会

## 📚 参考资料

- [Jumbo Exchange GitHub](https://github.com/jumbo-exchange)
- [NEAR 合约调用文档](https://docs.near.org/develop/contracts/crosscontract)
- [现有 REF Finance 集成代码](../ref-finance/)
- [现有 VEAX 集成代码](../veax/)
