/**
 * Jumbo Exchange 配置管理
 * 管理所有与 Jumbo Exchange 集成相关的配置信息
 */

import { JumboConfig, JumboTargetPool } from './types';

// ============================================================================
// 基础配置
// ============================================================================

/** Jumbo Exchange 主配置 */
export const JUMBO_CONFIG: JumboConfig = {
  contractAddress: 'v1.jumbo_exchange.near',
  apiBaseUrl: 'https://price-service.jumbo.exchange',
  networkId: 'mainnet',
  defaultGas: '300000000000000', // 300 TGas
  defaultSlippage: 0.5, // 0.5%
  cacheTimeout: 30000, // 30 秒
  retryAttempts: 3,
  retryDelay: 1000 // 1 秒
};

// ============================================================================
// 重点池子配置
// ============================================================================

/**
 * 8 个重点池子的详细配置
 *
 * 🔧 添加新池子的步骤：
 * 1. 在下面数组中添加新的池子配置
 * 2. 确保 poolId 与 Jumbo Exchange 实际池子ID一致
 * 3. 设置合理的 minLiquidity (最小流动性要求)
 * 4. 设置 isActive: true 启用池子
 *
 * 示例：
 * {
 *   poolId: 999,
 *   name: 'NEAR-NEWTOKEN',
 *   baseToken: 'wrap.near',
 *   quoteToken: 'newtoken.contract.near',
 *   baseSymbol: 'NEAR',
 *   quoteSymbol: 'NEWTOKEN',
 *   minLiquidity: 1000,
 *   isActive: true
 * }
 */
export const JUMBO_TARGET_POOLS: JumboTargetPool[] = [
  {
    poolId: 0,
    name: 'NEAR-AURORA',
    baseToken: 'wrap.near',
    quoteToken: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'AURORA',
    minLiquidity: 5000,
    isActive: true
  },
  {
    poolId: 1,
    name: 'NEAR-USDT.e',
    baseToken: 'wrap.near',
    quoteToken: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'USDT.e',
    minLiquidity: 5000,
    isActive: true
  },
  {
    poolId: 3,
    name: 'NEAR-OCT',
    baseToken: 'wrap.near',
    quoteToken: 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'OCT',
    minLiquidity: 1000,
    isActive: true
  },
  {
    poolId: 231,
    name: 'NEAR-LINEAR',
    baseToken: 'wrap.near',
    quoteToken: 'linear-protocol.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'LINEAR',
    minLiquidity: 100000,
    isActive: true
  },
  {
    poolId: 6,
    name: 'NEAR-HAPI',
    baseToken: 'wrap.near',
    quoteToken: 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'HAPI',
    minLiquidity: 6000,
    isActive: true
  },
  {
    poolId: 184,
    name: 'NEAR-UMINT',
    baseToken: 'wrap.near',
    quoteToken: 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'UMINT',
    minLiquidity: 3500,
    isActive: true
  },
  {
    poolId: 274,
    name: 'NEAR-CHICA',
    baseToken: 'wrap.near',
    quoteToken: 'token.bocachica_mars.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'CHICA',
    minLiquidity: 2000,
    isActive: true
  },
  {
    poolId: 294,
    name: 'NEAR-1MIL',
    baseToken: 'wrap.near',
    quoteToken: 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near',
    baseSymbol: 'NEAR',
    quoteSymbol: '1MIL',
    minLiquidity: 6000,
    isActive: true
  },
  {
    poolId: 266,
    name: 'NEAR-NearX',
    baseToken: 'wrap.near',
    quoteToken: 'v2-nearx.stader-labs.near',
    baseSymbol: 'NEAR',
    quoteSymbol: 'NearX',
    minLiquidity: 1500,
    isActive: true
  }
];

// ============================================================================
// 代币地址映射
// ============================================================================

/**
 * 常用代币地址映射
 *
 * 🔧 添加新代币地址的步骤：
 * 1. 在下面对象中添加: SYMBOL: 'contract.address.near'
 * 2. 确保符号与交易对配置中的 symbol 一致
 *
 * 示例：
 * NEWTOKEN: 'newtoken.contract.near'
 */
export const TOKEN_ADDRESSES = {
  NEAR: 'wrap.near',
  AURORA: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
  USDT: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
  USDC: 'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near',
  OCT: 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near',
  LINEAR: 'linear-protocol.near',
  HAPI: 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near',
  UMINT: 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near',
  CHICA: 'token.bocachica_mars.near',
  '1MIL': 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near',
  NEARX: 'v2-nearx.stader-labs.near',
  JUMBO: 'token.jumbo_exchange.near'
} as const;

/** 代币符号到地址的反向映射 */
export const SYMBOL_TO_ADDRESS = Object.fromEntries(
  Object.entries(TOKEN_ADDRESSES).map(([symbol, address]) => [symbol, address])
);

/** 地址到代币符号的映射 */
export const ADDRESS_TO_SYMBOL = Object.fromEntries(
  Object.entries(TOKEN_ADDRESSES).map(([symbol, address]) => [address, symbol])
);

// ============================================================================
// 代币精度配置
// ============================================================================

/**
 * 代币精度映射 - 统一管理所有代币的小数位数
 *
 * 🔧 添加新代币精度的步骤：
 * 1. 在下面对象中添加: 'contract.address.near': 精度位数
 * 2. 常见精度: NEAR生态=24位, USDT/USDC=6位, 其他=18位
 * 3. 添加注释说明代币名称
 *
 * 示例：
 * 'newtoken.contract.near': 18, // NEWTOKEN
 */
export const TOKEN_DECIMALS: { [address: string]: number } = {
  // NEAR 生态代币 (24位小数)
  'wrap.near': 24,
  'linear-protocol.near': 24,
  'v2-nearx.stader-labs.near': 24,

  // 桥接代币 (6位小数)
  'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near': 6, // USDT.e
  'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near': 6, // USDC.e

  // 其他代币 (18位小数)
  'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near': 18, // AURORA
  'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near': 18, // OCT
  'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near': 18, // HAPI
  'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near': 18, // UMINT
  'token.bocachica_mars.near': 18, // CHICA
  'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near': 18, // 1MIL
  'token.jumbo_exchange.near': 18 // JUMBO
};

// ============================================================================
// API 端点配置
// ============================================================================

/** API 端点配置 */
export const API_ENDPOINTS = {
  POOLS: '/pools',
  TOKEN_PRICES: '/token-prices',
  VOLUMES: '/pools/volumes'
} as const;

// ============================================================================
// 合约方法配置
// ============================================================================

/** 合约方法名称 */
export const CONTRACT_METHODS = {
  // 查询方法
  GET_RETURN: 'get_return',
  GET_POOL: 'get_pool',
  GET_POOLS: 'get_pools',
  GET_NUMBER_OF_POOLS: 'get_number_of_pools',
  
  // 交易方法
  SWAP: 'swap',
  STORAGE_DEPOSIT: 'storage_deposit',
  REGISTER_TOKENS: 'register_tokens',
  
  // 代币方法
  FT_TRANSFER_CALL: 'ft_transfer_call'
} as const;

// ============================================================================
// 费用和限制配置
// ============================================================================

/** 交易费用配置 */
export const FEE_CONFIG = {
  SWAP_FEE: 997n, // 0.3% 手续费 (1000 - 3)
  FEE_DIVISOR: 1000n,
  STORAGE_DEPOSIT: '0.00125', // NEAR
  TOKEN_REGISTRATION: '1' // yoctoNEAR
} as const;

/** 交易限制配置 */
export const LIMITS = {
  MIN_AMOUNT_IN: '1000000000000000000000', // 0.001 NEAR (wei) - 修复：21位不是24位
  MAX_AMOUNT_IN: '100000000000000000000000000', // 100 NEAR (wei) - 修复：26位不是29位
  MAX_SLIPPAGE: 5.0, // 5%
  MIN_SLIPPAGE: 0.1, // 0.1%
  MAX_PRICE_IMPACT: 10.0 // 10%
} as const;

// ============================================================================
// 工具函数
// ============================================================================

/** 获取池子配置 */
export function getPoolConfig(poolId: number): JumboTargetPool | undefined {
  return JUMBO_TARGET_POOLS.find(pool => pool.poolId === poolId);
}

/** 获取代币地址 */
export function getTokenAddress(symbol: string): string | undefined {
  return TOKEN_ADDRESSES[symbol as keyof typeof TOKEN_ADDRESSES];
}

/** 获取代币符号 */
export function getTokenSymbol(address: string): string | undefined {
  return ADDRESS_TO_SYMBOL[address];
}

/** 获取代币精度 */
export function getTokenDecimals(address: string): number {
  return TOKEN_DECIMALS[address] || 24; // 默认使用 24 位小数
}

/** 检查池子是否为重点池子 */
export function isTargetPool(poolId: number): boolean {
  return JUMBO_TARGET_POOLS.some(pool => pool.poolId === poolId && pool.isActive);
}

/** 获取所有活跃池子ID */
export function getActivePoolIds(): number[] {
  return JUMBO_TARGET_POOLS
    .filter(pool => pool.isActive)
    .map(pool => pool.poolId);
}

/** 构建完整的 API URL */
export function buildApiUrl(endpoint: string): string {
  return `${JUMBO_CONFIG.apiBaseUrl}${endpoint}`;
}

/** 验证交易参数 */
export function validateSwapParams(amountIn: string, slippage: number): boolean {
  const amountInBig = BigInt(amountIn);
  const minAmount = BigInt(LIMITS.MIN_AMOUNT_IN);
  const maxAmount = BigInt(LIMITS.MAX_AMOUNT_IN);
  
  return (
    amountInBig >= minAmount &&
    amountInBig <= maxAmount &&
    slippage >= LIMITS.MIN_SLIPPAGE &&
    slippage <= LIMITS.MAX_SLIPPAGE
  );
}
