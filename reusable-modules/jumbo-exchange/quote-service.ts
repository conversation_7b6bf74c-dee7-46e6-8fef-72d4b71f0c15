/**
 * Jumbo Exchange 报价服务
 * 实现多种报价获取方式：API、合约调用、本地计算
 */

import { Account } from 'near-api-js';
import { 
  IJumboQuoteService, 
  JumboQuoteRequest, 
  JumboQuoteResult,
  JumboError,
  JumboErrorType
} from './types';
import { jumboPoolManager } from './pool-manager';
import {
  calculateAMMOutput,
  calculatePriceImpact,
  toWei,
  fromWei,
  getTokenDecimals,
  withRetry,
  createLogger,
  getCurrentTimestamp,
  isValidAmount,
  isValidTokenAddress,
  isValidPoolId
} from './utils';
import { 
  JUMBO_CONFIG, 
  CONTRACT_METHODS,
  getPoolConfig,
  isTargetPool
} from './config';

const logger = createLogger('JumboQuote');

/**
 * Jumbo Exchange 报价服务实现
 */
export class JumboQuoteService implements IJumboQuoteService {
  private nearAccount?: Account;

  constructor(nearAccount?: Account) {
    this.nearAccount = nearAccount;
  }

  // ============================================================================
  // 主要报价方法
  // ============================================================================

  /**
   * 获取报价 - 智能选择最佳方法
   */
  async getQuote(request: JumboQuoteRequest): Promise<JumboQuoteResult> {
    try {
      // 验证请求参数
      this.validateQuoteRequest(request);

      logger.info(`Getting quote for pool ${request.poolId}: ${request.amountIn} ${request.tokenIn} -> ${request.tokenOut}`);

      // 🔧 修复：优先使用合约调用（最准确）
      if (this.nearAccount) {
        try {
          const contractResult = await this.getQuoteFromContract(request);
          if (contractResult.success) {
            logger.info(`Contract quote successful: ${contractResult.amountOut}`);
            return contractResult;
          }
        } catch (error) {
          logger.warn('Contract quote failed, trying local calculation:', error instanceof Error ? error.message : String(error));
        }
      }

      // 备选：本地计算
      try {
        const localResult = await this.calculateLocalQuote(request);
        if (localResult.success) {
          logger.info(`Local quote successful: ${localResult.amountOut}`);
          return localResult;
        }
      } catch (error) {
        logger.warn('Local quote failed:', error instanceof Error ? error.message : String(error));
      }

      // 最后备选：API 计算（如果有的话）
      throw new JumboError(
        JumboErrorType.CALCULATION_ERROR,
        'All quote methods failed'
      );

    } catch (error) {
      logger.error('Quote request failed:', error);
      return this.createErrorResult(request, error);
    }
  }

  /**
   * 从 API 获取报价（暂时使用本地计算）
   */
  async getQuoteFromAPI(request: JumboQuoteRequest): Promise<JumboQuoteResult> {
    // Jumbo 暂时没有直接的报价 API，使用本地计算
    return this.calculateLocalQuote(request);
  }

  /**
   * 从合约获取报价
   */
  async getQuoteFromContract(request: JumboQuoteRequest): Promise<JumboQuoteResult> {
    if (!this.nearAccount) {
      throw new JumboError(
        JumboErrorType.CONTRACT_ERROR,
        'NEAR account not provided for contract calls'
      );
    }

    try {
      const result = await withRetry(async () => {
        return await this.nearAccount!.viewFunction({
          contractId: JUMBO_CONFIG.contractAddress,
          methodName: CONTRACT_METHODS.GET_RETURN,
          args: {
            pool_id: request.poolId,
            token_in: request.tokenIn,
            amount_in: request.amountIn,
            token_out: request.tokenOut
          }
        });
      });

      const amountOutWei = result.toString();
      const tokenOutDecimals = getTokenDecimals(request.tokenOut);
      const tokenInDecimals = getTokenDecimals(request.tokenIn);
      const amountOut = fromWei(amountOutWei, tokenOutDecimals);

      return {
        success: true,
        poolId: request.poolId,
        tokenIn: request.tokenIn,
        tokenOut: request.tokenOut,
        amountIn: fromWei(request.amountIn, tokenInDecimals),
        amountInWei: request.amountIn,
        amountOut,
        amountOutWei,
        method: 'contract',
        timestamp: getCurrentTimestamp()
      };

    } catch (error) {
      throw new JumboError(
        JumboErrorType.CONTRACT_ERROR,
        `Contract quote failed: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  /**
   * 本地计算报价
   */
  async calculateLocalQuote(request: JumboQuoteRequest): Promise<JumboQuoteResult> {
    try {
      // 获取池子信息
      const poolInfo = await jumboPoolManager.getPoolInfo(request.poolId);
      if (!poolInfo) {
        throw new Error(`Pool ${request.poolId} not found`);
      }

      // 确定输入输出方向
      const isBaseToQuote = request.tokenIn === poolInfo.base_id;
      const isQuoteToBase = request.tokenIn === poolInfo.quote_id;

      if (!isBaseToQuote && !isQuoteToBase) {
        throw new Error(`Token ${request.tokenIn} not found in pool ${request.poolId}`);
      }

      // 获取储备量
      const reserveIn = isBaseToQuote ? 
        BigInt(poolInfo.base_liquidity) : 
        BigInt(poolInfo.quote_liquidity);
      
      const reserveOut = isBaseToQuote ? 
        BigInt(poolInfo.quote_liquidity) : 
        BigInt(poolInfo.base_liquidity);

      // 计算输出金额
      const amountInBig = BigInt(request.amountIn);
      const amountOutBig = calculateAMMOutput({
        amountIn: amountInBig,
        reserveIn,
        reserveOut
      });

      const amountOutWei = amountOutBig.toString();
      const tokenOutDecimals = getTokenDecimals(request.tokenOut);
      const tokenInDecimals = getTokenDecimals(request.tokenIn);
      const amountOut = fromWei(amountOutWei, tokenOutDecimals);

      // 计算价格影响
      const priceImpact = calculatePriceImpact(
        amountInBig,
        amountOutBig,
        reserveIn,
        reserveOut
      );

      return {
        success: true,
        poolId: request.poolId,
        tokenIn: request.tokenIn,
        tokenOut: request.tokenOut,
        amountIn: fromWei(request.amountIn, tokenInDecimals),
        amountInWei: request.amountIn,
        amountOut,
        amountOutWei,
        priceImpact,
        method: 'local',
        timestamp: getCurrentTimestamp()
      };

    } catch (error) {
      throw new JumboError(
        JumboErrorType.CALCULATION_ERROR,
        `Local quote calculation failed: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  // ============================================================================
  // 便捷方法 - 针对重点池子
  // ============================================================================

  /**
   * 获取 NEAR-AURORA 报价 (Pool 0)
   */
  async getNearAuroraQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 0,
      tokenIn: 'wrap.near',
      tokenOut: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-USDT.e 报价 (Pool 1)
   */
  async getNearUsdtQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 1,
      tokenIn: 'wrap.near',
      tokenOut: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-LINEAR 报价 (Pool 231)
   */
  async getNearLinearQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 231,
      tokenIn: 'wrap.near',
      tokenOut: 'linear-protocol.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-HAPI 报价 (Pool 6)
   */
  async getNearHapiQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 6,
      tokenIn: 'wrap.near',
      tokenOut: 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-UMINT 报价 (Pool 184)
   */
  async getNearUmintQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 184,
      tokenIn: 'wrap.near',
      tokenOut: 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-CHICA 报价 (Pool 274)
   */
  async getNearChicaQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 274,
      tokenIn: 'wrap.near',
      tokenOut: 'token.bocachica_mars.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-1MIL 报价 (Pool 294)
   */
  async getNear1MilQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 294,
      tokenIn: 'wrap.near',
      tokenOut: 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near',
      amountIn: amountInWei
    });
  }

  /**
   * 获取 NEAR-NearX 报价 (Pool 266)
   */
  async getNearNearxQuote(amountNear: string = '4'): Promise<JumboQuoteResult> {
    const amountInWei = toWei(amountNear);
    return this.getQuote({
      poolId: 266,
      tokenIn: 'wrap.near',
      tokenOut: 'v2-nearx.stader-labs.near',
      amountIn: amountInWei
    });
  }

  // ============================================================================
  // 批量报价方法
  // ============================================================================

  /**
   * 获取所有重点池子的报价
   */
  async getAllQuotes(): Promise<{ [poolId: number]: JumboQuoteResult }> {
    const results: { [poolId: number]: JumboQuoteResult } = {};

    const quotePromises = [
      { id: 0, fn: () => this.getNearAuroraQuote() },
      { id: 1, fn: () => this.getNearUsdtQuote() },
      { id: 231, fn: () => this.getNearLinearQuote() },
      { id: 6, fn: () => this.getNearHapiQuote() },
      { id: 184, fn: () => this.getNearUmintQuote() },
      { id: 274, fn: () => this.getNearChicaQuote() },
      { id: 294, fn: () => this.getNear1MilQuote() },
      { id: 266, fn: () => this.getNearNearxQuote() }
    ];

    const quotes = await Promise.allSettled(
      quotePromises.map(({ fn }) => fn())
    );

    quotes.forEach((result, index) => {
      const poolId = quotePromises[index].id;
      if (result.status === 'fulfilled') {
        results[poolId] = result.value;
      } else {
        logger.error(`Quote failed for pool ${poolId}:`, result.reason);
        results[poolId] = this.createErrorResult(
          { poolId, tokenIn: '', tokenOut: '', amountIn: '' },
          result.reason
        );
      }
    });

    return results;
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  /**
   * 验证报价请求参数
   */
  private validateQuoteRequest(request: JumboQuoteRequest): void {
    if (!isValidPoolId(request.poolId)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid pool ID');
    }

    if (!isTargetPool(request.poolId)) {
      throw new JumboError(JumboErrorType.POOL_NOT_FOUND, `Pool ${request.poolId} is not a target pool`);
    }

    if (!isValidTokenAddress(request.tokenIn)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid token in address');
    }

    if (!isValidTokenAddress(request.tokenOut)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid token out address');
    }

    if (!isValidAmount(request.amountIn)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid amount in');
    }
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(request: JumboQuoteRequest, error: any): JumboQuoteResult {
    const tokenInDecimals = getTokenDecimals(request.tokenIn);
    return {
      success: false,
      poolId: request.poolId,
      tokenIn: request.tokenIn,
      tokenOut: request.tokenOut,
      amountIn: request.amountIn ? fromWei(request.amountIn, tokenInDecimals) : '0',
      amountInWei: request.amountIn || '0',
      error: error.message || 'Unknown error',
      method: 'local',
      timestamp: getCurrentTimestamp()
    };
  }
}

// ============================================================================
// 导出单例实例
// ============================================================================

/** Jumbo 报价服务单例 */
export const jumboQuoteService = new JumboQuoteService();
