/**
 * Jumbo Exchange 交易执行服务
 * 负责构建和执行 Jumbo Exchange 的交易
 */

import { Account } from 'near-api-js';
import { parseNearAmount } from 'near-api-js/lib/utils/format';
import { 
  IJumboExecutionService, 
  JumboSwapRequest, 
  JumboSwapResult,
  JumboSwapAction,
  JumboError,
  JumboErrorType
} from './types';
import { 
  JUMBO_CONFIG, 
  CONTRACT_METHODS,
  FEE_CONFIG,
  getPoolConfig
} from './config';
import {
  toWei,
  fromWei,
  getTokenDecimals,
  calculateMinAmountOut,
  withRetry,
  createLogger,
  getCurrentTimestamp,
  isValidAmount,
  isValidTokenAddress,
  isValidPoolId,
  isValidSlippage
} from './utils';

const logger = createLogger('JumboExecution');

/**
 * Jumbo Exchange 交易执行服务实现
 */
export class JumboExecutionService implements IJumboExecutionService {
  private nearAccount: Account;

  constructor(nearAccount: Account) {
    this.nearAccount = nearAccount;
  }

  // ============================================================================
  // 主要交易方法
  // ============================================================================

  /**
   * 执行交换交易
   */
  async executeSwap(request: JumboSwapRequest): Promise<JumboSwapResult> {
    try {
      logger.info(`Executing swap: Pool ${request.poolId}, ${request.amountIn} ${request.tokenIn} -> ${request.tokenOut}`);
      
      // 验证请求参数
      this.validateSwapRequest(request);

      // 检查用户是否已注册到 Jumbo Exchange
      const isRegistered = await this.checkUserRegistration();
      if (!isRegistered) {
        logger.info('User not registered to Jumbo Exchange, registering...');
        await this.registerUser();
      }

      // 如果输入代币是 NEAR，还需要检查是否在 wrap.near 合约中注册并包装足够的 NEAR
      if (request.tokenIn === 'wrap.near') {
        const isWrapRegistered = await this.checkWrapNearRegistration();
        if (!isWrapRegistered) {
          logger.info('User not registered to wrap.near, registering...');
          await this.registerToWrapNear();
        }

        // 检查 wNEAR 余额是否足够
        const wNearBalance = await this.getWNearBalance();
        const requiredAmount = BigInt(request.amountIn);

        if (BigInt(wNearBalance) < requiredAmount) {
          const needToWrap = requiredAmount - BigInt(wNearBalance);
          logger.info(`Insufficient wNEAR balance. Need to wrap ${fromWei(needToWrap.toString())} NEAR`);
          await this.wrapNear(needToWrap.toString());
        }
      }

      // 检查输出代币的存储注册
      const isOutputTokenRegistered = await this.checkTokenRegistration(request.tokenOut);
      if (!isOutputTokenRegistered) {
        logger.info(`User not registered to ${request.tokenOut}, registering...`);
        await this.registerToToken(request.tokenOut);
      }

      // 构建交换动作
      const swapAction = this.buildSwapAction(request);
      logger.info('Swap action built:', swapAction);

      // 执行交易
      const result = await this.executeTransaction(swapAction, request);
      
      logger.info(`Swap executed successfully: ${result.transactionHash}`);
      return result;

    } catch (error) {
      logger.error('Swap execution failed:', error);
      return this.createErrorResult(request, error);
    }
  }

  /**
   * 构建交换动作
   */
  buildSwapAction(request: JumboSwapRequest): JumboSwapAction {
    return {
      pool_id: request.poolId,
      token_in: request.tokenIn,
      amount_in: request.amountIn,
      token_out: request.tokenOut,
      min_amount_out: request.minAmountOut
    };
  }

  /**
   * 检查用户注册状态
   */
  async checkUserRegistration(): Promise<boolean> {
    try {
      const result = await this.nearAccount.viewFunction({
        contractId: JUMBO_CONFIG.contractAddress,
        methodName: 'storage_balance_of',
        args: {
          account_id: this.nearAccount.accountId
        }
      });
      
      return result !== null;
    } catch (error) {
      logger.warn('Failed to check user registration:', error);
      return false;
    }
  }

  /**
   * 注册用户到 Jumbo Exchange
   */
  async registerUser(): Promise<void> {
    try {
      await withRetry(async () => {
        return await this.nearAccount.functionCall({
          contractId: JUMBO_CONFIG.contractAddress,
          methodName: CONTRACT_METHODS.STORAGE_DEPOSIT,
          args: {
            account_id: this.nearAccount.accountId,
            registration_only: true
          },
          gas: BigInt(JUMBO_CONFIG.defaultGas),
          attachedDeposit: BigInt(parseNearAmount(FEE_CONFIG.STORAGE_DEPOSIT) || '0')
        });
      });

      logger.info('User registered to Jumbo Exchange successfully');
    } catch (error) {
      throw new JumboError(
        JumboErrorType.TRANSACTION_FAILED,
        `Failed to register user to Jumbo Exchange: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  /**
   * 检查用户是否在 wrap.near 合约中注册
   */
  async checkWrapNearRegistration(): Promise<boolean> {
    try {
      const result = await this.nearAccount.viewFunction({
        contractId: 'wrap.near',
        methodName: 'storage_balance_of',
        args: {
          account_id: this.nearAccount.accountId
        }
      });

      return result !== null;
    } catch (error) {
      logger.warn('Failed to check wrap.near registration:', error);
      return false;
    }
  }

  /**
   * 注册用户到 wrap.near 合约
   */
  async registerToWrapNear(): Promise<void> {
    try {
      await withRetry(async () => {
        return await this.nearAccount.functionCall({
          contractId: 'wrap.near',
          methodName: 'storage_deposit',
          args: {
            account_id: this.nearAccount.accountId,
            registration_only: true
          },
          gas: BigInt(JUMBO_CONFIG.defaultGas),
          attachedDeposit: BigInt(parseNearAmount('0.00125') || '0') // wrap.near 注册费用
        });
      });

      logger.info('User registered to wrap.near successfully');
    } catch (error) {
      throw new JumboError(
        JumboErrorType.TRANSACTION_FAILED,
        `Failed to register user to wrap.near: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  /**
   * 获取 wNEAR 余额
   */
  async getWNearBalance(): Promise<string> {
    try {
      const balance = await this.nearAccount.viewFunction({
        contractId: 'wrap.near',
        methodName: 'ft_balance_of',
        args: {
          account_id: this.nearAccount.accountId
        }
      });

      return balance || '0';
    } catch (error) {
      logger.warn('Failed to get wNEAR balance:', error);
      return '0';
    }
  }

  /**
   * 包装 NEAR 为 wNEAR
   */
  async wrapNear(amount: string): Promise<void> {
    try {
      await withRetry(async () => {
        return await this.nearAccount.functionCall({
          contractId: 'wrap.near',
          methodName: 'near_deposit',
          args: {},
          gas: BigInt(JUMBO_CONFIG.defaultGas),
          attachedDeposit: BigInt(amount)
        });
      });

      logger.info(`Successfully wrapped ${fromWei(amount)} NEAR to wNEAR`);
    } catch (error) {
      throw new JumboError(
        JumboErrorType.TRANSACTION_FAILED,
        `Failed to wrap NEAR: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  /**
   * 检查用户是否在指定代币合约中注册
   */
  async checkTokenRegistration(tokenAddress: string): Promise<boolean> {
    try {
      const result = await this.nearAccount.viewFunction({
        contractId: tokenAddress,
        methodName: 'storage_balance_of',
        args: {
          account_id: this.nearAccount.accountId
        }
      });

      return result !== null;
    } catch (error) {
      logger.warn(`Failed to check ${tokenAddress} registration:`, error);
      return false;
    }
  }

  /**
   * 注册用户到指定代币合约
   */
  async registerToToken(tokenAddress: string): Promise<void> {
    try {
      await withRetry(async () => {
        return await this.nearAccount.functionCall({
          contractId: tokenAddress,
          methodName: 'storage_deposit',
          args: {
            account_id: this.nearAccount.accountId,
            registration_only: true
          },
          gas: BigInt(JUMBO_CONFIG.defaultGas),
          attachedDeposit: BigInt(parseNearAmount('0.00125') || '0') // 标准代币注册费用
        });
      });

      logger.info(`User registered to ${tokenAddress} successfully`);
    } catch (error) {
      throw new JumboError(
        JumboErrorType.TRANSACTION_FAILED,
        `Failed to register user to ${tokenAddress}: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  /**
   * 执行实际交易
   */
  private async executeTransaction(swapAction: JumboSwapAction, request: JumboSwapRequest): Promise<JumboSwapResult> {
    try {
      const startTime = Date.now();
      
      // 如果输入代币是 NEAR，使用 ft_transfer_call
      if (request.tokenIn === 'wrap.near') {
        const result = await withRetry(async () => {
          return await this.nearAccount.functionCall({
            contractId: request.tokenIn,
            methodName: CONTRACT_METHODS.FT_TRANSFER_CALL,
            args: {
              receiver_id: JUMBO_CONFIG.contractAddress,
              amount: request.amountIn,
              memo: null,
              msg: JSON.stringify({
                force: 0,
                actions: [{
                  pool_id: swapAction.pool_id,
                  token_in: swapAction.token_in,
                  token_out: swapAction.token_out,
                  amount_in: swapAction.amount_in,
                  min_amount_out: swapAction.min_amount_out
                }]
              })
            },
            gas: BigInt(JUMBO_CONFIG.defaultGas),
            attachedDeposit: 1n // 1 yoctoNEAR
          });
        });

        return this.parseTransactionResult(result, request, startTime);
      } else {
        // 对于其他代币，直接调用 swap 方法
        const result = await withRetry(async () => {
          return await this.nearAccount.functionCall({
            contractId: JUMBO_CONFIG.contractAddress,
            methodName: CONTRACT_METHODS.SWAP,
            args: swapAction,
            gas: BigInt(JUMBO_CONFIG.defaultGas),
            attachedDeposit: 1n // 1 yoctoNEAR
          });
        });

        return this.parseTransactionResult(result, request, startTime);
      }
    } catch (error) {
      throw new JumboError(
        JumboErrorType.TRANSACTION_FAILED,
        `Transaction execution failed: ${error instanceof Error ? error.message : String(error)}`,
        error
      );
    }
  }

  /**
   * 解析交易结果
   */
  private parseTransactionResult(result: any, request: JumboSwapRequest, startTime: number): JumboSwapResult {
    try {
      const transactionHash = result.transaction?.hash || result.transaction_outcome?.id;
      const gasUsed = result.transaction_outcome?.outcome?.gas_burnt?.toString();

      // 从交易收据中提取实际输出金额
      let outputAmountWei = '0';
      let isTransactionSuccessful = false;

      if (result.receipts_outcome) {
        for (const receipt of result.receipts_outcome) {
          if (receipt.outcome?.logs) {
            for (const log of receipt.outcome.logs) {
              // 查找 Jumbo Exchange 的交换日志
              if (log.includes('Swapped') && log.includes('wrap.near') && log.includes('for')) {
                // 解析日志: "Swapped 1000000000000000000000 wrap.near for 1985 dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near"
                const swapMatch = log.match(/Swapped \d+ wrap\.near for (\d+)/);
                if (swapMatch) {
                  outputAmountWei = swapMatch[1];
                  isTransactionSuccessful = true;
                  logger.info(`✅ Extracted output amount from swap log: ${outputAmountWei} wei`);
                  break;
                }
              }

              // 查找 NEP-141 转账事件
              if (log.includes('EVENT_JSON') && log.includes('ft_transfer')) {
                try {
                  const eventMatch = log.match(/EVENT_JSON:(.+)/);
                  if (eventMatch) {
                    const eventData = JSON.parse(eventMatch[1]);
                    if (eventData.event === 'ft_transfer' && eventData.data?.[0]?.amount) {
                      // 确认是转给用户的代币
                      if (eventData.data[0].new_owner_id === this.nearAccount.accountId) {
                        outputAmountWei = eventData.data[0].amount;
                        isTransactionSuccessful = true;
                        logger.info(`✅ Extracted output amount from transfer event: ${outputAmountWei} wei`);
                        break;
                      }
                    }
                  }
                } catch (parseError) {
                  // 忽略解析错误
                }
              }
            }

            if (isTransactionSuccessful) break;
          }
        }
      }

      // 检查交易是否真正成功
      if (!isTransactionSuccessful || outputAmountWei === '0') {
        logger.warn('⚠️ Transaction may have failed - no valid output amount found');
        return {
          success: false,
          error: 'No valid output amount found in transaction logs',
          transactionHash,
          inputAmount: fromWei(request.amountIn, getTokenDecimals(request.tokenIn)),
          inputAmountWei: request.amountIn,
          gasUsed,
          timestamp: getCurrentTimestamp()
        };
      }

      const tokenOutDecimals = getTokenDecimals(request.tokenOut);
      const tokenInDecimals = getTokenDecimals(request.tokenIn);
      const outputAmount = fromWei(outputAmountWei, tokenOutDecimals);
      const inputAmount = fromWei(request.amountIn, tokenInDecimals);

      // 计算实际滑点
      const expectedMinOutput = parseFloat(fromWei(request.minAmountOut, tokenOutDecimals));
      const actualOutput = parseFloat(outputAmount);
      let actualSlippage = '0';

      if (expectedMinOutput > 0) {
        actualSlippage = ((expectedMinOutput - actualOutput) / expectedMinOutput * 100).toFixed(2);
      }

      return {
        success: true,
        transactionHash,
        outputAmount,
        outputAmountWei,
        inputAmount,
        inputAmountWei: request.amountIn,
        actualSlippage,
        gasUsed,
        timestamp: getCurrentTimestamp()
      };

    } catch (error) {
      logger.error('Failed to parse transaction result:', error);

      return {
        success: false,
        error: `Failed to parse transaction result: ${error instanceof Error ? error.message : String(error)}`,
        transactionHash: result.transaction?.hash || 'unknown',
        inputAmount: fromWei(request.amountIn, getTokenDecimals(request.tokenIn)),
        inputAmountWei: request.amountIn,
        gasUsed: result.transaction_outcome?.outcome?.gas_burnt?.toString(),
        timestamp: getCurrentTimestamp()
      };
    }
  }

  /**
   * 验证交换请求参数
   */
  private validateSwapRequest(request: JumboSwapRequest): void {
    if (!isValidPoolId(request.poolId)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid pool ID');
    }

    if (!isValidTokenAddress(request.tokenIn)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid token in address');
    }

    if (!isValidTokenAddress(request.tokenOut)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid token out address');
    }

    if (!isValidAmount(request.amountIn)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid amount in');
    }

    // 验证最小输出金额不为空且为正数
    if (!request.minAmountOut || request.minAmountOut === '0') {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid min amount out');
    }

    if (request.slippage && !isValidSlippage(request.slippage)) {
      throw new JumboError(JumboErrorType.INVALID_PARAMETERS, 'Invalid slippage');
    }

    const poolConfig = getPoolConfig(request.poolId);
    if (!poolConfig) {
      throw new JumboError(JumboErrorType.POOL_NOT_FOUND, `Pool ${request.poolId} not found in configuration`);
    }
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(request: JumboSwapRequest, error: any): JumboSwapResult {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      inputAmount: fromWei(request.amountIn, getTokenDecimals(request.tokenIn)),
      inputAmountWei: request.amountIn,
      timestamp: getCurrentTimestamp()
    };
  }
}

// ============================================================================
// 导出
// ============================================================================

export default JumboExecutionService;
