/**
 * Jumbo Exchange 工具函数
 * 提供 AMM 计算、格式转换、验证等通用功能
 */

import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import { AMMCalculationParams, JumboError, JumboErrorType } from './types';
import { FEE_CONFIG, LIMITS, TOKEN_DECIMALS } from './config';

// ============================================================================
// AMM 计算函数
// ============================================================================

/**
 * 计算 AMM 输出金额 (Uniswap 风格)
 * 公式: (amountIn * fee * reserveOut) / (reserveIn * 1000 + amountIn * fee)
 */
export function calculateAMMOutput(params: AMMCalculationParams): bigint {
  const { amountIn, reserveIn, reserveOut, fee = FEE_CONFIG.SWAP_FEE } = params;

  // 验证输入参数
  if (amountIn <= 0n || reserveIn <= 0n || reserveOut <= 0n) {
    throw new JumboError(
      JumboErrorType.CALCULATION_ERROR,
      'Invalid AMM calculation parameters: amounts must be positive'
    );
  }

  try {
    const amountInWithFee = amountIn * fee;
    const numerator = amountInWithFee * reserveOut;
    const denominator = (reserveIn * FEE_CONFIG.FEE_DIVISOR) + amountInWithFee;
    
    if (denominator === 0n) {
      throw new Error('Division by zero in AMM calculation');
    }

    const result = numerator / denominator;
    
    // 验证结果合理性
    if (result >= reserveOut) {
      throw new Error('Output amount exceeds reserve');
    }

    return result;
  } catch (error) {
    throw new JumboError(
      JumboErrorType.CALCULATION_ERROR,
      `AMM calculation failed: ${error instanceof Error ? error.message : String(error)}`,
      error
    );
  }
}

/**
 * 计算价格影响
 * 价格影响 = (1 - (outputAmount / expectedOutput)) * 100
 */
export function calculatePriceImpact(
  amountIn: bigint,
  amountOut: bigint,
  reserveIn: bigint,
  reserveOut: bigint
): string {
  try {
    // 计算理想价格下的预期输出
    const idealPrice = reserveOut * BigInt(1e18) / reserveIn;
    const expectedOutput = (amountIn * idealPrice) / BigInt(1e18);
    
    if (expectedOutput === 0n) {
      return '0';
    }

    // 计算价格影响百分比
    const impact = ((expectedOutput - amountOut) * BigInt(10000)) / expectedOutput;
    return (Number(impact) / 100).toFixed(2);
  } catch (error) {
    console.warn('Failed to calculate price impact:', error);
    return '0';
  }
}

/**
 * 计算滑点保护的最小输出金额
 */
export function calculateMinAmountOut(amountOut: string, slippagePercent: number): string {
  try {
    const amountOutBig = BigInt(amountOut);
    const slippageBig = BigInt(Math.floor(slippagePercent * 100)); // 转换为基点
    const minAmount = amountOutBig * (BigInt(10000) - slippageBig) / BigInt(10000);
    return minAmount.toString();
  } catch (error) {
    throw new JumboError(
      JumboErrorType.CALCULATION_ERROR,
      `Failed to calculate min amount out: ${error instanceof Error ? error.message : String(error)}`,
      error
    );
  }
}

// ============================================================================
// 格式转换函数
// ============================================================================

/**
 * 将人类可读格式转换为 wei 格式
 * 使用 NEAR 官方方法确保精度，与现有代码保持一致
 */
export function toWei(amount: string, decimals: number = 24): string {
  try {
    if (decimals === 24) {
      // NEAR 代币使用官方方法
      const result = parseNearAmount(amount);
      if (!result) {
        throw new Error('Invalid NEAR amount format');
      }
      return result;
    } else {
      // 其他代币使用精确的字符串操作，避免浮点数精度问题
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  } catch (error) {
    throw new JumboError(
      JumboErrorType.CALCULATION_ERROR,
      `Failed to convert to wei: ${error instanceof Error ? error.message : String(error)}`,
      error
    );
  }
}

/**
 * 将 wei 格式转换为人类可读格式
 * 使用 NEAR 官方方法确保精度，与现有代码保持一致
 */
export function fromWei(amountWei: string, decimals: number = 24): string {
  try {
    if (decimals === 24) {
      // NEAR 代币使用官方方法并移除千分位分隔符
      return formatNearAmount(amountWei).replace(/,/g, '');
    } else {
      // 其他代币使用精确的字符串操作
      if (amountWei === '0') return '0';

      const paddedAmount = amountWei.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  } catch (error) {
    throw new JumboError(
      JumboErrorType.CALCULATION_ERROR,
      `Failed to convert from wei: ${error instanceof Error ? error.message : String(error)}`,
      error
    );
  }
}

/**
 * 根据代币地址获取正确的小数位数
 * 从配置文件中统一获取，便于管理
 */
export function getTokenDecimals(tokenAddress: string): number {
  return TOKEN_DECIMALS[tokenAddress] || 24; // 默认使用 24 位小数
}

// ============================================================================
// 验证函数
// ============================================================================

/**
 * 验证代币地址格式
 */
export function isValidTokenAddress(address: string): boolean {
  if (!address || typeof address !== 'string') {
    return false;
  }

  // NEAR 代币地址格式验证
  const nearAddressRegex = /^[a-z0-9._-]+\.near$|^[a-f0-9]{64}\.factory\.bridge\.near$|^wrap\.near$/;
  return nearAddressRegex.test(address);
}

/**
 * 验证金额格式
 *
 * 这个函数需要处理多种情况：
 * 1. NEAR 的人类可读格式 (如 "4")
 * 2. NEAR 的 wei 格式 (如 "4000000000000000000000000")
 * 3. 其他代币的各种格式
 */
export function isValidAmount(amount: string): boolean {
  if (!amount || typeof amount !== 'string') {
    return false;
  }

  try {
    // 首先验证是否为有效数字
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return false;
    }

    // 判断是否为 NEAR 的 wei 格式：超过 1e22 的整数（NEAR 的 wei 非常大）
    // 调整阈值，避免其他代币的大 wei 值被误认为是 NEAR
    const isNearWeiFormat = !amount.includes('.') && numAmount > 1e22;

    if (isNearWeiFormat) {
      // 这可能是 NEAR 的 wei 格式，使用 NEAR 的限制验证
      try {
        const amountBig = BigInt(amount);
        const minAmount = BigInt(LIMITS.MIN_AMOUNT_IN);
        const maxAmount = BigInt(LIMITS.MAX_AMOUNT_IN);
        return amountBig >= minAmount && amountBig <= maxAmount;
      } catch {
        // 如果 BigInt 转换失败，使用宽松验证
        return numAmount > 0 && numAmount < 1e30;
      }
    }

    // 判断是否为小的整数（可能是 NEAR 的人类可读格式）
    const isSmallInteger = !amount.includes('.') && numAmount <= 1000;

    if (isSmallInteger) {
      // 这可能是 NEAR 的人类可读格式，转换后验证
      try {
        const { parseNearAmount } = require('near-api-js/lib/utils/format');
        const amountWei = parseNearAmount(amount) || '0';

        const amountBig = BigInt(amountWei);
        const minAmount = BigInt(LIMITS.MIN_AMOUNT_IN);
        const maxAmount = BigInt(LIMITS.MAX_AMOUNT_IN);

        return amountBig >= minAmount && amountBig <= maxAmount;
      } catch {
        // 如果转换失败，使用宽松验证
        return numAmount > 0;
      }
    }

    // 其他情况（包含小数点的数字，或中等大小的整数）
    // 这些可能是其他代币的金额，使用宽松验证
    // 扩大范围以支持各种代币的 wei 格式
    return numAmount > 0 && numAmount < 1e21;

  } catch (error) {
    // 如果所有转换都失败，最后的宽松验证
    try {
      const numAmount = parseFloat(amount);
      return numAmount > 0 && numAmount < 1e21;
    } catch {
      return false;
    }
  }
}

/**
 * 验证滑点参数
 */
export function isValidSlippage(slippage: number): boolean {
  return (
    typeof slippage === 'number' &&
    !isNaN(slippage) &&
    slippage >= LIMITS.MIN_SLIPPAGE &&
    slippage <= LIMITS.MAX_SLIPPAGE
  );
}

/**
 * 验证池子 ID
 * 注意：Pool ID 0 是有效的（AURORA池子使用Pool ID 0）
 */
export function isValidPoolId(poolId: number): boolean {
  return (
    typeof poolId === 'number' &&
    Number.isInteger(poolId) &&
    poolId >= 0  // 🔧 修复：允许Pool ID为0
  );
}

// ============================================================================
// 重试机制
// ============================================================================

/**
 * 带重试的异步函数执行
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error | undefined;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      console.warn(`Attempt ${attempt}/${maxAttempts} failed:`, error instanceof Error ? error.message : String(error));

      if (attempt < maxAttempts) {
        await sleep(delay * attempt); // 指数退避
      }
    }
  }

  throw new JumboError(
    JumboErrorType.NETWORK_ERROR,
    `Operation failed after ${maxAttempts} attempts: ${lastError?.message || 'Unknown error'}`,
    lastError
  );
}

/**
 * 延迟函数
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// ============================================================================
// 时间和格式化工具
// ============================================================================

/**
 * 格式化时间戳
 */
export function formatTimestamp(timestamp: number): string {
  return new Date(timestamp).toISOString();
}

/**
 * 获取当前时间戳
 */
export function getCurrentTimestamp(): number {
  return Date.now();
}

/**
 * 格式化数字为可读格式
 */
export function formatNumber(num: number, decimals: number = 2): string {
  return num.toLocaleString('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
}

/**
 * 格式化百分比
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  return `${value.toFixed(decimals)}%`;
}

// ============================================================================
// 调试工具
// ============================================================================

/**
 * 创建调试日志函数
 */
export function createLogger(prefix: string) {
  return {
    info: (message: string, ...args: any[]) => {
      console.log(`[${prefix}] ${message}`, ...args);
    },
    warn: (message: string, ...args: any[]) => {
      console.warn(`[${prefix}] ${message}`, ...args);
    },
    error: (message: string, ...args: any[]) => {
      console.error(`[${prefix}] ${message}`, ...args);
    }
  };
}
