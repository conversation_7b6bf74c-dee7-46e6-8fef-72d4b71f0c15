/**
 * Jumbo Exchange 类型定义
 * 定义所有与 Jumbo Exchange 集成相关的 TypeScript 类型
 */

// ============================================================================
// 基础类型
// ============================================================================

/** 池子基本信息 */
export interface JumboPoolInfo {
  pool_id: string;
  liquidity: string;
  last_price: string;
  base_id: string;
  base_name: string;
  base_symbol: string;
  base_volume: string;
  base_price_usd: string;
  base_liquidity: string;
  base_liquidity_usd: string;
  quote_id: string;
  quote_name: string;
  quote_symbol: string;
  quote_volume: string;
  quote_price_usd: string;
  quote_liquidity: string;
  quote_liquidity_usd: string;
}

/** API 返回的池子数据格式 */
export interface JumboPoolsResponse {
  [key: string]: JumboPoolInfo;
}

/** 重点池子配置 */
export interface JumboTargetPool {
  poolId: number;
  name: string;
  baseToken: string;
  quoteToken: string;
  baseSymbol: string;
  quoteSymbol: string;
  minLiquidity: number;
  isActive: boolean;
}

// ============================================================================
// 报价相关类型
// ============================================================================

/** 报价请求参数 */
export interface JumboQuoteRequest {
  poolId: number;
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
}

/** 报价结果 */
export interface JumboQuoteResult {
  success: boolean;
  poolId: number;
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  amountInWei: string;
  amountOut?: string;
  amountOutWei?: string;
  priceImpact?: string;
  fee?: string;
  error?: string;
  method: 'api' | 'contract' | 'local';
  timestamp: number;
}

/** AMM 计算参数 */
export interface AMMCalculationParams {
  amountIn: bigint;
  reserveIn: bigint;
  reserveOut: bigint;
  fee?: bigint; // 默认 997 (0.3% 手续费)
}

// ============================================================================
// 交易相关类型
// ============================================================================

/** 交易动作 */
export interface JumboSwapAction {
  pool_id: number;
  token_in: string;
  amount_in: string;
  token_out: string;
  min_amount_out: string;
}

/** 交易请求参数 */
export interface JumboSwapRequest {
  poolId: number;
  tokenIn: string;
  tokenOut: string;
  amountIn: string;
  minAmountOut: string;
  slippage?: number; // 默认 0.5%
}

/** 交易结果 */
export interface JumboSwapResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  outputAmount?: string;
  outputAmountWei?: string;
  inputAmount?: string;
  inputAmountWei?: string;
  actualSlippage?: string;
  gasUsed?: string;
  timestamp: number;
}

// ============================================================================
// 配置相关类型
// ============================================================================

/** Jumbo Exchange 配置 */
export interface JumboConfig {
  contractAddress: string;
  apiBaseUrl: string;
  networkId: string;
  defaultGas: string;
  defaultSlippage: number;
  cacheTimeout: number;
  retryAttempts: number;
  retryDelay: number;
}

/** 池子缓存数据 */
export interface JumboPoolCache {
  pools: Map<number, JumboPoolInfo>;
  lastUpdated: number;
  isValid: boolean;
}

// ============================================================================
// 服务接口
// ============================================================================

/** 池子管理器接口 */
export interface IJumboPoolManager {
  getPoolInfo(poolId: number): Promise<JumboPoolInfo | null>;
  getAllPools(): Promise<JumboPoolsResponse>;
  getTargetPools(): JumboTargetPool[];
  refreshCache(): Promise<void>;
  isPoolActive(poolId: number): boolean;
}

/** 报价服务接口 */
export interface IJumboQuoteService {
  getQuote(request: JumboQuoteRequest): Promise<JumboQuoteResult>;
  getQuoteFromAPI(request: JumboQuoteRequest): Promise<JumboQuoteResult>;
  getQuoteFromContract(request: JumboQuoteRequest): Promise<JumboQuoteResult>;
  calculateLocalQuote(request: JumboQuoteRequest): Promise<JumboQuoteResult>;
}

/** 交易执行服务接口 */
export interface IJumboExecutionService {
  executeSwap(request: JumboSwapRequest): Promise<JumboSwapResult>;
  buildSwapAction(request: JumboSwapRequest): JumboSwapAction;
  checkUserRegistration(): Promise<boolean>;
  registerUser(): Promise<void>;
}

// ============================================================================
// 错误类型
// ============================================================================

/** Jumbo Exchange 错误类型 */
export enum JumboErrorType {
  POOL_NOT_FOUND = 'POOL_NOT_FOUND',
  INSUFFICIENT_LIQUIDITY = 'INSUFFICIENT_LIQUIDITY',
  API_ERROR = 'API_ERROR',
  CONTRACT_ERROR = 'CONTRACT_ERROR',
  CALCULATION_ERROR = 'CALCULATION_ERROR',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_PARAMETERS = 'INVALID_PARAMETERS'
}

/** Jumbo Exchange 自定义错误 */
export class JumboError extends Error {
  constructor(
    public type: JumboErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'JumboError';
  }
}

// ============================================================================
// 工具类型
// ============================================================================

/** 代币信息 */
export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
}

/** 价格信息 */
export interface PriceInfo {
  tokenAddress: string;
  priceUSD: string;
  timestamp: number;
}

/** 统计信息 */
export interface JumboStats {
  totalPools: number;
  activePools: number;
  totalLiquidity: string;
  last24hVolume: string;
  lastUpdated: number;
}
