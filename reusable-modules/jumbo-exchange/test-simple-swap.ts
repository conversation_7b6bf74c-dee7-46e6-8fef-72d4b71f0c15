/**
 * Jumbo Exchange 简单交易测试
 * 测试 0.001 NEAR -> USDT.e (Pool 1)
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../../.env') });
import { Account, connect, keyStores } from 'near-api-js';
import { parseNearAmount } from 'near-api-js/lib/utils/format';
import { JumboExchange } from './index';
import { calculateMinAmountOut, fromWei, getTokenDecimals } from './utils';

/**
 * NEAR 网络配置
 */
function getNearConfig(networkId: string) {
  if (networkId === 'mainnet') {
    return {
      networkId: 'mainnet',
      nodeUrl: 'https://rpc.mainnet.near.org',
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org'
    };
  } else {
    return {
      networkId: 'testnet',
      nodeUrl: 'https://rpc.testnet.near.org',
      walletUrl: 'https://wallet.testnet.near.org',
      helperUrl: 'https://helper.testnet.near.org'
    };
  }
}

/**
 * 初始化 NEAR 账户
 */
async function initializeAccount(): Promise<Account> {
  const accountId = process.env.NEAR_ACCOUNT_ID;
  const privateKey = process.env.NEAR_PRIVATE_KEY;
  const networkId = process.env.NEAR_NETWORK_ID || 'mainnet';

  if (!accountId || !privateKey) {
    throw new Error('请在 .env 文件中设置 NEAR_ACCOUNT_ID 和 NEAR_PRIVATE_KEY');
  }

  const keyStore = new keyStores.InMemoryKeyStore();
  const keyPair = require('near-api-js').utils.KeyPair.fromString(privateKey);
  await keyStore.setKey(networkId, accountId, keyPair);

  const config = {
    ...getNearConfig(networkId),
    keyStore
  };

  const near = await connect(config);
  return await near.account(accountId);
}

/**
 * 测试 0.001 NEAR -> USDT.e 交易
 */
async function testSimpleSwap() {
  console.log('🚀 Jumbo Exchange 交易测试');
  console.log('测试: 0.001 NEAR -> USDT.e (Pool 1)');
  console.log('='.repeat(50));

  try {
    // 1. 初始化账户
    console.log('\n📡 初始化 NEAR 账户...');
    const account = await initializeAccount();
    console.log(`✅ 账户: ${account.accountId}`);

    // 2. 初始化 Jumbo Exchange
    console.log('\n🔧 初始化 Jumbo Exchange...');
    const jumbo = new JumboExchange(account);
    await jumbo.initialize();
    console.log('✅ Jumbo Exchange 初始化完成');

    // 3. 测试参数
    const testAmount = '0.001'; // 0.001 NEAR
    const slippage = 1.0; // 1% 滑点
    
    console.log('\n📊 交易参数:');
    console.log(`  输入: ${testAmount} NEAR`);
    console.log(`  池子: Pool 1 (NEAR-USDT.e)`);
    console.log(`  滑点: ${slippage}%`);

    // 4. 获取报价
    console.log('\n💰 获取报价...');
    const quote = await jumbo.getNearUsdtQuote(testAmount);
    
    if (!quote.success) {
      throw new Error(`报价失败: ${quote.error}`);
    }

    console.log(`✅ 报价成功:`);
    console.log(`  输入: ${quote.amountIn} NEAR`);
    console.log(`  预期输出: ${quote.amountOut} USDT.e`);
    console.log(`  价格: ${(parseFloat(quote.amountOut!) / parseFloat(quote.amountIn)).toFixed(6)} USDT.e/NEAR`);

    // 5. 计算最小输出
    const minAmountOut = calculateMinAmountOut(quote.amountOutWei!, slippage);
    const minAmountOutReadable = fromWei(minAmountOut, getTokenDecimals('dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near'));
    
    console.log(`  最小输出: ${minAmountOutReadable} USDT.e (${slippage}% 滑点保护)`);

    // 6. 检查用户注册状态
    console.log('\n👤 检查用户注册状态...');
    const isRegistered = await jumbo.checkUserRegistration();
    console.log(`✅ 用户注册状态: ${isRegistered ? '已注册' : '未注册'}`);

    if (!isRegistered) {
      console.log('📝 注册用户中...');
      await jumbo.registerUser();
      console.log('✅ 用户注册完成');
    }

    // 7. 构建交易请求
    const swapRequest = {
      poolId: 1,
      tokenIn: 'wrap.near',
      tokenOut: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
      amountIn: quote.amountInWei,
      minAmountOut,
      slippage
    };

    console.log('\n🔄 交易详情:');
    console.log(`  池子ID: ${swapRequest.poolId}`);
    console.log(`  输入代币: ${swapRequest.tokenIn}`);
    console.log(`  输出代币: ${swapRequest.tokenOut}`);
    console.log(`  输入金额: ${quote.amountIn} NEAR`);
    console.log(`  最小输出: ${minAmountOutReadable} USDT.e`);

    // 8. 执行交易
    console.log('\n⚠️  准备执行真实交易!');
    console.log(`将花费: ${testAmount} NEAR`);
    console.log(`预期获得: ~${quote.amountOut} USDT.e`);
    console.log(`最少获得: ${minAmountOutReadable} USDT.e`);
    
    console.log('\n⏳ 执行交易中...');
    const result = await jumbo.executeSwap(swapRequest);

    // 9. 显示结果
    console.log('\n📋 交易结果:');
    if (result.success) {
      console.log('🎉 交易成功!');
      console.log(`  交易哈希: ${result.transactionHash}`);
      console.log(`  输入: ${result.inputAmount} NEAR`);
      console.log(`  输出: ${result.outputAmount} USDT.e`);
      
      if (result.actualSlippage) {
        console.log(`  实际滑点: ${result.actualSlippage}%`);
      }
      
      if (result.gasUsed) {
        console.log(`  Gas 消耗: ${result.gasUsed}`);
      }

      // 计算实际价格
      const actualPrice = parseFloat(result.outputAmount!) / parseFloat(result.inputAmount!);
      const expectedPrice = parseFloat(quote.amountOut!) / parseFloat(quote.amountIn);
      const priceDiff = ((actualPrice - expectedPrice) / expectedPrice * 100).toFixed(2);
      
      console.log(`  预期价格: ${expectedPrice.toFixed(6)} USDT.e/NEAR`);
      console.log(`  实际价格: ${actualPrice.toFixed(6)} USDT.e/NEAR`);
      console.log(`  价格差异: ${priceDiff}%`);

      // 浏览器链接
      const networkId = process.env.NEAR_NETWORK_ID || 'mainnet';
      if (networkId === 'mainnet') {
        console.log(`  🔗 浏览器: https://nearblocks.io/txns/${result.transactionHash}`);
      } else {
        console.log(`  🔗 浏览器: https://testnet.nearblocks.io/txns/${result.transactionHash}`);
      }
    } else {
      console.log('❌ 交易失败!');
      console.log(`  错误: ${result.error}`);
    }

    console.log('\n✅ 测试完成!');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    throw error;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await testSimpleSwap();
  } catch (error) {
    console.error('程序失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testSimpleSwap };
