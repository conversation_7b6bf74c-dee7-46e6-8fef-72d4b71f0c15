/**
 * NEAR包装服务
 *
 * 功能：提供NEAR与wNEAR之间的完整转换功能
 * 职责：
 * - 处理NEAR → wNEAR的包装操作
 * - 处理wNEAR → NEAR的解包操作
 * - 自动余额检查和智能包装
 * - 提供精确的数量转换工具
 * - 管理包装相关的存储存款
 * - 余额查询和状态监控
 *
 * 核心概念：
 * - NEAR：原生代币，用于支付gas费用
 * - wNEAR：包装代币，用于DeFi交易（遵循NEP-141标准）
 * - 1:1兑换：1 NEAR = 1 wNEAR（无手续费）
 * - 存储存款：首次包装需要少量NEAR作为存储费用
 *
 * 核心方法：
 * - wrapNear(): 包装NEAR为wNEAR
 * - unwrapNear(): 解包wNEAR为NEAR
 * - checkAndWrapNear(): 智能检查并自动包装
 * - getNearBalance(): 获取NEAR余额
 * - getWNearBalance(): 获取wNEAR余额
 * - getBalanceInfo(): 获取完整余额信息
 *
 * 精度处理：
 * - 使用parseNearAmount()进行精确转换
 * - 避免JavaScript浮点数精度问题
 * - 支持wei格式和人类可读格式的相互转换
 *
 * 智能功能：
 * - 自动检查余额是否足够
 * - 智能计算所需包装数量
 * - 缓冲机制避免频繁包装
 * - 存储存款自动管理
 *
 * 使用场景：
 * - DeFi交易前的NEAR包装
 * - 套利机器人的余额管理
 * - 自动化交易策略的资金准备
 * - 用户钱包的包装功能
 */

import { Account, connect, keyStores, Near } from 'near-api-js';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import { getNearConfig, GAS_CONFIG, DEPOSIT_CONFIG } from '../config';
import { NearWrapResult, NearBalanceInfo } from '../types';

/**
 * NEAR包装服务类
 */
export class NearWrapService {
  private account!: Account;
  private near!: Near;
  private wrapContractId: string;
  private initialized = false;

  constructor(
    private accountId: string, 
    private privateKey: string, 
    private networkId: 'mainnet' | 'testnet' = 'mainnet'
  ) {
    const config = getNearConfig(networkId);
    this.wrapContractId = config.wrapContract;
  }

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    const keyStore = new keyStores.InMemoryKeyStore();
    const keyPair = require('near-api-js').utils.KeyPair.fromString(this.privateKey);
    await keyStore.setKey(this.networkId, this.accountId, keyPair);

    const config = getNearConfig(this.networkId);
    const nearConfig = {
      networkId: this.networkId,
      keyStore,
      nodeUrl: config.nodeUrl,
      walletUrl: config.walletUrl,
      helperUrl: config.helperUrl,
    };

    this.near = await connect(nearConfig);
    this.account = await this.near.account(this.accountId);
    this.initialized = true;
  }

  /**
   * 获取账户的NEAR余额
   */
  async getNearBalance(): Promise<string> {
    await this.initialize();
    try {
      const balance = await this.account.getAccountBalance();
      return formatNearAmount(balance.available);
    } catch (error) {
      console.error('获取NEAR余额失败:', error);
      throw error;
    }
  }

  /**
   * 获取账户的wNEAR余额
   */
  async getWNearBalance(): Promise<string> {
    await this.initialize();
    try {
      const result = await this.account.viewFunction({
        contractId: this.wrapContractId,
        methodName: 'ft_balance_of',
        args: { account_id: this.account.accountId }
      });

      return formatNearAmount(result);
    } catch (error) {
      console.error('获取wNEAR余额失败:', error);
      return '0';
    }
  }

  /**
   * 包装NEAR为wNEAR
   * @param amount 要包装的NEAR数量（人类可读格式，如 "1.5"）
   */
  async wrapNear(amount: string): Promise<NearWrapResult> {
    await this.initialize();
    try {
      console.log(`🔄 开始包装 ${amount} NEAR → wNEAR...`);

      // 使用NEAR官方精确转换
      const amountInYocto = parseNearAmount(amount);
      if (!amountInYocto) {
        throw new Error('无效的NEAR数量');
      }

      // 调用wrap.near合约的near_deposit方法
      const result = await this.account.functionCall({
        contractId: this.wrapContractId,
        methodName: 'near_deposit',
        args: {},
        attachedDeposit: BigInt(amountInYocto),
        gas: GAS_CONFIG.nearWrap
      });

      console.log(`✅ NEAR包装成功: ${result.transaction.hash}`);
      console.log(`📊 包装数量: ${amount} NEAR → ${amount} wNEAR`);

      return {
        success: true,
        txHash: result.transaction.hash
      };
    } catch (error: any) {
      console.error('NEAR包装失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 解包装wNEAR为NEAR
   * @param amount 要解包装的wNEAR数量（人类可读格式，如 "1.5"）
   */
  async unwrapNear(amount: string): Promise<NearWrapResult> {
    await this.initialize();
    try {
      console.log(`🔄 开始解包装 ${amount} wNEAR → NEAR...`);

      // 使用NEAR官方精确转换
      const amountInYocto = parseNearAmount(amount);
      if (!amountInYocto) {
        throw new Error('无效的wNEAR数量');
      }

      // 调用wrap.near合约的near_withdraw方法
      const result = await this.account.functionCall({
        contractId: this.wrapContractId,
        methodName: 'near_withdraw',
        args: { amount: amountInYocto },
        attachedDeposit: DEPOSIT_CONFIG.nearWrapDeposit,
        gas: GAS_CONFIG.nearUnwrap
      });

      console.log(`✅ wNEAR解包装成功: ${result.transaction.hash}`);
      console.log(`📊 解包装数量: ${amount} wNEAR → ${amount} NEAR`);

      return {
        success: true,
        txHash: result.transaction.hash
      };
    } catch (error: any) {
      console.error('wNEAR解包装失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查并自动包装NEAR
   * @param requiredAmount 需要的wNEAR数量（人类可读格式）
   * @param bufferPercent 缓冲百分比，默认10%
   */
  async checkAndWrapNear(
    requiredAmount: string, 
    bufferPercent: number = 10
  ): Promise<{ success: boolean; wrapped?: boolean; amount?: string; error?: string }> {
    try {
      console.log(`🔍 检查wNEAR余额，需要: ${requiredAmount} wNEAR`);
      
      // 获取当前wNEAR余额
      const currentWNearBalance = await this.getWNearBalance();
      const currentBalance = parseFloat(currentWNearBalance);
      const required = parseFloat(requiredAmount);
      
      console.log(`💰 当前wNEAR余额: ${currentWNearBalance}`);
      console.log(`📊 需要wNEAR数量: ${requiredAmount}`);
      
      // 如果余额足够，不需要包装
      if (currentBalance >= required) {
        console.log(`✅ wNEAR余额充足，无需包装`);
        return { success: true, wrapped: false };
      }
      
      // 计算需要包装的数量（包含缓冲）
      const deficit = required - currentBalance;
      const bufferAmount = deficit * (bufferPercent / 100);
      const wrapAmount = deficit + bufferAmount;
      
      console.log(`⚠️ wNEAR余额不足，缺少: ${deficit.toFixed(6)} wNEAR`);
      console.log(`🔄 将包装: ${wrapAmount.toFixed(6)} NEAR (包含${bufferPercent}%缓冲)`);
      
      // 检查NEAR余额是否足够
      const nearBalance = await this.getNearBalance();
      const availableNear = parseFloat(nearBalance);
      
      // 预留一些NEAR用于gas费用
      const gasReserve = 0.1; // 预留0.1 NEAR用于gas
      const availableForWrap = availableNear - gasReserve;
      
      if (availableForWrap < wrapAmount) {
        const error = `NEAR余额不足: 可用 ${availableForWrap.toFixed(6)} NEAR，需要 ${wrapAmount.toFixed(6)} NEAR`;
        console.error(`❌ ${error}`);
        return { success: false, error };
      }
      
      // 执行包装
      const wrapResult = await this.wrapNear(wrapAmount.toFixed(6));
      
      if (!wrapResult.success) {
        return { success: false, error: wrapResult.error };
      }
      
      // 等待一下确保包装完成
      console.log('⏳ 等待包装确认...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 验证包装后的余额
      const newWNearBalance = await this.getWNearBalance();
      console.log(`✅ 包装完成，新的wNEAR余额: ${newWNearBalance}`);
      
      return {
        success: true,
        wrapped: true,
        amount: wrapAmount.toFixed(6)
      };
      
    } catch (error: any) {
      console.error('自动包装检查失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取余额信息
   */
  async getBalanceInfo(): Promise<NearBalanceInfo> {
    const nearBalance = await this.getNearBalance();
    const wNearBalance = await this.getWNearBalance();
    const totalBalance = (parseFloat(nearBalance) + parseFloat(wNearBalance)).toFixed(6);
    
    return {
      nearBalance,
      wNearBalance,
      totalBalance
    };
  }

  /**
   * 精确的wei转换（避免浮点数精度问题）
   */
  static toWei(amount: string, decimals: number): string {
    const [integer, decimal = ''] = amount.split('.');
    const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
    return (integer || '0') + paddedDecimal;
  }

  /**
   * 从wei转换为人类可读格式
   */
  static fromWei(amount: string, decimals: number): string {
    const amountStr = amount.padStart(decimals + 1, '0');
    const integerPart = amountStr.slice(0, -decimals) || '0';
    const decimalPart = amountStr.slice(-decimals);
    return `${integerPart}.${decimalPart}`.replace(/\.?0+$/, '');
  }
}

export default NearWrapService;
