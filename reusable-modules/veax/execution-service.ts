/**
 * VEAX DEX 交易执行服务
 *
 * 功能：提供VEAX DEX的完整交易执行功能
 * 职责：
 * - 执行VEAX DEX上的代币交换交易
 * - 管理用户和代币的注册状态
 * - 处理交易前的准备工作（注册检查）
 * - 解析交易结果并提取实际输出金额
 * - 提供wei格式的精确输出金额
 * - 错误处理和交易状态检查
 *
 * 交易流程：
 * 1. 检查用户是否已注册到VEAX
 * 2. 检查输入和输出代币是否已注册
 * 3. 构建交易消息（Deposit + SwapExactIn + Withdraw）
 * 4. 执行ft_transfer_call交易
 * 5. 解析交易日志，提取实际输出金额
 * 6. 返回包含wei格式输出的交易结果
 *
 * 核心方法：
 * - executeSwap(): 执行代币交换交易
 * - checkUserRegistration(): 检查用户注册状态
 * - registerUser(): 注册用户到VEAX
 * - checkTokenRegistration(): 检查代币注册状态
 * - registerToken(): 注册代币到VEAX
 *
 * 精度处理：
 * - 输入：wei格式的精确代币数量
 * - 输出：从交易日志中提取的实际wei输出金额
 * - 避免API返回值的精度损失问题
 *
 * 使用场景：
 * - 套利机器人的交易执行
 * - DeFi应用的代币交换
 * - 自动化交易策略
 * - 链式交易的第一步或第二步
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import { getNearConfig, GAS_CONFIG, DEPOSIT_CONFIG } from '../config';
import { VeaxTransactionResult, VeaxUserStatus, VeaxTokenStatus } from '../types';

/**
 * VEAX DEX 交易执行服务
 */
export class VeaxExecutionService {
  private near: Near | null = null;
  private account: Account | null = null;
  private readonly contractId = 'veax.near';

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: 'mainnet' | 'testnet' = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      const config = getNearConfig(this.networkId);
      const nearConfig = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: config.nodeUrl,
        walletUrl: config.walletUrl,
        helperUrl: config.helperUrl,
      };

      this.near = await connect(nearConfig);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ VEAX执行服务初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ VEAX执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户注册状态
   */
  async checkUserRegistration(): Promise<VeaxUserStatus> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      const result = await this.account.viewFunction({
        contractId: this.contractId,
        methodName: 'storage_balance_of',
        args: { account_id: this.accountId }
      });

      if (result) {
        return {
          isRegistered: true,
          storageBalance: {
            total: result.total,
            available: result.available
          }
        };
      } else {
        return { isRegistered: false };
      }
    } catch (error) {
      console.log(`ℹ️ 用户 ${this.accountId} 未在VEAX注册`);
      return { isRegistered: false };
    }
  }

  /**
   * 注册用户到VEAX
   */
  async registerUser(): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔄 注册用户到VEAX: ${this.accountId}`);

      const result = await this.account.functionCall({
        contractId: this.contractId,
        methodName: 'storage_deposit',
        args: {
          account_id: this.accountId,
          registration_only: false
        },
        attachedDeposit: DEPOSIT_CONFIG.veaxStorageDeposit,
        gas: GAS_CONFIG.veaxRegister
      });

      console.log(`✅ 用户注册成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ 用户注册失败:', error);
      return {
        success: false,
        error: error.message || '用户注册失败'
      };
    }
  }

  /**
   * 检查代币注册状态
   */
  async checkTokenRegistration(tokenId: string): Promise<VeaxTokenStatus> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      // 检查钱包在代币合约中的存储余额
      const result = await this.account.viewFunction({
        contractId: tokenId,
        methodName: 'storage_balance_of',
        args: {
          account_id: this.accountId
        }
      });

      if (result) {
        return {
          isRegistered: true,
          balance: result.total || '0'
        };
      } else {
        return { isRegistered: false };
      }
    } catch (error) {
      console.log(`ℹ️ 代币 ${tokenId} 未为用户 ${this.accountId} 注册`);
      return { isRegistered: false };
    }
  }

  /**
   * 注册代币
   */
  async registerToken(tokenId: string): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔄 在代币合约中注册钱包: ${tokenId}`);

      const result = await this.account.functionCall({
        contractId: tokenId,
        methodName: 'storage_deposit',
        args: {
          account_id: this.accountId,
          registration_only: false
        },
        attachedDeposit: DEPOSIT_CONFIG.veaxStorageDeposit,
        gas: GAS_CONFIG.veaxRegister
      });

      console.log(`✅ 代币注册成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ 代币注册失败:', error);
      return {
        success: false,
        error: error.message || '代币注册失败'
      };
    }
  }

  /**
   * 执行VEAX交易
   */
  async executeSwap(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    minAmountOut: string
  ): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🚀 开始执行VEAX交易: ${amountIn} ${tokenIn} → ${tokenOut}`);
      console.log(`📊 最小输出: ${minAmountOut}`);

      // 检查并注册用户
      const userStatus = await this.checkUserRegistration();
      if (!userStatus.isRegistered) {
        console.log('⚠️ 用户未注册，正在注册...');
        const registerResult = await this.registerUser();
        if (!registerResult.success) {
          throw new Error(`用户注册失败: ${registerResult.error}`);
        }
      }

      // 检查并注册代币
      const tokenInStatus = await this.checkTokenRegistration(tokenIn);
      if (!tokenInStatus.isRegistered) {
        console.log(`⚠️ 输入代币 ${tokenIn} 未注册，正在注册...`);
        const registerResult = await this.registerToken(tokenIn);
        if (!registerResult.success) {
          throw new Error(`输入代币注册失败: ${registerResult.error}`);
        }
      }

      const tokenOutStatus = await this.checkTokenRegistration(tokenOut);
      if (!tokenOutStatus.isRegistered) {
        console.log(`⚠️ 输出代币 ${tokenOut} 未注册，正在注册...`);
        const registerResult = await this.registerToken(tokenOut);
        if (!registerResult.success) {
          throw new Error(`输出代币注册失败: ${registerResult.error}`);
        }
      }

      // 构建VEAX交易消息
      const swapMsg = [
        "Deposit",
        {
          "SwapExactIn": {
            "token_in": tokenIn,
            "token_out": tokenOut,
            "amount": amountIn,
            "amount_limit": minAmountOut
          }
        },
        {
          "Withdraw": [tokenIn, "0", null]
        },
        {
          "Withdraw": [tokenOut, "0", null]
        }
      ];

      // 执行ft_transfer_call到输入代币合约
      const result = await this.account.functionCall({
        contractId: tokenIn,
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: this.contractId,
          amount: amountIn,
          msg: JSON.stringify(swapMsg)
        },
        attachedDeposit: DEPOSIT_CONFIG.veaxFtTransfer,
        gas: GAS_CONFIG.veaxSwap
      });

      console.log(`✅ VEAX交易成功: ${result.transaction.hash}`);

      // 从交易结果中提取实际输出金额（wei格式）
      const logs = result.receipts_outcome
        .flatMap(receipt => receipt.outcome.logs)
        .filter(log => log.includes('EVENT_JSON'));

      let actualAmountOutWei = minAmountOut; // 默认使用最小输出金额
      for (const log of logs) {
        try {
          const eventData = JSON.parse(log.replace('EVENT_JSON:', ''));
          if (eventData.event === 'swap' && eventData.data.amounts) {
            actualAmountOutWei = eventData.data.amounts[1]; // 输出金额（wei格式）
            console.log(`📊 VEAX交易实际输出: ${actualAmountOutWei} wei`);
            break;
          }
        } catch (e) {
          // 忽略解析错误
        }
      }

      return {
        success: true,
        transactionHash: result.transaction.hash,
        amountIn: amountIn,
        amountOut: actualAmountOutWei, // 保持原有字段兼容性
        outputAmountWei: actualAmountOutWei, // 🔧 关键修复：明确的wei格式输出
        inputAmountWei: amountIn // 🔧 关键修复：输入也是wei格式
      };

    } catch (error: any) {
      console.error('❌ VEAX交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 获取账户余额
   */
  async getAccountBalance(): Promise<{ balance: string; balanceNear: number }> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      const accountState = await this.account.state();
      const balanceYocto = accountState.amount;
      const balanceNear = parseFloat(balanceYocto) / Math.pow(10, 24);

      return {
        balance: balanceYocto,
        balanceNear: balanceNear
      };
    } catch (error) {
      console.error('❌ 获取账户余额失败:', error);
      throw error;
    }
  }
}

export default VeaxExecutionService;
