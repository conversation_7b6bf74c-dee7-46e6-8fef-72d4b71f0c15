/**
 * VEAX DEX 报价查询服务
 *
 * 功能：提供VEAX DEX的完整报价查询功能
 * 职责：
 * - 调用VEAX估价API获取交易报价
 * - 处理报价请求参数验证和格式化
 * - 支持单个和批量报价查询
 * - 提供池子存在性检查
 * - 计算价格影响和建议滑点
 * - 错误处理和重试机制
 *
 * API端点：https://veax-estimation-service.veax.com/v1/rpc
 *
 * 核心方法：
 * - getQuote(): 获取单个交易对的报价
 * - getBatchQuotes(): 批量获取多个交易对的报价
 * - checkPoolExists(): 检查指定交易对的池子是否存在
 * - getSuggestedSlippage(): 根据价格影响计算建议滑点
 *
 * 数据格式：
 * - 输入：wei格式的代币数量
 * - 输出：包含输出数量、价格影响、滑点建议等信息
 * - 错误：详细的错误信息和错误代码
 *
 * 使用场景：
 * - 套利机器人的价格监控
 * - DeFi应用的报价显示
 * - 交易前的价格预估
 * - 多DEX价格比较
 */

import axios from 'axios';
import { VEAX_CONFIG } from '../config';
import { VeaxSwapRequest, VeaxSwapResponse, VeaxQuoteResult } from '../types';

/**
 * VEAX报价服务类
 */
export class VeaxQuoteService {
  private static readonly DEFAULT_SLIPPAGE = 0.005; // 0.5%

  /**
   * 获取VEAX交换报价
   * @param tokenA 输入代币地址
   * @param tokenB 输出代币地址  
   * @param amountA 输入代币数量（wei格式字符串）
   * @param slippageTolerance 滑点容忍度，默认0.5%
   * @returns 报价结果
   */
  static async getQuote(
    tokenA: string,
    tokenB: string,
    amountA: string,
    slippageTolerance: number = VeaxQuoteService.DEFAULT_SLIPPAGE
  ): Promise<VeaxQuoteResult> {
    try {
      // 构建请求数据
      const requestData: VeaxSwapRequest = {
        jsonrpc: '2.0',
        method: 'estimate_swap_exact_in',
        params: {
          token_a: tokenA,
          token_b: tokenB,
          amount_a: amountA,
          slippage_tolerance: slippageTolerance
        },
        id: 0
      };

      // 发送HTTP请求
      const response = await axios.post<VeaxSwapResponse>(
        VEAX_CONFIG.apiUrl,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          timeout: VEAX_CONFIG.requestTimeout
        }
      );

      // 检查API错误
      if (response.data.error) {
        const errorMsg = `VEAX API错误: ${response.data.error.message}`;
        // 只对流动性不足以外的错误显示日志
        if (!response.data.error.data?.details?.includes('insufficient_liquidity')) {
          console.error(`[VEAX] ${errorMsg}`, response.data.error);
        }
        
        return {
          outputAmount: '0',
          priceImpact: '0',
          fee: '0',
          poolExists: false,
          success: false,
          error: errorMsg
        };
      }

      // 检查结果
      if (!response.data.result) {
        const errorMsg = 'VEAX API返回空结果';
        console.error(`[VEAX] ${errorMsg}`);
        
        return {
          outputAmount: '0',
          priceImpact: '0',
          fee: '0',
          poolExists: false,
          success: false,
          error: errorMsg
        };
      }

      const result = response.data.result;
      
      return {
        outputAmount: result.amount_b_expected,
        priceImpact: result.price_impact,
        fee: result.fee,
        poolExists: result.pool_exists,
        success: true
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      console.error(`[VEAX] 请求失败:`, errorMsg);
      
      return {
        outputAmount: '0',
        priceImpact: '0',
        fee: '0',
        poolExists: false,
        success: false,
        error: `网络请求失败: ${errorMsg}`
      };
    }
  }

  /**
   * 批量获取多个交易对的报价
   * @param quotes 报价请求数组
   * @returns 报价结果数组
   */
  static async getBatchQuotes(
    quotes: Array<{
      tokenA: string;
      tokenB: string;
      amountA: string;
      slippageTolerance?: number;
    }>
  ): Promise<VeaxQuoteResult[]> {
    console.log(`[VEAX] 批量获取 ${quotes.length} 个报价`);
    
    // 并发请求所有报价
    const promises = quotes.map(quote => 
      VeaxQuoteService.getQuote(
        quote.tokenA,
        quote.tokenB,
        quote.amountA,
        quote.slippageTolerance
      )
    );

    const results = await Promise.all(promises);
    
    const successCount = results.filter(r => r.success).length;
    console.log(`[VEAX] 批量报价完成: ${successCount}/${quotes.length} 成功`);
    
    return results;
  }

  /**
   * 检查交易对是否存在流动性池
   * @param tokenA 代币A地址
   * @param tokenB 代币B地址
   * @returns 是否存在池子
   */
  static async checkPoolExists(tokenA: string, tokenB: string): Promise<boolean> {
    try {
      // 用最小数量测试是否存在池子
      const result = await VeaxQuoteService.getQuote(tokenA, tokenB, '1');
      return result.poolExists;
    } catch (error) {
      console.error(`[VEAX] 检查池子存在性失败:`, error);
      return false;
    }
  }

  /**
   * 获取最佳滑点设置建议
   * @param priceImpact 价格影响百分比
   * @returns 建议的滑点设置
   */
  static getSuggestedSlippage(priceImpact: string): number {
    const impact = parseFloat(priceImpact);
    
    if (impact < 0.1) return 0.005;      // 0.5% for low impact
    if (impact < 0.5) return 0.01;       // 1% for medium impact  
    if (impact < 1.0) return 0.02;       // 2% for high impact
    return 0.05;                         // 5% for very high impact
  }

  /**
   * 格式化报价结果为人类可读格式
   * @param result 报价结果
   * @param inputDecimals 输入代币精度
   * @param outputDecimals 输出代币精度
   * @returns 格式化后的结果
   */
  static formatQuoteResult(
    result: VeaxQuoteResult,
    inputDecimals: number,
    outputDecimals: number
  ): {
    outputAmount: string;
    priceImpact: string;
    fee: string;
    exchangeRate: string;
  } {
    if (!result.success) {
      return {
        outputAmount: '0',
        priceImpact: '0',
        fee: '0',
        exchangeRate: '0'
      };
    }

    // 转换为人类可读格式
    const outputAmount = (parseFloat(result.outputAmount) / Math.pow(10, outputDecimals)).toFixed(6);
    const priceImpact = (parseFloat(result.priceImpact) * 100).toFixed(4);
    const fee = (parseFloat(result.fee) * 100).toFixed(4);
    
    // 计算汇率（假设输入1个单位）
    const oneUnit = Math.pow(10, inputDecimals).toString();
    const exchangeRate = (parseFloat(result.outputAmount) / Math.pow(10, outputDecimals)).toFixed(6);

    return {
      outputAmount,
      priceImpact: `${priceImpact}%`,
      fee: `${fee}%`,
      exchangeRate
    };
  }

  /**
   * 验证报价参数
   * @param tokenA 输入代币地址
   * @param tokenB 输出代币地址
   * @param amountA 输入金额
   * @returns 是否有效
   */
  static validateQuoteParams(tokenA: string, tokenB: string, amountA: string): boolean {
    if (!tokenA || !tokenB || !amountA) {
      return false;
    }

    if (tokenA === tokenB) {
      return false;
    }

    const amount = parseFloat(amountA);
    if (isNaN(amount) || amount <= 0) {
      return false;
    }

    return true;
  }
}

export default VeaxQuoteService;
