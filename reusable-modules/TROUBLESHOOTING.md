# REF/VEAX 交易执行问题解决指南

## 🚨 关键问题：精度损失导致的交易失败

### 问题描述
在REF Finance和VEAX之间进行套利交易时，经常遇到以下错误：
```
❌ V1交易失败: The account doesn't have enough balance
❌ REF合约错误: Smart contract panicked: The account doesn't have enough balance
```

### 根本原因分析

#### 1. 金额不匹配问题（主要原因）
```
VEAX实际输出: **********242963583583086513088000 wei (精确)
    ↓ (转换为人类可读格式用于REF报价)
人类可读: **********.2429636 BLACKDRAGON
    ↓ (REF Smart Router基于人类可读格式重新计算)
REF返回amount_in: **********242963594142722518155264 wei (不准确)
    ↓ (关键问题)
ft_transfer_call amount: **********242963583583086513088000 wei (实际金额)
交易消息 amount_in: **********242963594142722518155264 wei (Smart Router金额)
    ↓
差异: ***************** wei → E22错误
```

#### 2. 精度损失链条（次要原因）
```
VEAX输出: **********482225662847625042329600 wei (精确)
    ↓ (转换为人类可读格式)
人类可读: **********.4822257 BLACKDRAGON
    ↓ (重新转换为wei格式)
REF输入: **********482225700000000000000000 wei (精度损失)
    ↓
差异: ***************** wei (约0.*********万亿)
    ↓
错误: "The account doesn't have enough balance"
```

#### 2. NEAR代币精度问题
```javascript
// ❌ 错误的转换方式
const nearWei = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, 24))).toString();

// ✅ 正确的转换方式
const nearWei = parseNearAmount(amount) || '0';

// 差异示例：
// 5 NEAR:
// 错误方式: 4999999999999999379243008 yoctoNEAR
// 正确方式: 5000000000000000000000000 yoctoNEAR
// 差异: ********* yoctoNEAR (约0.0006 NEAR)
```

## 🔧 解决方案

### 1. 接口修改 - 添加wei格式字段

#### TransactionResult接口
```typescript
// 修改前
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  outputAmount?: string;      // 只有人类可读格式
  inputAmount?: string;
}

// 修改后
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  outputAmount?: string;      // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmount?: string;       // 人类可读格式
  inputAmountWei?: string;    // wei格式（精确，新增）
}
```

#### VeaxTransactionResult接口
```typescript
// 修改前
export interface VeaxTransactionResult {
  success: boolean;
  amountOut?: string;         // 只有人类可读格式
}

// 修改后
export interface VeaxTransactionResult {
  success: boolean;
  amountOut?: string;         // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmountWei?: string;    // wei格式（精确，新增）
}
```

### 2. REF执行服务修改

#### 修复金额不匹配问题
```typescript
// 修改buildV1SwapActions方法签名
private buildV1SwapActions(
  routeData: any,
  minOutputAmount: string,
  actualInputAmount: string  // 🔧 新增：实际的输入金额
): V1SwapAction[] {
  // ...
  if (routeIndex === 0 && poolIndex === 0) {
    action.amount_in = actualInputAmount;  // 🔧 使用实际金额而不是Smart Router返回的金额
    console.log(`使用实际金额: ${actualInputAmount}`);
    console.log(`Smart Router建议: ${pool.amount_in} (仅供参考)`);
  }
  // ...
}

// 调用时传递实际输入金额
const swapActions = this.buildV1SwapActions(
  quoteResult.rawResponse.result_data,
  minOutputAmount,
  inputAmount  // 🔧 传递实际的输入金额
);
```

#### 添加输出金额提取方法
```typescript
private extractOutputAmountFromResult(result: any): { humanReadable: string | null; wei: string | null } {
  try {
    // 查找所有receipts中的ft_transfer事件
    const allReceipts = result.receipts_outcome || [];

    for (const receipt of allReceipts) {
      const logs = receipt.outcome?.logs || [];

      for (const log of logs) {
        try {
          // 查找EVENT_JSON日志
          if (log.includes('EVENT_JSON:')) {
            const eventStr = log.split('EVENT_JSON:')[1];
            const event = JSON.parse(eventStr);

            // 查找ft_transfer事件
            if (event.standard === 'nep141' && event.event === 'ft_transfer') {
              const transferData = event.data?.[0];

              // 检查是否是转给我们账户的输出代币
              if (transferData &&
                  transferData.new_owner_id === this.account?.accountId &&
                  transferData.amount) {

                const weiAmount = transferData.amount;
                console.log(`📊 从交易结果提取实际输出: ${weiAmount} wei`);
                
                return {
                  wei: weiAmount,
                  humanReadable: null // 暂时不转换，避免精度损失
                };
              }
            }
          }
        } catch (parseError) {
          continue;
        }
      }
    }

    return { humanReadable: null, wei: null };
  } catch (error) {
    console.error('提取输出金额失败:', error);
    return { humanReadable: null, wei: null };
  }
}
```

#### 修改返回值
```typescript
// V1和DCL v2交易都需要修改返回值
return {
  success: true,
  transactionHash: result.transaction.hash,
  outputAmount: actualOutputAmount.humanReadable || quoteResult.outputAmount,
  outputAmountWei: actualOutputAmount.wei || undefined, // 🔧 关键修复
  inputAmount: inputAmount,
  inputAmountWei: inputAmount // 🔧 关键修复
};
```

### 3. VEAX执行服务修改

#### 提取实际输出金额
```typescript
// 从交易结果中提取实际输出金额（wei格式）
const logs = result.receipts_outcome
  .flatMap(receipt => receipt.outcome.logs)
  .filter(log => log.includes('EVENT_JSON'));

let actualAmountOutWei = minAmountOut; // 默认使用最小输出金额
for (const log of logs) {
  try {
    const eventData = JSON.parse(log.replace('EVENT_JSON:', ''));
    if (eventData.event === 'swap' && eventData.data.amounts) {
      actualAmountOutWei = eventData.data.amounts[1]; // 输出金额（wei格式）
      console.log(`📊 VEAX交易实际输出: ${actualAmountOutWei} wei`);
      break;
    }
  } catch (e) {
    // 忽略解析错误
  }
}

return {
  success: true,
  transactionHash: result.transaction.hash,
  amountOut: actualAmountOutWei, // 保持原有字段兼容性
  outputAmountWei: actualAmountOutWei, // 🔧 关键修复：明确的wei格式输出
  inputAmountWei: amountIn // 🔧 关键修复：输入也是wei格式
};
```

### 4. 套利机器人修改

#### 直接传递wei格式
```typescript
// 修改前：有精度损失
console.log(`✅ 第一步VEAX交易成功: ${step1Result.outputAmount} ${tokenB.symbol}`);
step2Result = await this.executeREFTrade(
  tokenB,
  tokenA,
  step1Result.outputAmount! // ❌ 可能有精度损失
);

// 修改后：直接传递wei格式
console.log(`✅ 第一步VEAX交易成功: ${step1Result.outputAmount} ${tokenB.symbol}`);
const step1OutputWei = step1Result.outputAmountWei || step1Result.outputAmount!;
console.log(`📊 使用wei格式传递: ${step1OutputWei} wei`);

step2Result = await this.executeREFTrade(
  tokenB,
  tokenA,
  step1OutputWei // ✅ 直接使用wei格式
);
```

#### 智能检测wei格式输入
```typescript
// 检查amount是否为wei格式，避免重复转换
const isWeiFormat = /^\d+$/.test(amount) && parseFloat(amount) > 1000;
let inputAmountWei: string;

if (isWeiFormat) {
  // 如果输入已经是wei格式，直接使用
  inputAmountWei = amount;
  console.log(`🔧 直接使用wei格式: ${inputAmountWei} wei`);
} else {
  // 如果是人类可读格式，进行精确转换
  if (tokenIn.id === 'wrap.near') {
    inputAmountWei = parseNearAmount(amount) || '0';
  } else {
    inputAmountWei = this.toWei(amount, tokenIn.decimals);
  }
}
```

## 🎯 最佳实践

### 1. 精确转换工具
```typescript
/**
 * 精确的wei转换（避免浮点数精度问题）
 */
static toWei(amount: string, decimals: number): string {
  const [integer, decimal = ''] = amount.split('.');
  const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
  return (integer || '0') + paddedDecimal;
}

/**
 * 从wei转换为人类可读格式
 */
static fromWei(amount: string, decimals: number): string {
  const amountStr = amount.padStart(decimals + 1, '0');
  const integerPart = amountStr.slice(0, -decimals) || '0';
  const decimalPart = amountStr.slice(-decimals);
  return `${integerPart}.${decimalPart}`.replace(/\.?0+$/, '');
}
```

### 2. NEAR代币特殊处理
```typescript
// 对于NEAR代币，始终使用官方方法
if (tokenId === 'wrap.near') {
  const result = parseNearAmount(amount);
  if (!result) {
    throw new Error('无效的NEAR数量');
  }
  return result;
} else {
  // 对于其他代币，使用精确的字符串计算
  return this.toWei(amount, decimals);
}
```

### 3. 链式交易模式
```typescript
// ✅ 正确的链式交易模式
const step1 = await veaxExecution.executeSwap(...);
if (step1.success) {
  const step2 = await refExecution.executeSwap(
    quote,
    tokenIn,
    step1.outputAmountWei, // 使用wei格式，避免精度损失
    minOutputWei
  );
}

// ❌ 错误的链式交易模式
const step1 = await veaxExecution.executeSwap(...);
if (step1.success) {
  const humanReadable = fromWei(step1.outputAmountWei, decimals);  // 精度损失
  const backToWei = toWei(humanReadable, decimals);               // 基于不准确的值
  const step2 = await refExecution.executeSwap(quote, tokenIn, backToWei, minOutputWei);
}
```

## 🔍 调试技巧

### 1. 精度损失检测
```typescript
console.log(`原始金额: ${originalWei} wei`);
console.log(`转换后金额: ${convertedWei} wei`);
const difference = BigInt(originalWei) - BigInt(convertedWei);
console.log(`精度损失: ${difference.toString()} wei`);
console.log(`损失百分比: ${(Number(difference) / Number(originalWei) * 100).toFixed(10)}%`);
```

### 2. 交易失败分析
```typescript
// 检查余额不足错误
if (error.message.includes('The account doesn\'t have enough balance')) {
  console.log('🚨 余额不足错误 - 可能是精度损失导致');
  console.log('💡 建议：检查wei格式传递是否正确');
}
```

## 📊 修复效果验证

### 修复前的问题
```
实际余额: **********482225662847625042329600 wei
请求金额: **********482225700000000000000000 wei (精度损失后)
缺少金额: ***************** wei
结果: ❌ "The account doesn't have enough balance"
```

### 修复后的效果
```
实际余额: **********482225662847625042329600 wei
请求金额: **********482225662847625042329600 wei (直接使用)
缺少金额: 0 wei
结果: ✅ 交易成功
```

## 🎉 总结

通过以上修复，我们解决了：
1. ✅ REF→VEAX方向的精度损失问题
2. ✅ VEAX→REF方向的余额不足错误
3. ✅ NEAR代币的E22错误
4. ✅ 所有代币的精度相关交易失败

**关键原则：在整个套利链中直接传递wei格式，避免任何中间转换导致的精度损失。**

---

## 🧪 测试验证

### 精度损失测试
```typescript
function testPrecisionFix() {
  const veaxOutputWei = '**********482225662847625042329600';

  // 旧方法（有精度损失）
  const humanReadable = fromWei(veaxOutputWei, 24);
  const backToWei = toWei(humanReadable, 24);
  const difference = BigInt(veaxOutputWei) - BigInt(backToWei);
  console.log(`精度损失: ${difference.toString()} wei`);

  // 新方法（无精度损失）
  const directWei = veaxOutputWei; // 直接使用
  console.log(`精度损失: 0 wei`);
}
```

### NEAR转换验证
```typescript
// 测试NEAR代币精确转换
const amounts = ['5', '1.5', '0.1'];
amounts.forEach(amount => {
  const oldWei = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, 24))).toString();
  const newWei = parseNearAmount(amount) || '0';
  const diff = BigInt(newWei) - BigInt(oldWei);
  console.log(`${amount} NEAR差异: ${diff.toString()} yoctoNEAR`);
});
```

## 🚨 常见错误排查

### 1. TypeScript编译错误
```
错误: Type 'string | null' is not assignable to type 'string | undefined'
解决: outputAmountWei: actualOutputAmount.wei || undefined
```

### 2. 余额不足错误
```
错误: The account doesn't have enough balance
检查: 是否使用了outputAmountWei字段传递精确数量
```

### 3. E22/E76错误
```
E22: REF合约存款不足 - 检查NEAR代币转换
E76: 滑点过大 - 检查精度损失是否影响价格
```

## 📊 修复效果对比

| 项目 | 修复前 | 修复后 |
|------|--------|--------|
| 精度损失 | 37万亿wei | 0 wei |
| 交易成功率 | 经常失败 | 稳定成功 |
| NEAR转换 | 有误差 | 精确 |
| 类型安全 | 部分支持 | 完全支持 |

## 🎯 快速检查清单

- [ ] 接口是否添加了`outputAmountWei`字段？
- [ ] 是否使用`parseNearAmount()`处理NEAR？
- [ ] 是否直接传递wei格式？
- [ ] 是否避免了中间转换？
- [ ] TypeScript类型是否正确？
- [ ] 是否保持了向后兼容性？

## 🚨 开发过程中的关键错误

### REF Finance Smart Router使用错误

#### 错误描述
在开发REF Finance交易执行时，完全误解了Smart Router API的工作机制，导致E22错误（存款不足）：

```typescript
// ❌ 完全错误的理解：自己构建actions
routes.forEach((route, routeIndex) => {
  route.pools.forEach((pool, poolIndex) => {
    const action = {
      pool_id: pool.pool_id,
      token_in: pool.token_in,
      token_out: pool.token_out,
      min_amount_out: "0"  // ❌ 忽略Smart Router的计算
    };

    // ❌ 自己决定amount_in分配
    if (poolIndex === 0) {
      action.amount_in = route.amount_in;
    }
  });
});
```

#### 问题分析
1. **根本性误解**：以为需要自己构建actions，实际上Smart Router已经提供了完整的action信息
2. **忽略关键数据**：忽略了Smart Router返回的`pool.amount_in`和`pool.min_amount_out`
3. **重复造轮子**：Smart Router已经做了所有计算，我们却要重新分配

#### Smart Router的真实机制
Smart Router返回的每个pool就是一个完整的action：

```json
{
  "routes": [
    {
      "pools": [
        {
          "pool_id": "5516",
          "token_in": "usdt.tether-token.near",
          "token_out": "dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near",
          "amount_in": "30000000",              // ✅ Smart Router已分配
          "min_amount_out": "0"                 // ✅ Smart Router已计算
        },
        {
          "pool_id": "5471",
          "token_in": "dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near",
          "token_out": "wrap.near",
          "amount_in": "0",                     // ✅ 链式交易中间步骤
          "min_amount_out": "**************************"  // ✅ Smart Router已计算
        }
      ]
    },
    {
      "pools": [
        {
          "pool_id": "5470",
          "token_in": "usdt.tether-token.near",
          "token_out": "wrap.near",
          "amount_in": "20000000",              // ✅ Smart Router已分配
          "min_amount_out": "9030268726905669583715490"   // ✅ Smart Router已计算
        }
      ]
    }
  ]
}
```

#### 正确的使用方式
```typescript
// ✅ 完全正确：直接使用Smart Router返回的pools作为actions
const actions = routes.flatMap(route =>
  route.pools.map(pool => ({
    pool_id: parseInt(pool.pool_id),
    token_in: pool.token_in,
    token_out: pool.token_out,
    amount_in: pool.amount_in === "0" ? undefined : pool.amount_in,  // 直接使用
    min_amount_out: pool.min_amount_out                              // 直接使用
  }))
);
```

#### 关键发现
1. **Smart Router是完整解决方案**：不仅提供路径规划，还提供精确的金额分配和输出计算
2. **pools就是actions**：每个pool包含了action所需的所有字段
3. **无需任何自定义逻辑**：直接使用Smart Router的计算结果即可

#### 错误后果
- **E22错误**：自定义分配逻辑破坏了Smart Router的精确计算
- **交易失败**：部分路径没有正确的金额分配
- **开发浪费**：花费大量时间在不必要的构建逻辑上

#### 经验教训
1. **完全理解API**：不要假设API的工作方式，要通过实际测试验证
2. **相信专业工具**：Smart Router是专业的路径优化工具，应该完全信任其计算
3. **简化代码**：最简单的方案往往是最正确的
4. **用户反馈宝贵**：用户的质疑往往指向根本问题

### 套利监控中的精度损失错误

#### 错误描述
在套利监控中，VEAX和REF返回不同格式的报价，导致反向报价时出现严重的精度损失：

```typescript
// ❌ 错误：格式不匹配导致精度损失
const [refReverseQuote, veaxReverseQuote] = await Promise.all([
  this.getREFQuote(pair.tokenB, pair.tokenA, veaxQuote),   // veaxQuote是wei格式！
  this.getVEAXQuote(pair.tokenB, pair.tokenA, refQuote)    // refQuote是人类可读格式
]);
```

#### 问题分析
1. **VEAX返回wei格式**：`"356885978204306485916616845251"` (24位精度)
2. **REF返回人类可读格式**：`"15.429735"` (6位精度)
3. **反向报价时格式错误**：REF期望人类可读，但收到wei格式
4. **REF内部错误处理**：`toNonDivisibleNumber()` 把wei当人类可读处理
5. **结果**：`356885978204306485916616845251 * 10^24` = 巨大错误数字

#### 精度损失的具体位置
```typescript
// ❌ VEAX执行时的不必要转换
const outputAmountFloat = parseFloat(quote.outputAmount);  // wei → 浮点数，精度损失！
const outputAmountBigInt = BigInt(Math.floor(outputAmountFloat * Math.pow(10, outputTokenDecimals)));
```

#### 正确的修复方案
```typescript
// ✅ 添加精确的格式转换方法
private fromWei(amount: string, decimals: number): string {
  if (decimals === 24) {
    return formatNearAmount(amount);  // 使用NEAR官方方法
  } else {
    // 精确的字符串操作，避免浮点数
    const paddedAmount = amount.padStart(decimals + 1, '0');
    const integerPart = paddedAmount.slice(0, -decimals) || '0';
    const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');
    return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
  }
}

// ✅ 反向报价前进行格式转换
const veaxQuoteHumanReadable = this.fromWei(veaxQuote, pair.tokenB.decimals);
const [refReverseQuote, veaxReverseQuote] = await Promise.all([
  this.getREFQuote(pair.tokenB, pair.tokenA, veaxQuoteHumanReadable),  // 使用人类可读格式
  this.getVEAXQuote(pair.tokenB, pair.tokenA, refQuote)                // 已经是人类可读格式
]);

// ✅ VEAX执行时直接使用wei格式
const outputAmountWei = quote.outputAmount;  // 直接使用，无需转换
```

#### 经验教训
1. **格式一致性**：不同API返回的数值格式必须统一处理
2. **精度优先**：使用NEAR官方方法和精确的字符串操作
3. **避免浮点数**：在区块链开发中，浮点数是精度损失的主要来源
4. **详细测试**：通过实际数值验证格式转换的正确性

### Big.js Invalid number错误

#### 错误描述
在REF Smart Router和DCL v2服务中出现`[big.js] Invalid number`错误，导致报价查询失败：

```
❌ V1 Smart Router 调用失败: [big.js] Invalid number
❌ DCL v2 报价失败: [big.js] Invalid number
```

#### 问题分析
1. **Big.js数值验证严格**：Big.js对输入数值格式要求严格
2. **异常输入未处理**：null、undefined、空字符串等异常输入直接传给Big.js
3. **科学计数法问题**：1.23e+10等格式被Big.js拒绝
4. **格式不匹配**：VEAX返回的人类可读格式与预期不符

#### 具体错误场景
```typescript
// ❌ 错误：直接使用Big.js处理未验证的输入
return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
// 当amount为null、undefined、''、'NaN'等时抛出错误
```

#### 解决方案
```typescript
// ✅ 添加严格的数值验证
private isValidNumber(value: any): boolean {
  if (value === null || value === undefined || value === '') return false;

  const str = String(value).trim();
  if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
    return false;
  }

  // 检查是否为纯数字字符串（允许小数点）
  return /^\d+(\.\d+)?$/.test(str);
}

// ✅ 替换Big.js为精确的转换方法
private toNonDivisibleNumber(amount: string, decimals: number): string {
  if (!this.isValidNumber(amount)) {
    console.warn(`⚠️ 收到无效数值: ${amount}, 返回'0'`);
    return '0';
  }

  if (decimals === 24) {
    return parseNearAmount(amount) || '0';  // 使用NEAR官方方法
  } else {
    // 精确的字符串操作
    const [integer, decimal = ''] = amount.split('.');
    const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
    return (integer || '0') + paddedDecimal;
  }
}
```

#### 修复范围
1. **REF Smart Router** (`src/services/v1SmartRouter.ts`)
2. **DCL v2合约服务** (`src/services/dclv2Contract.ts`)
3. **可复用模块** (`reusable-modules/ref-finance/v1-router.ts`)

#### 验证结果
```
✅ 无效数值处理：null、undefined、空字符串等都被正确处理为'0'
✅ 科学计数法处理：1.23e+10等格式被正确拒绝
✅ 边界情况正常：0、0.1、1000等都正常工作
✅ 精度转换正确：使用NEAR官方方法和精确字符串操作
```

#### 经验教训
1. **输入验证优先**：任何外部输入都必须严格验证
2. **官方方法优先**：使用NEAR官方parseNearAmount和formatNearAmount
3. **字符串操作精确**：避免浮点数运算，使用字符串操作保持精度
4. **错误处理完善**：异常输入返回安全的默认值'0'

### wei格式精度损失错误

#### 错误描述
在套利机器人执行交易时，发现REF Smart Router的amount_in字段出现精度损失：

```json
{
  "amount_in": "6331830778580438000000"  // ❌ 精度损失
}
// 应该是: "6331830778580438089728"
// 差异:    "89728" → "00000"
```

#### 问题分析
1. **JavaScript浮点数精度限制**：`parseFloat()`只能保持约15-17位有效数字
2. **错误的wei格式转换**：使用`parseFloat()`和`Math.pow()`进行精度转换
3. **精度损失位置**：在wei格式输入转换为人类可读格式时

#### 具体错误代码
```typescript
// ❌ 错误：使用parseFloat导致精度损失
const amountFloat = parseFloat(amount);  // "6331830778580438089728" → 6331830778580438000000
```

## 🔧 REF Finance输出金额提取优化

### 问题描述
在套利交易中发现REF Finance交易的实际输出金额与机器人获取的金额不匹配：

```
REF区块链实际输出: 1060173820656998309173701887367336 wei
机器人获取的输出:   1059583155507499803153604415250318 wei
差异:              590665149499505000000000117018 wei (0.055714%)
```

### 根本原因
REF Finance使用**普通Transfer日志格式**，而不是EVENT_JSON格式：

```
// REF Finance实际日志格式
Transfer 1060173820656998309173701887367336 from v2.ref-finance.near to whatdoyoumean.near

// 我们的代码只查找EVENT_JSON格式
if (log.includes('EVENT_JSON:')) {
  // 找不到REF的Transfer日志！
}
```

### 修复方案
修改`extractOutputAmountFromResult`方法，支持两种日志格式：

```typescript
/**
 * 从交易结果中提取实际输出金额（wei格式）
 * 🔧 优化版本：支持REF Finance的普通Transfer日志格式
 */
private extractOutputAmountFromResult(result: any): { humanReadable: string | null; wei: string | null } {
  try {
    const allReceipts = result.receipts_outcome || [];

    for (const receipt of allReceipts) {
      const logs = receipt.outcome?.logs || [];

      for (const log of logs) {
        try {
          // 🔧 首先查找EVENT_JSON日志（VEAX风格）
          if (log.includes('EVENT_JSON:')) {
            const eventStr = log.split('EVENT_JSON:')[1];
            const event = JSON.parse(eventStr);

            if (event.standard === 'nep141' && event.event === 'ft_transfer') {
              const transferData = event.data?.[0];
              if (transferData?.new_owner_id === this.account?.accountId && transferData.amount) {
                console.log(`📊 从EVENT_JSON提取实际输出: ${transferData.amount} wei`);
                return { wei: transferData.amount, humanReadable: null };
              }
            }
          } else {
            // 🔧 关键修复：查找普通Transfer日志格式（REF Finance风格）
            const transferMatch = log.match(/Transfer (\d+) from ([\w\.-]+) to ([\w\.-]+)/);
            if (transferMatch) {
              const [, amount, from, to] = transferMatch;

              if (to === this.account?.accountId) {
                console.log(`📊 从普通日志提取实际输出: ${amount} wei (from ${from} to ${to})`);
                return { wei: amount, humanReadable: null };
              }
            }
          }
        } catch (parseError) {
          continue;
        }
      }
    }

    console.log('❌ 未找到匹配的transfer事件');
    return { humanReadable: null, wei: null };
  } catch (error) {
    console.error('❌ 提取输出金额失败:', error);
    return { humanReadable: null, wei: null };
  }
}
```

### 修复效果
```
修复前(错误): 1059583155507499803153604415250318 wei
修复后(正确): 1060173820656998309173701887367336 wei
差异:        590665149498506020097472117018 wei
匹配预期:    ✅ 完全匹配
```

### 影响范围
- ✅ **REF→VEAX套利**：第二步VEAX交易现在使用正确的输入金额
- ✅ **精度保护**：避免了0.055714%的精度损失
- ✅ **交易成功率**：提高大额交易的成功率
- ✅ **向后兼容**：仍支持VEAX的EVENT_JSON格式

## 🚨 SSL证书过期问题

### 问题描述
REF Finance V1 Smart Router API出现SSL证书过期错误：

```
❌ V1 Smart Router 调用失败: certificate has expired
❌ 报价查询失败: 所有系统都失败了: V1 Smart Router API 调用失败: certificate has expired
```

### 影响范围
- ❌ **REF Finance V1 Smart Router API** 无法访问
- ❌ **所有报价查询失败**
- ❌ **套利机器人无法获取价格信息**

### 临时解决方案
在`src/services/v1SmartRouter.ts`第110-116行添加SSL证书忽略：

```typescript
// 修改前
const response = await axios.get<SmartRouterResponse>(url, {
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Accept': 'application/json',
    'User-Agent': 'REF-VEAX-Arbitrage-Bot/1.0'
  }
});

// 修改后
const response = await axios.get<SmartRouterResponse>(url, {
  timeout: REQUEST_TIMEOUT,
  headers: {
    'Accept': 'application/json',
    'User-Agent': 'REF-VEAX-Arbitrage-Bot/1.0'
  },
  // 🔧 临时解决方案：忽略SSL证书验证
  httpsAgent: new (require('https').Agent)({
    rejectUnauthorized: false
  })
});
```

### 修改位置记录
- **文件**: `src/services/v1SmartRouter.ts`
- **行数**: 第110-116行
- **修改类型**: 添加`httpsAgent`配置
- **影响**: 仅REF Finance V1 API调用
- **状态**: ⚠️ 临时解决方案，等待服务商修复证书

### 注意事项
- ⚠️ **仅临时使用**：一旦REF Finance修复证书，应立即移除此配置
- ✅ **影响范围有限**：只影响V1 Smart Router，不影响VEAX和DCL v2
- ✅ **功能正常**：SSL忽略后API调用恢复正常
- 🔒 **安全考虑**：生产环境中应谨慎使用SSL忽略
const decimals = tokenIn.decimals;
humanReadableAmount = (amountFloat / Math.pow(10, decimals)).toString();
```

#### 精度损失示例
```
原始wei: "6331830778580438089728"
parseFloat: 6331830778580438000000  // 丢失了 "89728"
最终结果: 精度损失导致amount_in不准确
```

#### 正确的修复方案
```typescript
// ✅ 正确：使用精确的fromWei方法
humanReadableAmount = this.fromWei(amount, tokenIn.decimals);

// fromWei方法实现
private fromWei(amount: string, decimals: number): string {
  if (decimals === 24) {
    return formatNearAmount(amount).replace(/,/g, '');  // NEAR官方方法
  } else {
    // 精确的字符串操作
    if (amount === '0') return '0';
    const paddedAmount = amount.padStart(decimals + 1, '0');
    const integerPart = paddedAmount.slice(0, -decimals) || '0';
    const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');
    return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
  }
}
```

#### 修复验证
```
测试案例: "6331830778580438089728" (18位精度)
❌ 错误方法: 6331.830778580438 (精度损失)
✅ 正确方法: 6331.830778580438089728 (完整精度)
```

#### 修复范围
1. **executeREFTrade** (`src/arbitrageBot.ts:570-573`)
2. **executeVEAXTrade** (`src/arbitrageBot.ts:677-679`)
3. **所有wei格式输入处理**

#### 经验教训
1. **避免parseFloat**：在区块链开发中，parseFloat是精度损失的主要来源
2. **字符串操作优先**：使用字符串操作保持完整精度
3. **NEAR官方方法**：对于24位精度，使用formatNearAmount
4. **完整测试验证**：通过实际数值验证精度保持

## API返回格式详细说明

### VEAX API返回格式

#### 确认：VEAX返回人类可读格式

**原始API测试结果**：
```json
// 请求：11 NEAR → BLACKDRAGON
{
  "token_a": "wrap.near",
  "token_b": "blackdragon.tkn.near",
  "amount_a": "11"
}

// 响应
{
  "amount_b_expected": "2518779270.443279475429878220322304"
}
```

**格式特征**：
- ✅ **包含小数点**：明确的人类可读格式标志
- ✅ **高精度小数**：24位小数精度（符合BLACKDRAGON配置）
- ✅ **合理汇率**：11 NEAR ≈ 25亿BLACKDRAGON（meme币典型汇率）
- ✅ **字符串格式**：完整精度保持

**多代币验证**：
```
USDT测试: 0.1 NEAR → "0.219304" USDT ✅
DOGSHIT测试: 1 NEAR → "26609.006684227304947712" DOGSHIT ✅
BLACKDRAGON测试: 1 NEAR → "230679897.910541286321725650590208" BLACKDRAGON ✅
```

**关键发现**：
- VEAX在所有代币类型下都返回人类可读格式
- 精度保持完整，无截断
- 直接可用于REF Smart Router输入

### REF Smart Router API返回格式

#### 确认：REF Smart Router返回人类可读格式

**内部处理流程**：
```typescript
// 1. 输入转换：人类可读 → wei
const amountInNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);

// 2. API调用：使用wei格式
url.searchParams.set('amountIn', amountInNonDivisible);

// 3. 输出转换：wei → 人类可读
const outputAmount = this.toReadableNumber(result_data.amount_out, params.tokenOut.decimals);
```

**返回格式**：
- ✅ **人类可读格式**：经过toReadableNumber转换
- ✅ **精度保持**：使用精确的字符串操作
- ✅ **格式一致**：与VEAX输出格式匹配

### 格式兼容性总结

#### 完美的格式匹配
```
VEAX输出 → REF输入：
"230679897.910541286321725650590208" → 直接使用 ✅

REF输出 → VEAX输入：
"15.429735" → 直接使用 ✅
```

#### 套利监控流程
```typescript
// 1. 获取初始报价（格式匹配）
const veaxQuote = "15.429735";  // 人类可读
const refQuote = "15.429735";   // 人类可读

// 2. 反向报价（格式匹配）
const refReverseQuote = await getREFQuote(tokenB, tokenA, veaxQuote);    // ✅
const veaxReverseQuote = await getVEAXQuote(tokenB, tokenA, refQuote);   // ✅

// 3. 利润计算（格式一致）
const profit = parseFloat(refReverseQuote) - parseFloat(tradeAmount);    // ✅
```

#### 关键优势
1. **无需格式转换**：两个API都返回人类可读格式
2. **精度保持完整**：字符串操作避免浮点数精度损失
3. **代码简化**：直接传递，无需复杂的格式处理
4. **错误减少**：格式匹配减少了转换错误的可能性

### 自动余额管理中的概念混淆

#### 错误描述
在开发自动余额管理功能时，错误地将`reserveAmount`描述为"预留gas费用"：

```typescript
// ❌ 错误的描述
reserveAmount: 0.5        // 预留gas费用（不会解包的wNEAR数量）
```

#### 问题分析
1. **概念混乱**：wNEAR是代币，不是gas费用
2. **gas费用**：在NEAR网络中，gas费用是用NEAR代币支付的
3. **wNEAR用途**：wNEAR主要用于DeFi交易，不是用于支付gas

#### 正确理解
```typescript
// ✅ 正确的描述
reserveAmount: 0.5        // 预留wNEAR数量（保留用于交易，不会被解包）
```

#### 真正作用
- **保留交易资金**：确保有足够的wNEAR用于套利交易
- **避免过度解包**：防止把所有wNEAR都解包导致无法交易
- **维持流动性**：保持一定的wNEAR余额用于DeFi操作

#### 经验教训
1. **概念准确性**：在区块链开发中，代币类型和用途的概念必须准确
2. **文档一致性**：所有文档和注释中的描述必须保持一致
3. **用户反馈**：及时响应用户的概念纠正，避免误导

## 💡 最佳实践

1. **优先使用wei格式** - 链式交易中传递精确数量
2. **NEAR特殊处理** - 使用官方parseNearAmount()
3. **智能检测输入** - 自动识别格式类型
4. **保持兼容性** - 同时返回两种格式
5. **错误处理** - 检测精度相关错误
6. **测试验证** - 验证修复效果
7. **概念准确** - 确保所有概念描述的准确性
