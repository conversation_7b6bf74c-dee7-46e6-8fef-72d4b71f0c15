/**
 * 简单交易示例和演示代码
 *
 * 功能：提供NEAR DeFi SDK的完整使用示例
 * 职责：
 * - 演示基本的代币交换操作
 * - 展示VEAX和REF Finance的使用方法
 * - 提供价格比较和最佳选择逻辑
 * - 演示自动余额管理的集成
 * - 作为学习和测试的起点
 * - 展示最佳实践和错误处理
 *
 * 核心示例：
 * - swapNearToUsdtOnVeax(): VEAX交易完整流程
 * - swapNearToUsdtOnRef(): REF Finance交易完整流程
 * - comparePrices(): 多DEX价格比较和最佳选择
 * - showBalances(): 余额查询和显示
 *
 * 演示功能：
 * 1. 服务初始化和配置
 * 2. 自动NEAR包装检查
 * 3. 报价获取和验证
 * 4. 交易执行和结果处理
 * 5. 错误处理和日志记录
 * 6. wei格式的精确处理
 *
 * 最佳实践展示：
 * - 滑点保护设置
 * - 最小输出金额计算
 * - 交易前余额检查
 * - 错误状态处理
 * - 精确数量转换
 * - 服务生命周期管理
 *
 * 学习价值：
 * - 了解DeFi交易的完整流程
 * - 学习多DEX集成的方法
 * - 掌握精度处理的重要性
 * - 理解错误处理的最佳实践
 * - 体验自动化交易的实现
 *
 * 使用方式：
 * - 直接运行：npx ts-node examples/simple-swap.ts
 * - 作为模板：复制代码到自己的项目
 * - 学习参考：理解SDK的使用方法
 * - 测试验证：验证SDK功能的正确性
 */

import VeaxQuoteService from '../veax/quote-service';
import VeaxExecutionService from '../veax/execution-service';
import { V1SmartRouterService } from '../ref-finance/v1-router';
import RefExecutionService from '../ref-finance/execution-service';
import NearWrapService from '../near-utils/wrap-service';
import { TOKENS } from '../config/tokens';
import { getEnvConfig } from '../config';

/**
 * 简单交易机器人类
 */
export class SimpleSwapBot {
  private veaxQuote = VeaxQuoteService;
  private veaxExecution: VeaxExecutionService;
  private refRouter = new V1SmartRouterService();
  private refExecution: RefExecutionService;
  private nearWrap: NearWrapService;

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: 'mainnet' | 'testnet' = 'mainnet'
  ) {
    this.veaxExecution = new VeaxExecutionService(accountId, privateKey, networkId);
    this.refExecution = new RefExecutionService(accountId, privateKey, networkId);
    this.nearWrap = new NearWrapService(accountId, privateKey, networkId);
  }

  /**
   * 初始化所有服务
   */
  async initialize(): Promise<void> {
    console.log('🚀 初始化简单交易机器人...');
    
    await Promise.all([
      this.veaxExecution.initialize(),
      this.refExecution.initialize(),
      this.nearWrap.initialize()
    ]);
    
    console.log('✅ 所有服务初始化完成');
  }

  /**
   * 在VEAX上交换NEAR为USDT
   */
  async swapNearToUsdtOnVeax(nearAmount: string): Promise<void> {
    try {
      console.log(`\n🔄 在VEAX上交换 ${nearAmount} NEAR → USDT`);
      
      // 1. 确保有足够的wNEAR
      const wrapResult = await this.nearWrap.checkAndWrapNear(nearAmount);
      if (!wrapResult.success) {
        throw new Error(`NEAR包装失败: ${wrapResult.error}`);
      }

      // 2. 转换为wei格式
      const amountWei = NearWrapService.toWei(nearAmount, TOKENS.NEAR.decimals);
      
      // 3. 获取VEAX报价
      const quote = await this.veaxQuote.getQuote(
        TOKENS.NEAR.id,
        TOKENS.USDT.id,
        amountWei
      );

      if (!quote.success) {
        throw new Error(`VEAX报价失败: ${quote.error}`);
      }

      console.log(`📊 VEAX报价: ${NearWrapService.fromWei(quote.outputAmount, TOKENS.USDT.decimals)} USDT`);
      console.log(`📊 价格影响: ${quote.priceImpact}`);

      // 4. 计算最小输出（1%滑点保护）
      const minOutputAmount = (BigInt(quote.outputAmount) * BigInt(99) / BigInt(100)).toString();

      // 5. 执行交易
      const result = await this.veaxExecution.executeSwap(
        TOKENS.NEAR.id,
        TOKENS.USDT.id,
        amountWei,
        minOutputAmount
      );

      if (result.success) {
        console.log(`✅ VEAX交易成功: ${result.transactionHash}`);
        console.log(`📊 实际输出: ${result.outputAmountWei} wei (精确格式)`);
      } else {
        console.log(`❌ VEAX交易失败: ${result.error}`);
      }

    } catch (error) {
      console.error('❌ VEAX交易过程失败:', error);
    }
  }

  /**
   * 在REF Finance上交换NEAR为USDT
   */
  async swapNearToUsdtOnRef(nearAmount: string): Promise<void> {
    try {
      console.log(`\n🔄 在REF Finance上交换 ${nearAmount} NEAR → USDT`);
      
      // 1. 确保有足够的wNEAR
      const wrapResult = await this.nearWrap.checkAndWrapNear(nearAmount);
      if (!wrapResult.success) {
        throw new Error(`NEAR包装失败: ${wrapResult.error}`);
      }

      // 2. 获取REF报价
      const quote = await this.refRouter.getV1Quote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDT,
        amountIn: nearAmount,
        slippage: 0.01 // 1%滑点
      });

      if (!quote) {
        throw new Error('REF报价失败');
      }

      console.log(`📊 REF报价: ${quote.outputAmount} USDT (${quote.system})`);

      // 3. 转换金额为wei格式
      const inputAmountWei = this.refExecution.convertToWei(nearAmount, TOKENS.NEAR.id, TOKENS.NEAR.decimals);
      const minOutputAmountWei = this.refExecution.convertToWei(
        (parseFloat(quote.outputAmount) * 0.99).toString(), // 1%滑点保护
        TOKENS.USDT.id,
        TOKENS.USDT.decimals
      );

      // 4. 执行交易
      const result = await this.refExecution.executeSwap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        0.01
      );

      if (result.success) {
        console.log(`✅ REF交易成功: ${result.transactionHash}`);
        console.log(`📊 实际输出: ${result.outputAmountWei} wei (精确格式)`);
      } else {
        console.log(`❌ REF交易失败: ${result.error}`);
      }

    } catch (error) {
      console.error('❌ REF交易过程失败:', error);
    }
  }

  /**
   * 比较两个DEX的价格
   */
  async comparePrices(nearAmount: string): Promise<void> {
    try {
      console.log(`\n📊 比较 ${nearAmount} NEAR → USDT 的价格`);
      
      const amountWei = NearWrapService.toWei(nearAmount, TOKENS.NEAR.decimals);
      
      // 并行获取两个DEX的报价
      const [veaxQuote, refQuote] = await Promise.all([
        this.veaxQuote.getQuote(TOKENS.NEAR.id, TOKENS.USDT.id, amountWei),
        this.refRouter.getV1Quote({
          tokenIn: TOKENS.NEAR,
          tokenOut: TOKENS.USDT,
          amountIn: nearAmount,
          slippage: 0.01
        })
      ]);

      console.log('\n📈 价格比较结果:');
      
      if (veaxQuote.success) {
        const veaxOutput = NearWrapService.fromWei(veaxQuote.outputAmount, TOKENS.USDT.decimals);
        console.log(`   VEAX: ${veaxOutput} USDT (价格影响: ${veaxQuote.priceImpact})`);
      } else {
        console.log(`   VEAX: 报价失败 - ${veaxQuote.error}`);
      }

      if (refQuote) {
        console.log(`   REF:  ${refQuote.outputAmount} USDT (系统: ${refQuote.system})`);
      } else {
        console.log(`   REF:  报价失败`);
      }

      // 计算最佳选择
      if (veaxQuote.success && refQuote) {
        const veaxOutput = parseFloat(NearWrapService.fromWei(veaxQuote.outputAmount, TOKENS.USDT.decimals));
        const refOutput = parseFloat(refQuote.outputAmount);
        
        if (veaxOutput > refOutput) {
          const advantage = ((veaxOutput - refOutput) / refOutput * 100).toFixed(4);
          console.log(`\n🏆 最佳选择: VEAX (优势: +${advantage}%)`);
        } else {
          const advantage = ((refOutput - veaxOutput) / veaxOutput * 100).toFixed(4);
          console.log(`\n🏆 最佳选择: REF Finance (优势: +${advantage}%)`);
        }
      }

    } catch (error) {
      console.error('❌ 价格比较失败:', error);
    }
  }

  /**
   * 显示账户余额
   */
  async showBalances(): Promise<void> {
    try {
      console.log('\n💰 账户余额信息:');
      
      const balanceInfo = await this.nearWrap.getBalanceInfo();
      console.log(`   NEAR余额: ${balanceInfo.nearBalance}`);
      console.log(`   wNEAR余额: ${balanceInfo.wNearBalance}`);
      console.log(`   总余额: ${balanceInfo.totalBalance} NEAR`);
      
    } catch (error) {
      console.error('❌ 获取余额失败:', error);
    }
  }
}

/**
 * 使用示例
 */
async function example() {
  // 从环境变量获取配置
  const config = getEnvConfig();
  
  if (!config.accountId || !config.privateKey) {
    console.error('❌ 请设置环境变量: NEAR_ACCOUNT_ID 和 NEAR_PRIVATE_KEY');
    return;
  }

  const bot = new SimpleSwapBot(config.accountId, config.privateKey, config.network);
  
  try {
    // 初始化
    await bot.initialize();
    
    // 显示余额
    await bot.showBalances();
    
    // 比较价格
    await bot.comparePrices('1'); // 1 NEAR
    
    // 执行交易（取消注释以实际执行）
    // await bot.swapNearToUsdtOnVeax('0.1');
    // await bot.swapNearToUsdtOnRef('0.1');
    
  } catch (error) {
    console.error('❌ 示例执行失败:', error);
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  example().catch(console.error);
}

export default SimpleSwapBot;
