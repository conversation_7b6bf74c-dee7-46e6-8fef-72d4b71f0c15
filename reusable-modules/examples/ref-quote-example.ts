/**
 * REF Finance 报价服务使用示例
 *
 * 功能：演示如何使用新的REF Finance统一报价服务
 * 职责：
 * - 展示基础报价查询功能
 * - 演示批量报价查询
 * - 展示报价比较和分析
 * - 提供完整的错误处理示例
 *
 * 使用场景：
 * - 学习如何集成REF Finance报价功能
 * - 测试不同交易对的报价
 * - 比较V1和DCL v2系统的性能
 * - 作为其他项目的参考实现
 */

import { RefQuoteService, refQuoteService } from '../ref-finance/quote-service';
import { TOKENS } from '../config/tokens';
import { QuoteParams, QuoteResult } from '../types';

/**
 * REF Finance 报价示例类
 */
export class RefQuoteExample {
  private quoteService: RefQuoteService;

  constructor() {
    // 可以使用单例实例或创建新实例
    this.quoteService = refQuoteService;
  }

  /**
   * 基础报价查询示例
   */
  async basicQuoteExample(): Promise<void> {
    console.log('\n🚀 REF Finance 基础报价查询示例');
    console.log('=====================================');

    try {
      // 查询 1000 NEAR → USDT 的报价
      const params: QuoteParams = {
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDT,
        amountIn: '1000',
        slippage: 0.005 // 0.5%
      };

      console.log(`📊 查询报价: ${params.amountIn} ${params.tokenIn.symbol} → ${params.tokenOut.symbol}`);
      
      const quote = await this.quoteService.getQuote(params);
      
      console.log(`✅ 最佳报价: ${quote.outputAmount} ${params.tokenOut.symbol}`);
      console.log(`🏆 获胜系统: ${quote.system}`);
      console.log(`📋 合约地址: ${quote.contractId}`);
      
      // 获取详细信息
      const details = this.quoteService.getQuoteDetails(quote);
      console.log(`📈 路径信息: ${details.route}`);
      console.log(`💰 价格影响: ${details.priceImpact}`);

    } catch (error: any) {
      console.error('❌ 报价查询失败:', error.message);
    }
  }

  /**
   * 批量报价查询示例
   */
  async batchQuoteExample(): Promise<void> {
    console.log('\n🔄 REF Finance 批量报价查询示例');
    console.log('=====================================');

    try {
      // 定义多个交易对
      const paramsList: QuoteParams[] = [
        {
          tokenIn: TOKENS.NEAR,
          tokenOut: TOKENS.USDT,
          amountIn: '100',
          slippage: 0.005
        },
        {
          tokenIn: TOKENS.NEAR,
          tokenOut: TOKENS.USDC,
          amountIn: '100',
          slippage: 0.005
        },
        {
          tokenIn: TOKENS.USDT,
          tokenOut: TOKENS.NEAR,
          amountIn: '1000',
          slippage: 0.005
        }
      ];

      console.log(`📊 批量查询 ${paramsList.length} 个交易对...`);
      
      const quotes = await this.quoteService.getBatchQuotes(paramsList);
      
      console.log('\n📋 批量报价结果:');
      quotes.forEach((quote, index) => {
        const params = paramsList[index];
        if (quote) {
          console.log(`${index + 1}. ${params.amountIn} ${params.tokenIn.symbol} → ${quote.outputAmount} ${params.tokenOut.symbol} (${quote.system})`);
        } else {
          console.log(`${index + 1}. ${params.amountIn} ${params.tokenIn.symbol} → ${params.tokenOut.symbol}: 无可用报价`);
        }
      });

    } catch (error: any) {
      console.error('❌ 批量报价查询失败:', error.message);
    }
  }

  /**
   * 报价比较示例
   */
  async quoteComparisonExample(): Promise<void> {
    console.log('\n⚖️ REF Finance 报价比较示例');
    console.log('=====================================');

    try {
      // 测试不同金额的报价
      const amounts = ['10', '100', '1000', '10000'];
      const tokenPair = { tokenIn: TOKENS.NEAR, tokenOut: TOKENS.USDT };

      console.log(`📊 比较不同金额的 ${tokenPair.tokenIn.symbol} → ${tokenPair.tokenOut.symbol} 报价:`);
      
      for (const amount of amounts) {
        const params: QuoteParams = {
          ...tokenPair,
          amountIn: amount,
          slippage: 0.005
        };

        try {
          const quote = await this.quoteService.getQuote(params);
          const rate = parseFloat(quote.outputAmount) / parseFloat(amount);
          
          console.log(`💰 ${amount} ${tokenPair.tokenIn.symbol} → ${quote.outputAmount} ${tokenPair.tokenOut.symbol} (${quote.system}, 汇率: ${rate.toFixed(4)})`);
        } catch (error) {
          console.log(`💰 ${amount} ${tokenPair.tokenIn.symbol}: 查询失败`);
        }
      }

    } catch (error: any) {
      console.error('❌ 报价比较失败:', error.message);
    }
  }

  /**
   * 错误处理示例
   */
  async errorHandlingExample(): Promise<void> {
    console.log('\n🚨 REF Finance 错误处理示例');
    console.log('=====================================');

    // 测试无效参数
    try {
      const invalidParams: QuoteParams = {
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.NEAR, // 相同代币
        amountIn: '100',
        slippage: 0.005
      };

      await this.quoteService.getQuote(invalidParams);
    } catch (error: any) {
      console.log(`✅ 正确捕获错误: ${error.message}`);
    }

    // 测试无效金额
    try {
      const invalidAmountParams: QuoteParams = {
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDT,
        amountIn: '0', // 无效金额
        slippage: 0.005
      };

      await this.quoteService.getQuote(invalidAmountParams);
    } catch (error: any) {
      console.log(`✅ 正确捕获错误: ${error.message}`);
    }

    // 测试不支持的交易对
    try {
      const unsupportedParams: QuoteParams = {
        tokenIn: TOKENS.BLACKDRAGON, // 可能不支持的代币
        tokenOut: TOKENS.WBTC,
        amountIn: '1000',
        slippage: 0.005
      };

      const quote = await this.quoteService.getQuote(unsupportedParams);
      if (quote) {
        console.log(`✅ 意外成功: ${quote.outputAmount} ${unsupportedParams.tokenOut.symbol} (${quote.system})`);
      }
    } catch (error: any) {
      console.log(`✅ 正确处理不支持的交易对: ${error.message}`);
    }
  }

  /**
   * 运行所有示例
   */
  async runAllExamples(): Promise<void> {
    console.log('🎯 REF Finance 报价服务完整示例');
    console.log('===================================');

    await this.basicQuoteExample();
    await this.batchQuoteExample();
    await this.quoteComparisonExample();
    await this.errorHandlingExample();

    console.log('\n🎉 所有示例运行完成！');
  }
}

/**
 * 快速运行示例的函数
 */
export async function runRefQuoteExamples(): Promise<void> {
  const example = new RefQuoteExample();
  await example.runAllExamples();
}

/**
 * 默认导出
 */
export default RefQuoteExample;

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runRefQuoteExamples().catch(console.error);
}
