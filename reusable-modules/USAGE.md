# NEAR DeFi SDK 使用指南

## 🚀 快速开始

### 安装依赖
```bash
npm install near-api-js axios big.js dotenv
```

### 环境配置
创建 `.env` 文件：
```env
NEAR_ACCOUNT_ID=your-account.near
NEAR_PRIVATE_KEY=ed25519:your-private-key
NEAR_NETWORK=mainnet
```

## 📦 模块导入

### 完整导入
```typescript
import SDK, { VEAX, REF, NEAR, TOKENS } from 'near-defi-sdk';
```

### 按需导入
```typescript
import VeaxQuoteService from 'near-defi-sdk/veax/quote';
import { RefQuoteService, refQuoteService } from 'near-defi-sdk/ref-finance/quote';
import RefExecutionService from 'near-defi-sdk/ref-finance/execution';
import { TOKENS } from 'near-defi-sdk/config/tokens';
```

## 🎯 基础使用

### 1. VEAX报价查询
```typescript
import VeaxQuoteService from './veax/quote-service';

// 获取1 NEAR → USDT的报价
const quote = await VeaxQuoteService.getQuote(
  'wrap.near',                    // 输入代币
  'usdt.tether-token.near',      // 输出代币
  '1000000000000000000000000'    // 1 NEAR (wei格式)
);

if (quote.success) {
  console.log(`输出: ${quote.outputAmount} USDT (wei格式)`);
  console.log(`价格影响: ${quote.priceImpact}`);
} else {
  console.log(`报价失败: ${quote.error}`);
}
```

### 2. REF Finance统一报价查询 ⭐ 新功能
```typescript
import { refQuoteService } from './ref-finance/quote-service';
import { TOKENS } from './config/tokens';

// 获取最佳报价（自动比较V1和DCL v2系统）
const quote = await refQuoteService.getQuote({
  tokenIn: TOKENS.NEAR,
  tokenOut: TOKENS.USDT,
  amountIn: '1000',  // 1000 NEAR (人类可读格式)
  slippage: 0.005    // 0.5%滑点
});

console.log(`✅ 最佳报价: ${quote.outputAmount} USDT`);
console.log(`🏆 获胜系统: ${quote.system} (V1 或 DCL_V2)`);
console.log(`📋 合约地址: ${quote.contractId}`);

// 获取详细信息
const details = refQuoteService.getQuoteDetails(quote);
console.log(`📈 路径信息: ${details.route}`);
console.log(`💰 价格影响: ${details.priceImpact}`);
```

### 3. REF Finance批量报价查询 ⭐ 新功能
```typescript
import { refQuoteService } from './ref-finance/quote-service';
import { TOKENS } from './config/tokens';

// 批量查询多个交易对
const paramsList = [
  {
    tokenIn: TOKENS.NEAR,
    tokenOut: TOKENS.USDT,
    amountIn: '100',
    slippage: 0.005
  },
  {
    tokenIn: TOKENS.NEAR,
    tokenOut: TOKENS.USDC,
    amountIn: '100',
    slippage: 0.005
  }
];

const quotes = await refQuoteService.getBatchQuotes(paramsList);

quotes.forEach((quote, index) => {
  if (quote) {
    console.log(`${index + 1}. ${quote.outputAmount} ${paramsList[index].tokenOut.symbol} (${quote.system})`);
  } else {
    console.log(`${index + 1}. 报价失败`);
  }
});
```

### 4. REF Finance V1单独查询（高级用法）
```typescript
import { V1SmartRouterService } from './ref-finance/v1-router';
import { TOKENS } from './config/tokens';

const router = new V1SmartRouterService();

const quote = await router.getV1Quote({
  tokenIn: TOKENS.NEAR,
  tokenOut: TOKENS.USDT,
  amountIn: '1',  // 1 NEAR (人类可读格式)
  slippage: 0.005 // 0.5%滑点
});

if (quote) {
  console.log(`V1报价: ${quote.outputAmount} USDT`);
  console.log(`路径: ${router.getRouteDetails(quote)}`);
} else {
  console.log('V1报价失败');
}
```

### 5. 执行交易
```typescript
import RefExecutionService from './ref-finance/execution-service';

const execution = new RefExecutionService(
  'your-account.near',
  'your-private-key',
  'mainnet'
);

await execution.initialize();

const result = await execution.executeSwap(
  quote,                    // 报价结果
  'wrap.near',             // 输入代币
  inputAmountWei,          // 输入金额(wei)
  minOutputAmountWei,      // 最小输出(wei)
  0.01                     // 1%滑点
);

if (result.success) {
  console.log(`交易成功: ${result.transactionHash}`);
  console.log(`实际输出: ${result.outputAmountWei} wei (精确格式)`);
  console.log(`人类可读: ${result.outputAmount} (显示格式)`);
} else {
  console.log(`交易失败: ${result.error}`);
}
```

### 4. NEAR包装服务
```typescript
import NearWrapService from './near-utils/wrap-service';

const wrapService = new NearWrapService(
  'your-account.near',
  'your-private-key',
  'mainnet'
);

await wrapService.initialize();

// 包装1 NEAR为wNEAR
const result = await wrapService.wrapNear('1');

if (result.success) {
  console.log(`包装成功: ${result.txHash}`);
} else {
  console.log(`包装失败: ${result.error}`);
}

// 自动检查并包装
const autoResult = await wrapService.checkAndWrapNear('5'); // 需要5 wNEAR
if (autoResult.success && autoResult.wrapped) {
  console.log(`自动包装了 ${autoResult.amount} NEAR`);
}
```

### 5. 自动余额管理
```typescript
import { AutoBalanceManager, type AutoBalanceConfig } from './near-utils/auto-balance-manager';

// 配置自动余额管理
const autoBalanceConfig: AutoBalanceConfig = {
  enabled: true,
  checkInterval: 30 * 60 * 1000,  // 30分钟检查一次
  minNearBalance: 1.0,            // 最小NEAR余额阈值
  unwrapAmount: 1.0,              // 自动解包数量
  reserveAmount: 0.5              // 预留wNEAR数量（保留用于交易）
};

// 创建自动余额管理器
const autoBalanceManager = new AutoBalanceManager(
  'your-account.near',
  'your-private-key',
  autoBalanceConfig,
  () => isExecutingTrade,  // 交易状态检查函数
  'mainnet'
);

await autoBalanceManager.initialize();

// 启动自动余额管理
autoBalanceManager.start();

// 手动触发余额检查
await autoBalanceManager.manualCheck();

// 获取状态信息
const status = autoBalanceManager.getStatus();
console.log('余额管理状态:', status);

// 停止自动余额管理
autoBalanceManager.stop();
```

## 🔧 高级功能

### 1. 批量报价
```typescript
const quotes = await VeaxQuoteService.getBatchQuotes([
  {
    tokenA: 'wrap.near',
    tokenB: 'usdt.tether-token.near',
    amountA: '1000000000000000000000000'
  },
  {
    tokenA: 'wrap.near',
    tokenB: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    amountA: '1000000000000000000000000'
  }
]);

quotes.forEach((quote, index) => {
  if (quote.success) {
    console.log(`报价${index + 1}: ${quote.outputAmount}`);
  }
});
```

### 2. 价格比较
```typescript
import { SimpleSwapBot } from './examples/simple-swap';

const bot = new SimpleSwapBot(accountId, privateKey);
await bot.initialize();

// 比较VEAX和REF的价格
await bot.comparePrices('1'); // 1 NEAR
```

### 3. 精确金额转换
```typescript
import { parseNearAmount } from 'near-api-js/lib/utils/format';
import NearWrapService from './near-utils/wrap-service';

// NEAR代币使用官方转换
const nearWei = parseNearAmount('1.5'); // 1.5 NEAR

// 其他代币使用精确字符串计算
const usdtWei = NearWrapService.toWei('100.123456', 6); // 100.123456 USDT

// 从wei转换回人类可读格式
const readable = NearWrapService.fromWei(usdtWei, 6);
```

## 🛡️ 错误处理

### 1. 交易错误检测
```typescript
// REF执行服务会自动检测以下错误：
// - E22: REF合约存款不足
// - E76: 滑点过大
// - 其他合约执行错误

const result = await execution.executeSwap(...);
if (!result.success) {
  switch (result.error) {
    case 'REF合约存款不足 (E22)':
      console.log('需要增加存款或减少交易金额');
      break;
    case 'REF交易滑点过大 (E76)':
      console.log('需要增加滑点容忍度');
      break;
    default:
      console.log(`其他错误: ${result.error}`);
  }
}
```

### 2. 网络错误处理
```typescript
try {
  const quote = await VeaxQuoteService.getQuote(...);
} catch (error) {
  if (error.message.includes('timeout')) {
    console.log('网络超时，请重试');
  } else if (error.message.includes('503')) {
    console.log('服务暂时不可用');
  } else {
    console.log(`未知错误: ${error.message}`);
  }
}
```

## 📊 最佳实践

### 1. 滑点设置
```typescript
// 根据价格影响动态设置滑点
const suggestedSlippage = VeaxQuoteService.getSuggestedSlippage(quote.priceImpact);
```

### 2. 余额检查
```typescript
// 执行交易前检查余额
const balanceInfo = await wrapService.getBalanceInfo();
console.log(`可用余额: ${balanceInfo.totalBalance} NEAR`);
```

### 3. 池子存在性检查
```typescript
// 检查交易对是否有流动性
const poolExists = await VeaxQuoteService.checkPoolExists(tokenA, tokenB);
if (!poolExists) {
  console.log('该交易对没有流动性池');
}
```

## 🔧 精度修复特性

### 关键修复
本SDK包含重要的精度修复，解决了DeFi交易中的精度损失问题：

```typescript
// ✅ 正确：使用wei格式传递精确数量
const result1 = await veaxExecution.executeSwap(...);
const result2 = await refExecution.executeSwap(
  quote,
  tokenIn,
  result1.outputAmountWei, // 使用wei格式，避免精度损失
  minOutputWei
);

// ❌ 错误：转换导致精度损失
const humanReadable = fromWei(result1.outputAmountWei, decimals);
const backToWei = toWei(humanReadable, decimals); // 可能有精度损失
```

### 精度保证
- ✅ 使用`parseNearAmount()`进行NEAR精确转换
- ✅ 交易结果同时返回wei和人类可读格式
- ✅ 套利链中直接传递wei格式
- ✅ 避免"余额不足"错误

## 🔐 安全注意事项

1. **私钥安全**: 永远不要在代码中硬编码私钥
2. **环境变量**: 使用.env文件管理敏感信息
3. **滑点设置**: 根据市场条件调整滑点容忍度
4. **错误处理**: 始终检查交易结果和错误信息
5. **测试网测试**: 在主网使用前先在测试网验证
6. **精度处理**: 优先使用wei格式进行链式交易

## 📚 更多示例

查看 `examples/` 目录获取更多使用示例：
- `simple-swap.ts` - 基础交易示例
- `price-monitor.ts` - 价格监控示例
- `arbitrage-basic.ts` - 基础套利示例

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个SDK！
