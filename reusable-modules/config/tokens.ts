/**
 * NEAR生态代币定义和精确转换工具
 *
 * 功能：管理代币定义和提供精确的数值转换
 * 职责：
 * - 定义所有支持的代币信息（ID、名称、符号、精度）
 * - 提供精确的wei格式转换工具
 * - 避免JavaScript浮点数精度问题
 * - 支持代币数量的格式化和解析
 *
 * 核心工具：
 * - toWei(): 人类可读格式 → wei格式（字符串精确计算）
 * - fromWei(): wei格式 → 人类可读格式
 * - formatTokenAmount(): 格式化代币数量显示
 * - parseTokenAmount(): 解析代币数量输入
 *
 * 精度处理：
 * - 使用字符串操作避免浮点数误差
 * - 支持任意精度的代币（6位、18位、24位等）
 * - 确保wei格式转换的完全精确性
 *
 * 支持代币：
 * - NEAR/wNEAR (24位精度)
 * - USDT (6位精度)
 * - USDC (6位精度)
 * - 其他主流DeFi代币和meme代币
 */

import { TokenMetadata } from '../types';

/**
 * 常用代币定义
 */
export const TOKENS: Record<string, TokenMetadata> = {
  // 主流代币
  NEAR: {
    id: 'wrap.near',
    symbol: 'NEAR',
    name: 'NEAR Protocol',
    decimals: 24,
    icon: 'https://assets.coingecko.com/coins/images/10365/small/near.jpg'
  },
  
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png'
  },

  USDC_e: {
    id: 'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near',
    symbol: 'USDC.e',
    name: 'USDC.e (Ethereum Bridge)',
    decimals: 6,
  },
  
  USDT: {
    id: 'usdt.tether-token.near',
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png'
  },
  
  USDT_e: {
    id: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
    symbol: 'USDT.e',
    name: 'USDT.e (Ethereum Bridge)',
    decimals: 6,
  },

  WBTC: {
    id: '2260fac5e5542a773aa44fbcfedf7c193bc2c599.factory.bridge.near',
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    decimals: 8,
    icon: 'https://assets.coingecko.com/coins/images/7598/small/wrapped_bitcoin_wbtc.png'
  },

  AURORA: {
    id: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
    symbol: 'AURORA',
    name: 'Aurora',
    decimals: 18,
  },

  REF: {
    id: 'token.v2.ref-finance.near',
    symbol: 'REF',
    name: 'Ref Finance Token',
    decimals: 18,
  },

  // DeFi代币
  nBTC: {
    id: 'nbtc.bridge.near',
    symbol: 'nBTC',
    name: 'NEAR BTC',
    decimals: 8,
  },

  // Meme代币
  BLACKDRAGON: {
    id: 'blackdragon.tkn.near',
    symbol: 'BLACKDRAGON',
    name: 'BLACKDRAGON',
    decimals: 24,
  },

  SHITZU: {
    id: 'token.0xshitzu.near',
    symbol: 'SHITZU',
    name: 'SHITZU',
    decimals: 18,
  },

  PURGE: {
    id: 'purge-558.meme-cooking.near',
    symbol: 'PURGE',
    name: 'PURGE',
    decimals: 18,
  },
  
  GEAR: {
    id: 'gear.enleap.near',
    symbol: 'GEAR',
    name: 'GEAR',
    decimals: 18,
  },

  DOGSHIT: {
    id: 'dogshit-1408.meme-cooking.near',
    symbol: 'DOGSHIT',
    name: 'DOGSHIT',
    decimals: 18,
  },

  LONK: {
    id: 'token.lonkingnearbackto2024.near',
    symbol: 'LONK',
    name: 'LONK',
    decimals: 8,
  },

  NEKO: {
    id: 'ftv2.nekotoken.near',
    symbol: 'NEKO',
    name: 'NEKO',
    decimals: 24,
  },

  mpDAO: {
    id: 'mpdao-token.near',
    symbol: 'mpDAO',
    name: 'mpDAO',
    decimals: 6,
  },
};

/**
 * 主流交易对代币
 */
export const MAJOR_TOKENS = {
  NEAR: TOKENS.NEAR,
  USDC: TOKENS.USDC,
  USDT: TOKENS.USDT,
  WBTC: TOKENS.WBTC,
  AURORA: TOKENS.AURORA
};

/**
 * 稳定币
 */
export const STABLECOINS = {
  USDC: TOKENS.USDC,
  USDC_e: TOKENS.USDC_e,
  USDT: TOKENS.USDT,
  USDT_e: TOKENS.USDT_e
};

/**
 * Meme代币
 */
export const MEME_TOKENS = {
  BLACKDRAGON: TOKENS.BLACKDRAGON,
  SHITZU: TOKENS.SHITZU,
  PURGE: TOKENS.PURGE,
  DOGSHIT: TOKENS.DOGSHIT,
  LONK: TOKENS.LONK,
  NEKO: TOKENS.NEKO
};

/**
 * 根据代币ID获取代币信息
 */
export function getTokenById(tokenId: string): TokenMetadata | undefined {
  return Object.values(TOKENS).find(token => token.id === tokenId);
}

/**
 * 根据代币符号获取代币信息
 */
export function getTokenBySymbol(symbol: string): TokenMetadata | undefined {
  return TOKENS[symbol.toUpperCase()];
}

/**
 * 检查是否为稳定币
 */
export function isStablecoin(tokenId: string): boolean {
  return Object.values(STABLECOINS).some(token => token.id === tokenId);
}

/**
 * 检查是否为主流代币
 */
export function isMajorToken(tokenId: string): boolean {
  return Object.values(MAJOR_TOKENS).some(token => token.id === tokenId);
}

/**
 * 获取所有代币列表
 */
export function getAllTokens(): TokenMetadata[] {
  return Object.values(TOKENS);
}

/**
 * 创建自定义代币
 */
export function createToken(
  id: string,
  symbol: string,
  name: string,
  decimals: number,
  icon?: string
): TokenMetadata {
  return {
    id,
    symbol: symbol.toUpperCase(),
    name,
    decimals,
    icon
  };
}

/**
 * 验证代币元数据
 */
export function validateToken(token: TokenMetadata): boolean {
  return !!(
    token.id &&
    token.symbol &&
    token.name &&
    typeof token.decimals === 'number' &&
    token.decimals >= 0 &&
    token.decimals <= 24
  );
}

/**
 * 格式化代币显示名称
 */
export function formatTokenDisplay(token: TokenMetadata): string {
  return `${token.symbol} (${token.name})`;
}

/**
 * 导出所有代币相关功能
 */
export {
  TOKENS as default,
  TOKENS,
  MAJOR_TOKENS,
  STABLECOINS,
  MEME_TOKENS
};
