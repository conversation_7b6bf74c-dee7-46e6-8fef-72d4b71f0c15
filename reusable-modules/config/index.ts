/**
 * NEAR DeFi SDK 配置模块
 *
 * 功能：管理所有系统配置和网络参数
 * 职责：
 * - 提供不同网络（主网/测试网）的配置
 * - 管理合约地址和API端点
 * - 定义Gas费用和存款参数
 * - 提供环境变量配置获取
 *
 * 核心配置：
 * - NEAR网络配置（RPC、钱包、助手URL）
 * - Gas费用配置（包装、解包、交易等操作）
 * - 存款配置（ft_transfer_call、存储存款等）
 * - 合约地址配置（REF、VEAX等）
 *
 * 使用场景：
 * - 初始化NEAR连接时获取网络配置
 * - 执行交易时获取Gas和存款参数
 * - 多环境部署时的配置管理
 */

import { RefConfig } from '../types';

/**
 * REF Finance 主网配置
 */
export const REF_CONFIG: RefConfig = {
  networkId: 'mainnet',
  rpcUrl: process.env.NEAR_RPC_URL || 'https://free.rpc.fastnear.com',
  smartRouterUrl: process.env.REF_SMART_ROUTER_URL || 'https://smartrouter.ref.finance',
  contracts: {
    v1: process.env.REF_V1_CONTRACT || 'v2.ref-finance.near',
    dclv2: process.env.REF_DCLV2_CONTRACT || 'dclv2.ref-labs.near'
  }
};

/**
 * REF Finance 测试网配置
 */
export const REF_CONFIG_TESTNET: RefConfig = {
  networkId: 'testnet',
  rpcUrl: process.env.NEAR_RPC_URL || 'https://test.rpc.fastnear.com',
  smartRouterUrl: 'https://smartroutertest.refburrow.top',
  contracts: {
    v1: 'ref-finance-101.testnet',
    dclv2: 'dclv2.ref-dev.testnet'
  }
};

/**
 * VEAX配置
 */
export const VEAX_CONFIG = {
  apiUrl: 'https://veax-estimation-service.veax.com/v1/rpc',
  contractId: 'veax.near',
  requestTimeout: 10000 // 10秒超时
};

/**
 * NEAR网络配置
 */
export const NEAR_CONFIG = {
  mainnet: {
    networkId: 'mainnet',
    nodeUrl: process.env.NEAR_RPC_URL || 'https://free.rpc.fastnear.com',
    walletUrl: 'https://wallet.mainnet.near.org',
    helperUrl: 'https://helper.mainnet.near.org',
    wrapContract: 'wrap.near'
  },
  testnet: {
    networkId: 'testnet',
    nodeUrl: process.env.NEAR_RPC_URL || 'https://test.rpc.fastnear.com',
    walletUrl: 'https://wallet.testnet.near.org',
    helperUrl: 'https://helper.testnet.near.org',
    wrapContract: 'wrap.testnet'
  }
};

/**
 * DCL v2 费用等级
 */
export const DCL_V2_FEE_LEVELS = [100, 400, 2000, 10000]; // 0.01%, 0.04%, 0.2%, 1%

/**
 * 默认参数
 */
export const DEFAULT_SLIPPAGE = 0.005; // 0.5%
export const DEFAULT_PATH_DEEP = 3;    // 最大路径深度
export const REQUEST_TIMEOUT = 30000;  // 30秒超时

/**
 * Gas配置
 */
export const GAS_CONFIG = {
  // REF Finance
  refSwap: BigInt('300000000000000'),      // 300 TGas
  refDeposit: BigInt('30000000000000'),    // 30 TGas
  
  // VEAX
  veaxSwap: BigInt('300000000000000'),     // 300 TGas
  veaxRegister: BigInt('30000000000000'),  // 30 TGas
  
  // NEAR包装
  nearWrap: BigInt('30000000000000'),      // 30 TGas
  nearUnwrap: BigInt('30000000000000'),    // 30 TGas
};

/**
 * 存款配置
 */
export const DEPOSIT_CONFIG = {
  // REF Finance
  refFtTransfer: BigInt('1'),                        // 1 yoctoNEAR
  
  // VEAX
  veaxFtTransfer: BigInt('1'),                       // 1 yoctoNEAR
  veaxStorageDeposit: BigInt('1250000000000000000000000'), // 0.00125 NEAR
  
  // NEAR包装
  nearWrapDeposit: BigInt('1'),                      // 1 yoctoNEAR
};

/**
 * 主要的DCL v2池子（高流动性）
 */
export const MAIN_DCL_POOLS = {
  'USDC-NEAR': '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',
  'USDT-NEAR': 'usdt.tether-token.near|wrap.near|100'
};

/**
 * 获取配置
 */
export function getConfig(network: 'mainnet' | 'testnet' = 'mainnet'): RefConfig {
  return network === 'testnet' ? REF_CONFIG_TESTNET : REF_CONFIG;
}

/**
 * 获取NEAR配置
 */
export function getNearConfig(network: 'mainnet' | 'testnet' = 'mainnet') {
  return NEAR_CONFIG[network];
}

/**
 * 获取RPC URL
 */
export function getRpcUrl(network: 'mainnet' | 'testnet' = 'mainnet'): string {
  return process.env.NEAR_RPC_URL || (network === 'testnet'
    ? 'https://test.rpc.fastnear.com'
    : 'https://free.rpc.fastnear.com');
}

/**
 * 获取合约地址
 */
export function getContractAddresses(network: 'mainnet' | 'testnet' = 'mainnet') {
  const config = getConfig(network);
  return {
    refV1: config.contracts.v1,
    refDclV2: config.contracts.dclv2,
    veax: 'veax.near',
    wrapNear: network === 'testnet' ? 'wrap.testnet' : 'wrap.near'
  };
}

/**
 * 验证网络配置
 */
export function validateNetworkConfig(network: string): network is 'mainnet' | 'testnet' {
  return network === 'mainnet' || network === 'testnet';
}

/**
 * 获取环境变量配置
 */
export function getEnvConfig() {
  return {
    accountId: process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID,
    privateKey: process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY,
    network: (process.env.NEAR_NETWORK || 'mainnet') as 'mainnet' | 'testnet',
    rpcUrl: process.env.NEAR_RPC_URL,
    refSmartRouterUrl: process.env.REF_SMART_ROUTER_URL
  };
}

/**
 * 导出所有配置
 */
export {
  REF_CONFIG,
  REF_CONFIG_TESTNET,
  VEAX_CONFIG,
  NEAR_CONFIG,
  DCL_V2_FEE_LEVELS,
  DEFAULT_SLIPPAGE,
  DEFAULT_PATH_DEEP,
  REQUEST_TIMEOUT,
  GAS_CONFIG,
  DEPOSIT_CONFIG,
  MAIN_DCL_POOLS
};
