# NEAR DeFi SDK 架构与功能说明

## 📁 项目结构概览

```
reusable-modules/
├── README.md                           # 项目介绍和快速开始
├── USAGE.md                           # 详细使用指南和示例
├── TROUBLESHOOTING.md                 # 问题解决和精度修复指南
├── ARCHITECTURE.md                    # 本文档：架构和功能详细说明
├── package.json                       # NPM包配置和依赖管理
├── tsconfig.json                      # TypeScript编译配置
├── index.ts                          # 主入口文件，导出所有公共API
├── types/
│   └── index.ts                      # 完整的TypeScript类型定义系统
├── config/
│   ├── index.ts                      # 网络配置、合约地址、Gas配置
│   └── tokens.ts                     # 代币定义和精确转换工具
├── near-utils/
│   ├── wrap-service.ts               # NEAR包装服务（NEAR ↔ wNEAR）
│   ├── auto-balance-manager.ts       # 自动余额管理服务
│   └── dynamic-amount-manager.ts     # 三档位动态金额管理服务
├── veax/
│   ├── quote-service.ts              # VEAX DEX报价查询服务
│   └── execution-service.ts          # VEAX DEX交易执行服务
├── ref-finance/
│   ├── v1-router.ts                  # REF Finance V1智能路由服务
│   └── execution-service.ts          # REF Finance交易执行服务
└── examples/
    └── simple-swap.ts                # 基础使用示例和演示代码
```

## 🏗️ 核心架构设计

### 设计原则
1. **模块化** - 每个功能独立封装，可单独使用
2. **类型安全** - 完整的TypeScript类型定义
3. **精度优先** - 解决DeFi交易中的精度损失问题
4. **错误处理** - 完善的错误检测和处理机制
5. **向后兼容** - 保持API的向后兼容性

### 分层架构
```
┌─────────────────────────────────────┐
│           应用层 (Examples)          │  ← 使用示例和演示
├─────────────────────────────────────┤
│           服务层 (Services)          │  ← 业务逻辑和交易执行
├─────────────────────────────────────┤
│           工具层 (Utils)             │  ← 通用工具和辅助功能
├─────────────────────────────────────┤
│           配置层 (Config)            │  ← 配置管理和常量定义
├─────────────────────────────────────┤
│           类型层 (Types)             │  ← TypeScript类型定义
└─────────────────────────────────────┘
```

## 📦 模块功能详细说明

### 🔧 types/ - 类型定义系统

#### `types/index.ts`
**功能**: 完整的TypeScript类型定义系统
**职责**:
- 定义所有接口和类型
- 确保类型安全
- 提供智能代码提示

**核心类型**:
```typescript
// 代币元数据定义
TokenMetadata: 代币基本信息（ID、名称、符号、精度）

// 交易结果（修复精度问题）
TransactionResult: 交易执行结果，包含wei和人类可读两种格式

// VEAX相关类型
VeaxSwapRequest: VEAX API请求格式
VeaxSwapResponse: VEAX API响应格式
VeaxQuoteResult: VEAX报价结果
VeaxTransactionResult: VEAX交易结果

// REF Finance相关类型
SmartRouterResponse: REF智能路由API响应
QuoteParams: 报价查询参数
QuoteResult: 统一报价结果格式
V1SwapAction: REF V1交易动作定义
DCLv2SwapParams: REF DCL v2交易参数

// 自动余额管理
AutoBalanceConfig: 自动余额管理配置
```

### ⚙️ config/ - 配置管理系统

#### `config/index.ts`
**功能**: 网络配置和系统参数管理
**职责**:
- 管理不同网络的配置
- 定义合约地址
- 配置Gas费用和存款参数

**核心配置**:
```typescript
// 网络配置
getNearConfig(): 获取NEAR网络配置（主网/测试网）

// Gas配置
GAS_CONFIG: {
  nearWrap: NEAR包装操作的Gas费用
  nearUnwrap: NEAR解包操作的Gas费用
  refSwap: REF交易的Gas费用
  veaxSwap: VEAX交易的Gas费用
  veaxRegister: VEAX注册的Gas费用
}

// 存款配置
DEPOSIT_CONFIG: {
  nearWrapDeposit: NEAR包装所需存款
  refFtTransfer: REF ft_transfer_call所需存款
  veaxStorageDeposit: VEAX存储存款
  veaxFtTransfer: VEAX ft_transfer_call所需存款
}
```

#### `config/tokens.ts`
**功能**: 代币定义和精确转换工具
**职责**:
- 定义支持的代币信息
- 提供精确的数值转换工具
- 避免浮点数精度问题

**核心功能**:
```typescript
// 代币定义
TOKENS: {
  NEAR: { id: 'wrap.near', decimals: 24, ... }
  USDT: { id: 'usdt.tether-token.near', decimals: 6, ... }
  USDC: { id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1', decimals: 6, ... }
  // ... 更多代币
}

// 精确转换工具
toWei(): 人类可读格式 → wei格式（字符串精确计算）
fromWei(): wei格式 → 人类可读格式
formatTokenAmount(): 格式化代币数量显示
parseTokenAmount(): 解析代币数量输入
```

### 🛠️ near-utils/ - NEAR工具集

#### `near-utils/wrap-service.ts`
**功能**: NEAR与wNEAR之间的转换服务
**职责**:
- 处理NEAR → wNEAR包装
- 处理wNEAR → NEAR解包
- 自动余额检查和管理
- 精确数量转换

**核心方法**:
```typescript
initialize(): 初始化NEAR连接
getNearBalance(): 获取NEAR余额
getWNearBalance(): 获取wNEAR余额
wrapNear(amount): 包装NEAR为wNEAR
unwrapNear(amount): 解包wNEAR为NEAR
checkAndWrapNear(required, buffer): 检查并自动包装
getBalanceInfo(): 获取完整余额信息
```

#### `near-utils/auto-balance-manager.ts`
**功能**: 自动余额管理服务
**职责**:
- 定期检查NEAR余额
- 自动解包wNEAR补充NEAR
- 避免交易期间的余额操作
- 保留足够的wNEAR用于交易

**核心方法**:
```typescript
initialize(): 初始化服务
start(): 启动自动余额管理
stop(): 停止自动余额管理
performBalanceCheck(): 执行余额检查
checkAndManageBalance(): 检查并管理余额
manualCheck(): 手动触发余额检查
getStatus(): 获取服务状态
```

**重要概念**:
- `minNearBalance`: 最小NEAR余额阈值（用于支付gas费用）
- `unwrapAmount`: 自动解包的wNEAR数量
- `reserveAmount`: 预留的wNEAR数量（保留用于DeFi交易，不是gas费用）

### 🔄 veax/ - VEAX DEX集成

#### `veax/quote-service.ts`
**功能**: VEAX DEX报价查询服务
**职责**:
- 调用VEAX估价API
- 处理报价请求和响应
- 批量报价查询
- 错误处理和重试

**核心方法**:
```typescript
getQuote(tokenA, tokenB, amountA, slippage): 获取单个报价
getBatchQuotes(quotes): 批量获取报价
checkPoolExists(tokenA, tokenB): 检查池子是否存在
getSuggestedSlippage(priceImpact): 获取建议滑点
formatQuoteResult(result, decimals): 格式化报价结果
validateQuoteParams(params): 验证报价参数
```

**API端点**: `https://veax-estimation-service.veax.com/v1/rpc`

#### `veax/execution-service.ts`
**功能**: VEAX DEX交易执行服务
**职责**:
- 执行VEAX交易
- 用户和代币注册
- 交易结果解析
- 提取实际输出金额（wei格式）

**核心方法**:
```typescript
initialize(): 初始化NEAR连接
checkUserRegistration(): 检查用户注册状态
registerUser(): 注册用户到VEAX
checkTokenRegistration(tokenId): 检查代币注册状态
registerToken(tokenId): 注册代币
executeSwap(tokenIn, tokenOut, amountIn, minAmountOut): 执行交易
getAccountBalance(): 获取账户余额
```

**交易流程**:
1. 检查用户注册状态
2. 检查代币注册状态
3. 构建交易消息
4. 执行ft_transfer_call
5. 解析交易结果，提取实际输出金额

### 💱 ref-finance/ - REF Finance集成

#### `ref-finance/v1-router.ts`
**功能**: REF Finance V1智能路由服务
**职责**:
- 调用REF智能路由API
- 路径优化和选择
- 价格影响计算
- 路径详情分析

**核心方法**:
```typescript
getV1Quote(params): 获取V1智能路由报价
buildSmartRouterUrl(params): 构建API URL
getRouteDetails(result): 获取路径详情
calculatePriceImpact(input, output, market): 计算价格影响
validateParams(params): 验证参数
getSupportedPairs(): 获取支持的交易对
getBestPathSuggestion(routes): 获取最佳路径建议
formatRouteInfo(route): 格式化路径信息
```

**API端点**: REF Finance智能路由API

#### `ref-finance/execution-service.ts`
**功能**: REF Finance交易执行服务（包含精度修复）
**职责**:
- 执行REF V1和DCL v2交易
- 构建正确的交易参数
- 解决金额不匹配问题
- 提取实际输出金额
- 错误检测和处理

**核心方法**:
```typescript
initialize(): 初始化NEAR连接
executeV1Swap(quote, tokenId, amount, minOutput): 执行V1交易
executeDCLv2Swap(quote, tokenId, amount, minOutput, poolId, outputToken): 执行DCL v2交易
buildV1SwapActions(routeData, minOutput, actualInput): 构建V1交易动作
extractOutputAmountFromResult(result): 提取实际输出金额
checkTransactionSuccess(result): 检查交易是否真正成功
convertToWei(amount, tokenId, decimals): 精确转换为wei格式
executeSwap(quote, tokenId, amount, minOutput, slippage, params): 统一执行接口
```

**关键修复**:
- **Smart Router金额分配修复**: 正确使用Smart Router为每个路径分配的金额
- **精度修复**: 返回wei格式的实际输出金额
- **错误检测**: 智能检测E22、E76等合约错误

### 📚 examples/ - 使用示例

#### `examples/simple-swap.ts`
**功能**: 基础使用示例和演示代码
**职责**:
- 演示SDK的基本用法
- 提供完整的交易流程示例
- 展示最佳实践
- 作为学习和测试的起点

**示例功能**:
```typescript
SimpleSwapBot: 简单交易机器人类
- swapNearToUsdtOnVeax(): VEAX交易示例
- swapNearToUsdtOnRef(): REF交易示例
- comparePrices(): 价格比较示例
- showBalances(): 余额显示示例
```

## 🔗 模块间依赖关系

```
examples/simple-swap.ts
    ↓ 依赖
┌─────────────────────────────────────┐
│ veax/quote-service.ts               │
│ veax/execution-service.ts           │
│ ref-finance/v1-router.ts            │
│ ref-finance/execution-service.ts    │
│ near-utils/wrap-service.ts          │
│ near-utils/auto-balance-manager.ts  │
└─────────────────────────────────────┘
    ↓ 依赖
┌─────────────────────────────────────┐
│ config/index.ts                     │
│ config/tokens.ts                    │
│ types/index.ts                      │
└─────────────────────────────────────┘
```

## 🎯 关键技术特性

### 1. 精度修复系统
- **问题**: DeFi交易中的精度损失导致交易失败
- **解决**: 直接传递wei格式，避免中间转换
- **实现**: 所有交易结果同时返回wei和人类可读格式

### 2. Smart Router使用方式修复
- **问题**: 完全误解Smart Router机制，自己构建actions而不是直接使用
- **发现**: Smart Router返回的pools就是完整的actions，包含所有必要字段
- **解决**: 直接使用Smart Router返回的pools作为actions，无需任何自定义逻辑
- **实现**: 修改`buildV1SwapActions`方法，使用`routes.flatMap(route => route.pools)`

### 3. Big.js Invalid number错误修复
- **问题**: REF Smart Router和DCL v2使用Big.js处理未验证的输入，导致Invalid number错误
- **发现**: null、undefined、空字符串等异常输入直接传给Big.js会抛出错误
- **解决**: 添加严格的数值验证，替换Big.js为NEAR官方方法和精确字符串操作
- **实现**: 修改`toNonDivisibleNumber`和`toReadableNumber`方法，添加`isValidNumber`验证

### 4. VEAX和REF API格式兼容性确认
- **问题**: 误解VEAX返回格式，导致套利监控中的精度损失和格式不匹配
- **发现**: VEAX和REF都返回人类可读格式，完全兼容
- **解决**: 直接传递API返回值，无需格式转换
- **实现**: 修改套利监控逻辑，移除不必要的格式转换

### 5. wei格式精度损失修复
- **问题**: 在wei格式输入转换时使用parseFloat导致精度损失，REF Smart Router的amount_in字段不准确
- **发现**: JavaScript浮点数精度限制导致大数值精度丢失
- **解决**: 使用精确的fromWei方法替换parseFloat和Math.pow运算
- **实现**: 修改executeREFTrade和executeVEAXTrade中的wei格式处理逻辑

### 3. 自动余额管理
- **功能**: 定期检查NEAR余额，自动解包wNEAR
- **安全**: 避免交易期间的余额操作
- **智能**: 保留足够的wNEAR用于DeFi交易

### 4. 错误检测系统
- **FunctionCallError检测**: 智能识别合约执行错误
- **E22错误**: REF合约存款不足
- **E76错误**: REF交易滑点过大
- **网络错误**: 超时、503等网络问题

## 🚀 使用流程

### 基本使用流程
1. **导入模块**: 按需导入所需的服务
2. **初始化服务**: 调用`initialize()`方法
3. **获取报价**: 使用报价服务获取最佳价格
4. **执行交易**: 使用执行服务进行交易
5. **处理结果**: 检查交易结果和错误

### 高级使用流程
1. **自动余额管理**: 启动自动余额管理服务
2. **批量报价**: 使用批量报价功能比较价格
3. **链式交易**: 使用wei格式进行精确的链式交易
4. **错误恢复**: 实现完整的错误处理和恢复机制

## 📈 性能优化

### 1. 缓存机制
- 代币信息缓存
- 网络配置缓存
- 报价结果缓存（短期）

### 2. 批量操作
- 批量报价查询
- 批量余额检查
- 批量交易执行

### 3. 错误处理
- 智能重试机制
- 降级策略
- 超时控制

## 🔒 安全考虑

### 1. 私钥安全
- 使用环境变量管理私钥
- 内存中临时存储
- 不在日志中输出敏感信息

### 2. 交易安全
- 滑点保护
- 最小输出金额验证
- 交易状态检查

### 3. 余额安全
- 预留机制防止余额耗尽
- 交易期间的余额保护
- 自动余额监控

## 🎯 三档位动态金额系统 (新增)

### 设计目标
基于实际交易利润动态调整交易金额，最大化资金利用率和套利收益。

### 核心特性
- **三档位设计**: 低档(基础)、中档(2-3倍)、高档(3-5倍)
- **基于实际利润**: 根据真实交易结果调整，而非预测
- **渐进式调整**: 避免激进变化，稳定运行
- **简单配置**: 每个币种只需配置3个金额

### 工作流程
```
1. 监控阶段: 用当前档位金额查询套利机会
2. 发现机会: 直接用查询时的金额执行交易
3. 执行完成: 根据实际利润调整下次查询金额
4. 循环监控: 用新的档位金额继续查询
```

### 档位切换规则
- **升到高档**: 实际利润 >= 0.1 NEAR
- **升到中档**: 实际利润 >= 0.052 NEAR
- **保持低档**: 实际利润 < 0.052 NEAR

### 配置示例
```typescript
tradeAmounts: {
  low: "3",      // 基础金额
  medium: "8",   // 中档金额
  high: "15"     // 高档金额
}
```

### 预期效果
- **资金利用率提升**: 根据市场条件自动调整规模
- **风险控制**: 低利润时使用小金额，高利润时放大规模
- **自适应性**: 无需人工干预，自动优化交易策略

这个架构文档提供了完整的功能说明，确保概念准确性，便于后续的代码维护和扩展。
