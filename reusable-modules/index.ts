/**
 * NEAR DeFi SDK - 主入口文件
 *
 * 功能：统一导出所有公共API，提供便捷的导入方式
 * 职责：
 * - 导出所有核心服务和工具类
 * - 提供快速开始的便捷导出
 * - 维护API的向后兼容性
 * - 提供版本信息
 *
 * 使用方式：
 * - 完整导入：import SDK from 'near-defi-sdk'
 * - 按需导入：import { VEAX, REF } from 'near-defi-sdk'
 * - 具体导入：import VeaxQuoteService from 'near-defi-sdk/veax/quote'
 */

// ==================== 类型定义 ====================
export * from './types';

// ==================== 配置模块 ====================
export * from './config';
export * from './config/tokens';

// ==================== NEAR工具 ====================
export { default as NearWrapService } from './near-utils/wrap-service';
export { AutoBalanceManager, type AutoBalanceConfig } from './near-utils/auto-balance-manager';

// ==================== VEAX模块 ====================
export { default as VeaxQuoteService } from './veax/quote-service';
export { default as VeaxExecutionService } from './veax/execution-service';

// ==================== REF Finance模块 ====================
export { V1SmartRouterService, v1SmartRouter } from './ref-finance/v1-router';
export { default as RefExecutionService } from './ref-finance/execution-service';
export {
  RefQuoteService,
  refQuoteService,
  V1SmartRouterService as RefV1Router,
  DCLv2ContractService as RefDCLv2Contract
} from './ref-finance/quote-service';

// 导入用于快速开始导出
import VeaxQuoteService from './veax/quote-service';
import VeaxExecutionService from './veax/execution-service';
import { V1SmartRouterService } from './ref-finance/v1-router';
import RefExecutionService from './ref-finance/execution-service';
import { RefQuoteService, refQuoteService } from './ref-finance/quote-service';
import NearWrapService from './near-utils/wrap-service';
import { AutoBalanceManager } from './near-utils/auto-balance-manager';

// ==================== 示例和工具 ====================
export { default as SimpleSwapBot } from './examples/simple-swap';

// 导入用于默认导出
import SimpleSwapBot from './examples/simple-swap';

// ==================== 版本信息 ====================
export const VERSION = '1.0.0';

// ==================== 快速开始导出 ====================

/**
 * 快速开始 - VEAX交易
 */
export const VEAX = {
  QuoteService: VeaxQuoteService,
  ExecutionService: VeaxExecutionService
};

/**
 * 快速开始 - REF Finance交易
 */
export const REF = {
  QuoteService: RefQuoteService,
  quoteService: refQuoteService, // 单例实例
  V1Router: V1SmartRouterService,
  ExecutionService: RefExecutionService
};

/**
 * 快速开始 - NEAR工具
 */
export const NEAR = {
  WrapService: NearWrapService,
  AutoBalanceManager: AutoBalanceManager
};

/**
 * 默认导出 - 主要功能集合
 */
export default {
  VERSION,
  VEAX,
  REF,
  NEAR,
  SimpleSwapBot
};
