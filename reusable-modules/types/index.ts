/**
 * NEAR DeFi SDK 类型定义
 * 
 * 提供REF Finance和VEAX DEX的完整类型系统
 * 支持V1 Smart Router、DCL v2、VEAX等所有主要功能
 */

// ==================== 基础类型 ====================

// 代币元数据
export interface TokenMetadata {
  id: string;
  name: string;
  symbol: string;
  decimals: number;
  icon?: string;
}

// 交易结果接口（修复精度问题）
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  outputAmount?: string;      // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmount?: string;       // 人类可读格式
  inputAmountWei?: string;    // wei格式（精确，新增）
}

// ==================== REF Finance 类型 ====================

// V1 Smart Router API 相关类型
export interface SmartRouterResponse {
  result_code: number;
  result_data: {
    routes: V1Route[];
    amount_out: string;
    contract_out: string;
  };
}

export interface V1Route {
  pools: V1Pool[];
  amount_out: string;
  percent?: number;
}

export interface V1Pool {
  pool_id: number;  // V1 池子使用数字 ID
  token_in: string;
  token_out: string;
  amount_in?: string;
  min_amount_out: string;
}

// V1交易动作接口（按照官方SDK格式）
export interface V1SwapAction {
  pool_id: number;
  token_in: string;
  token_out: string;
  amount_in?: string;
  min_amount_out: string;
}

// DCL v2 合约调用相关类型
export interface DCLv2QuoteParams {
  pool_ids: string[];  // DCL v2 池子 ID 格式：token1|token2|fee
  input_token: string;
  output_token: string;
  input_amount: string;
  tag?: string;
}

export interface DCLv2QuoteResponse {
  amount: string;
  tag?: string;
}

// DCL v2交易参数接口
export interface DCLv2SwapParams {
  pool_ids: string[];
  output_token: string;
  min_output_amount: string;
  skip_unwrap_near?: boolean;
}

// 统一报价结果
export interface QuoteResult {
  system: 'V1' | 'DCL_V2' | 'DCL_V2_LOCAL';
  contractId: string;
  outputAmount: string;
  inputAmount: string;
  priceImpact?: string;
  fee?: string;
  poolId?: string; // 用于DCL v2池子ID
  route?: V1Route | DCLv2QuoteParams;
  rawResponse: any;
}

// 报价查询参数
export interface QuoteParams {
  tokenIn: TokenMetadata;
  tokenOut: TokenMetadata;
  amountIn: string;  // 可读格式，如 "1000"
  slippage?: number; // 滑点容忍度，如 0.005 表示 0.5%
}

// 错误类型
export interface QuoteError {
  system: 'V1' | 'DCL_V2' | 'BOTH';
  message: string;
  originalError?: any;
}

// ==================== VEAX 类型 ====================

// VEAX API请求接口
export interface VeaxSwapRequest {
  jsonrpc: string;
  method: string;
  params: {
    token_a: string;
    token_b: string;
    amount_a: string;
    slippage_tolerance: number;
  };
  id: number;
}

// VEAX API响应接口
export interface VeaxSwapResponse {
  jsonrpc: string;
  id: number;
  result?: {
    amount_b_expected: string;
    amount_b_bound: string;
    price_impact: string;
    swap_price: string;
    fee: string;
    fee_amount: string;
    gas_fee: string;
    pool_exists: boolean;
    storage_cost: {
      init_account: string;
      register_token: string;
      create_pool: string;
      open_position: string;
    };
  };
  error?: {
    code: number;
    message: string;
    data?: {
      details: string;
      request_id: string;
    };
  };
}

// VEAX报价结果接口
export interface VeaxQuoteResult {
  outputAmount: string;
  priceImpact: string;
  fee: string;
  poolExists: boolean;
  success: boolean;
  error?: string;
}

// VEAX交易结果接口（修复精度问题）
export interface VeaxTransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  amountIn?: string;          // 人类可读格式
  amountOut?: string;         // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmountWei?: string;    // wei格式（精确，新增）
}

// VEAX用户注册状态
export interface VeaxUserStatus {
  isRegistered: boolean;
  storageBalance?: {
    total: string;
    available: string;
  };
}

// VEAX代币注册状态
export interface VeaxTokenStatus {
  isRegistered: boolean;
  balance?: string;
}

// ==================== NEAR 工具类型 ====================

// NEAR包装服务结果
export interface NearWrapResult {
  success: boolean;
  txHash?: string;
  error?: string;
}

// NEAR余额信息
export interface NearBalanceInfo {
  nearBalance: string;
  wNearBalance: string;
  totalBalance: string;
}

// ==================== 配置类型 ====================

// NEAR RPC 调用参数
export interface NearRPCParams {
  request_type: 'call_function';
  finality: 'optimistic' | 'final';
  account_id: string;
  method_name: string;
  args_base64: string;
}

// REF配置类型
export interface RefConfig {
  networkId: 'mainnet' | 'testnet';
  rpcUrl: string;
  smartRouterUrl: string;
  contracts: {
    v1: string;      // v2.ref-finance.near
    dclv2: string;   // dclv2.ref-labs.near
  };
}

// ==================== 工具函数类型 ====================

// 精度转换函数类型
export type ToWeiFunction = (amount: string, decimals: number) => string;
export type ToReadableFunction = (amount: string, decimals: number) => string;

// 错误检查函数类型
export type TransactionSuccessChecker = (result: any) => { success: boolean; error?: string };

// ==================== 导出所有类型 ====================

export * from './index';
