/**
 * REF Finance V1 智能路由服务
 *
 * 功能：提供REF Finance V1智能路由的完整报价查询功能
 * 职责：
 * - 调用REF智能路由API获取最优交易路径
 * - 处理多池子路径的路由优化
 * - 计算价格影响和交易成本
 * - 提供路径详情和复杂度分析
 * - 支持滑点设置和路径深度配置
 * - 验证交易参数和代币对支持性
 *
 * API特点：
 * - 自动寻找最优交易路径
 * - 支持多池子跳转交易
 * - 提供详细的路径信息
 * - 计算预期输出和价格影响
 *
 * 核心方法：
 * - getV1Quote(): 获取V1智能路由报价
 * - getRouteDetails(): 获取路径详细信息
 * - calculatePriceImpact(): 计算价格影响
 * - validateParams(): 验证报价参数
 * - getSupportedPairs(): 获取支持的交易对
 * - getBestPathSuggestion(): 获取最佳路径建议
 *
 * 路径分析：
 * - 简单路径：单池子直接交换
 * - 中等路径：2-3个池子的跳转
 * - 复杂路径：3个以上池子的多跳交易
 * - Gas估算：基于路径复杂度的Gas使用量预估
 *
 * 使用场景：
 * - 套利机器人的REF报价获取
 * - DeFi应用的最优路径查询
 * - 交易前的路径分析和成本估算
 * - 多DEX价格比较中的REF部分
 */

import axios from 'axios';
import Big from 'big.js';
import {
  SmartRouterResponse,
  QuoteParams,
  QuoteResult,
  TokenMetadata
} from '../types';
import { getConfig, DEFAULT_SLIPPAGE, DEFAULT_PATH_DEEP, REQUEST_TIMEOUT } from '../config';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';

/**
 * V1 Smart Router API 服务
 */
export class V1SmartRouterService {
  private config = getConfig();

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 将可读金额转换为非可分割单位（使用精确方法）
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ REF Smart Router收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 将非可分割单位转换为可读金额（使用精确方法）
   */
  private toReadableNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ REF Smart Router收到无效wei数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法并移除千分位分隔符
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      // 对于其他精度，使用精确的字符串操作
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 构建 Smart Router API URL
   */
  private buildSmartRouterUrl(params: QuoteParams): string {
    const { tokenIn, tokenOut, amountIn, slippage = DEFAULT_SLIPPAGE } = params;
    
    const amountInNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);
    const slippagePercent = slippage;
    
    const url = new URL(`${this.config.smartRouterUrl}/findPath`);
    url.searchParams.set('amountIn', amountInNonDivisible);
    url.searchParams.set('tokenIn', tokenIn.id);
    url.searchParams.set('tokenOut', tokenOut.id);
    url.searchParams.set('pathDeep', DEFAULT_PATH_DEEP.toString());
    url.searchParams.set('slippage', slippagePercent.toString());
    
    return url.toString();
  }

  /**
   * 调用 V1 Smart Router API
   */
  async getV1Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      const url = this.buildSmartRouterUrl(params);
      
      const response = await axios.get<SmartRouterResponse>(url, {
        timeout: REQUEST_TIMEOUT,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NEAR-DeFi-SDK/1.0'
        }
      });

      const data = response.data;
      
      // 检查响应状态
      if (data.result_code !== 0 || !data.result_data?.routes?.length) {
        console.log('❌ V1 Smart Router 未找到可用路径');
        return null;
      }

      const { result_data } = data;
      const outputAmount = this.toReadableNumber(result_data.amount_out, params.tokenOut.decimals);
      
      return {
        system: 'V1',
        contractId: this.config.contracts.v1,
        outputAmount,
        inputAmount: params.amountIn,
        route: result_data.routes[0], // 使用第一条路径作为主要路径
        rawResponse: data
      };

    } catch (error: any) {
      console.error('❌ V1 Smart Router 调用失败:', error.message);
      
      if (error.code === 'ECONNABORTED') {
        throw new Error('V1 Smart Router API 请求超时');
      }
      
      if (error.response?.status === 404) {
        throw new Error('V1 Smart Router API 未找到');
      }
      
      throw new Error(`V1 Smart Router API 调用失败: ${error.message}`);
    }
  }

  /**
   * 获取路径详情
   */
  getRouteDetails(quoteResult: QuoteResult): string {
    if (quoteResult.system !== 'V1' || !quoteResult.route) {
      return 'N/A';
    }

    const route = quoteResult.route as any;
    if (!route.pools) {
      return 'N/A';
    }

    const pools = route.pools.map((pool: any) => `Pool#${pool.pool_id}`).join(' → ');
    return pools;
  }

  /**
   * 计算价格影响
   */
  calculatePriceImpact(inputAmount: string, outputAmount: string, marketPrice?: string): string {
    if (!marketPrice) return 'N/A';
    
    try {
      const actualPrice = new Big(outputAmount).div(inputAmount);
      const market = new Big(marketPrice);
      const impact = actualPrice.minus(market).div(market).abs().times(100);
      return `${impact.toFixed(4)}%`;
    } catch {
      return 'N/A';
    }
  }

  /**
   * 验证报价参数
   */
  validateParams(params: QuoteParams): boolean {
    if (!params.tokenIn?.id || !params.tokenOut?.id) {
      return false;
    }
    
    if (!params.amountIn || parseFloat(params.amountIn) <= 0) {
      return false;
    }
    
    if (params.tokenIn.id === params.tokenOut.id) {
      return false;
    }
    
    if (params.slippage && (params.slippage < 0 || params.slippage > 1)) {
      return false;
    }

    return true;
  }

  /**
   * 获取支持的代币对
   */
  async getSupportedPairs(): Promise<Array<{tokenIn: string, tokenOut: string}>> {
    // 这里可以实现获取支持的代币对列表
    // 目前返回常用的代币对
    return [
      { tokenIn: 'wrap.near', tokenOut: 'usdt.tether-token.near' },
      { tokenIn: 'wrap.near', tokenOut: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1' },
      // 可以添加更多支持的代币对
    ];
  }

  /**
   * 获取最佳路径建议
   */
  getBestPathSuggestion(routes: any[]): any {
    if (!routes || routes.length === 0) {
      return null;
    }

    // 选择输出金额最大的路径
    return routes.reduce((best, current) => {
      const bestOutput = new Big(best.amount_out || '0');
      const currentOutput = new Big(current.amount_out || '0');
      return currentOutput.gt(bestOutput) ? current : best;
    });
  }

  /**
   * 格式化路径信息
   */
  formatRouteInfo(route: any): {
    pools: string[];
    totalPools: number;
    estimatedGas: string;
    complexity: 'simple' | 'medium' | 'complex';
  } {
    if (!route || !route.pools) {
      return {
        pools: [],
        totalPools: 0,
        estimatedGas: '0',
        complexity: 'simple'
      };
    }

    const pools = route.pools.map((pool: any) => `Pool#${pool.pool_id}`);
    const totalPools = pools.length;
    
    // 估算gas使用量（基于池子数量）
    const baseGas = 50; // TGas
    const gasPerPool = 30; // TGas per pool
    const estimatedGas = `${baseGas + (totalPools * gasPerPool)} TGas`;

    // 判断复杂度
    let complexity: 'simple' | 'medium' | 'complex' = 'simple';
    if (totalPools > 3) {
      complexity = 'complex';
    } else if (totalPools > 1) {
      complexity = 'medium';
    }

    return {
      pools,
      totalPools,
      estimatedGas,
      complexity
    };
  }
}

/**
 * 导出单例实例
 */
export const v1SmartRouter = new V1SmartRouterService();

export default V1SmartRouterService;
