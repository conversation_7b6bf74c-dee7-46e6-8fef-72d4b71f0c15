/**
 * REF Finance 统一报价服务
 *
 * 功能：提供REF Finance的完整报价查询功能
 * 职责：
 * - 整合V1 Smart Router和DCL v2系统
 * - 并行查询并选择最佳报价
 * - 提供完整的错误处理和重试机制
 * - 支持缓存和性能优化
 * - 提供详细的报价比较和分析
 *
 * 核心特性：
 * - 双系统并行报价：V1 Smart Router + DCL v2
 * - 智能报价选择：自动选择输出金额最大的报价
 * - 精度修复：使用NEAR官方方法避免精度损失
 * - 错误处理：完善的错误检测和处理机制
 * - 性能优化：缓存机制和批量查询支持
 *
 * 支持的系统：
 * - V1 Smart Router：支持多跳路径，广泛的流动性覆盖
 * - DCL v2：集中流动性，4个费用等级，精确价格控制
 *
 * 使用场景：
 * - 套利机器人的报价获取
 * - DeFi应用的最优价格查询
 * - 多DEX价格比较
 * - 交易前的路径分析和成本估算
 */

import axios from 'axios';
import Big from 'big.js';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import {
  QuoteParams,
  QuoteResult,
  QuoteError,
  TokenMetadata,
  SmartRouterResponse,
  DCLv2QuoteParams,
  DCLv2QuoteResponse,
  NearRPCParams
} from '../types';
import {
  getConfig,
  DEFAULT_SLIPPAGE,
  DEFAULT_PATH_DEEP,
  REQUEST_TIMEOUT,
  DCL_V2_FEE_LEVELS,
  MAIN_DCL_POOLS
} from '../config';

/**
 * V1 Smart Router 服务类
 */
class V1SmartRouterService {
  private config = getConfig();

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 将可读金额转换为非可分割单位（使用精确方法）
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ REF Smart Router收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 将非可分割单位转换为可读金额（使用精确方法）
   */
  private toReadableNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ REF Smart Router收到无效wei数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法并移除千分位分隔符
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      // 对于其他精度，使用精确的字符串操作
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 构建 Smart Router API URL
   */
  private buildSmartRouterUrl(params: QuoteParams): string {
    const { tokenIn, tokenOut, amountIn, slippage = DEFAULT_SLIPPAGE } = params;

    const amountInNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);
    const slippagePercent = slippage;

    const url = new URL(`${this.config.smartRouterUrl}/findPath`);
    url.searchParams.set('amountIn', amountInNonDivisible);
    url.searchParams.set('tokenIn', tokenIn.id);
    url.searchParams.set('tokenOut', tokenOut.id);
    url.searchParams.set('pathDeep', DEFAULT_PATH_DEEP.toString());
    url.searchParams.set('slippage', slippagePercent.toString());

    return url.toString();
  }

  /**
   * 调用 V1 Smart Router API
   */
  async getV1Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      const url = this.buildSmartRouterUrl(params);

      const response = await axios.get<SmartRouterResponse>(url, {
        timeout: REQUEST_TIMEOUT,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'NEAR-DeFi-SDK/1.0'
        }
      });

      const data = response.data;

      // 检查响应状态
      if (data.result_code !== 0 || !data.result_data?.routes?.length) {
        return null;
      }

      const { result_data } = data;
      const outputAmount = this.toReadableNumber(result_data.amount_out, params.tokenOut.decimals);

      return {
        system: 'V1',
        contractId: this.config.contracts.v1,
        outputAmount,
        inputAmount: params.amountIn,
        route: result_data.routes[0], // 使用第一条路径作为主要路径
        rawResponse: data
      };

    } catch (error: any) {
      console.error('❌ V1 Smart Router 调用失败:', error.message);

      if (error.code === 'ECONNABORTED') {
        throw new Error('V1 Smart Router API 请求超时');
      }

      if (error.response?.status === 404) {
        throw new Error('V1 Smart Router API 未找到');
      }

      throw new Error(`V1 Smart Router API 调用失败: ${error.message}`);
    }
  }

  /**
   * 获取路径详情
   */
  getRouteDetails(quoteResult: QuoteResult): string {
    if (quoteResult.system !== 'V1' || !quoteResult.route) {
      return 'N/A';
    }

    const route = quoteResult.route as any;
    if (!route.pools) {
      return 'N/A';
    }

    const pools = route.pools.map((pool: any) => `Pool#${pool.pool_id}`).join(' → ');
    return pools;
  }
}

}

/**
 * DCL v2 合约服务类
 */
class DCLv2ContractService {
  private config = getConfig();

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 将可读金额转换为非可分割单位（使用精确方法）
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ DCL v2收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 将非可分割单位转换为可读金额（使用精确方法）
   */
  private toReadableNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ DCL v2收到无效wei数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法并移除千分位分隔符
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      // 对于其他精度，使用精确的字符串操作
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 获取主要池子ID（简化版本，只查询高流动性池子）
   */
  private getMainPoolId(tokenIn: TokenMetadata, tokenOut: TokenMetadata): string | null {
    // USDC-NEAR交易对
    if ((tokenIn.id === '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1' && tokenOut.id === 'wrap.near') ||
        (tokenIn.id === 'wrap.near' && tokenOut.id === '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1')) {
      return MAIN_DCL_POOLS['USDC-NEAR'];
    }

    // USDT-NEAR交易对
    if ((tokenIn.id === 'usdt.tether-token.near' && tokenOut.id === 'wrap.near') ||
        (tokenIn.id === 'wrap.near' && tokenOut.id === 'usdt.tether-token.near')) {
      return MAIN_DCL_POOLS['USDT-NEAR'];
    }

    return null;
  }

  /**
   * 构建单个池子的 RPC 调用参数
   */
  private buildSinglePoolRPCParams(
    poolId: string,
    tokenIn: TokenMetadata,
    tokenOut: TokenMetadata,
    amountIn: string
  ): NearRPCParams {
    // 🔧 关键：使用精度单位的金额（模仿前端）
    const inputAmountNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);

    const quoteParams: DCLv2QuoteParams = {
      pool_ids: [poolId], // 只查询单个池子
      input_token: tokenIn.id,
      output_token: tokenOut.id,
      input_amount: inputAmountNonDivisible, // 使用精度单位
      tag: `${tokenIn.id}|${poolId.split('|')[2]}|${amountIn}`
    };

    const argsBase64 = Buffer.from(JSON.stringify(quoteParams)).toString('base64');

    return {
      request_type: 'call_function',
      finality: 'optimistic',
      account_id: this.config.contracts.dclv2,
      method_name: 'quote',
      args_base64: argsBase64
    };
  }

  /**
   * 查询单个池子
   */
  private async querySinglePool(poolId: string, params: QuoteParams): Promise<any> {
    const rpcParams = this.buildSinglePoolRPCParams(poolId, params.tokenIn, params.tokenOut, params.amountIn);

    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: rpcParams
    };

    const response = await axios.post(this.config.rpcUrl, rpcPayload, {
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;

    if (data.error) {
      throw new Error(`池子 ${poolId} RPC 错误: ${data.error.message}`);
    }

    const resultBytes = data.result?.result;
    if (!resultBytes || resultBytes.length === 0) {
      return null;
    }

    const resultString = String.fromCharCode(...resultBytes);
    return JSON.parse(resultString);
  }

  /**
   * 调用 DCL v2 合约获取报价（只查询主要池子）
   */
  async getDCLv2Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      // 获取主要池子ID
      const poolId = this.getMainPoolId(params.tokenIn, params.tokenOut);

      if (!poolId) {
        // 不是主要交易对，直接返回null
        return null;
      }

      // 查询单个主要池子
      const result = await this.querySinglePool(poolId, params);

      if (!result || !result.amount || result.amount === '0') {
        return null;
      }

      // 转换为人类可读格式
      const outputAmount = this.toReadableNumber(result.amount, params.tokenOut.decimals);

      return {
        system: 'DCL_V2',
        contractId: this.config.contracts.dclv2,
        outputAmount,
        inputAmount: params.amountIn,
        poolId: poolId,
        route: { pool_ids: [poolId], input_token: params.tokenIn.id, output_token: params.tokenOut.id, input_amount: params.amountIn },
        rawResponse: result
      };

    } catch (error: any) {
      console.error('❌ DCL v2 合约调用失败:', error.message);

      if (error.code === 'ECONNABORTED') {
        throw new Error('DCL v2 合约调用超时');
      }

      throw new Error(`DCL v2 合约调用失败: ${error.message}`);
    }
  }

  /**
   * 获取最佳池子信息
   */
  getBestPoolInfo(quoteResult: QuoteResult): string {
    if (quoteResult.system !== 'DCL_V2' || !quoteResult.poolId) {
      return 'N/A';
    }

    const parts = quoteResult.poolId.split('|');
    const fee = parts[2];
    const feePercent = (parseInt(fee) / 10000).toFixed(2);
    return `DCL Pool (${feePercent}% fee)`;
  }
}

/**
 * REF Finance 统一报价服务
 * 整合 V1 Smart Router 和 DCL v2 系统
 */
export class RefQuoteService {
  private v1Router = new V1SmartRouterService();
  private dclv2Contract = new DCLv2ContractService();

  /**
   * 获取最佳报价（并行调用两套系统）
   */
  async getBestQuote(params: QuoteParams): Promise<QuoteResult> {
    const errors: QuoteError[] = [];

    try {
      // 并行调用两套系统
      const [v1Result, dclv2Result] = await Promise.allSettled([
        this.v1Router.getV1Quote(params),
        this.dclv2Contract.getDCLv2Quote(params)
      ]);

      // 处理 V1 结果
      let v1Quote: QuoteResult | null = null;
      if (v1Result.status === 'fulfilled') {
        v1Quote = v1Result.value;
      } else {
        errors.push({
          system: 'V1',
          message: v1Result.reason?.message || 'V1 系统调用失败',
          originalError: v1Result.reason
        });
      }

      // 处理 DCL v2 结果
      let dclv2Quote: QuoteResult | null = null;
      if (dclv2Result.status === 'fulfilled') {
        dclv2Quote = dclv2Result.value;
      } else {
        errors.push({
          system: 'DCL_V2',
          message: dclv2Result.reason?.message || 'DCL v2 系统调用失败',
          originalError: dclv2Result.reason
        });
      }

      // 选择最佳报价
      const bestQuote = this.selectBestQuote(v1Quote, dclv2Quote);

      if (!bestQuote) {
        const errorMessage = errors.length > 0
          ? `所有系统都失败了: ${errors.map(e => e.message).join(', ')}`
          : '未找到可用的报价路径';
        throw new Error(errorMessage);
      }

      return bestQuote;

    } catch (error: any) {
      console.error('❌ 报价查询失败:', error.message);
      throw error;
    }
  }

  /**
   * 选择最佳报价
   */
  private selectBestQuote(v1Quote: QuoteResult | null, dclv2Quote: QuoteResult | null): QuoteResult | null {
    // 如果只有一个系统有结果，直接返回
    if (v1Quote && !dclv2Quote) return v1Quote;
    if (!v1Quote && dclv2Quote) return dclv2Quote;
    if (!v1Quote && !dclv2Quote) return null;

    // 两个系统都有结果，比较输出金额
    const v1Output = new Big(v1Quote!.outputAmount);
    const dclv2Output = new Big(dclv2Quote!.outputAmount);

    // 返回输出金额更大的报价
    return v1Output.gte(dclv2Output) ? v1Quote! : dclv2Quote!;
  }

  /**
   * 计算两个报价之间的价格差异
   */
  private calculatePriceDifference(quote1: QuoteResult, quote2: QuoteResult): string {
    try {
      const output1 = new Big(quote1.outputAmount);
      const output2 = new Big(quote2.outputAmount);

      const diff = output1.minus(output2).abs();
      const avg = output1.plus(output2).div(2);
      const percentage = diff.div(avg).times(100);

      return percentage.toFixed(4);
    } catch {
      return 'N/A';
    }
  }

  /**
   * 获取报价详情
   */
  getQuoteDetails(quote: QuoteResult): any {
    return {
      system: quote.system,
      contractId: quote.contractId,
      inputAmount: quote.inputAmount,
      outputAmount: quote.outputAmount,
      priceImpact: quote.priceImpact || 'N/A',
      route: quote.system === 'V1'
        ? this.v1Router.getRouteDetails(quote)
        : this.dclv2Contract.getBestPoolInfo(quote),
      rawResponse: quote.rawResponse
    };
  }

  /**
   * 验证报价参数
   */
  private validateQuoteParams(params: QuoteParams): void {
    if (!params.tokenIn?.id || !params.tokenOut?.id) {
      throw new Error('代币信息不完整');
    }

    if (!params.amountIn || parseFloat(params.amountIn) <= 0) {
      throw new Error('输入金额必须大于 0');
    }

    if (params.tokenIn.id === params.tokenOut.id) {
      throw new Error('输入和输出代币不能相同');
    }

    if (params.slippage && (params.slippage < 0 || params.slippage > 1)) {
      throw new Error('滑点容忍度必须在 0-1 之间');
    }
  }

  /**
   * 获取报价（带参数验证）
   */
  async getQuote(params: QuoteParams): Promise<QuoteResult> {
    this.validateQuoteParams(params);
    return this.getBestQuote(params);
  }

  /**
   * 批量获取报价
   */
  async getBatchQuotes(paramsList: QuoteParams[]): Promise<Array<QuoteResult | null>> {
    const promises = paramsList.map(params =>
      this.getQuote(params).catch(() => null)
    );
    return Promise.all(promises);
  }

  /**
   * 获取V1路径详情
   */
  getV1RouteDetails(quoteResult: QuoteResult): string {
    return this.v1Router.getRouteDetails(quoteResult);
  }

  /**
   * 获取DCL v2池子信息
   */
  getDCLv2PoolInfo(quoteResult: QuoteResult): string {
    return this.dclv2Contract.getBestPoolInfo(quoteResult);
  }
}

/**
 * 导出单例实例
 */
export const refQuoteService = new RefQuoteService();

/**
 * 导出服务类
 */
export { V1SmartRouterService, DCLv2ContractService };

/**
 * 默认导出
 */
export default RefQuoteService;
