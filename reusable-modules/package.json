{"name": "near-defi-sdk", "version": "1.0.0", "description": "高度模块化的NEAR DeFi开发工具包，支持REF Finance和VEAX DEX", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "ts-node examples/simple-swap.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "rm -rf dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["near", "defi", "dex", "ref-finance", "veax", "blockchain", "cryptocurrency", "trading", "arbitrage"], "author": "NEAR DeFi Developer", "license": "MIT", "dependencies": {"near-api-js": "^4.0.3", "axios": "^1.6.0", "big.js": "^6.2.1"}, "devDependencies": {"@types/node": "^20.0.0", "@types/big.js": "^6.1.6", "typescript": "^5.0.0", "ts-node": "^10.9.0", "jest": "^29.0.0", "@types/jest": "^29.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "peerDependencies": {"dotenv": "^16.0.0"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./types": {"import": "./dist/types/index.js", "require": "./dist/types/index.js", "types": "./dist/types/index.d.ts"}, "./config": {"import": "./dist/config/index.js", "require": "./dist/config/index.js", "types": "./dist/config/index.d.ts"}, "./config/tokens": {"import": "./dist/config/tokens.js", "require": "./dist/config/tokens.js", "types": "./dist/config/tokens.d.ts"}, "./near-utils": {"import": "./dist/near-utils/wrap-service.js", "require": "./dist/near-utils/wrap-service.js", "types": "./dist/near-utils/wrap-service.d.ts"}, "./veax": {"import": "./dist/veax/index.js", "require": "./dist/veax/index.js", "types": "./dist/veax/index.d.ts"}, "./veax/quote": {"import": "./dist/veax/quote-service.js", "require": "./dist/veax/quote-service.js", "types": "./dist/veax/quote-service.d.ts"}, "./veax/execution": {"import": "./dist/veax/execution-service.js", "require": "./dist/veax/execution-service.js", "types": "./dist/veax/execution-service.d.ts"}, "./ref-finance": {"import": "./dist/ref-finance/index.js", "require": "./dist/ref-finance/index.js", "types": "./dist/ref-finance/index.d.ts"}, "./ref-finance/v1-router": {"import": "./dist/ref-finance/v1-router.js", "require": "./dist/ref-finance/v1-router.js", "types": "./dist/ref-finance/v1-router.d.ts"}, "./ref-finance/execution": {"import": "./dist/ref-finance/execution-service.js", "require": "./dist/ref-finance/execution-service.js", "types": "./dist/ref-finance/execution-service.d.ts"}, "./examples": {"import": "./dist/examples/index.js", "require": "./dist/examples/index.js", "types": "./dist/examples/index.d.ts"}}, "files": ["dist", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/your-username/near-defi-sdk.git"}, "bugs": {"url": "https://github.com/your-username/near-defi-sdk/issues"}, "homepage": "https://github.com/your-username/near-defi-sdk#readme", "engines": {"node": ">=16.0.0"}}