# NEAR DeFi SDK - 可复用模块

这是一个高度模块化的NEAR DeFi开发工具包，提供REF Finance和VEAX DEX的完整集成功能。

## 🚀 特性

- ✅ **完全模块化** - 每个功能都是独立模块，可按需使用
- ✅ **类型安全** - 完整的TypeScript类型定义
- ✅ **精度修复** - 使用NEAR官方parseNearAmount避免精度损失
- ✅ **错误检测** - 智能检测FunctionCallError和ExecutionError
- ✅ **生产就绪** - 经过实际套利机器人验证的代码

## 📦 模块结构

```
reusable-modules/
├── types/                    # 类型定义模块
├── config/                   # 配置模块
├── near-utils/              # NEAR通用工具
├── veax/                    # VEAX DEX模块
├── ref-finance/             # REF Finance模块
├── examples/                # 使用示例
└── docs/                    # 详细文档
```

## 🎯 快速开始

### 1. 安装依赖
```bash
npm install near-api-js axios big.js
```

### 2. 基础使用

#### VEAX报价查询
```typescript
import { VeaxQuoteService } from './veax/quote-service';

const quote = await VeaxQuoteService.getQuote(
  'wrap.near',                    // 输入代币
  'usdt.tether-token.near',      // 输出代币
  '1000000000000000000000000'    // 1 NEAR (wei格式)
);

console.log(`输出: ${quote.outputAmount} USDT`);
```

#### REF Finance报价查询
```typescript
import { refQuoteService } from './ref-finance/quote-service';
import { TOKENS } from './config/tokens';

const quote = await refQuoteService.getQuote({
  tokenIn: TOKENS.NEAR,
  tokenOut: TOKENS.USDT,
  amountIn: '1',  // 1 NEAR (人类可读格式)
  slippage: 0.005 // 0.5%滑点
});

console.log(`最佳报价: ${quote.outputAmount} USDT (${quote.system})`);
```

#### 执行交易
```typescript
import RefExecutionServiceCorrect from './ref-finance/execution-service';

const refExecution = new RefExecutionServiceCorrect(
  'your-account.near',
  'your-private-key',
  'mainnet'
);

await refExecution.initialize();

const result = await refExecution.executeSwap(
  quote,
  'wrap.near',
  inputAmountWei,
  minOutputAmountWei,
  0.01
);

if (result.success) {
  console.log(`交易成功: ${result.transactionHash}`);
} else {
  console.log(`交易失败: ${result.error}`);
}
```

## 🔧 核心功能

### 精度修复
- 使用`parseNearAmount()`进行NEAR精确转换
- 字符串精确计算避免浮点数精度问题
- 修复了E22错误的根本原因

### 错误检测
- 智能检测`FunctionCallError`和`ExecutionError`
- 区分E22(存款不足)、E76(滑点过大)等具体错误
- 防止"假成功"问题

### 自动化功能
- 自动NEAR包装/解包装
- 自动用户和代币注册
- 智能滑点和参数设置

## 📚 详细文档

- [架构说明](./ARCHITECTURE.md) - 完整的架构设计和功能详细说明
- [使用指南](./USAGE.md) - 完整的使用说明和示例
- [问题解决指南](./TROUBLESHOOTING.md) - 精度修复和常见问题解决方案

## 🎯 使用场景

### 1. 简单交易机器人
```typescript
import { SimpleSwapBot } from './examples/simple-swap';

const bot = new SimpleSwapBot(accountId, privateKey);
await bot.swapNearToUsdt('1'); // 交换1 NEAR为USDT
```

### 2. 价格监控系统
```typescript
import { PriceMonitor } from './examples/price-monitor';

const monitor = new PriceMonitor();
monitor.startMonitoring(['NEAR-USDT', 'NEAR-USDC']);
```

### 3. 套利机器人
```typescript
import { BasicArbitrageBot } from './examples/arbitrage-basic';

const arbitrageBot = new BasicArbitrageBot(config);
await arbitrageBot.start();
```

## 🔐 安全注意事项

1. **私钥安全** - 永远不要在代码中硬编码私钥
2. **环境变量** - 使用.env文件管理敏感信息
3. **滑点设置** - 根据市场条件调整滑点容忍度
4. **错误处理** - 始终检查交易结果和错误信息

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个SDK！

## 📄 许可证

MIT License - 详见LICENSE文件

---

**这个SDK是基于实际生产环境的套利机器人代码提取而来，经过充分测试和验证。** 🎉
