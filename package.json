{"name": "ref-veax-arbitrage", "version": "1.0.0", "description": "REF Finance & VEAX 套利机器人", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "ts-node src/index.ts", "dev": "ts-node src/index.ts", "debug:dcl": "ts-node src/debug/checkDCLPools.ts", "debug:amounts": "ts-node src/debug/testDCLAmounts.ts", "debug:working": "ts-node src/debug/testDCLWorking.ts", "debug:veax": "ts-node src/debug/testVeaxQuote.ts", "test:arbitrage": "ts-node src/debug/simpleArbitrageTest.ts", "test:ref-execution": "ts-node src/debug/testRefExecution.ts", "test:ref-execution-fixed": "ts-node src/debug/testRefExecutionFixed.ts", "diagnose:ref-execution": "ts-node src/debug/diagnoseRefExecution.ts", "test:ref-quick": "ts-node src/debug/quickRefTest.ts", "test:v1-fixed": "ts-node src/debug/testV1Fixed.ts", "verify:dcl-pools": "ts-node src/debug/verifyDCLv2Pools.ts", "test:optimized-dclv2": "ts-node src/debug/testOptimizedDCLv2.ts", "test:arbitrage-monitor": "ts-node src/debug/testArbitrageMonitor.ts", "explore:dclv2-methods": "ts-node src/debug/exploreDCLv2Methods.ts", "test:local-calculation": "ts-node src/debug/testLocalCalculation.ts", "test:main-pools": "ts-node src/debug/testMainPoolsOptimization.ts", "test:optimized": "ts-node src/debug/testOptimizedSystem.ts", "test:main-program": "ts-node src/debug/testMainProgram.ts", "check:pools": "ts-node src/debug/quickPoolCheck.ts", "test:v1-correct-slippage": "ts-node src/debug/testV1WithCorrectSlippage.ts", "test:dynamic-amount": "ts-node src/debug/testDynamicAmount.ts", "check:detailed-pools": "ts-node src/debug/detailedPoolCheck.ts", "test:v1-high-slippage": "ts-node src/debug/testV1HighSlippage.ts", "test:veax-execution": "ts-node src/debug/testVeaxExecution.ts", "setup:env": "ts-node src/scripts/setupEnv.ts", "check:balance": "ts-node src/scripts/checkBalance.ts", "test:veax-swap": "ts-node src/debug/testVeaxSwap.ts", "diagnose:veax": "ts-node src/debug/diagnoseVeax.ts", "diagnose:veax-registration": "ts-node src/debug/veaxRegistrationDiagnosis.ts", "test:veax-real": "ts-node src/debug/testVeaxRealTrade.ts", "test:veax-pairs": "ts-node src/debug/testVeaxRealTrade.ts --pairs", "test:ref-real": "ts-node src/debug/testRefRealTrade.ts", "test:ref-real-fixed": "ts-node src/debug/testRefRealTradeFixed.ts", "test:ref-systems": "ts-node src/debug/testRefRealTrade.ts --systems", "register:all-tokens": "ts-node src/tools/batchTokenRegistration.ts", "test:custom": "ts-node src/tools/customQuoteTester.ts", "test:quick": "ts-node src/tools/quickTest.ts", "diagnose:comprehensive": "ts-node src/debug/comprehensiveDiagnosis.ts", "fix:ref-execution": "ts-node src/debug/fixRefExecutionIssues.ts", "fix:quick": "ts-node src/debug/quickFix.ts", "test:real-trade": "ts-node src/debug/realTradeTest.ts", "test:correct-ref": "ts-node src/debug/testCorrectRefExecution.ts", "test:ref-both-systems": "ts-node src/debug/testBothRefSystems.ts", "test:ref-real-execution": "ts-node src/debug/testRefRealExecution.ts", "test": "jest", "test:watch": "jest --watch", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop arbitrage-bot", "pm2:restart": "pm2 restart arbitrage-bot", "pm2:delete": "pm2 delete arbitrage-bot", "pm2:logs": "pm2 logs arbitrage-bot", "pm2:monit": "pm2 monit", "pm2:status": "pm2 list"}, "keywords": ["arbitrage", "ref-finance", "veax", "near", "defi"], "author": "Arbitrage Bot Developer", "license": "MIT", "dependencies": {"axios": "^1.6.0", "big.js": "^6.2.1", "dotenv": "^16.5.0", "near-api-js": "^4.0.3"}, "devDependencies": {"@types/big.js": "^6.2.0", "@types/jest": "^29.5.5", "@types/node": "^20.8.0", "jest": "^29.7.0", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}