module.exports = {
  apps: [
    {
      // 应用基本信息
      name: 'refveax',
      script: 'src/index.ts',
      interpreter: 'npx',
      interpreter_args: 'ts-node',

      // 运行环境
      cwd: './',
      env: {
        NODE_ENV: 'production',
        TZ: 'Asia/Shanghai'
      },

      // 进程管理
      instances: 1,                    // 单实例运行（避免并发冲突）
      exec_mode: 'fork',              // fork模式

      // 自动重启配置
      autorestart: true,              // 自动重启
      watch: false,                   // 不监听文件变化（生产环境）
      max_memory_restart: '1G',       // 内存超过1G时重启

      // 重启策略
      restart_delay: 5000,            // 重启延迟5秒
      max_restarts: 10,               // 最大重启次数（1小时内）
      min_uptime: '10s',              // 最小运行时间

      // 日志配置
      log_file: './logs/arbitrage-bot.log',
      out_file: './logs/arbitrage-bot-out.log',
      error_file: './logs/arbitrage-bot-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      log_type: 'json',               // JSON格式日志

      // 🔧 日志轮转和大小管理
      max_size: '10M',               // 单个日志文件最大10MB
      retain: 10,                     // 保留最近10个日志文件
      compress: true,                 // 压缩旧日志文件
      dateFormat: 'YYYY-MM-DD_HH-mm-ss', // 轮转文件的日期格式
      rotateInterval: '0 0 * * *',    // 每天午夜轮转日志
      rotateModule: true,             // 启用日志轮转模块

      // 进程标识
      pid_file: './pids/arbitrage-bot.pid',

      // 高级配置
      kill_timeout: 5000,             // 强制杀死进程的超时时间
      listen_timeout: 8000,           // 监听超时时间

      // 错误处理
      exp_backoff_restart_delay: 100, // 指数退避重启延迟

      // 环境变量（从.env文件读取）
      env_production: {
        NODE_ENV: 'production'
        // 其他环境变量会从.env文件自动读取
      }
    }
  ]
};
