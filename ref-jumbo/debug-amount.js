const { parseNearAmount } = require('near-api-js/lib/utils/format');

const LIMITS = {
  MIN_AMOUNT_IN: '1000000000000000000000',
  MAX_AMOUNT_IN: '100000000000000000000000000'
};

function isValidAmount(amount) {
  console.log('验证金额:', amount);

  if (!amount || typeof amount !== 'string') {
    console.log('失败: 空值或非字符串');
    return false;
  }

  try {
    const numAmount = parseFloat(amount);
    console.log('parseFloat结果:', numAmount);

    if (isNaN(numAmount) || numAmount <= 0) {
      console.log('失败: NaN或非正数');
      return false;
    }

    // 判断是否为 NEAR 的 wei 格式：超过 1e22 的整数
    const isNearWeiFormat = !amount.includes('.') && numAmount > 1e22;
    console.log('是否NEAR wei格式:', isNearWeiFormat);

    if (isNearWeiFormat) {
      console.log('NEAR wei格式路径: 使用NEAR限制验证');
      try {
        const amountBig = BigInt(amount);
        const minAmount = BigInt(LIMITS.MIN_AMOUNT_IN);
        const maxAmount = BigInt(LIMITS.MAX_AMOUNT_IN);
        const result = amountBig >= minAmount && amountBig <= maxAmount;
        console.log('NEAR范围检查:', result);
        return result;
      } catch {
        console.log('BigInt转换失败，使用宽松验证');
        return numAmount > 0 && numAmount < 1e30;
      }
    }

    // 判断是否为小的整数（可能是 NEAR 的人类可读格式）
    const isSmallInteger = !amount.includes('.') && numAmount <= 1000;
    console.log('是否小整数:', isSmallInteger);

    if (isSmallInteger) {
      console.log('NEAR人类可读格式路径: 转换后验证');
      try {
        const amountWei = parseNearAmount(amount) || '0';
        console.log('转换为wei:', amountWei);

        const amountBig = BigInt(amountWei);
        const minAmount = BigInt(LIMITS.MIN_AMOUNT_IN);
        const maxAmount = BigInt(LIMITS.MAX_AMOUNT_IN);

        const result = amountBig >= minAmount && amountBig <= maxAmount;
        console.log('NEAR范围检查:', result);
        return result;
      } catch {
        console.log('转换失败，使用宽松验证');
        return numAmount > 0;
      }
    }

    // 其他情况：其他代币的金额
    console.log('其他代币路径: 宽松验证');
    const result = numAmount > 0 && numAmount < 1e21;
    console.log('宽松验证结果:', result);
    return result;

  } catch (error) {
    console.log('异常:', error.message);
    const numAmount = parseFloat(amount);
    return numAmount > 0 && numAmount < 1e18;
  }
}

console.log('=== 测试 "4" ===');
isValidAmount('4');
console.log('');

console.log('=== 测试 "4000000000000000000000000" (4 NEAR wei) ===');
isValidAmount('4000000000000000000000000');
console.log('');

console.log('=== parseNearAmount("4") ===');
console.log('结果:', parseNearAmount('4'));
console.log('');

console.log('=== 测试 UMINT 大数字 "1657875994766194" ===');
isValidAmount('1657875994766194');
console.log('');

console.log('=== 测试 CHICA "86192.69028233412160243" ===');
isValidAmount('86192.69028233412160243');
console.log('');

console.log('=== 测试 1MIL "405.39506392061006209" ===');
isValidAmount('405.39506392061006209');
console.log('');

console.log('=== 测试标准化后的值 ===');
console.log('UMINT 标准化后: "1657875994766194"');
isValidAmount('1657875994766194');
console.log('');

console.log('CHICA 标准化后: "86192.69028233412160243"');
isValidAmount('86192.69028233412160243');
console.log('');

console.log('1MIL 标准化后: "405.39506392061006209"');
isValidAmount('405.39506392061006209');
console.log('');

console.log('=== 测试实际失败的值 ===');
console.log('HAPI wei: "3449266571001987503"');
isValidAmount('3449266571001987503');
console.log('');

console.log('1MIL wei: "405395063920610062090"');
isValidAmount('405395063920610062090');
