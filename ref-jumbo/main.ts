/**
 * REF-Jumbo 套利系统主程序
 * 基于现有 REF-VEAX 系统架构
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../.env') });
import RefJumboArbitrageBot from './refJumboArbitrageBot';

/**
 * 主程序
 */
async function main() {
  console.log('🚀 REF-Jumbo 套利系统');
  console.log('='.repeat(50));

  // 验证环境变量
  if (!process.env.NEAR_ACCOUNT_ID && !process.env.ACCOUNT_ID) {
    console.error('❌ 缺少环境变量: NEAR_ACCOUNT_ID 或 ACCOUNT_ID');
    process.exit(1);
  }

  if (!process.env.NEAR_PRIVATE_KEY && !process.env.PRIVATE_KEY) {
    console.error('❌ 缺少环境变量: NEAR_PRIVATE_KEY 或 PRIVATE_KEY');
    process.exit(1);
  }

  try {
    // 创建套利机器人
    const bot = new RefJumboArbitrageBot();

    // 设置优雅退出
    let isShuttingDown = false;

    const gracefulShutdown = async () => {
      if (isShuttingDown) return;
      isShuttingDown = true;

      console.log('\n🛑 接收到退出信号，正在优雅关闭...');
      await bot.stop();
      process.exit(0);
    };

    // 监听退出信号
    process.on('SIGINT', gracefulShutdown);  // Ctrl+C
    process.on('SIGTERM', gracefulShutdown); // 终止信号
    process.on('SIGQUIT', gracefulShutdown); // 退出信号

    // 启动套利机器人
    await bot.start();

    // 保持程序运行
    console.log('💡 按 Ctrl+C 停止套利机器人');

  } catch (error) {
    console.error('❌ 程序启动失败:', error);
    process.exit(1);
  }
}

// 运行主程序
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 程序异常退出:', error);
    process.exit(1);
  });
}

export { main };
