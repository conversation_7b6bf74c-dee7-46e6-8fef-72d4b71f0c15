/**
 * REF-Jumbo 套利系统测试脚本
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../.env') });
import { refQuoteService } from '../src/services/refQuoteService';
import JumboQuoteService from './services/jumboQuoteService';
import { tradingPairManager } from './config/tradingPairs';
import { Account, connect, keyStores } from 'near-api-js';

/**
 * 初始化 NEAR 账户
 */
async function initializeAccount(): Promise<Account> {
  const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;
  const privateKey = process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!;

  if (!accountId || !privateKey) {
    throw new Error('请在 .env 文件中设置 NEAR_ACCOUNT_ID 和 NEAR_PRIVATE_KEY');
  }

  const keyStore = new keyStores.InMemoryKeyStore();
  const keyPair = require('near-api-js').utils.KeyPair.fromString(privateKey);
  await keyStore.setKey('mainnet', accountId, keyPair);

  const config = {
    networkId: 'mainnet',
    nodeUrl: 'https://rpc.mainnet.near.org',
    keyStore
  };

  const near = await connect(config);
  return await near.account(accountId);
}

/**
 * 测试报价功能
 */
async function testQuotes() {
  console.log('🧪 REF-Jumbo 报价测试');
  console.log('='.repeat(50));

  try {
    // 初始化账户
    console.log('📡 初始化 NEAR 账户...');
    const account = await initializeAccount();
    console.log(`✅ 账户: ${account.accountId}`);

    // 初始化 Jumbo 报价服务
    console.log('\n🔧 初始化 Jumbo 报价服务...');
    await JumboQuoteService.initialize(account);

    // 获取启用的交易对
    const tradingPairs = tradingPairManager.getEnabledPairs();
    console.log(`\n📊 测试 ${tradingPairs.length} 个交易对的报价:`);

    for (const pair of tradingPairs) {
      console.log(`\n🔍 测试交易对: ${pair.id}`);
      console.log(`   输入: ${pair.tradeAmount} ${pair.tokenA.symbol}`);

      try {
        // 并行获取 REF 和 Jumbo 报价
        const [refQuote, jumboQuote] = await Promise.all([
          getREFQuote(pair.tokenA, pair.tokenB, pair.tradeAmount),
          getJumboQuote(pair.tokenA, pair.tokenB, pair.tradeAmount)
        ]);

        if (refQuote && jumboQuote) {
          const refPrice = parseFloat(refQuote) / parseFloat(pair.tradeAmount);
          const jumboPrice = parseFloat(jumboQuote) / parseFloat(pair.tradeAmount);
          const priceDiff = ((jumboPrice - refPrice) / refPrice) * 100;

          console.log(`   REF 输出: ${refQuote} ${pair.tokenB.symbol} (价格: ${refPrice.toFixed(6)})`);
          console.log(`   Jumbo 输出: ${jumboQuote} ${pair.tokenB.symbol} (价格: ${jumboPrice.toFixed(6)})`);
          console.log(`   价格差异: ${priceDiff.toFixed(2)}%`);

          if (Math.abs(priceDiff) >= 0.5) {
            console.log(`   🎯 发现套利机会! 差异超过 0.5%`);
          }

          // 测试反向报价
          const [refReverseQuote, jumboReverseQuote] = await Promise.all([
            getREFQuote(pair.tokenB, pair.tokenA, jumboQuote),
            getJumboQuote(pair.tokenB, pair.tokenA, refQuote)
          ]);

          if (refReverseQuote && jumboReverseQuote) {
            const refToJumboProfit = parseFloat(jumboReverseQuote) - parseFloat(pair.tradeAmount);
            const jumboToRefProfit = parseFloat(refReverseQuote) - parseFloat(pair.tradeAmount);

            console.log(`   REF→Jumbo 利润: ${refToJumboProfit.toFixed(4)} NEAR`);
            console.log(`   Jumbo→REF 利润: ${jumboToRefProfit.toFixed(4)} NEAR`);

            if (refToJumboProfit >= 0.015 || jumboToRefProfit >= 0.015) {
              console.log(`   💰 发现有利可图的套利机会!`);
            }
          }
        } else {
          console.log(`   ❌ 报价获取失败: REF=${refQuote ? '✅' : '❌'}, Jumbo=${jumboQuote ? '✅' : '❌'}`);
        }

      } catch (error) {
        console.log(`   ❌ 测试失败:`, error);
      }
    }

    console.log('\n✅ 报价测试完成!');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    throw error;
  }
}

/**
 * 获取REF报价
 */
async function getREFQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
  try {
    const result = await refQuoteService.getQuote({
      tokenIn,
      tokenOut,
      amountIn: amount,
      slippage: 0.005
    });
    return result.outputAmount;
  } catch (error) {
    return null;
  }
}

/**
 * 获取Jumbo报价
 */
async function getJumboQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
  try {
    // 标准化金额格式
    const normalizedAmount = normalizeAmount(amount);
    if (!normalizedAmount) {
      console.warn(`⚠️ Jumbo报价输入无效: ${amount}`);
      return null;
    }

    const result = await JumboQuoteService.getQuote(tokenIn.id, tokenOut.id, normalizedAmount);
    if (!result.success) {
      return null;
    }
    return result.outputAmount;
  } catch (error) {
    return null;
  }
}

/**
 * 标准化金额格式
 */
function normalizeAmount(amount: string): string | null {
  try {
    // 验证是否为有效数字
    const numAmount = parseFloat(amount);
    if (isNaN(numAmount) || numAmount <= 0) {
      return null;
    }

    // 如果包含小数点，需要根据数字大小判断处理方式
    if (amount.includes('.')) {
      // 如果是非常大的数字（如 1657875994766194.226361），
      // 这可能是 wei 格式的代币金额，保留整数部分
      if (numAmount > 1e12) {
        return Math.floor(numAmount).toString();
      }

      // 如果是较小的数字（如 86192.69028233412160243），
      // 这可能是人类可读格式，保持原样
      return amount;
    }

    // 如果没有小数点，直接返回
    return amount;
  } catch (error) {
    return null;
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await testQuotes();
  } catch (error) {
    console.error('程序失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testQuotes };
