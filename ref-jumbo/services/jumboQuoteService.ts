/**
 * Jumbo Exchange 报价服务
 * 
 * 基于现有的 JumboExchange 模块，提供与 VEAX 相同的接口
 * 使用本地 AMM 计算获取报价，比 REF Finance 的智能合约调用更简单
 */

import { JumboExchange } from '../../reusable-modules/jumbo-exchange';
import { Account } from 'near-api-js';

// Jumbo 报价结果接口 (与 VEAX 保持一致)
export interface JumboQuoteResult {
  outputAmount: string;
  priceImpact: string;
  fee: string;
  poolExists: boolean;
  success: boolean;
  error?: string;
}

/**
 * Jumbo 报价服务类
 */
export class JumboQuoteService {
  private static jumboExchange: JumboExchange | null = null;
  private static isInitialized: boolean = false;
  private static nearAccount: Account | null = null;

  /**
   * 初始化 Jumbo Exchange 实例
   */
  static async initialize(account: Account): Promise<void> {
    if (!this.isInitialized) {
      this.jumboExchange = new JumboExchange(account);
      this.nearAccount = account; // 🔧 保存账户引用
      await this.jumboExchange.initialize();
      this.isInitialized = true;
      console.log('✅ Jumbo Exchange 报价服务初始化完成');
    }
  }

  /**
   * 获取 Jumbo Exchange 报价
   * @param tokenA 输入代币地址
   * @param tokenB 输出代币地址
   * @param amountA 输入代币数量（字符串格式）
   * @param slippageTolerance 滑点容忍度，默认0.5%
   * @returns 报价结果
   */
  static async getQuote(
    tokenA: string,
    tokenB: string,
    amountA: string,
    slippageTolerance: number = 0.005
  ): Promise<JumboQuoteResult> {
    try {
      if (!this.jumboExchange) {
        throw new Error('Jumbo Exchange 未初始化，请先调用 initialize()');
      }

      // 根据代币对确定使用哪个池子
      const poolId = this.getPoolIdForTokenPair(tokenA, tokenB);
      if (poolId === null) { // 🔧 修复：明确检查null，而不是falsy值（因为poolId可能是0）
        return {
          outputAmount: '0',
          priceImpact: '0',
          fee: '0',
          poolExists: false,
          success: false,
          error: `不支持的代币对: ${tokenA} -> ${tokenB}`
        };
      }

      // 判断交易方向
      const isForwardTrade = tokenA === 'wrap.near'; // NEAR → 其他代币
      const isReverseTrade = tokenB === 'wrap.near'; // 其他代币 → NEAR

      let quote;

      // 🔧 修复：直接使用合约调用，不使用本地计算
      if (isForwardTrade) {
        // 正向交易: NEAR → 其他代币
        // 直接调用合约 get_return 方法
        const amountInWei = this.toTokenWei(amountA, 24); // NEAR 是 24 位小数
        quote = await this.callContractGetReturn(poolId, tokenA, amountInWei, tokenB);
      } else if (isReverseTrade) {
        // 反向交易: 其他代币 → NEAR
        // 🔧 修复：REF返回的都是人类可读格式，需要转换为wei
        const tokenDecimals = this.getTokenDecimals(tokenA);
        const amountInWei = this.toTokenWei(amountA, tokenDecimals);

        // 直接调用合约 get_return 方法
        quote = await this.callContractGetReturn(poolId, tokenA, amountInWei, tokenB);
      } else {
        throw new Error('不支持的交易方向：必须有一个代币是 wrap.near');
      }

      if (!quote.success) {
        return {
          outputAmount: '0',
          priceImpact: '0',
          fee: '0',
          poolExists: false,
          success: false,
          error: quote.error || 'Jumbo 报价失败'
        };
      }

      return {
        outputAmount: quote.outputAmount,
        priceImpact: '0', // Jumbo 暂不提供价格影响
        fee: '0', // Jumbo 暂不提供手续费信息
        poolExists: true,
        success: true
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      console.error(`[Jumbo] 请求失败:`, errorMsg);
      
      return {
        outputAmount: '0',
        priceImpact: '0',
        fee: '0',
        poolExists: false,
        success: false,
        error: `网络请求失败: ${errorMsg}`
      };
    }
  }

  /**
   * 根据代币对获取池子ID
   */
  private static getPoolIdForTokenPair(tokenA: string, tokenB: string): number | null {
    // 标准化代币对 (总是 NEAR 在前)
    const isNearFirst = tokenA === 'wrap.near';
    const baseToken = isNearFirst ? tokenA : tokenB;
    const quoteToken = isNearFirst ? tokenB : tokenA;

    if (baseToken !== 'wrap.near') {
      return null; // 只支持 NEAR 为基础代币的交易对
    }

    // 根据报价代币确定池子ID
    switch (quoteToken) {
      case 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near': // AURORA
        return 0;
      case 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near': // USDT.e
        return 1;
      case 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near': // OCT
        return 3;
      case 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near': // HAPI
        return 6;
      case 'linear-protocol.near': // LINEAR
        return 231;
      case 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near': // UMINT
        return 184;
      case 'v2-nearx.stader-labs.near': // NearX
        return 266;
      case 'token.bocachica_mars.near': // CHICA
        return 274;
      case 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near': // 1MIL
        return 294;
      default:
        return null;
    }
  }

  /**
   * 检查是否支持指定的代币对
   */
  static isSupportedPair(tokenA: string, tokenB: string): boolean {
    return this.getPoolIdForTokenPair(tokenA, tokenB) !== null;
  }

  /**
   * 获取支持的代币对列表
   */
  static getSupportedPairs(): Array<{tokenA: string, tokenB: string, poolId: number}> {
    return [
      { tokenA: 'wrap.near', tokenB: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near', poolId: 0 }, // AURORA
      { tokenA: 'wrap.near', tokenB: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near', poolId: 1 }, // USDT.e
      { tokenA: 'wrap.near', tokenB: 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near', poolId: 3 }, // OCT
      { tokenA: 'wrap.near', tokenB: 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near', poolId: 6 }, // HAPI
      { tokenA: 'wrap.near', tokenB: 'linear-protocol.near', poolId: 231 }, // LINEAR
      { tokenA: 'wrap.near', tokenB: 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near', poolId: 184 }, // UMINT
      { tokenA: 'wrap.near', tokenB: 'v2-nearx.stader-labs.near', poolId: 266 }, // NearX
      { tokenA: 'wrap.near', tokenB: 'token.bocachica_mars.near', poolId: 274 }, // CHICA
      { tokenA: 'wrap.near', tokenB: 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near', poolId: 294 } // 1MIL
    ];
  }

  /**
   * 获取代币的小数位数
   *
   * 🔧 添加新代币精度的步骤：
   * 1. 在下面的 switch 语句中添加新的 case
   * 2. 格式: case 'token.contract.near': return 精度位数;
   * 3. 常见精度: NEAR生态=24位, USDT/USDC=6位, 其他=18位
   *
   * 示例：
   * case 'newtoken.contract.near': // NEWTOKEN
   *   return 18;
   */
  private static getTokenDecimals(tokenAddress: string): number {
    switch (tokenAddress) {
      case 'wrap.near':
      case 'linear-protocol.near':
      case 'v2-nearx.stader-labs.near':
        return 24;
      case 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near': // USDT.e
      case 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near': // UMINT
        return 6;
      case 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near': // AURORA
      case 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near': // HAPI
      case 'token.bocachica_mars.near': // CHICA
      case 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near': // 1MIL
      case 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near': // OCT
        return 18;
      default:
        return 18; // 默认 18 位小数
    }
  }

  /**
   * 将代币金额转换为 wei 格式
   */
  private static toTokenWei(amount: string, decimals: number): string {
    if (decimals === 24) {
      // 对于 24 位小数（NEAR），使用官方方法
      const { parseNearAmount } = require('near-api-js/lib/utils/format');
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 直接调用 Jumbo 合约的 get_return 方法
   */
  private static async callContractGetReturn(
    poolId: number,
    tokenIn: string,
    amountIn: string,
    tokenOut: string
  ): Promise<JumboQuoteResult> {
    try {
      if (!this.jumboExchange) {
        throw new Error('Jumbo Exchange 未初始化');
      }

      // 获取 NEAR 账户
      if (!this.nearAccount) {
        throw new Error('NEAR 账户未初始化');
      }

      // 直接调用合约
      const result = await this.nearAccount.viewFunction({
        contractId: 'v1.jumbo_exchange.near',
        methodName: 'get_return',
        args: {
          pool_id: poolId,
          token_in: tokenIn,
          amount_in: amountIn,
          token_out: tokenOut
        }
      });

      const outputAmountWei = result.toString();
      const tokenOutDecimals = this.getTokenDecimals(tokenOut);

      // 转换为人类可读格式
      const outputAmount = tokenOutDecimals === 24
        ? require('near-api-js/lib/utils/format').formatNearAmount(outputAmountWei).replace(/,/g, '')
        : this.fromTokenWei(outputAmountWei, tokenOutDecimals);

      return {
        outputAmount,
        priceImpact: '0',
        fee: '0',
        poolExists: true,
        success: true
      };

    } catch (error) {
      console.error(`❌ Jumbo 合约调用失败:`, error);
      return {
        outputAmount: '0',
        priceImpact: '0',
        fee: '0',
        poolExists: false,
        success: false,
        error: error instanceof Error ? error.message : '合约调用失败'
      };
    }
  }

  /**
   * 将 wei 格式转换为人类可读格式
   */
  private static fromTokenWei(amount: string, decimals: number): string {
    if (amount === '0') return '0';
    const paddedAmount = amount.padStart(decimals + 1, '0');
    const integerPart = paddedAmount.slice(0, -decimals) || '0';
    const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');
    return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
  }
}

export default JumboQuoteService;
