/**
 * Jumbo Exchange 交易执行服务
 *
 * 功能：提供 Jumbo Exchange 的完整交易执行功能（参考 VEAX 架构）
 * 职责：
 * - 执行 Jumbo Exchange 交易
 * - 从交易日志中提取实际输出金额（wei格式）
 * - 智能检测和处理合约执行错误
 * - 支持用户注册和代币注册
 *
 * 关键特性：
 * 1. 精度保持：返回wei格式的实际输出金额，避免精度损失
 * 2. 错误检测：智能识别Jumbo合约错误
 * 3. 交易方向智能处理：正确区分正向和反向交易
 *
 * 交易流程：
 * 1. 验证交易参数
 * 2. 执行ft_transfer_call交易到Jumbo合约
 * 3. 检查交易是否真正成功
 * 4. 从交易日志中提取实际输出金额
 * 5. 返回包含wei格式输出的交易结果
 */

import { Account, Near, connect, keyStores, utils } from 'near-api-js';
import { TradeResult } from '../types';

// 交易结果接口
export interface JumboTransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  outputAmount?: string;        // 人类可读格式
  outputAmountWei?: string;     // wei格式 - 关键！
  inputAmount?: string;
  inputAmountWei?: string;
}

// Gas和存储配置
const GAS_CONFIG = {
  jumboSwap: BigInt('**************0'),      // 300 TGas
  storageDeposit: BigInt('**************')   // 30 TGas
};

const DEPOSIT_CONFIG = {
  jumboFtTransfer: BigInt('1'),              // 1 yoctoNEAR
  storageDeposit: BigInt('1250000000000000000000000') // 0.00125 NEAR
};

/**
 * Jumbo Exchange 交易执行服务
 */
export class JumboExecutionService {
  private near: Near | null = null;
  private account: Account | null = null;
  private readonly contractId = 'v1.jumbo_exchange.near';

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: string = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      console.log('🔧 初始化 Jumbo 执行服务...');

      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      const rpcUrl = process.env.NEAR_RPC_URL || (this.networkId === 'mainnet'
        ? 'https://free.rpc.fastnear.com'
        : 'https://test.rpc.fastnear.com');

      const config = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: rpcUrl,
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(config);
      this.account = await this.near.account(this.accountId);

      console.log(`✅ Jumbo执行服务初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ Jumbo执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行交换交易 - 参考 VEAX 架构
   * @param tokenIn 输入代币信息
   * @param tokenOut 输出代币信息
   * @param amountIn 输入金额 (wei格式)
   * @param minAmountOut 最小输出金额 (wei格式)
   * @param slippage 滑点容忍度
   * @returns 交易结果
   */
  async executeSwap(
    tokenIn: { id: string; symbol: string; decimals: number },
    tokenOut: { id: string; symbol: string; decimals: number },
    amountIn: string,
    minAmountOut: string,
    slippage: number = 0.01
  ): Promise<TradeResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🚀 开始执行Jumbo交易: ${amountIn} wei ${tokenIn.symbol} → ${tokenOut.symbol}`);

      // 根据代币对确定池子ID
      const poolId = this.getPoolIdForTokenPair(tokenIn.id, tokenOut.id);
      if (!poolId) {
        throw new Error(`不支持的代币对: ${tokenIn.id} -> ${tokenOut.id}`);
      }

      console.log(`📋 池子ID: ${poolId}, ${tokenIn.id} → ${tokenOut.id}`);

      // 🔧 修复：使用正确的 Jumbo Exchange 消息格式 (与 REF Finance 相同)
      const swapMsg = {
        force: 0,
        actions: [{
          pool_id: poolId,
          token_in: tokenIn.id,
          token_out: tokenOut.id,
          amount_in: amountIn,  // 🔧 关键：添加 amount_in 字段
          min_amount_out: minAmountOut
        }]
      };

      console.log('📋 交易消息(修复版):', JSON.stringify(swapMsg, null, 2));

      // 执行ft_transfer_call到输入代币合约
      const result = await this.account.functionCall({
        contractId: tokenIn.id, // 调用输入代币合约
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: this.contractId, // v1.jumbo_exchange.near
          amount: amountIn,
          msg: JSON.stringify(swapMsg)
        },
        attachedDeposit: DEPOSIT_CONFIG.jumboFtTransfer,
        gas: GAS_CONFIG.jumboSwap
      });

      console.log(`📋 Jumbo交易已提交: ${result.transaction.hash}`);

      // 检查交易是否真正成功
      const successCheck = this.checkTransactionSuccess(result);
      if (!successCheck.success) {
        console.error(`❌ Jumbo交易执行失败: ${successCheck.error}`);
        return {
          success: false,
          error: successCheck.error
        };
      }

      console.log(`✅ Jumbo交易真正成功: ${result.transaction.hash}`);

      // 从交易结果中提取实际输出金额
      const actualOutputAmount = this.extractOutputAmountFromResult(result);

      return {
        success: true,
        txHash: result.transaction.hash,
        outputAmount: actualOutputAmount.humanReadable || '0',
        outputAmountWei: actualOutputAmount.wei || minAmountOut // 🔧 关键：返回wei格式
      };

    } catch (error: any) {
      console.error('❌ Jumbo交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 根据代币对获取池子ID
   */
  private getPoolIdForTokenPair(tokenA: string, tokenB: string): number | null {
    // 标准化代币对 (总是 NEAR 在前)
    const isNearFirst = tokenA === 'wrap.near';
    const baseToken = isNearFirst ? tokenA : tokenB;
    const quoteToken = isNearFirst ? tokenB : tokenA;

    if (baseToken !== 'wrap.near') {
      return null; // 只支持 NEAR 为基础代币的交易对
    }

    // 根据报价代币确定池子ID
    switch (quoteToken) {
      case 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near': // USDT.e
        return 1;
      case 'linear-protocol.near': // LINEAR
        return 231;
      case 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near': // HAPI
        return 6;
      case 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near': // UMINT
        return 184;
      case 'token.bocachica_mars.near': // CHICA
        return 274;
      case 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near': // 1MIL
        return 294;
      case 'v2-nearx.stader-labs.near': // NearX
        return 266;
      default:
        return null;
    }
  }

  /**
   * 检查交易是否真正成功
   */
  private checkTransactionSuccess(result: any): { success: boolean; error?: string } {
    try {
      // 检查是否有FunctionCallError
      const hasError = result.receipts_outcome?.some((receipt: any) =>
        receipt.outcome?.status?.Failure?.ActionError?.kind?.FunctionCallError
      );

      if (hasError) {
        const errorDetails = result.receipts_outcome
          .find((receipt: any) => receipt.outcome?.status?.Failure)
          ?.outcome?.status?.Failure?.ActionError?.kind?.FunctionCallError;

        return {
          success: false,
          error: `合约执行失败: ${JSON.stringify(errorDetails)}`
        };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: `交易状态检查失败: ${error}`
      };
    }
  }

  /**
   * 从交易结果中提取实际输出金额
   */
  private extractOutputAmountFromResult(result: any): { wei?: string; humanReadable?: string } {
    try {
      // 从交易日志中查找输出金额
      const logs = result.receipts_outcome
        ?.flatMap((receipt: any) => receipt.outcome.logs)
        ?.filter((log: string) => log.includes('EVENT_JSON') || log.includes('swap')) || [];

      for (const log of logs) {
        try {
          // 尝试解析JSON格式的日志
          if (log.includes('EVENT_JSON:')) {
            const jsonStr = log.replace('EVENT_JSON:', '');
            const eventData = JSON.parse(jsonStr);

            if (eventData.event === 'ft_transfer' && eventData.data?.[0]?.amount) {
              // 检查是否是转给用户的代币 (输出)
              if (eventData.data[0].new_owner_id === this.accountId) {
                return {
                  wei: eventData.data[0].amount,
                  humanReadable: eventData.data[0].amount
                };
              }
            }
          }

          // 尝试解析 Swapped 日志
          const swapMatch = log.match(/Swapped .+ for (\d+) /);
          if (swapMatch) {
            return {
              wei: swapMatch[1],
              humanReadable: swapMatch[1]
            };
          }

          // 尝试解析 Transfer 日志 (from v1.jumbo_exchange.near to user)
          const transferMatch = log.match(/Transfer (\d+) from v1\.jumbo_exchange\.near to/);
          if (transferMatch) {
            console.log(`📊 从Transfer日志提取输出: ${transferMatch[1]} wei`);
            return {
              wei: transferMatch[1],
              humanReadable: transferMatch[1]
            };
          }

          // 🔧 新增：尝试解析 Swapped 日志中的输出金额 (更精确的匹配)
          const swappedMatch = log.match(/Swapped .+ for (\d+) wrap\.near/);
          if (swappedMatch) {
            console.log(`📊 从Swapped日志提取输出: ${swappedMatch[1]} wei`);
            return {
              wei: swappedMatch[1],
              humanReadable: swappedMatch[1]
            };
          }

          // 🔧 备用：尝试解析任何 "for 数字" 的模式
          const forAmountMatch = log.match(/for (\d+) wrap\.near/);
          if (forAmountMatch) {
            console.log(`📊 从for模式提取输出: ${forAmountMatch[1]} wei`);
            return {
              wei: forAmountMatch[1],
              humanReadable: forAmountMatch[1]
            };
          }
        } catch (parseError) {
          // 忽略解析错误，继续尝试下一个日志
        }
      }

      console.warn('⚠️ 无法从交易日志中提取输出金额');
      console.log('📋 所有日志内容:');
      logs.forEach((log: string, index: number) => {
        console.log(`   ${index + 1}. ${log}`);
      });
      return {};
    } catch (error) {
      console.error('❌ 提取输出金额失败:', error);
      return {};
    }
  }

  /**
   * 检查用户是否已注册
   */
  async checkUserRegistration(): Promise<boolean> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      // 检查用户在Jumbo合约中的存储
      const result = await this.account.viewFunction({
        contractId: this.contractId,
        methodName: 'storage_balance_of',
        args: { account_id: this.accountId }
      });

      return result !== null;
    } catch (error) {
      console.error('❌ 检查用户注册状态失败:', error);
      return false;
    }
  }

  /**
   * 注册用户到Jumbo
   */
  async registerUser(): Promise<JumboTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔄 注册用户到Jumbo: ${this.accountId}`);

      const result = await this.account.functionCall({
        contractId: this.contractId,
        methodName: 'storage_deposit',
        args: {
          account_id: this.accountId,
          registration_only: false
        },
        attachedDeposit: DEPOSIT_CONFIG.storageDeposit,
        gas: GAS_CONFIG.storageDeposit
      });

      console.log(`✅ 用户注册成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ 用户注册失败:', error);
      return {
        success: false,
        error: error.message || '用户注册失败'
      };
    }
  }

  /**
   * 检查账户余额
   */
  async checkAccountBalance(): Promise<{ balance: string; balanceNear: number }> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      const accountState = await this.account.state();
      const balanceYocto = accountState.amount;
      const balanceNear = parseFloat(balanceYocto) / Math.pow(10, 24);

      return {
        balance: balanceYocto,
        balanceNear: balanceNear
      };
    } catch (error) {
      console.error('❌ 获取账户余额失败:', error);
      throw error;
    }
  }
}

export default JumboExecutionService;
