/**
 * REF-Jumbo 交易对配置
 * 基于现有 REF-VEAX 系统的配置结构
 *
 * 🔧 添加新交易对的完整流程：
 * ================================
 *
 * 1️⃣ 【必须】在本文件 TRADING_PAIRS 数组中添加新交易对配置
 * 2️⃣ 【必须】在 ref-jumbo/services/jumboQuoteService.ts 的 getTokenDecimals() 方法中添加代币精度
 * 3️⃣ 【必须】在 reusable-modules/jumbo-exchange/config.ts 中添加：
 *    - JUMBO_TARGET_POOLS 数组中添加池子配置
 *    - TOKEN_ADDRESSES 中添加代币地址映射
 *    - TOKEN_DECIMALS 中添加代币精度
 * 4️⃣ 【可选】如果需要在 REF Finance 中交易，在 REF_CONFIG.tokenMapping 中添加映射
 *
 * 📋 需要的信息：
 * - 代币合约地址 (如: f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near)
 * - Jumbo Pool ID (如: 3)
 * - 代币符号 (如: OCT)
 * - 代币精度 (通常: NEAR生态=24位, 桥接USDT/USDC=6位, 其他=18位)
 * - 最小流动性要求 (用于风险控制)
 *
 * 💡 提示：
 * - 可以通过 Jumbo Exchange API 查询池子信息: https://price-service.jumbo.exchange/pools
 * - 可以通过 NEAR RPC 查询代币精度: ft_metadata 方法
 * - 建议先设置 enabled: false 进行测试，确认无误后再启用
 */

import { TradingPairConfig } from '../types';

// ============================================================================
// 交易对配置
// ============================================================================

/**
 * REF-Jumbo 支持的交易对列表
 *
 * 🔧 添加新交易对示例：
 * {
 *   id: 'NEAR-NEWTOKEN',                    // 交易对唯一标识
 *   tokenA: {                              // 基础代币 (通常是 NEAR)
 *     id: 'wrap.near',
 *     symbol: 'NEAR',
 *     decimals: 24
 *   },
 *   tokenB: {                              // 报价代币 (新代币)
 *     id: 'newtoken.contract.near',         // 代币合约地址
 *     symbol: 'NEWTOKEN',                   // 代币符号
 *     decimals: 18                          // 代币精度
 *   },
 *   tradeAmount: '4',                       // 测试交易金额 (NEAR)
 *   enabled: true,                          // 是否启用监控
 *   jumboPoolId: 999                        // Jumbo Exchange 池子ID
 * }
 */
export const TRADING_PAIRS: TradingPairConfig[] = [
    {
    id: 'NEAR-AURORA',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
      symbol: 'AURORA',
      decimals: 18
    },
    tradeAmount: '4', // 4 NEAR
    enabled: true,
    jumboPoolId: 0
  },
  {
    id: 'NEAR-USDT.e',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
      symbol: 'USDT.e',
      decimals: 6
    },
    tradeAmount: '4', // 1 NEAR
    enabled: true,
    jumboPoolId: 1
  },
  {
    id: 'NEAR-LINEAR',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'linear-protocol.near',
      symbol: 'LINEAR',
      decimals: 24
    },
    tradeAmount: '14', // 1 NEAR
    enabled: true,
    jumboPoolId: 231
  },
  {
    id: 'NEAR-HAPI',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near',
      symbol: 'HAPI',
      decimals: 18
    },
    tradeAmount: '6', // 1 NEAR
    enabled: true,
    jumboPoolId: 6
  },
  {
    id: 'NEAR-UMINT',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'e99de844ef3ef72806cf006224ef3b813e82662f.factory.bridge.near',
      symbol: 'UMINT',
      decimals: 6
    },
    tradeAmount: '4', // 1 NEAR
    enabled: true,
    jumboPoolId: 184
  },
  {
    id: 'NEAR-CHICA',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'token.bocachica_mars.near',
      symbol: 'CHICA',
      decimals: 18
    },
    tradeAmount: '4', // 1 NEAR
    enabled: true,
    jumboPoolId: 274
  },
  {
    id: 'NEAR-1MIL',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'a4ef4b0b23c1fc81d3f9ecf93510e64f58a4a016.factory.bridge.near',
      symbol: '1MIL',
      decimals: 18
    },
    tradeAmount: '4', // 1 NEAR
    enabled: true,
    jumboPoolId: 294
  },
  {
    id: 'NEAR-NearX',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'v2-nearx.stader-labs.near',
      symbol: 'NearX',
      decimals: 24
    },
    tradeAmount: '4', // 1 NEAR
    enabled: false, // 暂时禁用，因为注册费用较高
    jumboPoolId: 266
  },
  {
    id: 'NEAR-OCT',
    tokenA: {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    },
    tokenB: {
      id: 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near',
      symbol: 'OCT',
      decimals: 18 // 桥接代币通常使用 18 位小数
    },
    tradeAmount: '5', // 1 NEAR
    enabled: true,
    jumboPoolId: 3
  }
];

// ============================================================================
// 套利配置
// ============================================================================

/**
 * 套利机器人配置
 */
export const ARBITRAGE_CONFIG = {
  // 监控配置
  monitoringInterval: 3000, // 5秒监控间隔
  statusDisplayInterval: 20, // 每20次检查显示一次状态
  
  // 利润阈值
  minProfitThreshold: 0.015, // 最小利润阈值 (NEAR) - 提高到 0.05
  
  // 调试配置
  debugMode: false, // 调试模式
  verboseLogging: false, // 详细日志
  
  // 风险管理
  maxConcurrentTrades: 1, // 最大并发交易数
  emergencyStopLoss: 0.5, // 紧急止损阈值 (NEAR)
  
  // 交易配置
  defaultSlippage: 0.005, // 默认滑点 1%
  maxSlippage: 0.01, // 最大滑点 5%
  
  // 余额管理
  minNearBalance: 1.0, // 最小 NEAR 余额保护
  autoWrapThreshold: 0.5, // 自动包装阈值

  // 自动余额管理配置
  autoBalanceManagement: {
    enabled: true,            // 是否启用自动余额管理
    checkInterval: 30 * 60 * 1000,  // 检查间隔：30分钟
    minNearBalance: 1.0,      // 最小NEAR余额阈值（低于此值时自动解包）
    unwrapAmount: 1.0,        // 自动解包数量
    reserveAmount: 5.5        // 预留wNEAR数量（保留用于交易，不会被解包）
  }
};

// ============================================================================
// 管理器类
// ============================================================================

/**
 * 交易对管理器
 */
export class TradingPairManager {
  /**
   * 获取所有启用的交易对
   */
  getEnabledPairs(): TradingPairConfig[] {
    return TRADING_PAIRS.filter(pair => pair.enabled);
  }

  /**
   * 获取所有交易对
   */
  getAllPairs(): TradingPairConfig[] {
    return TRADING_PAIRS;
  }

  /**
   * 根据ID获取交易对
   */
  getPairById(id: string): TradingPairConfig | undefined {
    return TRADING_PAIRS.find(pair => pair.id === id);
  }

  /**
   * 获取套利配置
   */
  getArbitrageConfig() {
    return ARBITRAGE_CONFIG;
  }

  /**
   * 启用交易对
   */
  enablePair(id: string): boolean {
    const pair = this.getPairById(id);
    if (pair) {
      pair.enabled = true;
      return true;
    }
    return false;
  }

  /**
   * 禁用交易对
   */
  disablePair(id: string): boolean {
    const pair = this.getPairById(id);
    if (pair) {
      pair.enabled = false;
      return true;
    }
    return false;
  }

  /**
   * 更新交易金额
   */
  updateTradeAmount(id: string, amount: string): boolean {
    const pair = this.getPairById(id);
    if (pair) {
      pair.tradeAmount = amount;
      return true;
    }
    return false;
  }

  /**
   * 获取配置摘要
   */
  getConfigSummary() {
    const enabledPairs = this.getEnabledPairs();
    return {
      totalPairs: TRADING_PAIRS.length,
      enabledPairs: enabledPairs.length,
      disabledPairs: TRADING_PAIRS.length - enabledPairs.length,
      pairs: enabledPairs.map(pair => ({
        id: pair.id,
        tradeAmount: pair.tradeAmount,
        jumboPoolId: pair.jumboPoolId
      })),
      config: ARBITRAGE_CONFIG
    };
  }
}

// ============================================================================
// 导出单例
// ============================================================================

export const tradingPairManager = new TradingPairManager();

// 默认导出
export default {
  TRADING_PAIRS,
  ARBITRAGE_CONFIG,
  tradingPairManager
};
