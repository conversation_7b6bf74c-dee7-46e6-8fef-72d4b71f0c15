/**
 * REF-Jumbo 套利系统类型定义
 * 基于现有 REF-VEAX 系统的类型结构
 */

// ============================================================================
// 基础类型 (与 REF-VEAX 保持一致)
// ============================================================================

/** 套利方向 */
export type ArbitrageDirection = 'REF_TO_JUMBO' | 'JUMBO_TO_REF';

/** 交易结果 */
export interface TradeResult {
  success: boolean;
  txHash?: string;
  outputAmount?: string;
  outputAmountWei?: string;
  error?: string;
}

/** 简化的套利机会 (用于机器人) */
export interface SimpleArbitrageOpportunity {
  direction: ArbitrageDirection;
  pair: TradingPairConfig;
  inputAmount: string;
  intermediateAmount: string;
  finalAmount: string;
  profit: number;
}

/** 交易对配置 (与 REF-VEAX 系统保持一致) */
export interface TradingPairConfig {
  id: string;
  tokenA: {
    id: string;
    symbol: string;
    decimals: number;
  };
  tokenB: {
    id: string;
    symbol: string;
    decimals: number;
  };
  tradeAmount: string;
  enabled: boolean;
  jumboPoolId: number;
}

// ============================================================================
// 价格相关类型
// ============================================================================

/** 价格信息 */
export interface PriceInfo {
  exchange: ExchangeType;
  pair: string;
  price: number;
  amountIn: string;
  amountOut: string;
  amountOutWei: string;
  timestamp: number;
  method: string;
  success: boolean;
  error?: string;
}

/** 价格比较结果 */
export interface PriceComparison {
  pair: string;
  refPrice: PriceInfo;
  jumboPrice: PriceInfo;
  priceDifference: number; // 百分比
  priceDifferenceAbs: number; // 绝对值
  bestExchange: ExchangeType;
  worstExchange: ExchangeType;
  timestamp: number;
}

// ============================================================================
// 套利相关类型
// ============================================================================

/** 套利机会 */
export interface ArbitrageOpportunity {
  pair: string;
  direction: ArbitrageDirection;
  buyExchange: ExchangeType;
  sellExchange: ExchangeType;
  buyPrice: number;
  sellPrice: number;
  priceDifference: number;
  expectedProfit: number;
  expectedProfitPercent: number;
  amountIn: string;
  estimatedAmountOut: string;
  timestamp: number;
  isExecutable: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
}

/** 套利执行请求 */
export interface ArbitrageExecutionRequest {
  opportunity: ArbitrageOpportunity;
  maxSlippage: number;
  minProfitThreshold: number;
  dryRun?: boolean;
}

/** 套利执行结果 */
export interface ArbitrageExecutionResult {
  success: boolean;
  opportunity: ArbitrageOpportunity;
  
  // 第一步交易 (买入)
  firstTrade?: {
    exchange: ExchangeType;
    transactionHash?: string;
    inputAmount: string;
    outputAmount: string;
    outputAmountWei: string;
    actualPrice: number;
    gasUsed?: string;
  };
  
  // 第二步交易 (卖出)
  secondTrade?: {
    exchange: ExchangeType;
    transactionHash?: string;
    inputAmount: string;
    outputAmount: string;
    outputAmountWei: string;
    actualPrice: number;
    gasUsed?: string;
  };
  
  // 总体结果
  totalProfit?: number;
  totalProfitPercent?: number;
  totalGasCost?: string;
  executionTime?: number;
  
  error?: string;
  timestamp: number;
}

// ============================================================================
// 监控相关类型
// ============================================================================

/** 监控配置 */
export interface MonitorConfig {
  pairs: TradingPair[];
  monitorInterval: number; // 毫秒
  priceThreshold: number; // 价格差异阈值 (%)
  minProfitThreshold: number; // 最小利润阈值 (NEAR)
  testAmount: string; // 测试金额
  maxSlippage: number; // 最大滑点 (%)
  isActive: boolean;
}

/** 监控状态 */
export interface MonitorStatus {
  isRunning: boolean;
  startTime: number;
  lastUpdateTime: number;
  totalChecks: number;
  opportunitiesFound: number;
  executedTrades: number;
  totalProfit: number;
  errors: number;
}

/** 监控事件 */
export interface MonitorEvent {
  type: 'PRICE_UPDATE' | 'OPPORTUNITY_FOUND' | 'TRADE_EXECUTED' | 'ERROR';
  timestamp: number;
  data: any;
  message: string;
}

// ============================================================================
// 风险管理类型
// ============================================================================

/** 风险评估结果 */
export interface RiskAssessment {
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  factors: {
    priceVolatility: number;
    liquidityRisk: number;
    slippageRisk: number;
    gasRisk: number;
    balanceRisk: number;
  };
  recommendations: string[];
  isExecutable: boolean;
}

/** 余额信息 */
export interface BalanceInfo {
  near: string;
  wNear: string;
  tokens: { [tokenAddress: string]: string };
  lastUpdated: number;
}

// ============================================================================
// 配置类型
// ============================================================================

/** REF-Jumbo 系统配置 */
export interface RefJumboConfig {
  monitor: MonitorConfig;
  risk: {
    maxTradeAmount: string; // 最大单次交易金额
    minBalanceThreshold: string; // 最小余额保护
    maxDailyTrades: number; // 每日最大交易次数
    cooldownPeriod: number; // 交易冷却期 (毫秒)
  };
  execution: {
    defaultGas: string;
    retryAttempts: number;
    retryDelay: number;
    timeoutMs: number;
  };
  logging: {
    level: 'DEBUG' | 'INFO' | 'WARN' | 'ERROR';
    enableFileLogging: boolean;
    logRetentionDays: number;
  };
}

// ============================================================================
// 错误类型
// ============================================================================

/** REF-Jumbo 错误类型 */
export enum RefJumboErrorType {
  PRICE_FETCH_ERROR = 'PRICE_FETCH_ERROR',
  ARBITRAGE_EXECUTION_ERROR = 'ARBITRAGE_EXECUTION_ERROR',
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  SLIPPAGE_TOO_HIGH = 'SLIPPAGE_TOO_HIGH',
  NETWORK_ERROR = 'NETWORK_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  RISK_TOO_HIGH = 'RISK_TOO_HIGH'
}

/** REF-Jumbo 自定义错误 */
export class RefJumboError extends Error {
  constructor(
    public type: RefJumboErrorType,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'RefJumboError';
  }
}

// ============================================================================
// 服务接口
// ============================================================================

/** 价格监控服务接口 */
export interface IPriceMonitor {
  startMonitoring(): Promise<void>;
  stopMonitoring(): void;
  getCurrentPrices(): Promise<PriceComparison[]>;
  getStatus(): MonitorStatus;
}

/** 套利检测器接口 */
export interface IArbitrageDetector {
  detectOpportunities(priceComparisons: PriceComparison[]): ArbitrageOpportunity[];
  evaluateOpportunity(opportunity: ArbitrageOpportunity): Promise<RiskAssessment>;
}

/** 套利执行器接口 */
export interface IArbitrageExecutor {
  executeArbitrage(request: ArbitrageExecutionRequest): Promise<ArbitrageExecutionResult>;
  estimateGasCost(opportunity: ArbitrageOpportunity): Promise<string>;
}

/** 风险管理器接口 */
export interface IRiskManager {
  assessRisk(opportunity: ArbitrageOpportunity): Promise<RiskAssessment>;
  checkBalance(): Promise<BalanceInfo>;
  isExecutionAllowed(opportunity: ArbitrageOpportunity): Promise<boolean>;
}
