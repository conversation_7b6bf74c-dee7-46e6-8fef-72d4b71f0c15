/**
 * REF-Jumbo 套利机器人主程序
 * 
 * 基于现有 REF-VEAX 系统架构，实现 REF Finance 和 Jumbo Exchange 之间的套利
 * 
 * 功能：
 * 1. 监控模块：每5秒获取REF和Jumbo报价，检测套利机会
 * 2. 交易执行模块：自动执行套利交易
 * 3. 风险管理：处理交易失败情况
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../.env') });
import { refQuoteService } from '../src/services/refQuoteService';
import JumboQuoteService from './services/jumboQuoteService';
import RefExecutionServiceCorrect from '../src/services/refExecutionServiceCorrect';
import JumboExecutionService from './services/jumboExecutionService';
import NearWrapService from '../src/services/nearWrapService';
import { AutoBalanceManager } from '../src/services/autoBalanceManager';
import { tradingPairManager } from './config/tradingPairs';
import { TradingPairConfig } from './types';
import { SimpleArbitrageOpportunity, TradeResult } from './types';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import { Account, connect, keyStores } from 'near-api-js';

/**
 * REF-Jumbo 套利机器人主类
 */
class RefJumboArbitrageBot {
  private isRunning: boolean = false;
  private monitoringInterval: NodeJS.Timeout | null = null;

  // 执行锁机制
  private isExecutingTrade: boolean = false;
  private pendingOpportunities: SimpleArbitrageOpportunity[] = [];

  // 执行服务
  private refExecutionService: RefExecutionServiceCorrect;
  private jumboExecutionService: JumboExecutionService;
  private nearWrapService: NearWrapService;
  private autoBalanceManager: AutoBalanceManager;

  // 从配置文件获取参数
  private readonly tradingPairs: TradingPairConfig[];
  private readonly arbitrageConfig;
  
  // 统计数据
  private stats = {
    totalChecks: 0,
    opportunitiesFound: 0,
    successfulTrades: 0,
    failedTrades: 0,
    totalProfit: 0,
    startTime: 0
  };

  constructor() {
    // 从配置文件获取交易对和套利配置
    this.tradingPairs = tradingPairManager.getEnabledPairs();
    this.arbitrageConfig = tradingPairManager.getArbitrageConfig();

    // 初始化执行服务
    this.refExecutionService = new RefExecutionServiceCorrect(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    this.jumboExecutionService = new JumboExecutionService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    this.nearWrapService = new NearWrapService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    this.autoBalanceManager = new AutoBalanceManager(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      this.arbitrageConfig.autoBalanceManagement, // 传递配置对象
      () => this.isExecutingTrade, // 传递执行状态检查回调
      'mainnet'
    );
  }

  /**
   * 启动套利机器人
   */
  async start(): Promise<void> {
    console.log('🚀 启动 REF-Jumbo 套利机器人');
    console.log(`📊 监控交易对: ${this.tradingPairs.length} 个`);
    console.log(`💰 最小利润阈值: ${this.arbitrageConfig.minProfitThreshold} NEAR`);
    console.log(`⏱️ 监控间隔: ${this.arbitrageConfig.monitoringInterval / 1000} 秒`);

    console.log('='.repeat(60));

    // 初始化执行服务
    console.log('🔧 初始化执行服务...');
    await this.refExecutionService.initialize();
    await this.jumboExecutionService.initialize();
    await this.nearWrapService.initialize();
    await this.autoBalanceManager.initialize();

    // 初始化 Jumbo 报价服务
    const account = await this.initializeAccount();
    await JumboQuoteService.initialize(account);

    // 🔧 关键：启动时检查所有注册状态
    console.log('🔍 检查DApp和代币注册状态...');
    await this.checkAllRegistrations();

    this.isRunning = true;
    this.stats.startTime = Date.now();

    // 启动自动余额管理
    this.autoBalanceManager.start();

    // 启动监控循环
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.monitoringCycle();
      } catch (error) {
        console.error('❌ 监控循环错误:', error);
      }
    }, this.arbitrageConfig.monitoringInterval);

    console.log('✅ REF-Jumbo 套利机器人已启动，开始监控...\n');
  }

  /**
   * 🔧 检查所有DApp和代币注册状态
   * 在程序启动时确保所有必要的注册都已完成，避免交易时出现问题
   */
  private async checkAllRegistrations(): Promise<void> {
    try {
      // 1. 检查REF Finance DApp注册
      console.log('📋 检查REF Finance DApp注册...');
      await this.checkRefFinanceRegistration();

      // 2. 检查Jumbo Exchange DApp注册
      console.log('📋 检查Jumbo Exchange DApp注册...');
      await this.checkJumboExchangeRegistration();

      // 3. 检查所有配置代币的注册状态
      console.log('📋 检查所有配置代币注册...');
      await this.checkAllTokenRegistrations();

      console.log('✅ 所有注册检查完成');
    } catch (error) {
      console.error('❌ 注册检查失败:', error);
      throw error;
    }
  }

  /**
   * 检查REF Finance DApp注册
   */
  private async checkRefFinanceRegistration(): Promise<void> {
    try {
      const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;
      const account = await this.initializeAccount();

      const result = await account.viewFunction({
        contractId: 'v2.ref-finance.near',
        methodName: 'get_user_storage_state',
        args: { account_id: accountId }
      });

      if (result) {
        console.log('✅ REF Finance DApp已注册');
      } else {
        console.log('⚠️ REF Finance DApp未注册，正在注册...');
        await account.functionCall({
          contractId: 'v2.ref-finance.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000' // 0.125 NEAR
        });
        console.log('✅ REF Finance DApp注册成功');
      }
    } catch (error: any) {
      if (error.message?.includes('not registered')) {
        console.log('⚠️ REF Finance DApp未注册，正在注册...');
        const account = await this.initializeAccount();
        await account.functionCall({
          contractId: 'v2.ref-finance.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000'
        });
        console.log('✅ REF Finance DApp注册成功');
      } else {
        console.warn('⚠️ REF Finance注册检查失败:', error.message);
      }
    }
  }

  /**
   * 检查Jumbo Exchange DApp注册
   */
  private async checkJumboExchangeRegistration(): Promise<void> {
    try {
      const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;
      const account = await this.initializeAccount();

      // 检查账户是否在Jumbo Exchange中注册
      const result = await account.viewFunction({
        contractId: 'v1.jumbo_exchange.near',
        methodName: 'get_user_storage_state',
        args: { account_id: accountId }
      });

      if (result) {
        console.log('✅ Jumbo Exchange DApp已注册');
      } else {
        console.log('⚠️ Jumbo Exchange DApp未注册，正在注册...');
        await account.functionCall({
          contractId: 'v1.jumbo_exchange.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000' // 0.125 NEAR
        });
        console.log('✅ Jumbo Exchange DApp注册成功');
      }
    } catch (error: any) {
      if (error.message?.includes('not registered')) {
        console.log('⚠️ Jumbo Exchange DApp未注册，正在注册...');
        const account = await this.initializeAccount();
        await account.functionCall({
          contractId: 'v1.jumbo_exchange.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000'
        });
        console.log('✅ Jumbo Exchange DApp注册成功');
      } else {
        console.warn('⚠️ Jumbo Exchange注册检查失败:', error.message);
      }
    }
  }

  /**
   * 检查所有配置代币的注册状态
   */
  private async checkAllTokenRegistrations(): Promise<void> {
    // 收集所有需要检查的代币
    const allTokens = new Set<string>();

    this.tradingPairs.forEach(pair => {
      allTokens.add(pair.tokenA.id);
      allTokens.add(pair.tokenB.id);
    });

    console.log(`📊 需要检查 ${allTokens.size} 个代币的注册状态`);

    let registeredCount = 0;
    let newRegistrations = 0;

    for (const tokenId of allTokens) {
      try {
        const isRegistered = await this.isTokenRegistered(tokenId);

        if (isRegistered) {
          console.log(`✅ ${tokenId} 已注册`);
          registeredCount++;
        } else {
          console.log(`⚠️ ${tokenId} 未注册，正在注册...`);
          await this.registerToken(tokenId);
          console.log(`✅ ${tokenId} 注册成功`);
          newRegistrations++;
        }
      } catch (error: any) {
        console.error(`❌ 检查/注册代币 ${tokenId} 失败:`, error.message);
        throw new Error(`代币注册失败: ${tokenId}`);
      }
    }

    console.log(`✅ 代币注册检查完成: ${registeredCount} 个已注册, ${newRegistrations} 个新注册`);
  }

  /**
   * 检查代币是否已注册
   */
  private async isTokenRegistered(tokenId: string): Promise<boolean> {
    try {
      const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;
      const account = await this.initializeAccount();

      const result = await account.viewFunction({
        contractId: tokenId,
        methodName: 'storage_balance_of',
        args: { account_id: accountId }
      });

      return result !== null && result !== undefined;
    } catch (error: any) {
      console.log(`❌ ${tokenId} 注册检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 注册代币
   */
  private async registerToken(tokenId: string): Promise<void> {
    try {
      const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;
      const account = await this.initializeAccount();

      await account.functionCall({
        contractId: tokenId,
        methodName: 'storage_deposit',
        args: { account_id: accountId },
        attachedDeposit: '1250000000000000000000', // 0.00125 NEAR
        gas: '**************' // 30 TGas
      });
    } catch (error: any) {
      console.error(`❌ 注册代币 ${tokenId} 失败:`, error.message);
      throw error;
    }
  }

  /**
   * 停止套利机器人
   */
  async stop(): Promise<void> {
    console.log('\n🛑 停止 REF-Jumbo 套利机器人...');
    
    this.isRunning = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    // 停止自动余额管理
    this.autoBalanceManager.stop();

    // 显示统计信息
    const runTime = (Date.now() - this.stats.startTime) / 1000;
    console.log('\n📊 运行统计:');
    console.log(`⏱️ 运行时间: ${Math.floor(runTime)} 秒`);
    console.log(`🔍 总检查次数: ${this.stats.totalChecks}`);
    console.log(`💡 发现机会: ${this.stats.opportunitiesFound}`);
    console.log(`✅ 成功交易: ${this.stats.successfulTrades}`);
    console.log(`❌ 失败交易: ${this.stats.failedTrades}`);
    console.log(`💰 总利润: ${this.stats.totalProfit.toFixed(4)} NEAR`);

    console.log('\n✅ REF-Jumbo 套利机器人已停止');
  }

  /**
   * 初始化 NEAR 账户
   */
  private async initializeAccount(): Promise<Account> {
    const keyStore = new keyStores.InMemoryKeyStore();
    const keyPair = require('near-api-js').utils.KeyPair.fromString(
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!
    );
    await keyStore.setKey('mainnet', process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!, keyPair);

    // 🔧 修复：使用环境变量配置的RPC URL
    const rpcUrl = process.env.NEAR_RPC_URL || 'https://free.rpc.fastnear.com';

    const config = {
      networkId: 'mainnet',
      nodeUrl: rpcUrl,
      keyStore
    };

    const near = await connect(config);
    return await near.account(process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!);
  }

  /**
   * 监控周期
   */
  private async monitoringCycle(): Promise<void> {
    this.stats.totalChecks++;

    try {
      // 如果正在执行交易，跳过本次扫描
      if (this.isExecutingTrade) {
        console.log('🔒 正在执行交易，跳过本次扫描');
        return;
      }

      // 定期显示状态
      if (this.stats.totalChecks % this.arbitrageConfig.statusDisplayInterval === 0) {
        const runTime = (Date.now() - this.stats.startTime) / 1000;
        console.log(`⏱️ 监控中... 已运行${Math.floor(runTime)}秒，检查${this.stats.totalChecks}次，发现${this.stats.opportunitiesFound}个机会`);
      }

      // 清空上次的机会列表
      this.pendingOpportunities = [];

      // 并行检查所有启用的交易对
      const checkPromises = this.tradingPairs.map(pair =>
        this.checkPair(pair)
      );

      await Promise.allSettled(checkPromises);

      // 如果发现多个套利机会，选择最大的执行
      if (this.pendingOpportunities.length > 0) {
        const bestOpportunity = this.selectBestOpportunity();
        await this.executeArbitrage(bestOpportunity);
      }

    } catch (error) {
      console.error('❌ 监控周期错误:', error);
    }
  }

  /**
   * 检查单个交易对的套利机会 - 全程使用wei格式，避免精度损失
   */
  private async checkPair(pair: TradingPairConfig): Promise<void> {
    try {
      // 🔧 关键修复：全程使用wei格式，避免人类可读格式转换

      // 第一步：获取 tokenA → tokenB 的报价，立即转换为wei格式
      const [refStep1QuoteHuman, jumboStep1QuoteHuman] = await Promise.all([
        this.getREFQuote(pair.tokenA, pair.tokenB, pair.tradeAmount),    // REF: NEAR → HAPI
        this.getJumboQuote(pair.tokenA, pair.tokenB, pair.tradeAmount)   // Jumbo: NEAR → HAPI
      ]);

      if (!refStep1QuoteHuman || !jumboStep1QuoteHuman) {
        if (this.arbitrageConfig.debugMode) {
          console.log(`⚠️ ${pair.id} 第一步报价获取失败: REF=${refStep1QuoteHuman ? '✅' : '❌'}, Jumbo=${jumboStep1QuoteHuman ? '✅' : '❌'}`);
        }
        return;
      }

      // 🔧 关键：立即转换为wei格式，避免后续精度损失
      const refStep1QuoteWei = this.toWei(refStep1QuoteHuman, pair.tokenB.decimals);
      const jumboStep1QuoteWei = this.toWei(jumboStep1QuoteHuman, pair.tokenB.decimals);

      // 第二步：使用wei格式作为输入，获取反向报价
      // 🔧 先转换wei为人类可读格式，然后获取报价
      const refStep1QuoteForJumbo = this.fromWei(refStep1QuoteWei, pair.tokenB.decimals);
      const jumboStep1QuoteForREF = this.fromWei(jumboStep1QuoteWei, pair.tokenB.decimals);

      const [refToJumboStep2QuoteHuman, jumboToRefStep2QuoteHuman] = await Promise.all([
        this.getJumboQuote(pair.tokenB, pair.tokenA, refStep1QuoteForJumbo),     // REF输出 → Jumbo反向
        this.getREFQuote(pair.tokenB, pair.tokenA, jumboStep1QuoteForREF)        // Jumbo输出 → REF反向
      ]);

      if (!refToJumboStep2QuoteHuman || !jumboToRefStep2QuoteHuman) {
        if (this.arbitrageConfig.debugMode) {
          console.log(`⚠️ ${pair.id} 第二步报价获取失败: REF→Jumbo=${refToJumboStep2QuoteHuman ? '✅' : '❌'}, Jumbo→REF=${jumboToRefStep2QuoteHuman ? '✅' : '❌'}`);
        }
        return;
      }

      // 计算两个方向的利润
      // REF→Jumbo: 最终输出 - 初始输入
      const refToJumboProfit = parseFloat(refToJumboStep2QuoteHuman) - parseFloat(pair.tradeAmount);
      // Jumbo→REF: 最终输出 - 初始输入
      const jumboToRefProfit = parseFloat(jumboToRefStep2QuoteHuman) - parseFloat(pair.tradeAmount);



      // 根据配置决定是否显示日志
      const shouldShowLogs = this.arbitrageConfig.debugMode ||
                             this.arbitrageConfig.verboseLogging ||
                             refToJumboProfit >= this.arbitrageConfig.minProfitThreshold * 0.8 ||
                             jumboToRefProfit >= this.arbitrageConfig.minProfitThreshold * 0.8;

      if (shouldShowLogs) {
        // 计算利润率（用于显示）
        const refToJumboProfitRate = (refToJumboProfit / parseFloat(pair.tradeAmount)) * 100;
        const jumboToRefProfitRate = (jumboToRefProfit / parseFloat(pair.tradeAmount)) * 100;

        console.log(`📊 ${pair.id}:`);
        console.log(`   REF→Jumbo: ${pair.tradeAmount}-${refStep1QuoteHuman}-${refToJumboStep2QuoteHuman} (利润: ${refToJumboProfit.toFixed(4)} NEAR, ${refToJumboProfitRate.toFixed(2)}%)`);
        console.log(`   Jumbo→REF: ${pair.tradeAmount}-${jumboStep1QuoteHuman}-${jumboToRefStep2QuoteHuman} (利润: ${jumboToRefProfit.toFixed(4)} NEAR, ${jumboToRefProfitRate.toFixed(2)}%)`);
      }

      // 检查是否有套利机会
      if (refToJumboProfit >= this.arbitrageConfig.minProfitThreshold) {
        console.log(`💰 发现套利机会! ${pair.id} REF→Jumbo 利润: ${refToJumboProfit.toFixed(4)} NEAR`);

        const opportunity: SimpleArbitrageOpportunity = {
          direction: 'REF_TO_JUMBO',
          pair,
          inputAmount: pair.tradeAmount,
          intermediateAmount: refStep1QuoteHuman,
          finalAmount: refToJumboStep2QuoteHuman,
          profit: refToJumboProfit
        };

        this.pendingOpportunities.push(opportunity);
      }

      if (jumboToRefProfit >= this.arbitrageConfig.minProfitThreshold) {
        console.log(`💰 发现套利机会! ${pair.id} Jumbo→REF 利润: ${jumboToRefProfit.toFixed(4)} NEAR`);

        const opportunity: SimpleArbitrageOpportunity = {
          direction: 'JUMBO_TO_REF',
          pair,
          inputAmount: pair.tradeAmount,
          intermediateAmount: jumboStep1QuoteHuman,
          finalAmount: jumboToRefStep2QuoteHuman,
          profit: jumboToRefProfit
        };

        this.pendingOpportunities.push(opportunity);
      }

    } catch (error) {
      // 调试模式下显示错误信息
      if (this.arbitrageConfig.debugMode) {
        console.error(`❌ 检查${pair.id}失败:`, error);
      }
    }
  }

  /**
   * 获取REF报价
   */
  private async getREFQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
    try {
      const result = await refQuoteService.getQuote({
        tokenIn,
        tokenOut,
        amountIn: amount,
        slippage: 0.005
      });
      return result.outputAmount;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取Jumbo报价
   */
  private async getJumboQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
    try {
      // 验证和标准化输入参数
      const normalizedAmount = this.normalizeAmount(amount);
      if (!normalizedAmount) {
        console.warn(`⚠️ Jumbo报价输入无效: ${amount}`);
        return null;
      }

      const result = await JumboQuoteService.getQuote(tokenIn.id, tokenOut.id, normalizedAmount);

      if (!result.success) {
        return null;
      }

      // 验证Jumbo返回的数值
      if (!this.isValidNumber(result.outputAmount)) {
        console.warn(`⚠️ Jumbo返回无效数值: ${result.outputAmount}`);
        return null;
      }

      return result.outputAmount;
    } catch (error) {
      console.error(`❌ Jumbo报价错误:`, error);
      return null;
    }
  }

  /**
   * 标准化金额格式
   */
  private normalizeAmount(amount: string): string | null {
    try {
      // 验证是否为有效数字
      const numAmount = parseFloat(amount);
      if (isNaN(numAmount) || numAmount <= 0) {
        return null;
      }

      // 如果包含小数点，需要根据数字大小判断处理方式
      if (amount.includes('.')) {
        // 如果是非常大的数字（如 1657875994766194.226361），
        // 这可能是 wei 格式的代币金额，保留整数部分
        if (numAmount > 1e12) {
          return Math.floor(numAmount).toString();
        }

        // 如果是较小的数字（如 86192.69028233412160243），
        // 这可能是人类可读格式，保持原样
        return amount;
      }

      // 如果没有小数点，直接返回
      return amount;
    } catch (error) {
      return null;
    }
  }

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 选择最佳套利机会
   */
  private selectBestOpportunity(): SimpleArbitrageOpportunity {
    // 按利润从大到小排序，选择最大的
    const sorted = this.pendingOpportunities.sort((a, b) => b.profit - a.profit);
    const best = sorted[0];

    console.log(`🎯 选择最佳套利机会: ${best.pair.id} ${best.direction} 利润: ${best.profit.toFixed(4)} NEAR`);
    if (sorted.length > 1) {
      console.log(`   跳过其他 ${sorted.length - 1} 个机会`);
    }

    return best;
  }

  /**
   * 执行套利交易（带执行锁）
   */
  private async executeArbitrage(opportunity: SimpleArbitrageOpportunity): Promise<void> {
    // 设置执行锁
    if (this.isExecutingTrade) {
      console.log(`⚠️ 已有交易在执行中，跳过套利机会`);
      return;
    }

    this.isExecutingTrade = true;
    console.log(`🔒 获取执行锁，开始执行套利交易 (${opportunity.direction})`);
    console.log(`   预期利润: ${opportunity.profit.toFixed(4)} NEAR`);

    this.stats.opportunitiesFound++;

    try {
      let step1Result: TradeResult;
      let step2Result: TradeResult;

      if (opportunity.direction === 'REF_TO_JUMBO') {
        // 第一步：REF交易 (tokenA -> tokenB)
        step1Result = await this.executeREFTrade(
          opportunity.pair.tokenA,
          opportunity.pair.tokenB,
          opportunity.inputAmount
        );

        if (!step1Result.success) {
          console.log(`❌ 第一步REF交易失败，放弃套利: ${step1Result.error}`);
          this.stats.failedTrades++;
          return;
        }

        console.log(`✅ 第一步REF交易成功: ${step1Result.outputAmount} ${opportunity.pair.tokenB.symbol}`);
        console.log(`🔄 立即执行第二步Jumbo交易...`);

        // 🔧 关键修复：使用wei格式传递，避免精度损失
        const step1OutputWei = step1Result.outputAmountWei || step1Result.outputAmount!;
        console.log(`📊 使用wei格式传递: ${step1OutputWei} wei`);

        // 第二步：Jumbo交易 (tokenB -> tokenA) - 立即执行，不等待
        step2Result = await this.executeJumboTrade(
          opportunity.pair.tokenB,
          opportunity.pair.tokenA,
          step1OutputWei // 🔧 使用wei格式
        );

        if (!step2Result.success) {
          console.log(`❌ 第二步Jumbo交易失败，启动风险管理: ${step2Result.error}`);
          await this.riskManagement(opportunity.pair.tokenB, opportunity.pair.tokenA, step1Result.outputAmountWei!);
          return;
        }

      } else {
        // JUMBO_TO_REF方向
        // 第一步：Jumbo交易 (tokenA -> tokenB)
        const inputAmountWei = parseNearAmount(opportunity.inputAmount) || '0';
        step1Result = await this.executeJumboTrade(
          opportunity.pair.tokenA,
          opportunity.pair.tokenB,
          inputAmountWei // 🔧 使用wei格式
        );

        if (!step1Result.success) {
          console.log(`❌ 第一步Jumbo交易失败，放弃套利: ${step1Result.error}`);
          this.stats.failedTrades++;
          return;
        }

        console.log(`✅ 第一步Jumbo交易成功: ${step1Result.outputAmount} ${opportunity.pair.tokenB.symbol}`);
        console.log(`🔄 立即执行第二步REF交易...`);

        // 使用wei格式传递，避免精度损失
        const step1OutputWei = step1Result.outputAmountWei || step1Result.outputAmount!;
        console.log(`📊 使用wei格式传递: ${step1OutputWei} wei`);

        // 第二步：REF交易 (tokenB -> tokenA)
        step2Result = await this.executeREFTrade(
          opportunity.pair.tokenB,
          opportunity.pair.tokenA,
          step1OutputWei
        );

        if (!step2Result.success) {
          console.log(`❌ 第二步REF交易失败，启动风险管理: ${step2Result.error}`);
          await this.riskManagement(opportunity.pair.tokenB, opportunity.pair.tokenA, step1Result.outputAmountWei!);
          return;
        }
      }

      // 计算实际利润
      const actualProfit = parseFloat(step2Result.outputAmount!) - parseFloat(opportunity.inputAmount);

      console.log(`🎉 套利交易成功完成!`);
      console.log(`   实际利润: ${actualProfit.toFixed(4)} NEAR`);

      this.stats.successfulTrades++;
      this.stats.totalProfit += actualProfit;

    } catch (error) {
      console.error(`❌ 套利执行错误:`, error);
      this.stats.failedTrades++;
    } finally {
      // 释放执行锁
      this.isExecutingTrade = false;
      console.log(`🔓 释放执行锁，继续监控`);
    }
  }

  /**
   * 执行REF交易
   */
  private async executeREFTrade(tokenIn: any, tokenOut: any, amount: string): Promise<TradeResult> {
    try {
      // 检查amount是否为wei格式，避免精度损失
      const isWeiFormat = /^\d+$/.test(amount) && parseFloat(amount) > 1000;
      let humanReadableAmount: string;
      let inputAmountWei: string;

      if (isWeiFormat) {
        // 如果是wei格式，使用精确的fromWei方法转换
        inputAmountWei = amount;
        humanReadableAmount = this.fromWei(amount, tokenIn.decimals);
        console.log(`🔄 检测到wei格式输入: ${amount} wei → ${humanReadableAmount} ${tokenIn.symbol}`);
      } else {
        // 如果是人类可读格式，转换为wei格式
        humanReadableAmount = amount;
        console.log(`📊 使用人类可读格式: ${humanReadableAmount} ${tokenIn.symbol}`);

        if (tokenIn.id === 'wrap.near') {
          inputAmountWei = parseNearAmount(humanReadableAmount) || '0';
        } else {
          inputAmountWei = this.toWei(humanReadableAmount, tokenIn.decimals);
        }
      }

      // 如果输入代币是wNEAR，检查并自动包装NEAR
      if (tokenIn.id === 'wrap.near') {
        console.log(`🔍 检查wNEAR余额，交易需要: ${humanReadableAmount} wNEAR`);

        const wrapResult = await this.nearWrapService.checkAndWrapNear(humanReadableAmount, 10);

        if (!wrapResult.success) {
          throw new Error(`自动包装失败: ${wrapResult.error}`);
        }

        if (wrapResult.wrapped) {
          console.log(`✅ 自动包装完成: ${wrapResult.amount} NEAR → wNEAR`);
        }
      }

      // 先获取报价（使用人类可读格式）
      const quote = await refQuoteService.getQuote({
        tokenIn,
        tokenOut,
        amountIn: humanReadableAmount,
        slippage: 0.005
      });

      if (!quote) {
        throw new Error('无法获取REF报价');
      }

      // 计算最小输出金额（考虑滑点）
      const minOutputAmount = (parseFloat(quote.outputAmount) * 0.995).toString(); // 1%滑点保护

      // 使用精确转换最小输出金额
      let minOutputAmountWei: string;
      if (tokenOut.id === 'wrap.near') {
        minOutputAmountWei = parseNearAmount(minOutputAmount) || '0';
      } else {
        minOutputAmountWei = this.toWei(minOutputAmount, tokenOut.decimals);
      }

      console.log(`💱 REF交易参数: ${humanReadableAmount} ${tokenIn.symbol} (${inputAmountWei} wei) → 最少 ${minOutputAmountWei} wei (${minOutputAmount} ${tokenOut.symbol})`);

      // 执行交易
      const result = await this.refExecutionService.executeSwap(
        quote,
        tokenIn.id,
        inputAmountWei,
        minOutputAmountWei,
        0.01,
        quote.poolId ? { poolId: quote.poolId, outputToken: tokenOut.id } : undefined
      );

      return {
        success: result.success,
        txHash: result.transactionHash,
        outputAmount: result.outputAmount,
        outputAmountWei: result.outputAmountWei,
        error: result.error
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 执行Jumbo交易 - 修复版本，正确处理wei格式
   */
  private async executeJumboTrade(tokenIn: any, tokenOut: any, amount: string): Promise<TradeResult> {
    try {
      console.log(`🚀 执行Jumbo交易: ${amount} wei ${tokenIn.symbol} → ${tokenOut.symbol}`);

      // 🔧 关键修复：amount已经是wei格式，直接使用
      const amountWei = amount;

      // 转换为人类可读格式用于显示和包装检查
      const humanReadableAmount = this.fromWei(amount, tokenIn.decimals);
      console.log(`📊 wei格式: ${amountWei} → 人类可读: ${humanReadableAmount} ${tokenIn.symbol}`);

      // 如果输入代币是wNEAR，检查并自动包装NEAR
      if (tokenIn.id === 'wrap.near') {
        console.log(`🔍 检查wNEAR余额，交易需要: ${humanReadableAmount} wNEAR`);

        const wrapResult = await this.nearWrapService.checkAndWrapNear(humanReadableAmount, 10);

        if (!wrapResult.success) {
          throw new Error(`自动包装失败: ${wrapResult.error}`);
        }

        if (wrapResult.wrapped) {
          console.log(`✅ 自动包装完成: ${wrapResult.amount} NEAR → wNEAR`);
        }
      }

      // 计算最小输出金额（wei格式）
      // 🔧 关键修复：执行前重新获取实时报价，避免价格变动
      console.log(`🔄 获取实时Jumbo报价...`);
      const quote = await JumboQuoteService.getQuote(tokenIn.id, tokenOut.id, humanReadableAmount);

      if (!quote.success) {
        throw new Error('无法获取Jumbo报价');
      }

      console.log(`📊 实时Jumbo报价: ${quote.outputAmount} ${tokenOut.symbol}`);

      // 将报价转换为wei格式并应用滑点保护
      const outputAmountWei = this.toWei(quote.outputAmount, tokenOut.decimals);
      const minOutputAmountWei = (BigInt(outputAmountWei) * BigInt(800) / BigInt(1000)).toString(); // 20%滑点保护 (极度宽松)

      console.log(`💱 Jumbo交易参数: ${amountWei} wei ${tokenIn.symbol} → 最少 ${minOutputAmountWei} wei ${tokenOut.symbol}`);

      // 🔧 关键修复：直接使用wei格式执行交易
      const result = await this.jumboExecutionService.executeSwap(
        tokenIn,
        tokenOut,
        amountWei,        // 🔧 输入wei格式
        minOutputAmountWei, // 🔧 最小输出wei格式
        0.005
      );

      return {
        success: result.success,
        txHash: result.txHash,
        outputAmount: result.outputAmount,
        outputAmountWei: result.outputAmountWei, // 🔧 关键：返回wei格式
        error: result.error
      };
    } catch (error: any) {
      console.error(`❌ Jumbo交易失败:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 风险管理 - 紧急卖出
   */
  private async riskManagement(tokenIn: any, tokenOut: any, amount: string): Promise<void> {
    console.log(`🚨 启动风险管理: 紧急卖出 ${amount} ${tokenIn.symbol}`);

    try {
      // 尝试通过REF紧急卖出
      const emergencyResult = await this.executeREFTrade(tokenIn, tokenOut, amount);

      if (emergencyResult.success) {
        console.log(`✅ 紧急卖出成功: ${emergencyResult.outputAmount} ${tokenOut.symbol}`);
        const loss = parseFloat(this.tradingPairs[0].tradeAmount) - parseFloat(emergencyResult.outputAmount!);
        console.log(`💸 损失: ${loss.toFixed(4)} NEAR`);
        this.stats.totalProfit -= loss;
      } else {
        console.log(`❌ 紧急卖出失败: ${emergencyResult.error}`);
      }
    } catch (error) {
      console.error(`❌ 风险管理失败:`, error);
    }
  }

  /**
   * 精确的wei转人类可读格式
   */
  private fromWei(amount: string, decimals: number): string {
    if (decimals === 24) {
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      if (amount === '0') return '0';
      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');
      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 精确的人类可读格式转wei - 修复版本
   */
  private toWei(amount: string, decimals: number): string {
    if (decimals === 24) {
      return parseNearAmount(amount) || '0';
    } else {
      // 🔧 修复：正确处理小数转wei
      const [integer = '0', decimal = ''] = amount.split('.');

      // 确保整数部分不为空
      const integerPart = integer || '0';

      // 处理小数部分：截取或补零到指定位数
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);

      // 🔧 关键修复：移除前导零，确保返回正确的wei格式
      const result = integerPart + paddedDecimal;
      return result.replace(/^0+/, '') || '0'; // 移除前导零，但保留单个'0'
    }
  }
}

export default RefJumboArbitrageBot;
