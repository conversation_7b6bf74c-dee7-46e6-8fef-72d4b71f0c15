/**
 * 测试监控交易对是否包含AURORA和OCT
 */

import { tradingPairManager } from './config/tradingPairs';

function testMonitoringPairs() {
  console.log('🔍 测试监控交易对配置...\n');

  // 1. 获取所有交易对
  const allPairs = tradingPairManager.getAllPairs();
  console.log(`📊 总交易对数量: ${allPairs.length}`);
  
  console.log('\n所有交易对列表:');
  allPairs.forEach((pair, index) => {
    console.log(`   ${index + 1}. ${pair.id}: ${pair.enabled ? '✅ 启用' : '❌ 禁用'} (Pool ${pair.jumboPoolId})`);
  });

  // 2. 获取启用的交易对
  const enabledPairs = tradingPairManager.getEnabledPairs();
  console.log(`\n✅ 启用的交易对数量: ${enabledPairs.length}`);
  
  console.log('\n启用的交易对列表:');
  enabledPairs.forEach((pair, index) => {
    console.log(`   ${index + 1}. ${pair.id} (Pool ${pair.jumboPoolId})`);
  });

  // 3. 检查AURORA和OCT是否在启用列表中
  console.log('\n🎯 关键检查:');
  
  const auroraInAll = allPairs.find(pair => pair.id === 'NEAR-AURORA');
  const octInAll = allPairs.find(pair => pair.id === 'NEAR-OCT');
  
  const auroraInEnabled = enabledPairs.find(pair => pair.id === 'NEAR-AURORA');
  const octInEnabled = enabledPairs.find(pair => pair.id === 'NEAR-OCT');
  
  console.log(`   NEAR-AURORA 在所有交易对中: ${auroraInAll ? '✅ 存在' : '❌ 不存在'}`);
  if (auroraInAll) {
    console.log(`     - enabled: ${auroraInAll.enabled}`);
    console.log(`     - jumboPoolId: ${auroraInAll.jumboPoolId}`);
  }
  
  console.log(`   NEAR-AURORA 在启用交易对中: ${auroraInEnabled ? '✅ 存在' : '❌ 不存在'}`);
  
  console.log(`   NEAR-OCT 在所有交易对中: ${octInAll ? '✅ 存在' : '❌ 不存在'}`);
  if (octInAll) {
    console.log(`     - enabled: ${octInAll.enabled}`);
    console.log(`     - jumboPoolId: ${octInAll.jumboPoolId}`);
  }
  
  console.log(`   NEAR-OCT 在启用交易对中: ${octInEnabled ? '✅ 存在' : '❌ 不存在'}`);

  // 4. 模拟监控循环会处理的交易对
  console.log('\n🔄 监控循环将处理的交易对:');
  enabledPairs.forEach((pair, index) => {
    console.log(`   ${index + 1}. 将监控 ${pair.id} (${pair.tokenA.symbol} → ${pair.tokenB.symbol})`);
    console.log(`      - 交易金额: ${pair.tradeAmount} ${pair.tokenA.symbol}`);
    console.log(`      - Jumbo Pool ID: ${pair.jumboPoolId}`);
  });

  // 5. 检查配置摘要
  console.log('\n📋 配置摘要:');
  const summary = tradingPairManager.getConfigSummary();
  console.log(`   总交易对: ${summary.totalPairs}`);
  console.log(`   启用交易对: ${summary.enabledPairs}`);
  console.log(`   禁用交易对: ${summary.disabledPairs}`);

  // 6. 最终结论
  console.log('\n🎉 结论:');
  if (auroraInEnabled && octInEnabled) {
    console.log('   ✅ AURORA和OCT都会被监控');
    console.log('   ✅ 如果程序运行中看不到它们，可能是：');
    console.log('      1. 没有套利机会（价格差异太小）');
    console.log('      2. 流动性不足');
    console.log('      3. 报价获取失败');
    console.log('      4. 日志被隐藏了');
  } else {
    console.log('   ❌ 发现问题：');
    if (!auroraInEnabled) console.log('      - AURORA不在启用列表中');
    if (!octInEnabled) console.log('      - OCT不在启用列表中');
  }
}

// 运行测试
if (require.main === module) {
  testMonitoringPairs();
}

export { testMonitoringPairs };
