#!/usr/bin/env node

/**
 * REF-Jumbo 依赖检查脚本
 * 检查系统是否可以独立运行
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 REF-Jumbo 依赖检查');
console.log('='.repeat(40));

// 检查必要文件和目录
const requiredPaths = [
  // 核心目录
  { path: 'ref-jumbo', type: 'dir', description: 'REF-Jumbo 核心目录' },
  { path: 'reusable-modules', type: 'dir', description: '可复用模块目录' },
  
  // 核心文件
  { path: 'ref-jumbo/main.ts', type: 'file', description: '主程序入口' },
  { path: 'ref-jumbo/package.json', type: 'file', description: 'REF-Jumbo 包配置' },
  { path: 'ref-jumbo/config/tradingPairs.ts', type: 'file', description: '交易对配置' },
  
  // 服务文件
  { path: 'ref-jumbo/refJumboArbitrageBot.ts', type: 'file', description: '套利机器人' },
  { path: 'ref-jumbo/services/jumboQuoteService.ts', type: 'file', description: 'Jumbo 报价服务' },
  
  // 可复用模块
  { path: 'reusable-modules/ref-finance', type: 'dir', description: 'REF Finance 模块' },
  { path: 'reusable-modules/jumbo-exchange', type: 'dir', description: 'Jumbo Exchange 模块' },
  { path: 'reusable-modules/near-utils', type: 'dir', description: 'NEAR 工具模块' },
  
  // 配置文件
  { path: '.env', type: 'file', description: '环境变量配置', optional: true },
  { path: 'src/services/refQuoteService.ts', type: 'file', description: 'REF 报价服务', optional: true }
];

let allGood = true;
let warnings = [];

console.log('📋 检查必要文件和目录...\n');

requiredPaths.forEach(item => {
  const fullPath = path.resolve(item.path);
  const exists = fs.existsSync(fullPath);
  
  if (exists) {
    const stats = fs.statSync(fullPath);
    const isCorrectType = (item.type === 'dir' && stats.isDirectory()) || 
                         (item.type === 'file' && stats.isFile());
    
    if (isCorrectType) {
      console.log(`✅ ${item.description}: ${item.path}`);
    } else {
      console.log(`❌ ${item.description}: ${item.path} (类型错误)`);
      allGood = false;
    }
  } else {
    if (item.optional) {
      console.log(`⚠️  ${item.description}: ${item.path} (可选，不存在)`);
      warnings.push(`${item.description} 不存在，可能需要手动配置`);
    } else {
      console.log(`❌ ${item.description}: ${item.path} (不存在)`);
      allGood = false;
    }
  }
});

console.log('\n' + '='.repeat(40));

// 检查 package.json 依赖
console.log('📦 检查依赖配置...\n');

const packageJsonPath = path.resolve('ref-jumbo/package.json');
if (fs.existsSync(packageJsonPath)) {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const requiredDeps = ['near-api-js', 'big.js', 'axios', 'dotenv'];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`✅ 依赖: ${dep} (${packageJson.dependencies[dep]})`);
      } else {
        console.log(`❌ 缺少依赖: ${dep}`);
        allGood = false;
      }
    });
  } catch (error) {
    console.log(`❌ package.json 解析失败: ${error.message}`);
    allGood = false;
  }
} else {
  console.log('❌ package.json 不存在');
  allGood = false;
}

console.log('\n' + '='.repeat(40));

// 检查环境变量
console.log('🔧 检查环境变量配置...\n');

const envPath = path.resolve('.env');
if (fs.existsSync(envPath)) {
  try {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const requiredEnvVars = ['NEAR_ACCOUNT_ID', 'NEAR_PRIVATE_KEY'];
    
    requiredEnvVars.forEach(envVar => {
      if (envContent.includes(`${envVar}=`) && !envContent.includes(`${envVar}=your_`)) {
        console.log(`✅ 环境变量: ${envVar}`);
      } else {
        console.log(`⚠️  环境变量: ${envVar} (需要配置)`);
        warnings.push(`需要配置 ${envVar} 环境变量`);
      }
    });
  } catch (error) {
    console.log(`❌ .env 文件读取失败: ${error.message}`);
    warnings.push('.env 文件读取失败');
  }
} else {
  console.log('⚠️  .env 文件不存在，需要创建');
  warnings.push('需要创建 .env 文件并配置环境变量');
}

console.log('\n' + '='.repeat(40));

// 总结
console.log('📊 检查结果总结\n');

if (allGood && warnings.length === 0) {
  console.log('🎉 所有检查通过！系统可以独立运行。');
  console.log('\n启动命令:');
  console.log('  cd ref-jumbo && npm install && npx ts-node main.ts');
} else if (allGood && warnings.length > 0) {
  console.log('⚠️  基本检查通过，但有一些警告需要注意：');
  warnings.forEach(warning => {
    console.log(`   - ${warning}`);
  });
  console.log('\n建议完成配置后再启动系统。');
} else {
  console.log('❌ 检查失败！系统无法独立运行。');
  console.log('\n请确保所有必要文件都存在，然后重新运行检查。');
}

console.log('\n📖 详细部署指南: ref-jumbo/docs/独立部署指南.md');
console.log('🚀 自动部署脚本: ref-jumbo/scripts/create-standalone.sh');

process.exit(allGood ? 0 : 1);
