#!/bin/bash

# REF-Jumbo 独立部署脚本
# 自动创建可独立运行的 REF-Jumbo 套利系统

set -e

echo "🚀 REF-Jumbo 独立部署脚本"
echo "================================"

# 检查参数
if [ $# -eq 0 ]; then
    echo "❌ 请提供目标目录名称"
    echo "用法: $0 <target-directory>"
    echo "示例: $0 ref-jumbo-standalone"
    exit 1
fi

TARGET_DIR="$1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo "📁 源项目目录: $PROJECT_ROOT"
echo "📁 目标目录: $TARGET_DIR"

# 检查源目录是否存在
if [ ! -d "$PROJECT_ROOT/ref-jumbo" ]; then
    echo "❌ 源目录不存在: $PROJECT_ROOT/ref-jumbo"
    exit 1
fi

if [ ! -d "$PROJECT_ROOT/reusable-modules" ]; then
    echo "❌ 源目录不存在: $PROJECT_ROOT/reusable-modules"
    exit 1
fi

# 创建目标目录
echo "📦 创建目标目录..."
mkdir -p "$TARGET_DIR"
cd "$TARGET_DIR"

# 复制核心文件
echo "📋 复制 ref-jumbo 核心目录..."
cp -r "$PROJECT_ROOT/ref-jumbo" ./

echo "📋 复制 reusable-modules 目录..."
cp -r "$PROJECT_ROOT/reusable-modules" ./

echo "📋 复制 REF 报价服务..."
mkdir -p src/services
if [ -f "$PROJECT_ROOT/src/services/refQuoteService.ts" ]; then
    cp "$PROJECT_ROOT/src/services/refQuoteService.ts" ./src/services/
else
    echo "⚠️  警告: REF 报价服务文件不存在，可能需要手动复制"
fi

echo "📋 复制环境变量文件..."
if [ -f "$PROJECT_ROOT/.env" ]; then
    cp "$PROJECT_ROOT/.env" ./
else
    echo "⚠️  警告: .env 文件不存在，创建模板文件..."
    cat > .env << 'EOF'
# NEAR 账户配置
NEAR_ACCOUNT_ID=your_account.near
NEAR_PRIVATE_KEY=ed25519:your_private_key
NEAR_NETWORK_ID=mainnet
NEAR_RPC_URL=https://rpc.mainnet.near.org

# 可选配置
NEAR_WALLET_URL=https://wallet.mainnet.near.org
NEAR_HELPER_URL=https://helper.mainnet.near.org
NEAR_EXPLORER_URL=https://explorer.mainnet.near.org
EOF
fi

# 创建独立的 package.json
echo "📋 创建独立的 package.json..."
cat > package.json << 'EOF'
{
  "name": "ref-jumbo-standalone",
  "version": "1.0.0",
  "description": "REF-Jumbo 套利系统 - 独立版本",
  "main": "ref-jumbo/main.ts",
  "scripts": {
    "start": "cd ref-jumbo && npx ts-node main.ts",
    "test": "cd ref-jumbo && npx ts-node test-monitor.ts",
    "test:quotes": "cd ref-jumbo && npx ts-node test-monitor.ts",
    "dev": "cd ref-jumbo && npx ts-node --watch main.ts",
    "build": "tsc",
    "clean": "rm -rf dist",
    "register-tokens": "cd reusable-modules/jumbo-exchange && npx ts-node register-all-tokens.ts"
  },
  "keywords": [
    "near",
    "defi", 
    "arbitrage",
    "ref-finance",
    "jumbo-exchange"
  ],
  "author": "REF-Jumbo Team",
  "license": "MIT",
  "dependencies": {
    "near-api-js": "^4.0.3",
    "big.js": "^6.2.1",
    "axios": "^1.6.0", 
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "ts-node": "^10.9.0"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
EOF

# 创建 TypeScript 配置
echo "📋 创建 TypeScript 配置..."
cat > tsconfig.json << 'EOF'
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "commonjs",
    "lib": ["ES2020"],
    "outDir": "./dist",
    "rootDir": "./",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "declaration": true,
    "declarationMap": true,
    "sourceMap": true
  },
  "include": [
    "ref-jumbo/**/*",
    "reusable-modules/**/*", 
    "src/**/*"
  ],
  "exclude": [
    "node_modules",
    "dist",
    "**/*.test.ts"
  ]
}
EOF

# 创建启动脚本
echo "📋 创建启动脚本..."
cat > start.sh << 'EOF'
#!/bin/bash

echo "🚀 启动 REF-Jumbo 套利系统"
echo "================================"

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "❌ .env 文件不存在，请先配置环境变量"
    exit 1
fi

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 启动系统
echo "🚀 启动套利机器人..."
npm start
EOF

chmod +x start.sh

# 创建 README
echo "📋 创建 README..."
cat > README.md << 'EOF'
# REF-Jumbo 套利系统 - 独立版本

这是一个独立部署的 REF-Jumbo 套利系统，可以在任何环境中运行。

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
编辑 `.env` 文件，配置你的 NEAR 账户信息：
```env
NEAR_ACCOUNT_ID=your_account.near
NEAR_PRIVATE_KEY=ed25519:your_private_key
```

### 3. 注册代币 (首次运行)
```bash
npm run register-tokens
```

### 4. 启动系统
```bash
npm start
# 或者使用启动脚本
./start.sh
```

## 📊 监控交易对

系统默认监控以下交易对：
- NEAR-USDT.e (Jumbo Pool 1)
- NEAR-OCT (Jumbo Pool 3) 
- NEAR-LINEAR (Jumbo Pool 231)
- NEAR-HAPI (Jumbo Pool 6)
- NEAR-UMINT (Jumbo Pool 184)
- NEAR-CHICA (Jumbo Pool 274)
- NEAR-1MIL (Jumbo Pool 294)

## ⚙️ 配置修改

- 交易对配置: `ref-jumbo/config/tradingPairs.ts`
- 系统参数: 监控间隔、利润阈值等

## 📝 日志

系统会显示实时的套利监控信息，包括价格差异和套利机会。

## 🔧 故障排除

如果遇到问题，请检查：
1. 环境变量配置是否正确
2. 账户余额是否充足
3. 网络连接是否正常
4. 代币是否已注册
EOF

echo ""
echo "✅ 独立部署完成！"
echo "================================"
echo "📁 目标目录: $(pwd)"
echo ""
echo "🔧 下一步操作："
echo "1. cd $TARGET_DIR"
echo "2. 编辑 .env 文件配置账户信息"
echo "3. npm install"
echo "4. npm run register-tokens (首次运行)"
echo "5. npm start"
echo ""
echo "或者直接运行: ./start.sh"
echo ""
echo "📖 详细说明请查看: README.md"
