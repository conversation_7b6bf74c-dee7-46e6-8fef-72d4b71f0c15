/**
 * 测试OCT和AURORA代币映射是否正常工作
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../.env') });

import JumboQuoteService from './services/jumboQuoteService';
import { tradingPairManager } from './config/tradingPairs';
import { connect, keyStores } from 'near-api-js';

async function testOctAuroraMapping() {
  console.log('🧪 测试OCT和AURORA代币映射...\n');

  try {
    // 1. 初始化NEAR连接
    const keyStore = new keyStores.InMemoryKeyStore();
    const nearConfig = {
      networkId: 'mainnet',
      keyStore,
      nodeUrl: 'https://rpc.mainnet.near.org',
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org',
      explorerUrl: 'https://explorer.mainnet.near.org',
    };

    const near = await connect(nearConfig);
    const account = await near.account(process.env.NEAR_ACCOUNT_ID!);

    // 2. 初始化Jumbo服务
    await JumboQuoteService.initialize(account);

    // 3. 获取OCT和AURORA交易对配置
    const octPair = tradingPairManager.getPairById('NEAR-OCT');
    const auroraPair = tradingPairManager.getPairById('NEAR-AURORA');

    if (!octPair) {
      console.error('❌ 找不到NEAR-OCT交易对配置');
      return;
    }

    if (!auroraPair) {
      console.error('❌ 找不到NEAR-AURORA交易对配置');
      return;
    }

    console.log('✅ 交易对配置检查通过:');
    console.log(`   NEAR-OCT: ${octPair.enabled ? '启用' : '禁用'}, Pool ID: ${octPair.jumboPoolId}`);
    console.log(`   NEAR-AURORA: ${auroraPair.enabled ? '启用' : '禁用'}, Pool ID: ${auroraPair.jumboPoolId}\n`);

    // 4. 测试代币对支持检查
    console.log('🔍 测试代币对支持检查...');
    
    const octSupported = JumboQuoteService.isSupportedPair(octPair.tokenA.id, octPair.tokenB.id);
    const auroraSupported = JumboQuoteService.isSupportedPair(auroraPair.tokenA.id, auroraPair.tokenB.id);
    
    console.log(`   NEAR-OCT 支持状态: ${octSupported ? '✅ 支持' : '❌ 不支持'}`);
    console.log(`   NEAR-AURORA 支持状态: ${auroraSupported ? '✅ 支持' : '❌ 不支持'}\n`);

    if (!octSupported) {
      console.error('❌ NEAR-OCT代币对不被支持，检查getPoolIdForTokenPair映射');
    }

    if (!auroraSupported) {
      console.error('❌ NEAR-AURORA代币对不被支持，检查getPoolIdForTokenPair映射');
    }

    // 5. 测试实际报价获取
    console.log('💱 测试实际报价获取...');

    // 测试OCT报价
    console.log('   测试NEAR→OCT报价...');
    try {
      const octQuote = await JumboQuoteService.getQuote(
        octPair.tokenA.id,
        octPair.tokenB.id,
        '1' // 1 NEAR
      );
      
      if (octQuote.success) {
        console.log(`   ✅ NEAR→OCT: 1 NEAR = ${octQuote.outputAmount} OCT`);
      } else {
        console.log(`   ❌ NEAR→OCT报价失败: ${octQuote.error}`);
      }
    } catch (error) {
      console.log(`   ❌ NEAR→OCT报价异常:`, error);
    }

    // 测试AURORA报价
    console.log('   测试NEAR→AURORA报价...');
    try {
      const auroraQuote = await JumboQuoteService.getQuote(
        auroraPair.tokenA.id,
        auroraPair.tokenB.id,
        '1' // 1 NEAR
      );
      
      if (auroraQuote.success) {
        console.log(`   ✅ NEAR→AURORA: 1 NEAR = ${auroraQuote.outputAmount} AURORA`);
      } else {
        console.log(`   ❌ NEAR→AURORA报价失败: ${auroraQuote.error}`);
      }
    } catch (error) {
      console.log(`   ❌ NEAR→AURORA报价异常:`, error);
    }

    // 6. 测试反向报价
    console.log('\n   测试反向报价...');
    
    // 测试OCT→NEAR报价
    console.log('   测试OCT→NEAR报价...');
    try {
      const octReverseQuote = await JumboQuoteService.getQuote(
        octPair.tokenB.id,
        octPair.tokenA.id,
        '100' // 100 OCT
      );
      
      if (octReverseQuote.success) {
        console.log(`   ✅ OCT→NEAR: 100 OCT = ${octReverseQuote.outputAmount} NEAR`);
      } else {
        console.log(`   ❌ OCT→NEAR报价失败: ${octReverseQuote.error}`);
      }
    } catch (error) {
      console.log(`   ❌ OCT→NEAR报价异常:`, error);
    }

    // 测试AURORA→NEAR报价
    console.log('   测试AURORA→NEAR报价...');
    try {
      const auroraReverseQuote = await JumboQuoteService.getQuote(
        auroraPair.tokenB.id,
        auroraPair.tokenA.id,
        '10' // 10 AURORA
      );
      
      if (auroraReverseQuote.success) {
        console.log(`   ✅ AURORA→NEAR: 10 AURORA = ${auroraReverseQuote.outputAmount} NEAR`);
      } else {
        console.log(`   ❌ AURORA→NEAR报价失败: ${auroraReverseQuote.error}`);
      }
    } catch (error) {
      console.log(`   ❌ AURORA→NEAR报价异常:`, error);
    }

    console.log('\n🎉 OCT和AURORA代币映射测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testOctAuroraMapping().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}

export { testOctAuroraMapping };
