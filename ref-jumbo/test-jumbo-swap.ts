/**
 * 测试 Jumbo Exchange 交易
 * 手动执行 0.0001 NEAR → HAPI 交易来验证消息格式
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../.env') });

import { Account, connect, keyStores, utils } from 'near-api-js';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';

// 测试配置
const TEST_CONFIG = {
  accountId: process.env.NEAR_ACCOUNT_ID!,
  privateKey: process.env.NEAR_PRIVATE_KEY!,
  networkId: 'mainnet',
  
  // 测试交易参数
  inputToken: 'wrap.near',
  outputToken: 'd9c2d319cd7e6177336b0a9c93c21cb48d84fb54.factory.bridge.near', // HAPI
  testAmount: '0.0001', // 0.0001 NEAR
  poolId: 6, // NEAR-HAPI 池子
  
  // Jumbo 合约
  jumboContract: 'v1.jumbo_exchange.near'
};

// Gas 和存储配置
const GAS_CONFIG = {
  jumboSwap: BigInt('**************0'),      // 300 TGas
  storageDeposit: BigInt('**************')   // 30 TGas
};

const DEPOSIT_CONFIG = {
  jumboFtTransfer: BigInt('1'),              // 1 yoctoNEAR
  storageDeposit: BigInt('1250000000000000000000000') // 0.00125 NEAR
};

/**
 * 测试类
 */
class JumboSwapTest {
  private near?: any;
  private account?: Account;

  /**
   * 初始化 NEAR 连接
   */
  async initialize(): Promise<void> {
    console.log('🔧 初始化 NEAR 连接...');
    
    const keyStore = new keyStores.InMemoryKeyStore();
    const keyPair = utils.KeyPair.fromString(TEST_CONFIG.privateKey as any);
    await keyStore.setKey(TEST_CONFIG.networkId, TEST_CONFIG.accountId, keyPair);

    const rpcUrl = process.env.NEAR_RPC_URL || 'https://free.rpc.fastnear.com';

    const config = {
      networkId: TEST_CONFIG.networkId,
      keyStore,
      nodeUrl: rpcUrl,
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org',
    };

    this.near = await connect(config);
    this.account = await this.near.account(TEST_CONFIG.accountId);
    
    console.log(`✅ NEAR 连接初始化成功: ${TEST_CONFIG.accountId}`);
  }

  /**
   * 检查账户余额
   */
  async checkBalances(): Promise<void> {
    if (!this.account) throw new Error('账户未初始化');

    console.log('\n💰 检查账户余额...');

    try {
      // 检查 NEAR 余额
      const accountState = await this.account.state();
      const nearBalance = formatNearAmount(accountState.amount);
      console.log(`   NEAR 余额: ${nearBalance} NEAR`);

      // 检查 wNEAR 余额
      try {
        const wNearBalance = await this.account.viewFunction({
          contractId: 'wrap.near',
          methodName: 'ft_balance_of',
          args: { account_id: TEST_CONFIG.accountId }
        });
        console.log(`   wNEAR 余额: ${formatNearAmount(wNearBalance)} wNEAR`);
      } catch (error) {
        console.log(`   wNEAR 余额: 查询失败 (${error})`);
      }

      // 检查 HAPI 余额
      try {
        const hapiBalance = await this.account.viewFunction({
          contractId: TEST_CONFIG.outputToken,
          methodName: 'ft_balance_of',
          args: { account_id: TEST_CONFIG.accountId }
        });
        console.log(`   HAPI 余额: ${parseFloat(hapiBalance) / 1e18} HAPI`);
      } catch (error) {
        console.log(`   HAPI 余额: 查询失败 (${error})`);
      }

    } catch (error) {
      console.error('❌ 余额查询失败:', error);
    }
  }

  /**
   * 测试错误的消息格式 (当前使用的格式)
   */
  async testWrongMessageFormat(): Promise<void> {
    if (!this.account) throw new Error('账户未初始化');

    console.log('\n🧪 测试错误的消息格式...');

    const amountWei = parseNearAmount(TEST_CONFIG.testAmount) || '0';
    const minAmountOut = '1'; // 最小输出 1 wei HAPI

    // ❌ 错误的消息格式 (当前使用的)
    const wrongMsg = {
      pool_id: TEST_CONFIG.poolId,
      token_in: TEST_CONFIG.inputToken,
      token_out: TEST_CONFIG.outputToken,
      min_amount_out: minAmountOut
    };

    console.log('📋 错误的消息格式:', JSON.stringify(wrongMsg, null, 2));

    try {
      const result = await this.account.functionCall({
        contractId: TEST_CONFIG.inputToken, // wrap.near
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: TEST_CONFIG.jumboContract,
          amount: amountWei,
          msg: JSON.stringify(wrongMsg)
        },
        attachedDeposit: DEPOSIT_CONFIG.jumboFtTransfer,
        gas: GAS_CONFIG.jumboSwap
      });

      console.log(`📋 交易哈希: ${result.transaction.hash}`);
      console.log('✅ 错误格式交易成功 (意外!)');

    } catch (error: any) {
      console.log('❌ 错误格式交易失败 (预期):', error.message);
      
      // 检查是否是预期的错误
      if (error.message.includes('E28: Illegal msg in ft_transfer_call')) {
        console.log('✅ 确认是消息格式错误!');
      }
    }
  }

  /**
   * 测试正确的消息格式
   */
  async testCorrectMessageFormat(): Promise<void> {
    if (!this.account) throw new Error('账户未初始化');

    console.log('\n🧪 测试正确的消息格式...');

    const amountWei = parseNearAmount(TEST_CONFIG.testAmount) || '0';
    const minAmountOut = '1'; // 最小输出 1 wei HAPI

    // ✅ 正确的消息格式 (REF Finance 格式)
    const correctMsg = {
      force: 0,
      actions: [{
        pool_id: TEST_CONFIG.poolId,
        token_in: TEST_CONFIG.inputToken,
        token_out: TEST_CONFIG.outputToken,
        amount_in: amountWei,  // 🔧 关键：添加 amount_in 字段
        min_amount_out: minAmountOut
      }]
    };

    console.log('📋 正确的消息格式:', JSON.stringify(correctMsg, null, 2));

    try {
      const result = await this.account.functionCall({
        contractId: TEST_CONFIG.inputToken, // wrap.near
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: TEST_CONFIG.jumboContract,
          amount: amountWei,
          msg: JSON.stringify(correctMsg)
        },
        attachedDeposit: DEPOSIT_CONFIG.jumboFtTransfer,
        gas: GAS_CONFIG.jumboSwap
      });

      console.log(`📋 交易哈希: ${result.transaction.hash}`);
      console.log('✅ 正确格式交易成功!');

      // 分析交易结果
      this.analyzeTransactionResult(result);

    } catch (error: any) {
      console.log('❌ 正确格式交易失败:', error.message);
    }
  }

  /**
   * 分析交易结果
   */
  private analyzeTransactionResult(result: any): void {
    console.log('\n📊 分析交易结果...');

    try {
      // 检查交易状态
      const hasError = result.receipts_outcome?.some((receipt: any) => 
        receipt.outcome?.status?.Failure
      );

      if (hasError) {
        console.log('❌ 交易执行失败');
        const errorDetails = result.receipts_outcome
          .find((receipt: any) => receipt.outcome?.status?.Failure)
          ?.outcome?.status?.Failure;
        console.log('错误详情:', JSON.stringify(errorDetails, null, 2));
        return;
      }

      // 提取交易日志
      const logs = result.receipts_outcome
        ?.flatMap((receipt: any) => receipt.outcome.logs)
        ?.filter((log: string) => log) || [];

      console.log('📋 交易日志:');
      logs.forEach((log: string, index: number) => {
        console.log(`   ${index + 1}. ${log}`);
      });

      // 查找输出金额
      for (const log of logs) {
        if (log.includes('EVENT_JSON') && log.includes('ft_transfer')) {
          try {
            const eventMatch = log.match(/EVENT_JSON:(.+)/);
            if (eventMatch) {
              const eventData = JSON.parse(eventMatch[1]);
              if (eventData.event === 'ft_transfer' && 
                  eventData.data?.[0]?.new_owner_id === TEST_CONFIG.accountId) {
                const outputAmount = eventData.data[0].amount;
                console.log(`✅ 提取到输出金额: ${outputAmount} wei HAPI`);
                console.log(`   人类可读: ${parseFloat(outputAmount) / 1e18} HAPI`);
              }
            }
          } catch (parseError) {
            // 忽略解析错误
          }
        }
      }

    } catch (error) {
      console.error('❌ 交易结果分析失败:', error);
    }
  }

  /**
   * 运行完整测试
   */
  async runTest(): Promise<void> {
    try {
      await this.initialize();
      await this.checkBalances();
      
      // 先测试错误格式
      await this.testWrongMessageFormat();
      
      // 等待一下
      console.log('\n⏳ 等待 3 秒...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 再测试正确格式
      await this.testCorrectMessageFormat();
      
      // 最后再检查余额
      await this.checkBalances();

    } catch (error) {
      console.error('❌ 测试失败:', error);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 Jumbo Exchange 交易格式测试');
  console.log('='.repeat(50));
  console.log(`📋 测试账户: ${TEST_CONFIG.accountId}`);
  console.log(`💰 测试金额: ${TEST_CONFIG.testAmount} NEAR`);
  console.log(`🔄 交易对: NEAR → HAPI (池子 ${TEST_CONFIG.poolId})`);
  console.log('='.repeat(50));

  const test = new JumboSwapTest();
  await test.runTest();

  console.log('\n✅ 测试完成!');
}

// 运行测试
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

export default main;
