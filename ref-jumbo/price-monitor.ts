/**
 * REF-Jumbo 价格监控服务
 * 实时监控 REF Finance 和 Jumbo Exchange 的价格差异
 */

import { Account } from 'near-api-js';
import { JumboExchange, JumboQuoteResult } from '../reusable-modules/jumbo-exchange';
import { 
  IPriceMonitor, 
  PriceInfo, 
  PriceComparison, 
  MonitorStatus, 
  TradingPair,
  ExchangeType
} from './types';
import { 
  REF_JUMBO_CONFIG, 
  getActiveTradingPairs, 
  getRefTokenAddress,
  formatPrice,
  formatPercentage,
  formatProfit,
  getTimestamp
} from './config';

/**
 * 价格监控服务实现
 */
export class PriceMonitor implements IPriceMonitor {
  private nearAccount: Account;
  private jumboExchange: JumboExchange;
  private isRunning: boolean = false;
  private monitorInterval?: NodeJS.Timeout;
  private status: MonitorStatus;

  constructor(nearAccount: Account) {
    this.nearAccount = nearAccount;
    this.jumboExchange = new JumboExchange(nearAccount);
    
    this.status = {
      isRunning: false,
      startTime: 0,
      lastUpdateTime: 0,
      totalChecks: 0,
      opportunitiesFound: 0,
      executedTrades: 0,
      totalProfit: 0,
      errors: 0
    };
  }

  // ============================================================================
  // 主要监控方法
  // ============================================================================

  /**
   * 开始价格监控
   */
  async startMonitoring(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 监控已在运行中');
      return;
    }

    try {
      console.log('🚀 启动 REF-Jumbo 价格监控系统');
      console.log('='.repeat(80));

      // 初始化 Jumbo Exchange
      await this.jumboExchange.initialize();
      console.log('✅ Jumbo Exchange 初始化完成');

      // 初始化状态
      this.status.isRunning = true;
      this.status.startTime = Date.now();
      this.isRunning = true;

      console.log(`📊 监控配置:`);
      console.log(`  - 监控间隔: ${REF_JUMBO_CONFIG.monitor.monitorInterval / 1000}秒`);
      console.log(`  - 价格阈值: ${REF_JUMBO_CONFIG.monitor.priceThreshold}%`);
      console.log(`  - 最小利润: ${REF_JUMBO_CONFIG.monitor.minProfitThreshold} NEAR`);
      console.log(`  - 测试金额: ${REF_JUMBO_CONFIG.monitor.testAmount} NEAR`);
      console.log(`  - 交易对数: ${getActiveTradingPairs().length}`);

      console.log('\n🔍 开始监控价格差异...\n');

      // 立即执行一次检查
      await this.checkPrices();

      // 设置定时监控
      this.monitorInterval = setInterval(async () => {
        try {
          await this.checkPrices();
        } catch (error) {
          console.error('❌ 监控检查失败:', error);
          this.status.errors++;
        }
      }, REF_JUMBO_CONFIG.monitor.monitorInterval);

    } catch (error) {
      console.error('❌ 启动监控失败:', error);
      this.isRunning = false;
      this.status.isRunning = false;
      throw error;
    }
  }

  /**
   * 停止价格监控
   */
  stopMonitoring(): void {
    if (!this.isRunning) {
      console.log('⚠️ 监控未在运行');
      return;
    }

    console.log('\n🛑 停止价格监控...');
    
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = undefined;
    }

    this.isRunning = false;
    this.status.isRunning = false;

    // 显示统计信息
    const runTime = Date.now() - this.status.startTime;
    const runTimeMinutes = Math.floor(runTime / 60000);
    
    console.log('\n📊 监控统计:');
    console.log(`  - 运行时间: ${runTimeMinutes} 分钟`);
    console.log(`  - 总检查次数: ${this.status.totalChecks}`);
    console.log(`  - 发现机会: ${this.status.opportunitiesFound}`);
    console.log(`  - 执行交易: ${this.status.executedTrades}`);
    console.log(`  - 总利润: ${formatProfit(this.status.totalProfit)}`);
    console.log(`  - 错误次数: ${this.status.errors}`);
    
    console.log('\n✅ 监控已停止');
  }

  /**
   * 获取当前价格比较
   */
  async getCurrentPrices(): Promise<PriceComparison[]> {
    const activePairs = getActiveTradingPairs();
    const comparisons: PriceComparison[] = [];

    for (const pair of activePairs) {
      try {
        const comparison = await this.comparePairPrices(pair);
        comparisons.push(comparison);
      } catch (error) {
        console.error(`❌ 获取 ${pair.name} 价格失败:`, error);
      }
    }

    return comparisons;
  }

  /**
   * 获取监控状态
   */
  getStatus(): MonitorStatus {
    return { ...this.status };
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  /**
   * 执行价格检查
   */
  private async checkPrices(): Promise<void> {
    try {
      this.status.totalChecks++;
      this.status.lastUpdateTime = Date.now();

      const comparisons = await this.getCurrentPrices();
      
      // 显示价格信息
      this.displayPrices(comparisons);

      // 检查套利机会
      const opportunities = this.findArbitrageOpportunities(comparisons);
      
      if (opportunities.length > 0) {
        this.status.opportunitiesFound += opportunities.length;
        this.displayOpportunities(opportunities);
      }

    } catch (error) {
      console.error('❌ 价格检查失败:', error);
      this.status.errors++;
    }
  }

  /**
   * 比较单个交易对的价格
   */
  private async comparePairPrices(pair: TradingPair): Promise<PriceComparison> {
    const testAmount = REF_JUMBO_CONFIG.monitor.testAmount;
    
    // 获取 Jumbo 价格
    const jumboPrice = await this.getJumboPrice(pair, testAmount);
    
    // 获取 REF 价格
    const refPrice = await this.getRefPrice(pair, testAmount);

    // 计算价格差异
    const priceDiff = ((jumboPrice.price - refPrice.price) / refPrice.price) * 100;
    const priceDiffAbs = Math.abs(priceDiff);
    
    const bestExchange: ExchangeType = jumboPrice.price > refPrice.price ? 'JUMBO' : 'REF';
    const worstExchange: ExchangeType = bestExchange === 'JUMBO' ? 'REF' : 'JUMBO';

    return {
      pair: pair.name,
      refPrice,
      jumboPrice,
      priceDifference: priceDiff,
      priceDifferenceAbs: priceDiffAbs,
      bestExchange,
      worstExchange,
      timestamp: Date.now()
    };
  }

  /**
   * 获取 Jumbo Exchange 价格
   */
  private async getJumboPrice(pair: TradingPair, amount: string): Promise<PriceInfo> {
    try {
      // 根据池子ID调用对应的报价方法
      let quote: JumboQuoteResult;
      switch (pair.jumboPoolId) {
        case 1:
          quote = await this.jumboExchange.getNearUsdtQuote(amount);
          break;
        case 231:
          quote = await this.jumboExchange.getNearLinearQuote(amount);
          break;
        case 6:
          quote = await this.jumboExchange.getNearHapiQuote(amount);
          break;
        case 184:
          quote = await this.jumboExchange.getNearUmintQuote(amount);
          break;
        case 274:
          quote = await this.jumboExchange.getNearChicaQuote(amount);
          break;
        case 294:
          quote = await this.jumboExchange.getNear1MilQuote(amount);
          break;
        case 266:
          quote = await this.jumboExchange.getNearNearxQuote(amount);
          break;
        default:
          throw new Error(`Unsupported pool ID: ${pair.jumboPoolId}`);
      }

      if (!quote.success) {
        throw new Error(quote.error || 'Quote failed');
      }

      const price = parseFloat(quote.amountOut!) / parseFloat(quote.amountIn);

      return {
        exchange: 'JUMBO',
        pair: pair.name,
        price,
        amountIn: quote.amountIn,
        amountOut: quote.amountOut!,
        amountOutWei: quote.amountOutWei!,
        timestamp: quote.timestamp,
        method: quote.method,
        success: true
      };

    } catch (error) {
      return {
        exchange: 'JUMBO',
        pair: pair.name,
        price: 0,
        amountIn: amount,
        amountOut: '0',
        amountOutWei: '0',
        timestamp: Date.now(),
        method: 'error',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 获取 REF Finance 价格 (暂时使用模拟数据)
   */
  private async getRefPrice(pair: TradingPair, amount: string): Promise<PriceInfo> {
    try {
      // TODO: 实现真实的 REF Finance 价格获取
      // 这里暂时使用模拟数据，基于 Jumbo 价格加上随机差异
      
      // 获取 Jumbo 参考价格
      let jumboQuote: JumboQuoteResult;
      switch (pair.jumboPoolId) {
        case 1:
          jumboQuote = await this.jumboExchange.getNearUsdtQuote(amount);
          break;
        case 231:
          jumboQuote = await this.jumboExchange.getNearLinearQuote(amount);
          break;
        case 6:
          jumboQuote = await this.jumboExchange.getNearHapiQuote(amount);
          break;
        case 184:
          jumboQuote = await this.jumboExchange.getNearUmintQuote(amount);
          break;
        case 274:
          jumboQuote = await this.jumboExchange.getNearChicaQuote(amount);
          break;
        case 294:
          jumboQuote = await this.jumboExchange.getNear1MilQuote(amount);
          break;
        case 266:
          jumboQuote = await this.jumboExchange.getNearNearxQuote(amount);
          break;
        default:
          throw new Error(`Unsupported pool ID: ${pair.jumboPoolId}`);
      }

      if (!jumboQuote.success) {
        throw new Error('Failed to get reference price');
      }

      // 模拟 REF 价格 (在 Jumbo 价格基础上 ±2% 的随机变化)
      const jumboPrice = parseFloat(jumboQuote.amountOut!) / parseFloat(jumboQuote.amountIn);
      const randomFactor = 0.98 + Math.random() * 0.04; // 0.98 - 1.02
      const refPrice = jumboPrice * randomFactor;
      const refAmountOut = (parseFloat(amount) * refPrice).toFixed(6);

      return {
        exchange: 'REF',
        pair: pair.name,
        price: refPrice,
        amountIn: amount,
        amountOut: refAmountOut,
        amountOutWei: refAmountOut, // 简化处理
        timestamp: Date.now(),
        method: 'simulated',
        success: true
      };

    } catch (error) {
      return {
        exchange: 'REF',
        pair: pair.name,
        price: 0,
        amountIn: amount,
        amountOut: '0',
        amountOutWei: '0',
        timestamp: Date.now(),
        method: 'error',
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 显示价格信息
   */
  private displayPrices(comparisons: PriceComparison[]): void {
    const timestamp = getTimestamp();
    
    console.log(`[${timestamp}] 价格监控:`);
    
    for (const comp of comparisons) {
      if (!comp.refPrice.success || !comp.jumboPrice.success) {
        console.log(`  ${comp.pair}: ❌ 价格获取失败`);
        continue;
      }

      const refPriceStr = formatPrice(comp.refPrice.price);
      const jumboPriceStr = formatPrice(comp.jumboPrice.price);
      const diffStr = formatPercentage(comp.priceDifference);
      
      // 计算潜在利润
      const testAmount = parseFloat(REF_JUMBO_CONFIG.monitor.testAmount);
      const potentialProfit = Math.abs(comp.priceDifference / 100) * testAmount;
      const profitStr = formatProfit(potentialProfit);

      console.log(`  ${comp.pair}: REF ${refPriceStr} | Jumbo ${jumboPriceStr} | ${diffStr} | ${profitStr}`);
    }
    
    console.log(''); // 空行分隔
  }

  /**
   * 查找套利机会
   */
  private findArbitrageOpportunities(comparisons: PriceComparison[]): PriceComparison[] {
    return comparisons.filter(comp => {
      if (!comp.refPrice.success || !comp.jumboPrice.success) {
        return false;
      }

      const testAmount = parseFloat(REF_JUMBO_CONFIG.monitor.testAmount);
      const potentialProfit = Math.abs(comp.priceDifference / 100) * testAmount;
      
      return (
        comp.priceDifferenceAbs >= REF_JUMBO_CONFIG.monitor.priceThreshold &&
        potentialProfit >= REF_JUMBO_CONFIG.monitor.minProfitThreshold
      );
    });
  }

  /**
   * 显示套利机会
   */
  private displayOpportunities(opportunities: PriceComparison[]): void {
    console.log('🎯 发现套利机会:');
    
    for (const opp of opportunities) {
      const direction = opp.bestExchange === 'JUMBO' ? 'REF→Jumbo' : 'Jumbo→REF';
      const testAmount = parseFloat(REF_JUMBO_CONFIG.monitor.testAmount);
      const profit = Math.abs(opp.priceDifference / 100) * testAmount;
      
      console.log(`  ${opp.pair}: ${direction} | ${formatPercentage(opp.priceDifference)} | ${formatProfit(profit)}`);
    }
    
    console.log('');
  }
}

export default PriceMonitor;
