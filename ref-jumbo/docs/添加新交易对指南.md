# 🔧 REF-Jumbo 添加新交易对完整指南

## 📋 准备工作

在添加新交易对之前，需要收集以下信息：

1. **代币合约地址** (如: `f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near`)
2. **Jumbo Pool ID** (如: `3`)
3. **代币符号** (如: `OCT`)
4. **代币精度** (通常: NEAR生态=24位, 桥接USDT/USDC=6位, 其他=18位)
5. **最小流动性要求** (用于风险控制，建议1000+)

## 🔍 信息查询方法

### 查询 Jumbo Exchange 池子信息
```bash
# 方法1: 通过 API 查询
curl https://price-service.jumbo.exchange/pools

# 方法2: 通过 NEAR RPC 查询
near view v1.jumbo_exchange.near get_pools '{"from_index": 0, "limit": 100}'
```

### 查询代币精度
```bash
# 通过 NEAR RPC 查询代币元数据
near view TOKEN_CONTRACT_ADDRESS ft_metadata
```

## 🔧 添加步骤

### 1️⃣ 修改主配置文件
**文件**: `ref-jumbo/config/tradingPairs.ts`

在 `TRADING_PAIRS` 数组中添加新配置：

```typescript
{
  id: 'NEAR-NEWTOKEN',                    // 交易对唯一标识
  tokenA: {                              // 基础代币 (通常是 NEAR)
    id: 'wrap.near',
    symbol: 'NEAR',
    decimals: 24
  },
  tokenB: {                              // 报价代币 (新代币)
    id: 'newtoken.contract.near',         // 代币合约地址
    symbol: 'NEWTOKEN',                   // 代币符号
    decimals: 18                          // 代币精度
  },
  tradeAmount: '4',                       // 测试交易金额 (NEAR)
  enabled: true,                          // 是否启用监控
  jumboPoolId: 999                        // Jumbo Exchange 池子ID
}
```

### 2️⃣ 添加代币精度支持
**文件**: `ref-jumbo/services/jumboQuoteService.ts`

在 `getTokenDecimals()` 方法的 switch 语句中添加：

```typescript
case 'newtoken.contract.near': // NEWTOKEN
  return 18;
```

### 3️⃣ 配置 Jumbo Exchange 支持
**文件**: `reusable-modules/jumbo-exchange/config.ts`

#### 3.1 添加池子配置
在 `JUMBO_TARGET_POOLS` 数组中添加：

```typescript
{
  poolId: 999,
  name: 'NEAR-NEWTOKEN',
  baseToken: 'wrap.near',
  quoteToken: 'newtoken.contract.near',
  baseSymbol: 'NEAR',
  quoteSymbol: 'NEWTOKEN',
  minLiquidity: 1000,
  isActive: true
}
```

#### 3.2 添加代币地址映射
在 `TOKEN_ADDRESSES` 对象中添加：

```typescript
NEWTOKEN: 'newtoken.contract.near'
```

#### 3.3 添加代币精度
在 `TOKEN_DECIMALS` 对象中添加：

```typescript
'newtoken.contract.near': 18, // NEWTOKEN
```

## 🧪 测试步骤

### 1. 启动系统测试
```bash
cd ref-jumbo
npx ts-node main.ts
```

### 2. 检查启动日志
确认看到以下信息：
- ✅ 监控交易对数量增加
- ✅ Jumbo Exchange 配置包含新池子
- ✅ 无错误信息

### 3. 观察监控输出
- 系统应该开始监控新交易对
- 如果有套利机会会显示相关信息

## ⚠️ 注意事项

1. **先测试后启用**: 建议先设置 `enabled: false` 进行测试
2. **精度很重要**: 错误的精度会导致交易失败
3. **流动性检查**: 确保池子有足够的流动性
4. **Pool ID 准确性**: 错误的 Pool ID 会导致合约调用失败

## 🔄 回滚步骤

如果需要移除交易对：

1. 在 `tradingPairs.ts` 中设置 `enabled: false` 或删除配置
2. 重启系统即可

## 📊 监控和维护

- 定期检查交易对的流动性
- 监控套利机会的频率和收益
- 根据市场情况调整 `tradeAmount`

## 🎯 成功示例

最近添加的 NEAR-OCT 交易对：
- Pool ID: 3
- 代币地址: `f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near`
- 精度: 18 位
- 状态: ✅ 正常运行
