# 🚀 REF-Jumbo 独立部署指南

## 📋 概述

REF-Jumbo 套利系统可以独立运行，但需要复制相关的依赖模块。

## 🔗 依赖关系分析

### ✅ 外部依赖 (npm packages)
```json
{
  "dependencies": {
    "near-api-js": "^4.0.3",    // NEAR 区块链交互
    "big.js": "^6.2.1",         // 精确数值计算  
    "axios": "^1.6.0",          // HTTP 请求
    "dotenv": "^16.3.1"         // 环境变量
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0", 
    "ts-node": "^10.9.0"
  }
}
```

### 🔗 内部模块依赖
1. **`reusable-modules/`** - 核心功能模块 (必须)
2. **`src/services/refQuoteService.ts`** - REF 报价服务 (必须)
3. **`.env`** - 环境变量配置 (必须)

## 📦 独立部署步骤

### 1️⃣ 创建新项目目录
```bash
mkdir ref-jumbo-standalone
cd ref-jumbo-standalone
```

### 2️⃣ 复制必要文件
```bash
# 复制 ref-jumbo 核心目录
cp -r /path/to/original/ref-jumbo ./

# 复制 reusable-modules 目录
cp -r /path/to/original/reusable-modules ./

# 复制 REF 报价服务
mkdir -p src/services
cp /path/to/original/src/services/refQuoteService.ts ./src/services/

# 复制环境变量文件
cp /path/to/original/.env ./
```

### 3️⃣ 创建独立的 package.json
```json
{
  "name": "ref-jumbo-standalone",
  "version": "1.0.0",
  "description": "REF-Jumbo 套利系统 - 独立版本",
  "main": "ref-jumbo/main.ts",
  "scripts": {
    "start": "cd ref-jumbo && npx ts-node main.ts",
    "test": "cd ref-jumbo && npx ts-node test-monitor.ts",
    "build": "tsc",
    "dev": "cd ref-jumbo && npx ts-node --watch main.ts"
  },
  "dependencies": {
    "near-api-js": "^4.0.3",
    "big.js": "^6.2.1", 
    "axios": "^1.6.0",
    "dotenv": "^16.3.1"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "typescript": "^5.0.0",
    "ts-node": "^10.9.0"
  }
}
```

### 4️⃣ 安装依赖
```bash
npm install
```

### 5️⃣ 配置环境变量
编辑 `.env` 文件：
```env
NEAR_ACCOUNT_ID=your_account.near
NEAR_PRIVATE_KEY=ed25519:your_private_key
NEAR_NETWORK_ID=mainnet
NEAR_RPC_URL=https://rpc.mainnet.near.org
```

### 6️⃣ 启动系统
```bash
npm start
```

## 📁 最终目录结构

```
ref-jumbo-standalone/
├── package.json              # 独立的依赖配置
├── .env                      # 环境变量
├── ref-jumbo/               # REF-Jumbo 核心系统
│   ├── main.ts              # 主程序入口
│   ├── config/              # 配置文件
│   ├── services/            # 服务模块
│   ├── types.ts             # 类型定义
│   └── docs/                # 文档
├── reusable-modules/        # 可复用模块
│   ├── ref-finance/         # REF Finance 模块
│   ├── jumbo-exchange/      # Jumbo Exchange 模块
│   ├── near-utils/          # NEAR 工具
│   └── config/              # 配置模块
└── src/
    └── services/
        └── refQuoteService.ts  # REF 报价服务
```

## ⚠️ 注意事项

### 1. 路径依赖
- 确保所有相对路径引用正确
- 特别注意 `.env` 文件的加载路径

### 2. 环境配置
- 必须配置正确的 NEAR 账户信息
- 确保账户有足够的 NEAR 余额

### 3. 代币注册
首次运行前需要注册代币：
```bash
cd reusable-modules/jumbo-exchange
npx ts-node register-all-tokens.ts
```

### 4. 网络连接
- 确保网络可以访问 NEAR RPC 节点
- 确保可以访问 REF Finance API

## 🧪 测试独立部署

### 1. 测试报价功能
```bash
cd ref-jumbo
npx ts-node test-monitor.ts
```

### 2. 测试完整系统
```bash
npm start
```

### 3. 验证输出
应该看到类似输出：
```
🚀 REF-Jumbo 套利系统
📊 监控交易对: 7 个
[JumboExchange] Configured 8 target pools
✅ REF-Jumbo 套利机器人已启动，开始监控...
```

## 🔄 更新和维护

### 同步更新
如果原项目有更新，只需要重新复制相关文件：
```bash
# 更新核心模块
cp -r /path/to/original/ref-jumbo ./
cp -r /path/to/original/reusable-modules ./

# 更新 REF 服务
cp /path/to/original/src/services/refQuoteService.ts ./src/services/
```

### 配置修改
- 交易对配置：`ref-jumbo/config/tradingPairs.ts`
- 系统参数：监控间隔、利润阈值等

## ✅ 独立性确认

**REF-Jumbo 系统在复制必要文件后完全独立运行，不依赖原项目的其他部分。**
