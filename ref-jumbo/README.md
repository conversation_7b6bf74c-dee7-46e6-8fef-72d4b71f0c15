# REF-Jumbo 套利监控系统

## 项目概述

REF-Jumbo 套利监控系统是一个专门用于监控 REF Finance 和 Jumbo Exchange 之间价格差异的自动化套利系统。

## 功能特性

### 🔍 价格监控
- 实时监控 REF Finance 和 Jumbo Exchange 的价格
- 支持多个交易对的同时监控
- 智能价格差异检测

### 🤖 自动套利
- 自动检测套利机会
- 智能路径选择 (REF→Jumbo 或 Jumbo→REF)
- 自动执行套利交易

### 📊 风险管理
- 滑点保护
- 最小利润阈值
- 余额管理
- 失败重试机制

## 文件结构

```
ref-jumbo/
├── README.md                 # 项目说明
├── types.ts                  # 类型定义
├── config.ts                 # 配置管理
├── price-monitor.ts          # 价格监控服务
├── arbitrage-detector.ts     # 套利机会检测
├── arbitrage-executor.ts     # 套利执行器
├── risk-manager.ts           # 风险管理
├── main.ts                   # 主程序入口
├── test-monitor.ts           # 监控测试脚本
└── test-arbitrage.ts         # 套利测试脚本
```

## 支持的交易对

### 主要监控池子
1. **NEAR-USDT.e** (Jumbo Pool 1)
2. **NEAR-LINEAR** (Jumbo Pool 231)
3. **NEAR-HAPI** (Jumbo Pool 6)
4. **NEAR-UMINT** (Jumbo Pool 184)
5. **NEAR-CHICA** (Jumbo Pool 274)
6. **NEAR-1MIL** (Jumbo Pool 294)
7. **NEAR-NearX** (Jumbo Pool 266)

## 使用方法

### 1. 环境配置
确保 `.env` 文件中配置了正确的账户信息：
```
NEAR_ACCOUNT_ID=your_account.near
NEAR_PRIVATE_KEY=your_private_key
NEAR_NETWORK_ID=mainnet
```

### 2. 代币注册
首先注册所有需要的代币：
```bash
cd reusable-modules/jumbo-exchange
npx ts-node register-all-tokens.ts
```

### 3. 启动监控
```bash
cd ref-jumbo
npx ts-node main.ts
```

### 4. 测试功能
```bash
# 测试价格监控
npx ts-node test-monitor.ts

# 测试套利执行
npx ts-node test-arbitrage.ts
```

## 配置参数

### 监控配置
- **监控间隔**: 5秒
- **价格差异阈值**: 0.5%
- **最小利润**: 0.015 NEAR

### 交易配置
- **测试金额**: 4 NEAR
- **滑点保护**: 1%
- **Gas 限制**: 300 TGas

### 风险管理
- **最大单次交易**: 10 NEAR
- **最小余额保护**: 1 NEAR
- **失败重试次数**: 3次

## 技术架构

### 价格监控
- 使用本地 AMM 计算获取 Jumbo 价格
- 调用 REF Finance API 获取价格
- 实时比较价格差异

### 套利执行
- 智能选择最优路径
- 自动处理代币注册和包装
- 精确的输出金额传递

### 错误处理
- 完整的错误分类和处理
- 自动重试机制
- 详细的日志记录

## 监控日志格式

```
[Monitor] REF: 1.924 | Jumbo: 1.985 | Diff: +3.17% | Profit: 0.061 NEAR
[Arbitrage] Opportunity: REF→Jumbo | Expected: 0.061 NEAR | Executing...
[Executor] REF Trade: 4 NEAR → 7.696 USDT.e | Hash: abc123...
[Executor] Jumbo Trade: 7.696 USDT.e → 4.061 NEAR | Hash: def456...
[Result] ✅ Profit: 0.061 NEAR | ROI: 1.53%
```

## 注意事项

1. **余额管理**: 确保账户有足够的 NEAR 余额
2. **代币注册**: 首次使用前必须注册所有代币
3. **网络状况**: 监控网络延迟对套利的影响
4. **市场波动**: 注意市场剧烈波动时的风险

## 开发计划

- [x] 基础架构设计
- [x] Jumbo Exchange 集成
- [ ] 价格监控实现
- [ ] 套利检测逻辑
- [ ] 自动执行系统
- [ ] 风险管理机制
- [ ] 性能优化
- [ ] 监控界面
