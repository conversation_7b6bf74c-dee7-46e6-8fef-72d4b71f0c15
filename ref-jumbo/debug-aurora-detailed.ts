/**
 * 详细调试AURORA代币问题
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载主目录的 .env 文件
config({ path: resolve(__dirname, '../.env') });

import JumboQuoteService from './services/jumboQuoteService';
import { JumboExchange } from '../reusable-modules/jumbo-exchange';
import { tradingPairManager } from './config/tradingPairs';
import { connect, keyStores } from 'near-api-js';

async function debugAuroraDetailed() {
  console.log('🔍 详细调试AURORA代币问题...\n');

  try {
    // 1. 初始化NEAR连接
    const keyStore = new keyStores.InMemoryKeyStore();
    const nearConfig = {
      networkId: 'mainnet',
      keyStore,
      nodeUrl: 'https://rpc.mainnet.near.org',
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org',
      explorerUrl: 'https://explorer.mainnet.near.org',
    };

    const near = await connect(nearConfig);
    const account = await near.account(process.env.NEAR_ACCOUNT_ID!);

    // 2. 获取AURORA交易对配置
    const auroraPair = tradingPairManager.getPairById('NEAR-AURORA');
    if (!auroraPair) {
      console.error('❌ 找不到NEAR-AURORA交易对配置');
      return;
    }

    console.log('✅ AURORA交易对配置:');
    console.log(`   ID: ${auroraPair.id}`);
    console.log(`   TokenA: ${auroraPair.tokenA.id} (${auroraPair.tokenA.symbol})`);
    console.log(`   TokenB: ${auroraPair.tokenB.id} (${auroraPair.tokenB.symbol})`);
    console.log(`   Pool ID: ${auroraPair.jumboPoolId}`);
    console.log(`   Enabled: ${auroraPair.enabled}\n`);

    // 3. 测试ref-jumbo服务中的映射
    console.log('🔍 测试ref-jumbo服务中的映射...');
    await JumboQuoteService.initialize(account);
    
    const supported = JumboQuoteService.isSupportedPair(auroraPair.tokenA.id, auroraPair.tokenB.id);
    console.log(`   isSupportedPair结果: ${supported ? '✅ 支持' : '❌ 不支持'}\n`);

    // 4. 测试reusable-modules中的JumboExchange
    console.log('🔍 测试reusable-modules中的JumboExchange...');
    const jumboExchange = new JumboExchange(account);
    await jumboExchange.initialize();

    // 5. 测试getAllQuotes方法
    console.log('💱 测试getAllQuotes方法...');
    try {
      const allQuotes = await jumboExchange.getAllQuotes();
      console.log('   所有池子报价结果:');

      for (const [poolId, result] of Object.entries(allQuotes)) {
        if (parseInt(poolId) === 0) { // AURORA池子
          console.log(`   Pool ${poolId} (AURORA): ${result.success ? '✅ 成功' : '❌ 失败'}`);
          if (result.success) {
            console.log(`     1 NEAR = ${result.amountOut} AURORA`);
          } else {
            console.log(`     错误: ${result.error}`);
          }
        }
      }
    } catch (error) {
      console.log(`   ❌ getAllQuotes异常:`, error);
    }

    // 6. 测试ref-jumbo服务的报价
    console.log('\n💱 测试ref-jumbo服务报价...');
    try {
      const result = await JumboQuoteService.getQuote(
        auroraPair.tokenA.id,
        auroraPair.tokenB.id,
        '1'
      );
      
      if (result.success) {
        console.log(`   ✅ ref-jumbo服务报价成功: 1 NEAR = ${result.outputAmount} AURORA`);
      } else {
        console.log(`   ❌ ref-jumbo服务报价失败: ${result.error}`);
      }
    } catch (error) {
      console.log(`   ❌ ref-jumbo服务报价异常:`, error);
    }

    // 7. 检查reusable-modules配置
    console.log('\n🔍 检查reusable-modules配置...');
    const { JUMBO_TARGET_POOLS, TOKEN_ADDRESSES, TOKEN_DECIMALS, isTargetPool } = await import('../reusable-modules/jumbo-exchange/config');
    
    console.log('   AURORA池子配置:');
    const auroraPool = JUMBO_TARGET_POOLS.find(pool => pool.poolId === 0);
    if (auroraPool) {
      console.log('   ✅ 找到AURORA池子配置:', JSON.stringify(auroraPool, null, 2));
    } else {
      console.log('   ❌ 未找到AURORA池子配置');
    }

    console.log('\n   TOKEN_ADDRESSES中的AURORA:');
    if (TOKEN_ADDRESSES.AURORA) {
      console.log(`   ✅ AURORA地址: ${TOKEN_ADDRESSES.AURORA}`);
    } else {
      console.log('   ❌ TOKEN_ADDRESSES中没有AURORA');
    }

    console.log('\n   TOKEN_DECIMALS中的AURORA:');
    const auroraDecimals = TOKEN_DECIMALS['aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near'];
    if (auroraDecimals !== undefined) {
      console.log(`   ✅ AURORA精度: ${auroraDecimals}`);
    } else {
      console.log('   ❌ TOKEN_DECIMALS中没有AURORA');
    }

    console.log('\n   isTargetPool(0)检查:');
    const isTarget = isTargetPool(0);
    console.log(`   结果: ${isTarget ? '✅ 是目标池子' : '❌ 不是目标池子'}`);

    console.log('\n🎉 详细调试完成！');

  } catch (error) {
    console.error('❌ 调试过程中发生错误:', error);
  }
}

// 运行调试
if (require.main === module) {
  debugAuroraDetailed().catch(error => {
    console.error('❌ 调试失败:', error);
    process.exit(1);
  });
}

export { debugAuroraDetailed };
