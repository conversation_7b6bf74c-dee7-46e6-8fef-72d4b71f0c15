# PM2 套利机器人管理指南

## 🚀 快速开始

### 1. 安装PM2
```bash
npm install -g pm2
```

### 2. 创建必要目录
```bash
mkdir -p logs pids
```

### 3. 启动套利机器人
```bash
pm2 start ecosystem.config.js --env production
```

## 📋 常用命令

### 进程管理
```bash
# 查看所有进程状态
pm2 list

# 查看详细信息
pm2 show arbitrage-bot

# 重启机器人
pm2 restart arbitrage-bot

# 停止机器人
pm2 stop arbitrage-bot

# 删除进程
pm2 delete arbitrage-bot

# 重新加载配置
pm2 reload arbitrage-bot
```

### 日志管理
```bash
# 查看实时日志
pm2 logs arbitrage-bot

# 查看最近100行日志
pm2 logs arbitrage-bot --lines 100

# 清空日志
pm2 flush arbitrage-bot

# 查看错误日志
pm2 logs arbitrage-bot --err

# 查看输出日志
pm2 logs arbitrage-bot --out
```

### 监控
```bash
# 打开监控面板
pm2 monit

# 查看进程资源使用
pm2 show arbitrage-bot
```

## 🔧 配置说明

### 关键配置项
- **instances: 1** - 单实例运行，避免套利冲突
- **autorestart: true** - 自动重启
- **max_memory_restart: '1G'** - 内存限制
- **max_restarts: 10** - 最大重启次数
- **restart_delay: 5000** - 重启延迟5秒

### 日志文件位置
- 综合日志: `./logs/arbitrage-bot.log`
- 输出日志: `./logs/arbitrage-bot-out.log`
- 错误日志: `./logs/arbitrage-bot-error.log`
- 进程ID: `./pids/arbitrage-bot.pid`

## 📋 日志管理

### 🔧 自动日志轮转配置
项目已配置自动日志轮转功能：
- **单文件最大大小**: 100MB
- **保留文件数量**: 10个
- **自动压缩**: 是
- **轮转时间**: 每天午夜
- **日期格式**: YYYY-MM-DD_HH-mm-ss

### 安装和配置日志轮转
```bash
# 运行日志管理脚本（推荐，首次使用）
./scripts/log-management.sh

# 手动安装PM2日志轮转模块
pm2 install pm2-logrotate

# 手动配置轮转参数
pm2 set pm2-logrotate:max_size 100M          # 单文件最大100MB
pm2 set pm2-logrotate:retain 10              # 保留10个文件
pm2 set pm2-logrotate:compress true          # 压缩旧文件
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD-HH-mm-ss
pm2 set pm2-logrotate:rotateModule true
pm2 set pm2-logrotate:workerInterval 30      # 每30秒检查一次
pm2 set pm2-logrotate:rotateInterval 0 0 * * * # 每天午夜轮转

# 查看当前配置
pm2 conf pm2-logrotate
```

### 定期日志清理
```bash
# 手动运行清理脚本
./scripts/auto-log-cleanup.sh

# 设置定时清理（每天凌晨2点）
crontab -e
# 添加以下行：
# 0 2 * * * cd /path/to/your/project && ./scripts/auto-log-cleanup.sh

# 查看当前定时任务
crontab -l
```

### 日志文件结构
```
logs/
├── arbitrage-bot.log              # 当前主日志文件
├── arbitrage-bot-out.log          # 当前标准输出日志
├── arbitrage-bot-error.log        # 当前错误日志
├── arbitrage-bot.log.1.gz         # 轮转的压缩日志（最新）
├── arbitrage-bot.log.2.gz         # 轮转的压缩日志
├── arbitrage-bot.log.3.gz         # 轮转的压缩日志
├── ...                            # 更多轮转文件（最多10个）
└── cleanup.log                    # 清理操作日志
```

### 日志管理命令
```bash
# 查看实时日志
pm2 logs arbitrage-bot

# 查看最近100行日志
pm2 logs arbitrage-bot --lines 100

# 查看错误日志
pm2 logs arbitrage-bot --err

# 清空当前日志
pm2 flush arbitrage-bot

# 手动触发日志轮转
pm2 reloadLogs

# 查看日志文件大小
ls -lh ./logs/

# 查看压缩日志内容
zcat ./logs/arbitrage-bot.log.1.gz | tail -100
```

## 🛠️ 故障排除

### 常见问题

#### 1. 进程启动失败
```bash
# 检查日志
pm2 logs arbitrage-bot --err

# 检查配置文件
pm2 show arbitrage-bot
```

#### 2. 内存泄漏
```bash
# 查看内存使用
pm2 monit

# 手动重启
pm2 restart arbitrage-bot
```

#### 3. 频繁重启
```bash
# 查看重启历史
pm2 show arbitrage-bot

# 检查错误日志
pm2 logs arbitrage-bot --err --lines 50
```

### 调试模式
```bash
# 停止PM2进程
pm2 stop arbitrage-bot

# 直接运行查看详细错误
npx ts-node src/index.ts

# 重新启动PM2
pm2 start ecosystem.config.js
```

## 🔄 开机自启动

### 设置开机自启动
```bash
# 保存当前进程列表
pm2 save

# 生成启动脚本
pm2 startup

# 按照提示执行生成的命令（通常需要sudo）
```

### 取消开机自启动
```bash
pm2 unstartup
```

## 📊 性能监控

### 基本监控
```bash
# 实时监控
pm2 monit

# 查看进程详情
pm2 show arbitrage-bot
```

### 高级监控（可选）
```bash
# 安装PM2 Plus（在线监控）
pm2 install pm2-server-monit

# 或使用PM2 Web界面
pm2 web
```

## 🔐 安全建议

1. **环境变量安全**
   - 确保`.env`文件权限正确: `chmod 600 .env`
   - 不要在配置文件中硬编码敏感信息

2. **日志安全**
   - 定期清理日志文件
   - 确保日志目录权限正确

3. **进程安全**
   - 使用非root用户运行
   - 定期更新依赖包

## 📈 性能优化

1. **内存管理**
   - 根据实际使用调整`max_memory_restart`
   - 监控内存使用趋势

2. **日志管理**
   - 定期轮转日志文件
   - 考虑使用外部日志系统

3. **重启策略**
   - 根据网络稳定性调整`max_restarts`
   - 优化`restart_delay`设置

## 🎯 最佳实践

1. **部署前测试**
   ```bash
   # 本地测试
   npx ts-node src/index.ts
   
   # PM2测试
   pm2 start ecosystem.config.js
   pm2 logs arbitrage-bot
   ```

2. **定期维护**
   - 每周检查日志
   - 每月更新依赖
   - 定期备份配置

3. **监控告警**
   - 设置内存使用告警
   - 监控重启频率
   - 关注错误日志

## 📞 支持

如果遇到问题，请检查：
1. PM2日志: `pm2 logs arbitrage-bot`
2. 系统资源: `pm2 monit`
3. 配置文件: `ecosystem.config.js`
4. 环境变量: `.env`文件
