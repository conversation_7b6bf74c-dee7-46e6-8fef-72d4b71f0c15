# REF-VEAX 套利机器人

一个自动化的NEAR生态套利机器人，在REF Finance和VEAX DEX之间寻找并执行套利机会。

## 🎯 功能特点

- **自动监控**: 每秒检查NEAR-USDC和NEAR-USDT的套利机会
- **智能执行**: 自动执行发现的套利交易
- **动态金额**: 三档位动态交易金额，根据利润自动调整规模
- **风险管理**: 内置风险控制和失败处理机制
- **高性能**: 优化的报价查询，减少75%的RPC调用
- **实时监控**: 命令行界面，实时显示运行状态

## 🚀 快速开始

### 1. 环境配置

创建 `.env` 文件：

```bash
# NEAR网络配置
RPC_URL=https://rpc.mainnet.near.org
ACCOUNT_ID=your-account.near
PRIVATE_KEY=ed25519:your-private-key

# 可选配置
NETWORK_ID=mainnet
```

### 2. 安装依赖

```bash
npm install
```

### 3. 运行机器人

```bash
# 查看帮助
npm run bot help

# 检查配置
npm run bot config

# 测试模式（只监控，不执行交易）
npm run bot test

# 生产模式（完整套利机器人）
npm run bot start
```

## 📚 文档

- **[REF Finance 完整开发指南](./REF_Finance_完整开发指南.md)** - 完整的技术实现文档，包含所有代码细节
- **[RayNear 套利总结](./RayNear套利总结.md)** - 其他项目经验总结
- **[ref-intents 项目开发总结](./ref-intents项目开发总结2.md)** - 相关项目开发经验

## ✨ 核心功能

- ✅ **双系统并行报价**：V1 Smart Router + DCL v2 直接合约调用
- ✅ **智能报价选择**：自动选择输出金额最大的报价
- ✅ **完整错误处理**：优雅处理网络错误和合约调用失败
- ✅ **详细日志输出**：清晰的报价对比和调试信息
- ✅ **类型安全**：完整的 TypeScript 类型定义
- ✅ **灵活测试工具**：支持任意交易对测试和调试

## 🎯 REF Finance 报价机制总结

### 核心发现
1. **双合约架构**: V1 (`v2.ref-finance.near`) + DCL v2 (`dclv2.ref-labs.near`)
2. **不能混用**: 每笔交易只能使用一套系统
3. **并行查询**: 同时调用两套系统，选择最优报价
4. **分别查询**: DCL v2 必须分别查询每个费用等级的池子

### 关键技术点
- **V1**: Smart Router API，支持多跳路径
- **DCL v2**: 直接合约调用，4个费用等级 (0.01%, 0.04%, 0.2%, 1%)
- **精度处理**: 输入使用精度单位，输出转换为人类可读
- **池子发现**: 动态查询实际存在的池子

### 性能表现
- **大额交易**: DCL v2 通常更优 (0.01-0.02% 价格优势)
- **小额交易**: V1 系统更稳定
- **复杂路径**: V1 系统支持更好
- **响应时间**: V1 ~2s, DCL v2 ~3s

## 🛠️ 项目结构

```
src/
├── types/           # 类型定义
├── config/          # 配置文件
├── services/        # 核心服务
│   ├── v1SmartRouter.ts      # V1 Smart Router
│   ├── dclv2Contract.ts      # DCL v2 合约
│   └── refQuoteService.ts    # 统一报价服务
├── tools/           # 测试工具
│   ├── customQuoteTester.ts  # 自定义测试
│   └── quickTest.ts          # 快速测试
├── debug/           # 调试工具
└── index.ts         # 主入口
```

## 📊 支持的代币

| 符号 | 地址 | 精度 |
|------|------|------|
| NEAR | wrap.near | 24 |
| USDT | usdt.tether-token.near | 6 |
| USDC | 17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1 | 6 |
| REF | token.v2.ref-finance.near | 18 |
| AURORA | aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near | 18 |
| WETH | c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2.factory.bridge.near | 18 |
| WBTC | 2260fac5e5542a773aa44fbcfedf7c193bc2c599.factory.bridge.near | 8 |

## 🧪 测试命令

```bash
# 基础测试
npm run test:custom NEAR USDT 1000
npm run test:custom USDC NEAR 100 0.5

# 批量测试 (交互模式)
npm run test:custom
# 选择模式 2，输入: 1,10,100,1000

# 预设测试套件
npm run test:quick

# 调试工具
npm run debug:dcl        # 查看 DCL v2 池子
npm run debug:amounts    # 测试不同金额
npm run debug:working    # 测试已知工作的交易对
```

## 📈 实际测试结果

| 测试场景 | 金额 | V1 报价 | DCL v2 报价 | 获胜者 | 价格差异 |
|----------|------|---------|-------------|--------|----------|
| NEAR→USDT | 1000 | **2367.976** | 2363.366 | V1 | 0.19% |
| USDT→NEAR | 25000 | **10490.842** | 10104.814 | V1 | 3.75% |
| USDT→NEAR | 100 | 42.105 | **42.108** | DCL v2 | 0.0077% |
| USDT→NEAR | 1000 | 420.321 | **420.367** | DCL v2 | 0.0110% |
| REF→USDC | 10000 | **782.423** | 无流动性 | V1 | N/A |
| WETH→USDC | 1 | **1233.236** | 无流动性 | V1 | N/A |

## 🎯 三档位动态金额系统

### 核心概念
根据实际交易利润动态调整交易金额，最大化资金利用率：

- **低档** (基础): 利润 >= 0.012 NEAR
- **中档** (2-3倍): 利润 >= 0.052 NEAR
- **高档** (3-5倍): 利润 >= 0.1 NEAR

### 工作原理
```
监控 → 发现机会 → 执行交易 → 根据实际利润调整下次金额 → 循环
```

### 配置示例
```typescript
// NEAR-USDC 配置
tradeAmounts: {
  low: "3",      // 基础 3 NEAR
  medium: "8",   // 中档 8 NEAR
  high: "15"     // 高档 15 NEAR
}
```

### 优势
- **自动优化**: 无需人工调整，根据市场自动适应
- **风险控制**: 低利润时用小金额，高利润时放大规模
- **资金效率**: 最大化资金利用率和收益

## 🚀 开发计划

### 已完成 ✅
- [x] REF Finance 和 VEAX 报价系统
- [x] 套利机会检测和执行
- [x] 自动余额管理
- [x] 风险控制和错误处理

### 进行中 🔄
- [ ] 三档位动态金额系统实现
- [ ] 配置接口扩展
- [ ] 档位管理器开发

### 计划中 📋
- [ ] 性能优化和监控
- [ ] 更多交易对支持
- [ ] 高级策略算法

## 📄 许可证

MIT License

---

**版本**: v1.1.0 | **状态**: 🔄 开发中 (三档位动态金额) | **更新**: 2024年12月
