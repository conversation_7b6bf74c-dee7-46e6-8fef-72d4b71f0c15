/**
 * 测试独立监控功能
 */

import { tradingPairManager } from './src/config/tradingPairs';

function testIndependentMonitoring() {
  console.log('🔍 测试独立监控配置...\n');

  // 获取启用的交易对
  const enabledPairs = tradingPairManager.getEnabledPairs();
  
  console.log(`📊 启用的交易对: ${enabledPairs.length}个\n`);

  // 显示每个交易对的监控间隔
  enabledPairs.forEach((pair, index) => {
    const interval = pair.checkInterval || 3000; // 默认3秒
    const intervalSeconds = interval / 1000;
    
    let amountDisplay: string;
    if (pair.dynamicAmount?.enabled) {
      amountDisplay = `动态(${pair.dynamicAmount.low}-${pair.dynamicAmount.high})`;
    } else if (pair.tradeAmount) {
      amountDisplay = pair.tradeAmount;
    } else {
      amountDisplay = '未配置';
    }
    
    console.log(`${index + 1}. ${pair.id}:`);
    console.log(`   📊 交易金额: ${amountDisplay} ${pair.tokenA.symbol}`);
    console.log(`   ⏱️ 监控间隔: ${intervalSeconds}秒`);
    console.log(`   🎯 优先级: ${intervalSeconds <= 1 ? '高' : intervalSeconds <= 5 ? '中' : '低'}`);
    console.log('');
  });

  // 统计不同间隔的交易对数量
  const intervalStats = new Map<number, number>();
  enabledPairs.forEach(pair => {
    const interval = pair.checkInterval || 3000;
    intervalStats.set(interval, (intervalStats.get(interval) || 0) + 1);
  });

  console.log('📈 监控间隔统计:');
  Array.from(intervalStats.entries())
    .sort(([a], [b]) => a - b)
    .forEach(([interval, count]) => {
      const seconds = interval / 1000;
      console.log(`   ${seconds}秒: ${count}个交易对`);
    });

  // 模拟启动时的错开时间
  console.log('\n🚀 模拟启动时序:');
  enabledPairs.forEach((pair, index) => {
    const startDelay = index * 200; // 每个交易对错开200ms
    const interval = pair.checkInterval || 3000;
    
    console.log(`   ${pair.id}: ${startDelay}ms后启动，每${interval/1000}秒检查`);
  });

  console.log('\n✅ 独立监控配置测试完成！');
}

// 运行测试
if (require.main === module) {
  testIndependentMonitoring();
}

export { testIndependentMonitoring };
