# 交易执行功能设置指南

## 🚀 快速开始

### 1. 环境变量设置

运行交互式设置脚本：

```bash
npm run setup:env
```

或者手动创建`.env`文件：

```bash
cp .env.example .env
# 编辑.env文件，填入你的配置
```

### 2. 必需的环境变量

```env
# NEAR账户配置
NEAR_ACCOUNT_ID=your-account.near
NEAR_PRIVATE_KEY=ed25519:your-private-key-here
NEAR_NETWORK_ID=mainnet

# 安全配置
ENABLE_REAL_TRADING=false
MAX_TRADE_AMOUNT_NEAR=10
REQUIRE_CONFIRMATION=true
```

## 🧪 测试功能

### REF Finance执行测试

```bash
npm run test:ref-execution
```

### VEAX执行测试

```bash
npm run test:veax-execution
```

### 套利监控测试

```bash
npm run test:arbitrage
```

## 🔧 功能特性

### REF Finance执行服务

- ✅ **V1系统交易**：支持Smart Router多跳路径
- ✅ **DCL v2系统交易**：支持集中流动性池
- ✅ **自动路径选择**：根据报价结果自动选择最优系统
- ✅ **交易构建**：从API响应自动构建交易参数

### VEAX执行服务

- ✅ **自动注册**：自动检查和注册用户/代币
- ✅ **批量注册**：支持批量注册多个代币
- ✅ **状态检查**：检查注册状态和余额
- ✅ **交易执行**：`swap_exact_in`方法执行

## 🔒 安全特性

### 环境变量管理

- 私钥通过环境变量管理，不硬编码
- `.env`文件已添加到`.gitignore`
- 支持配置验证和错误提示

### 交易安全

- 默认禁用真实交易（`ENABLE_REAL_TRADING=false`）
- 最大交易金额限制
- 可选的交易确认机制
- 完整的错误处理和重试逻辑

## 📋 使用示例

### REF Finance交易

```typescript
import RefExecutionService from './services/refExecutionService';
import { EXECUTION_CONFIG } from './config/executionConfig';

const refExecution = new RefExecutionService(
  EXECUTION_CONFIG.ACCOUNT_ID,
  EXECUTION_CONFIG.PRIVATE_KEY,
  EXECUTION_CONFIG.NETWORK_ID
);

await refExecution.initialize();

// 执行V1交易
const result = await refExecution.executeV1Swap(
  quoteResult,
  inputAmountWei,
  minOutputAmountWei,
  slippage
);
```

### VEAX交易

```typescript
import VeaxExecutionService from './services/veaxExecutionService';
import { EXECUTION_CONFIG } from './config/executionConfig';

const veaxExecution = new VeaxExecutionService(
  EXECUTION_CONFIG.ACCOUNT_ID,
  EXECUTION_CONFIG.PRIVATE_KEY,
  EXECUTION_CONFIG.NETWORK_ID
);

await veaxExecution.initialize();

// 自动处理注册和交易
const result = await veaxExecution.executeSwap(
  tokenIn,
  tokenOut,
  amountIn,
  minAmountOut
);
```

## ⚠️ 重要提醒

1. **私钥安全**：请妥善保管私钥，不要分享给他人
2. **测试先行**：建议先在小额资金上测试
3. **网络费用**：mainnet交易需要真实的NEAR作为gas费
4. **滑点设置**：根据市场情况合理设置滑点容忍度
5. **监控交易**：执行交易后请监控交易状态

## 🛠️ 故障排除

### 常见错误

1. **环境变量缺失**
   ```
   ❌ 缺少必要的环境变量: NEAR_ACCOUNT_ID
   ```
   解决：运行`npm run setup:env`设置环境变量

2. **私钥格式错误**
   ```
   ❌ 私钥格式不正确
   ```
   解决：确保私钥格式为`ed25519:xxx`

3. **账户余额不足**
   ```
   ❌ 账户余额不足
   ```
   解决：确保账户有足够的NEAR余额

4. **网络连接问题**
   ```
   ❌ 网络请求失败
   ```
   解决：检查网络连接和RPC节点状态

### 调试模式

设置环境变量启用详细日志：

```env
DEBUG_MODE=true
LOG_LEVEL=debug
```

## 📞 支持

如果遇到问题，请检查：

1. 环境变量配置是否正确
2. 账户余额是否充足
3. 网络连接是否正常
4. 私钥权限是否正确

更多技术细节请参考源代码注释和类型定义。

## 🎉 VEAX执行问题解决方案

### ✅ 问题已完全解决！

经过深入研究VEAX官方文档，我们成功解决了所有VEAX交易执行问题：

#### 🔍 根本原因
1. **代币未在VEAX合约中注册**（不仅仅是代币合约注册）
2. **存储余额不足**（用户在VEAX中的存储余额为0）

#### 🛠️ 解决方案
1. **双重注册机制**：
   - 代币合约注册：`tokenContract.storage_deposit()`
   - VEAX合约注册：`veax.register_tokens()`

2. **智能存储管理**：
   - 自动检测存储余额不足
   - 自动计算所需存储空间（每个代币约0.00284 NEAR）
   - 自动增加存储余额

3. **完整的错误处理**：
   - 自动重试机制
   - 详细的错误诊断
   - 智能恢复策略

#### 🧪 测试验证
```
✅ 交易成功：0.1 NEAR → 0.222045 USDC
✅ 汇率：1 NEAR = 2.22045 USDC
✅ 交易哈希：3DL73e5Fw2psTCsesXT61Ao5Z13h1aF8ysLnhaovbYXd
```

#### 🔧 诊断工具
```bash
# 完整注册诊断（推荐）
npm run diagnose:veax-registration

# 真实交易测试
npm run test:veax-real

# 不同代币对测试
npm run test:veax-pairs
```

#### 📚 技术细节
VEAX使用批量操作系统，通过`ft_transfer_call`执行：
```json
["Deposit", {"SwapExactIn": {...}}, {"Withdraw": [...]}]
```

关键技术发现：
- 使用更好的RPC提供商（fastnear.com）
- 正确的批量操作格式
- 自动化的注册和存储管理
- 符合VEAX官方文档规范
