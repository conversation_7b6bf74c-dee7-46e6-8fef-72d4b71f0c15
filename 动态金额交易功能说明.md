# 🎯 动态金额交易功能说明

## 📋 功能概述

动态金额交易功能是REF-VEAX套利机器人的核心特性之一，它能够根据实际交易利润自动调整下次查询的交易金额，实现智能的三档位金额管理。

## 🔧 工作原理

### 三档位系统
- **低档 (Low)**: 基础交易金额，用于初始查询和低利润情况
- **中档 (Medium)**: 中等交易金额，当利润 >= 0.052 NEAR 时使用
- **高档 (High)**: 最大交易金额，当利润 >= 0.1 NEAR 时使用

### 渐进式调整逻辑
1. **初始状态**: 所有交易对从低档开始
2. **渐进式升档**: 避免激进跳跃，逐步试探市场
3. **渐进式降档**: 风险控制，避免剧烈档位变化
4. **自然调节**: 低利润时档位自然降低，无需强制重置
5. **实时统计**: 记录档位分布和平均利润

#### 具体调整规则（以NEAR-USDT为例）：

**当前低档 (12 NEAR)**:
- 利润 >= 0.1 NEAR → 升到中档 (18 NEAR) *[渐进升档，不直接跳高档]*
- 利润 >= 0.052 NEAR → 升到中档 (18 NEAR)
- 利润 < 0.052 NEAR → 保持低档 (12 NEAR)

**当前中档 (18 NEAR)**:
- 利润 >= 0.1 NEAR → 升到高档 (24 NEAR)
- 利润 >= 0.052 NEAR → 保持中档 (18 NEAR)
- 利润 < 0.052 NEAR → 降到低档 (12 NEAR)

**当前高档 (24 NEAR)**:
- 利润 >= 0.1 NEAR → 保持高档 (24 NEAR)
- 利润 >= 0.052 NEAR → 降到中档 (18 NEAR) *[渐进降档]*
- 利润 < 0.052 NEAR → 降到中档 (18 NEAR) *[渐进降档，不直接跳低档]*

## ⚙️ 配置方法

### 1. 在交易对配置中启用动态金额

```typescript
{
  id: 'NEAR-USDT',
  tokenA: TOKENS.NEAR,
  tokenB: TOKENS.USDT,
  enabled: true,
  // 注释掉固定金额
  // tradeAmount: '12',
  
  // 启用动态金额配置
  dynamicAmount: {
    enabled: true,
    low: '12',      // 基础 12 NEAR
    medium: '18',   // 中档 18 NEAR  
    high: '24'      // 高档 24 NEAR
  },
  checkInterval: 1000,
}
```

### 2. 利润阈值配置

利润阈值在 `dynamicAmountManager.ts` 中定义：

```typescript
export const PROFIT_THRESHOLDS = {
  MEDIUM: 0.052,  // 中档阈值：>= 0.052 NEAR
  HIGH: 0.1       // 高档阈值：>= 0.1 NEAR
} as const;
```

## 📊 已启用的交易对

目前已启用动态金额的交易对：

| 交易对 | 低档 | 中档 | 高档 | 状态 |
|--------|------|------|------|------|
| NEAR-USDC | 3 NEAR | 8 NEAR | 15 NEAR | ✅ 已启用 |
| NEAR-USDT | 12 NEAR | 18 NEAR | 24 NEAR | ✅ 已启用 |
| NEAR-BLACKDRAGON | 5 NEAR | 10 NEAR | 18 NEAR | ✅ 已启用 |

## 🔍 监控和调试

### 1. 实时统计显示

套利机器人运行时会定期显示动态金额统计：

```
📈 动态金额档位统计:
   NEAR-USDT: 当前中档 | 低档45.2% | 中档38.7% | 高档16.1% | 平均利润0.0847 NEAR
   NEAR-USDC: 当前低档 | 低档78.3% | 中档21.7% | 高档0.0% | 平均利润0.0234 NEAR
```

### 2. 档位调整日志

当档位发生变化时会显示详细日志：

```
📊 NEAR-USDT: 利润0.0634 NEAR → 切换到中档 (18 NEAR)
📊 NEAR-USDT: 利润0.1245 NEAR → 切换到高档 (24 NEAR)
🔄 NEAR-USDC: 连续无机会，重置到低档 (3 NEAR)
```

### 3. 测试脚本

运行测试脚本验证功能：

```bash
npm run test:dynamic-amount
```

## 🎯 核心修复内容

本次修复解决了以下问题：

### 1. 套利成功后的渐进式金额调整 ✅
- **位置**: `src/arbitrageBot.ts` 第567行 + `src/services/dynamicAmountManager.ts` 第72-153行
- **修复**: 添加了 `dynamicAmountManager.adjustNextAmount()` 调用，实现渐进式调整逻辑
- **效果**: 套利成功后根据当前档位和实际利润渐进式调整下次查询金额
- **核心特性**: 避免激进档位跳跃，逐步试探市场，控制风险

### 2. 无套利机会的处理 ✅
- **位置**: `src/arbitrageBot.ts` 第215-219行
- **修复**: 添加了无机会时的 `handleNoOpportunity()` 调用
- **效果**: 连续无机会时自动重置到低档

### 3. 日志显示修复 ✅
- **位置**: `src/arbitrageBot.ts` 第305-306行
- **修复**: 使用实际查询金额 `queryAmount` 替代固定的 `pair.tradeAmount`
- **效果**: 日志正确显示当前使用的动态金额

### 4. 配置优化 ✅
- **位置**: `src/config/tradingPairs.ts`
- **修复**: 为主要交易对启用动态金额配置
- **效果**: 更多交易对可以使用动态金额功能

## 🚀 使用建议

1. **初期观察**: 启用动态金额后，观察档位分布和平均利润
2. **参数调整**: 根据实际表现调整各档位的金额设置
3. **阈值优化**: 可以根据市场情况调整利润阈值
4. **风险控制**: 高档金额不要设置过大，避免单次损失过多

## 📈 预期效果

- **提高效率**: 高利润时使用更大金额，最大化收益
- **降低风险**: 低利润时使用较小金额，控制风险
- **智能适应**: 根据市场情况自动调整交易规模
- **统计分析**: 提供详细的档位分布和利润统计

---

**注意**: 动态金额功能现已完全实装并可正常使用！🎉
