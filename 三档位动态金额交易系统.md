# 三档位动态金额交易系统

## 📋 概述

基于用户需求，设计了一个简化的三档位动态金额交易系统，用于REF Finance套利机器人。该系统根据实际交易利润动态调整下次查询的交易金额，以最大化资金利用率和套利收益。

## 🎯 核心逻辑

### **工作流程**
```
1. 监控阶段：用当前档位金额查询套利机会
2. 发现机会：直接用查询时的金额执行交易
3. 执行完成：根据实际利润调整下次查询金额
4. 循环监控：用新的档位金额继续查询
```

### **关键原则**
- ✅ **一次查询，一次执行** - 不重复查询，避免错过机会
- ✅ **基于实际利润调整** - 根据真实交易结果而非预测调整
- ✅ **渐进式调整** - 逐步升档或降档，避免激进变化
- ✅ **简单配置** - 每个币种只需配置3个金额档位

## 📊 三档位设计

### **利润阈值**
```typescript
const PROFIT_LEVELS = {
  low: 0.012,      // 低档：>= 0.012 NEAR (最小利润要求)
  medium: 0.052,   // 中档：>= 0.052 NEAR
  high: 0.1        // 高档：>= 0.1 NEAR
};
```

### **档位切换规则**
```typescript
function adjustNextAmount(actualProfit: number, pair: TradingPairConfig): string {
  if (actualProfit >= 0.1) {
    return pair.tradeAmounts.high;    // 升到高档
  } else if (actualProfit >= 0.052) {
    return pair.tradeAmounts.medium;  // 升到中档
  } else {
    return pair.tradeAmounts.low;     // 保持或降到低档
  }
}
```

## 🔧 配置接口扩展

### **扩展TradingPairConfig接口**
```typescript
export interface TradingPairConfig {
  id: string;
  tokenA: TokenInfo;
  tokenB: TokenInfo;
  enabled: boolean;
  minProfitThreshold: number;  // 现有的最小利润要求
  
  // 新增：三档位交易金额配置
  tradeAmounts: {
    low: string;     // 低档金额 (利润 >= 0.012)
    medium: string;  // 中档金额 (利润 >= 0.052)
    high: string;    // 高档金额 (利润 >= 0.1)
  };
  
  checkInterval: number;
  description?: string;
}
```

### **NEAR-USDC参考配置**
```typescript
{
  id: 'NEAR-USDC',
  tokenA: TOKENS.NEAR,
  tokenB: TOKENS.USDC,
  enabled: true,
  minProfitThreshold: 0.012,    // 最小利润要求
  
  // 三档位交易金额
  tradeAmounts: {
    low: "3",      // 利润 0.012-0.052 → 用3 NEAR
    medium: "8",   // 利润 0.052-0.1   → 用8 NEAR  
    high: "15"     // 利润 >= 0.1      → 用15 NEAR
  },
  
  checkInterval: 1000,
  description: 'NEAR-USDC套利交易对'
}
```

## 📈 实际运行示例

```
时间    查询金额   发现利润   执行?   实际利润   下次查询金额   档位变化
10:00   3 NEAR    0.08      是      0.075     8 NEAR        低档→中档
10:01   8 NEAR    0.15      是      0.14      15 NEAR       中档→高档
10:02   15 NEAR   0.12      是      0.11      15 NEAR       保持高档
10:03   15 NEAR   0.05      是      0.045     8 NEAR        高档→中档
10:04   8 NEAR    0.03      是      0.028     3 NEAR        中档→低档
10:05   3 NEAR    0.008     否      -         3 NEAR        无机会，保持低档
```

## 🎯 实现计划

### **阶段1: 配置扩展**
- [ ] 扩展TradingPairConfig接口
- [ ] 更新现有配置文件
- [ ] 添加档位管理器类

### **阶段2: 核心逻辑实现**
- [ ] 实现动态金额计算器
- [ ] 集成到监控循环
- [ ] 添加档位切换日志

### **阶段3: 测试和优化**
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化

### **阶段4: 文档和部署**
- [ ] 更新用户文档
- [ ] 部署到生产环境
- [ ] 监控和调优

## 📝 开发进度

### **当前状态**: 实现阶段 🔄
- [x] 需求分析完成
- [x] 核心逻辑设计完成
- [x] 配置接口设计完成
- [x] 实现计划制定完成
- [x] 配置接口扩展完成
- [x] 动态金额管理器实现完成
- [x] 集成到监控系统完成
- [x] 模拟交易执行完成
- [x] 统计显示集成完成

### **下一步**: 测试和优化
- [ ] 单元测试编写
- [ ] 集成测试验证
- [ ] 性能优化调整

## 🔍 技术细节

### **档位管理器类设计**
```typescript
class DynamicAmountManager {
  private currentAmounts: Map<string, string> = new Map();
  
  // 获取当前查询金额
  getCurrentAmount(pairId: string): string;
  
  // 根据实际利润调整下次金额
  adjustNextAmount(pairId: string, actualProfit: number, pair: TradingPairConfig): void;
  
  // 重置到默认档位
  resetToDefault(pairId: string, pair: TradingPairConfig): void;
  
  // 获取档位统计
  getStats(pairId: string): DynamicAmountStats;
}
```

### **集成点**
1. **监控循环**: 使用动态金额查询
2. **交易执行**: 记录实际利润
3. **结果处理**: 调整下次查询金额
4. **状态显示**: 显示当前档位信息

## 📊 预期效果

### **资金利用率提升**
- 低利润时使用小金额，降低风险
- 高利润时使用大金额，最大化收益
- 根据市场条件自动调整

### **风险控制**
- 渐进式调整，避免激进变化
- 最小/最大金额限制
- 基于实际结果而非预测

### **操作简化**
- 自动化档位切换
- 简单的三档位配置
- 清晰的切换逻辑

## 🛠️ 实现代码示例

### **动态金额管理器**
```typescript
export class DynamicAmountManager {
  private currentAmounts: Map<string, string> = new Map();
  private stats: Map<string, DynamicAmountStats> = new Map();

  constructor() {
    // 初始化所有交易对为低档
    this.initializeDefaults();
  }

  /**
   * 获取当前查询金额
   */
  getCurrentAmount(pairId: string, pair: TradingPairConfig): string {
    const current = this.currentAmounts.get(pairId);
    if (!current) {
      // 首次查询，使用低档金额
      const defaultAmount = pair.tradeAmounts.low;
      this.currentAmounts.set(pairId, defaultAmount);
      return defaultAmount;
    }
    return current;
  }

  /**
   * 根据实际利润调整下次查询金额
   */
  adjustNextAmount(pairId: string, actualProfit: number, pair: TradingPairConfig): void {
    let nextAmount: string;
    let level: string;

    if (actualProfit >= 0.1) {
      nextAmount = pair.tradeAmounts.high;
      level = '高档';
    } else if (actualProfit >= 0.052) {
      nextAmount = pair.tradeAmounts.medium;
      level = '中档';
    } else {
      nextAmount = pair.tradeAmounts.low;
      level = '低档';
    }

    const currentAmount = this.currentAmounts.get(pairId) || pair.tradeAmounts.low;
    this.currentAmounts.set(pairId, nextAmount);

    // 记录统计
    this.updateStats(pairId, currentAmount, nextAmount, actualProfit);

    if (currentAmount !== nextAmount) {
      console.log(`📊 ${pairId}: 利润${actualProfit} NEAR → 切换到${level} (${nextAmount} NEAR)`);
    } else {
      console.log(`📊 ${pairId}: 利润${actualProfit} NEAR → 保持${level} (${nextAmount} NEAR)`);
    }
  }

  /**
   * 重置到默认档位（连续无机会时）
   */
  resetToDefault(pairId: string, pair: TradingPairConfig): void {
    const defaultAmount = pair.tradeAmounts.low;
    const currentAmount = this.currentAmounts.get(pairId);

    if (currentAmount !== defaultAmount) {
      this.currentAmounts.set(pairId, defaultAmount);
      console.log(`🔄 ${pairId}: 重置到低档 (${defaultAmount} NEAR)`);
    }
  }

  /**
   * 获取档位统计
   */
  getStats(pairId: string): DynamicAmountStats | undefined {
    return this.stats.get(pairId);
  }

  private updateStats(pairId: string, currentAmount: string, nextAmount: string, profit: number): void {
    // 实现统计逻辑
  }
}
```

### **集成到监控循环**
```typescript
export class ArbitrageMonitor {
  private dynamicAmountManager = new DynamicAmountManager();

  async monitorPair(pair: TradingPairConfig): Promise<void> {
    // 获取当前档位的查询金额
    const queryAmount = this.dynamicAmountManager.getCurrentAmount(pair.id, pair);

    // 使用动态金额查询套利机会
    const opportunity = await this.findArbitrageOpportunity(pair, queryAmount);

    if (opportunity && opportunity.profit >= pair.minProfitThreshold) {
      console.log(`💰 ${pair.id}: 发现机会，利润${opportunity.profit} NEAR，使用${queryAmount} NEAR执行`);

      // 执行套利交易
      const result = await this.executeArbitrage(opportunity);

      if (result.success) {
        // 根据实际利润调整下次查询金额
        this.dynamicAmountManager.adjustNextAmount(
          pair.id,
          result.actualProfit,
          pair
        );
      }
    } else {
      // 连续无机会时考虑重置到低档
      this.handleNoOpportunity(pair);
    }
  }

  private handleNoOpportunity(pair: TradingPairConfig): void {
    // 实现无机会处理逻辑
    // 比如连续10次无机会就重置到低档
  }
}
```

## 📋 配置迁移指南

### **从固定金额迁移到三档位**

#### **步骤1: 备份现有配置**
```bash
cp src/config/tradingPairs.ts src/config/tradingPairs.backup.ts
```

#### **步骤2: 更新接口定义**
```typescript
// 在 TradingPairConfig 中添加
tradeAmounts: {
  low: string;
  medium: string;
  high: string;
};

// 保持向后兼容
tradeAmount?: string;  // 可选，用于迁移
```

#### **步骤3: 转换现有配置**
```typescript
// 自动转换脚本
function convertToThreeTier(oldConfig: TradingPairConfig): TradingPairConfig {
  const baseAmount = parseFloat(oldConfig.tradeAmount);

  return {
    ...oldConfig,
    tradeAmounts: {
      low: oldConfig.tradeAmount,                    // 保持原金额作为低档
      medium: (baseAmount * 2).toString(),          // 2倍作为中档
      high: (baseAmount * 3).toString()             // 3倍作为高档
    }
  };
}
```

## 🔍 监控和调试

### **档位切换日志**
```
📊 NEAR-USDC: 利润0.075 NEAR → 切换到中档 (8 NEAR)
📊 NEAR-USDT: 利润0.14 NEAR → 切换到高档 (20 NEAR)
📊 NEAR-USDC.e: 利润0.028 NEAR → 保持低档 (2 NEAR)
🔄 NEAR-LONK: 重置到低档 (2 NEAR)
```

### **统计信息显示**
```
📈 动态金额统计 (最近24小时):
   NEAR-USDC: 低档45% | 中档35% | 高档20% | 平均利润0.067 NEAR
   NEAR-USDT: 低档60% | 中档30% | 高档10% | 平均利润0.045 NEAR
```

## 📝 开发过程记录

### **实现阶段 (2024-12-XX)**

#### **第一步: 扩展配置接口** ✅
```typescript
// 新增动态金额配置接口
export interface DynamicAmountConfig {
  enabled: boolean;
  low: string;
  medium: string;
  high: string;
}

// 扩展交易对配置，保持向后兼容
export interface TradingPairConfig {
  // ... 现有字段
  tradeAmount?: string;          // 可选，向后兼容
  dynamicAmount?: DynamicAmountConfig; // 新增动态配置
}
```

**关键决策**: 保持向后兼容性，现有固定金额配置仍然有效。

#### **第二步: 实现动态金额管理器** ✅
创建 `src/services/dynamicAmountManager.ts`，核心功能：
- `getCurrentAmount()`: 获取当前查询金额
- `adjustNextAmount()`: 根据实际利润调整下次金额
- `handleNoOpportunity()`: 处理无机会情况
- `resetToDefault()`: 重置到低档
- `displayStatsSummary()`: 显示统计信息

**核心逻辑实现**:
```typescript
// 档位切换逻辑
if (actualProfit >= 0.1) {
  nextLevel = 'high';
} else if (actualProfit >= 0.052) {
  nextLevel = 'medium';
} else {
  nextLevel = 'low';
}
```

#### **第三步: 集成到监控系统** ✅
修改 `src/services/arbitrageMonitor.ts`：
- 导入动态金额管理器
- 在 `checkArbitrageOpportunity()` 中使用动态金额查询
- 添加无机会处理逻辑
- 更新套利机会分析，使用动态金额

**关键修改**:
```typescript
// 获取动态查询金额
const queryAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);

// 使用动态金额查询
const [refQuote, veaxQuote] = await Promise.all([
  this.getREFQuote(pair.tokenA.id, pair.tokenB.id, queryAmount),
  this.getVEAXQuote(pair.tokenA.id, pair.tokenB.id, queryAmount)
]);
```

#### **第四步: 模拟交易执行** ✅
添加模拟交易执行功能，用于测试动态金额调整：
- 模拟交易延迟 (1-3秒)
- 模拟实际利润 (90%-110%的预期利润)
- 调用动态金额调整逻辑

**实现思路**: 在真实交易执行完成前，先用模拟执行验证逻辑。

#### **第五步: 统计显示集成** ✅
在主程序 `src/arbitrageBot.ts` 中：
- 导入动态金额管理器
- 在状态显示中添加档位统计
- 定期显示档位分布和平均利润

### **开发中的关键思考**

#### **设计决策**
1. **向后兼容**: 保留 `tradeAmount` 字段，确保现有配置不受影响
2. **模块化设计**: 动态金额管理器独立于监控系统，便于测试和维护
3. **渐进式调整**: 避免激进的金额变化，确保系统稳定

#### **技术挑战**
1. **状态管理**: 需要跟踪每个交易对的当前档位和统计信息
2. **时机控制**: 确保在正确的时机调整金额（交易完成后）
3. **错误处理**: 处理配置错误和边界情况

#### **性能考虑**
1. **内存使用**: 使用Map存储状态，避免内存泄漏
2. **计算效率**: 简单的档位切换逻辑，避免复杂计算
3. **日志控制**: 合理的日志输出，避免信息过载

### **测试策略**
1. **单元测试**: 测试动态金额管理器的核心逻辑
2. **集成测试**: 验证与监控系统的集成
3. **模拟测试**: 使用模拟交易验证完整流程

---

**文档版本**: v1.1
**创建时间**: 2024-12-XX
**最后更新**: 2024-12-XX
**状态**: 实现完成，待测试
