# 动态金额功能开发记录

## 📅 开发时间线

### 2025-07-18 - 渐进式调整逻辑实现与问题修复

---

## 🎯 第一阶段：渐进式调整逻辑实现

### **用户需求理解**
用户指出当前的动态金额调整逻辑与文档设计不符：
- **文档设计**: 渐进式调整，避免激进档位跳跃
- **当前实现**: 直接根据利润跳转到对应档位

### **用户的正确理念**
以NEAR-USDT为例：
```
当前24 NEAR (高档) + 利润0.02 → 下次12 NEAR (低档)  ❌ 激进跳跃
当前24 NEAR (高档) + 利润0.08 → 下次18 NEAR (中档)  ✅ 渐进降档
当前24 NEAR (高档) + 利润0.12 → 下次24 NEAR (高档)  ✅ 保持档位
```

### **修改实现**
**文件**: `src/services/dynamicAmountManager.ts` (第72-153行)

**核心逻辑**:
```typescript
// 🎯 渐进式调整逻辑：基于当前档位和利润表现
if (actualProfit >= PROFIT_THRESHOLDS.HIGH) {
  // 高利润：保持当前档位或渐进升档
  if (currentLevel === 'low') {
    nextLevel = 'medium';  // 低档 → 中档（渐进升档，不直接跳高档）
  } else if (currentLevel === 'medium') {
    nextLevel = 'high';    // 中档 → 高档
  } else {
    nextLevel = 'high';    // 高档 → 保持高档
  }
} else if (actualProfit >= PROFIT_THRESHOLDS.MEDIUM) {
  // 中等利润：目标是中档
  nextLevel = currentLevel === 'high' ? 'medium' : 'medium';
} else {
  // 低利润：逐步降档到低档
  if (currentLevel === 'high') {
    nextLevel = 'medium';  // 高档 → 中档（渐进降档，不直接跳低档）
  } else {
    nextLevel = 'low';     // 中档/低档 → 低档
  }
}
```

**修改理由**: 
- 避免激进的档位跳跃，控制风险
- 逐步试探市场，而不是激进押注
- 符合用户的"渐进式调整"理念

---

## 🔍 第二阶段：利润计算精度问题修复

### **问题发现**
用户提供的日志显示严重的精度问题：
```
实际利润: 2.399313298193435e+25 NEAR  ❌ 错误
应该是: 0.023993... NEAR              ✅ 正确
```

### **问题根因**
`step2Result.outputAmount` 返回wei格式大数值 `23993132981934349361810355`，但代码直接用 `parseFloat()` 处理，导致精度溢出。

### **修复实现**
**文件**: `src/arbitrageBot.ts` (第562-583行)

**修复逻辑**:
```typescript
// 🔧 修复利润计算的精度问题
let actualProfit: number;

// 检查step2Result.outputAmount的格式
if (step2Result.outputAmountWei) {
  // 如果有wei格式的输出，使用精确转换
  const outputAmountHuman = this.fromWei(step2Result.outputAmountWei, decimals);
  actualProfit = parseFloat(outputAmountHuman) - parseFloat(opportunity.inputAmount);
} else if (step2Result.outputAmount && /^\d+$/.test(step2Result.outputAmount) && parseFloat(step2Result.outputAmount) > 1000000) {
  // 如果outputAmount看起来像wei格式（纯数字且很大）
  const outputAmountHuman = this.fromWei(step2Result.outputAmount, decimals);
  actualProfit = parseFloat(outputAmountHuman) - parseFloat(opportunity.inputAmount);
} else {
  // 否则假设是人类可读格式
  actualProfit = parseFloat(step2Result.outputAmount!) - parseFloat(opportunity.inputAmount);
}
```

**修改理由**:
- 智能检测输出格式（wei vs 人类可读）
- 使用精确的 `fromWei()` 方法转换大数值
- 避免 `parseFloat()` 处理wei格式导致的精度溢出

---

## 🚫 第三阶段：移除"连续10次无机会重置"机制

### **用户指导**
用户指出在渐进式调整逻辑下，"连续10次无机会重置"机制不再需要：
- **原理**: 低利润会自然导致档位逐步降低
- **结论**: 无需强制重置，让市场自然调节

### **修改实现**

**1. 主程序修改** (`src/arbitrageBot.ts`)
```typescript
// 移除前
} else {
  this.tradingPairs.forEach(pair => {
    dynamicAmountManager.handleNoOpportunity(pair.id, pair);
  });
}

// 修改后
} else {
  // 无套利机会时不需要特殊处理
  // 在渐进式调整逻辑下，档位会在下次有利润的交易中自然调整
}
```

**2. 动态金额管理器修改** (`src/services/dynamicAmountManager.ts`)
- 保留 `handleNoOpportunity` 方法仅用于统计
- 移除强制重置逻辑
- 添加注释说明渐进式调整下的新理念

**修改理由**:
- 符合渐进式调整的自然调节理念
- 避免破坏渐进式逻辑的强制重置
- 简化代码逻辑，减少不必要的复杂性

---

## ❌ 第四阶段：问题修复 - logCounter错误

### **问题发现**
TypeScript编译错误：
```
src/arbitrageBot.ts:218:18 - error TS2339: Property 'logCounter' does not exist on type 'ArbitrageBot'.
```

### **问题原因**
在移除"连续10次无机会重置"机制时，我引入了不存在的 `this.logCounter` 属性来控制日志频率。

### **错误代码**
```typescript
if (this.logCounter % 10 === 0) {
  console.log(`📊 当前无套利机会，继续监控...`);
}
```

### **修复方案**
采用用户建议的**方案1（简单修复）**：
```typescript
} else {
  // 📊 无套利机会时不需要特殊处理
  // 在渐进式调整逻辑下，档位会在下次有利润的交易中自然调整
  // 无需强制重置或频繁日志输出
}
```

**修复理由**:
- 在渐进式调整下，无套利机会时确实不需要特殊处理
- 避免引入不必要的复杂性（logCounter属性）
- 符合"自然调节"的设计理念
- 保持代码简洁性

---

## 📊 修改总结

### **核心改进**
1. ✅ **渐进式调整逻辑**: 避免激进档位跳跃，逐步试探市场
2. ✅ **精度问题修复**: 正确处理wei格式，避免精度溢出
3. ✅ **机制简化**: 移除不必要的强制重置机制
4. ✅ **代码修复**: 解决TypeScript编译错误

### **预期效果**
- 利润显示正确：`0.024 NEAR` 而不是 `2.39e+25 NEAR`
- 档位调整合理：基于当前档位渐进式调整
- 逻辑清晰：执行→计算→调整的清晰流程
- 代码稳定：无编译错误，可正常运行

### **测试建议**
1. `npm run test:dynamic-amount` - 验证渐进式逻辑
2. `npx tsc` - 验证TypeScript编译通过
3. `npm start` - 观察实际运行效果

---

## 🐛 第五阶段：档位卡死Bug修复

### **Bug发现**
用户发现重要问题：某些代币一直维持高档但长时间无套利机会，档位无法自然降低。

**日志证据**:
```
NEAR-BLACKDRAGON: 当前高档 | 平均利润0.0741 NEAR  ❌ 应该降档
NEAR-DOGSHIT: 当前高档 | 平均利润0.6549 NEAR     ✅ 保持高档正确
```

### **Bug根因分析**
**问题场景**:
1. 代币升到高档 → 执行交易成功 → 档位保持高档
2. 下次监控：高档查询 → **没有套利机会** → 档位卡在高档
3. 结果：代币永远停留在高档，无法降档

**技术原因**:
- ✅ 降档逻辑已实现（渐进式调整代码正确）
- ❌ 调用时机错误：只有在实际执行交易后才调用 `adjustNextAmount()`
- ❌ 无套利机会时没有任何档位调整逻辑

### **用户的解决方案**
**核心思路**: "如果下一次没有机会直接降低到低档，这样就重置了"

**优势**:
- ✅ 简单直接：没机会就重置到低档
- ✅ 避免卡档：解决档位卡死问题
- ✅ 快速响应：立即响应市场变化
- ✅ 保守策略：宁可用小金额也不冒险

### **修复实现**

**1. 主程序修改** (`src/arbitrageBot.ts` 第215-223行)
```typescript
} else {
  // 🎯 关键修复：无套利机会时重置到低档
  // 避免档位卡在高档/中档，确保下次用保守的低档金额查询
  this.tradingPairs.forEach(pair => {
    if (pair.dynamicAmount?.enabled) {
      dynamicAmountManager.resetToLowLevel(pair.id, pair);
    }
  });
}
```

**2. 动态金额管理器新增方法** (`src/services/dynamicAmountManager.ts` 第178-199行)
```typescript
/**
 * 重置到低档（无套利机会时使用）
 *
 * 核心逻辑：无套利机会时直接重置到低档，避免档位卡死
 * 这样确保下次查询使用保守的低档金额
 */
resetToLowLevel(pairId: string, pair: TradingPairConfig): void {
  if (!pair.dynamicAmount?.enabled) {
    return;
  }

  const lowAmount = pair.dynamicAmount.low;
  const currentLevel = this.currentLevels.get(pairId) || 'low';

  // 只有当前不是低档时才需要重置
  if (currentLevel !== 'low') {
    this.currentAmounts.set(pairId, lowAmount);
    this.currentLevels.set(pairId, 'low');
    this.consecutiveNoOpportunity.set(pairId, 0);

    const levelNames = { low: '低档', medium: '中档', high: '高档' };
    console.log(`🔄 ${pairId}: 无套利机会，${levelNames[currentLevel]}→低档 (${lowAmount} NEAR)`);
  }
}
```

### **修复理由**
1. **解决档位卡死**: 防止代币永远停留在高档/中档
2. **保守策略**: 无机会时使用最小金额，控制风险
3. **快速响应**: 立即响应市场变化，不等待下次交易
4. **简单有效**: 直接重置比复杂的渐进降档更可靠

### **预期效果**
修复后的行为：
- **有套利机会**: 执行交易 → 根据实际利润渐进调整档位
- **无套利机会**: 直接重置到低档 → 下次用保守金额查询
- **档位流转**: 低档 ↔ 中档 ↔ 高档 ↔ 低档（循环）

---

## 🔧 第六阶段：REF-Jumbo代币映射缺失Bug修复

### **Bug发现**
用户发现REF-Jumbo程序不会检查某些代币交易对，怀疑存在问题。

### **问题分析**
通过检查`ref-jumbo/services/jumboQuoteService.ts`的`getPoolIdForTokenPair`方法，发现：

**配置文件中启用的代币**:
- ✅ NEAR-AURORA (jumboPoolId: 0, enabled: true)
- ✅ NEAR-OCT (jumboPoolId: 3, enabled: true)
- ✅ 其他代币...

**报价服务中的映射**:
- ❌ **缺少AURORA映射**: `aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near` → poolId: 0
- ❌ **缺少OCT映射**: `f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near` → poolId: 3

### **问题根因**
当`getPoolIdForTokenPair`方法找不到代币映射时，返回`null`，导致：
1. 报价请求直接失败，返回"不支持的代币对"错误
2. 这些交易对永远不会被监控和检查
3. 用户看不到这些代币的套利机会

### **修复实现**

**1. 添加缺失的代币映射** (`ref-jumbo/services/jumboQuoteService.ts` 第145-167行)
```typescript
switch (quoteToken) {
  case 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near': // AURORA
    return 0;
  case 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near': // OCT
    return 3;
  // ... 其他代币映射
}
```

**2. 更新支持的代币对列表** (`getSupportedPairs`方法)
```typescript
{ tokenA: 'wrap.near', tokenB: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near', poolId: 0 }, // AURORA
{ tokenA: 'wrap.near', tokenB: 'f5cfbc74057c610c8ef151a439252680ac68c6dc.factory.bridge.near', poolId: 3 }, // OCT
```

**3. 添加AURORA精度定义** (`getTokenDecimals`方法)
```typescript
case 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near': // AURORA
  return 18;
```

### **修复理由**
1. **完整性**: 确保配置文件中的所有启用代币都能被正确处理
2. **一致性**: 保持配置文件和报价服务的同步
3. **功能性**: 让AURORA和OCT交易对能够正常监控和交易

### **预期效果**
修复后：
- ✅ NEAR-AURORA交易对将被正常监控
- ✅ NEAR-OCT交易对将被正常监控
- ✅ 用户可以看到这些代币的套利机会
- ✅ 程序将检查所有配置的交易对

---

## 📝 开发经验总结

### **重要教训**
1. **每次修改代码都必须记录在开发文档中** ⭐⭐⭐
2. **修改前要充分理解用户需求和现有逻辑**
3. **引入新属性或方法时要确保在类中正确声明**
4. **测试修改后的代码，确保无编译错误**

### **最佳实践**
1. 先理解问题 → 设计方案 → 征求同意 → 实施修改 → 记录文档
2. 每个修改都要有明确的理由和预期效果
3. 保持代码简洁，避免不必要的复杂性
4. 及时更新相关文档和测试用例
