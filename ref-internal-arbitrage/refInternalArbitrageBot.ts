/**
 * REF Finance 内部套利机器人
 * 
 * 功能：
 * 1. 监控REF V1和V2系统之间的价格差异
 * 2. 检测套利机会并自动执行
 * 3. 风险管理和错误处理
 * 4. 统计和日志记录
 */

import 'dotenv/config';
import { internalMonitor } from './services/internalMonitor';
import { profitCalculator } from './services/profitCalculator';
import { arbitrageExecutor } from './services/arbitrageExecutor';
import {
  getEnabledPairs,
  getExecutablePairs,
  INTERNAL_ARBITRAGE_CONFIG,
  validateEnvironmentConfig,
  MONITORING_CONFIG
} from './config';
import {
  InternalArbitrageOpportunity,
  ArbitrageExecutionResult,
  InternalTradingPair
} from './types';

/**
 * REF Finance 内部套利机器人主类
 */
export class RefInternalArbitrageBot {
  private isRunning: boolean = false;
  private isExecuting: boolean = false; // 🔧 新增：执行状态标志，用于暂停监控日志
  private monitoringInterval: NodeJS.Timeout | null = null;

  // 执行锁机制
  private executionLocks = new Map<string, boolean>();
  

  

  
  /**
   * 初始化机器人
   */
  async initialize(): Promise<void> {
    console.log('🚀 REF Finance 内部套利机器人启动中...');

    // 验证环境配置
    if (!validateEnvironmentConfig()) {
      throw new Error('环境配置验证失败');
    }

    // 显示当前交易配置
    console.log('\n📊 当前交易配置:');
    const enabledPairs = getEnabledPairs();
    enabledPairs.forEach(pair => {
      console.log(`   ${pair.name}:`);
      console.log(`     最小利润: ${pair.minProfitThresholdNear} NEAR`);
      console.log(`     最大滑点: ${(pair.maxSlippage * 100).toFixed(2)}%`);
      console.log(`     测试金额: ${pair.testAmount} NEAR`);
      console.log(`     重试次数: ${('maxRetries' in pair) ? (pair as any).maxRetries : 3} 次`);
      console.log(`     执行状态: ${pair.executionEnabled ? '启用' : '禁用'}`);
      console.log('');
    });
    
    console.log('✅ REF报价服务已就绪');

    // 初始化交易执行服务
    if (INTERNAL_ARBITRAGE_CONFIG.executionEnabled) {
      await arbitrageExecutor.initialize();
      console.log('✅ 套利执行服务已就绪');

      // 🔧 关键：检查REF Finance DApp注册
      await this.checkRefFinanceRegistration();

      // 🔧 关键：检查所有配置代币的注册状态
      await this.checkAllTokenRegistrations();
    }

    // 验证交易对配置
    if (enabledPairs.length === 0) {
      throw new Error('没有启用的交易对');
    }
    
    console.log(`📋 已启用 ${enabledPairs.length} 个交易对:`);
    enabledPairs.forEach(pair => {
      console.log(`   - ${pair.name}: ${pair.testAmount} ${pair.tokenA.symbol} → ${pair.tokenB.symbol}`);
    });
    
    console.log('✅ REF Finance 内部套利机器人初始化完成');
  }
  
  /**
   * 启动监控
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 机器人已在运行中');
      return;
    }
    
    this.isRunning = true;
    console.log('🎯 开始监控REF内部套利机会...');
    
    // 启动主监控循环
    this.monitoringInterval = setInterval(
      () => this.monitoringLoop(),
      INTERNAL_ARBITRAGE_CONFIG.monitoringInterval
    );
    

    
    // 立即执行一次监控
    await this.monitoringLoop();
  }
  
  /**
   * 🔧 检查REF Finance DApp注册状态
   * 在程序启动时确保账户在REF Finance中已注册
   */
  private async checkRefFinanceRegistration(): Promise<void> {
    try {
      const accountId = process.env.ACCOUNT_ID;
      if (!accountId) {
        throw new Error('未设置ACCOUNT_ID环境变量');
      }

      console.log('🔍 检查REF Finance DApp注册状态...');

      const { connect, keyStores } = require('near-api-js');
      const keyStore = new keyStores.InMemoryKeyStore();
      const config = {
        networkId: 'mainnet',
        keyStore,
        nodeUrl: 'https://rpc.mainnet.near.org',
        walletUrl: 'https://wallet.mainnet.near.org',
        helperUrl: 'https://helper.mainnet.near.org',
      };

      const near = await connect(config);
      const account = await near.account(accountId);

      // 检查账户是否在REF Finance中注册
      const result = await account.viewFunction({
        contractId: 'v2.ref-finance.near',
        methodName: 'get_user_storage_state',
        args: { account_id: accountId }
      });

      if (result) {
        console.log('✅ REF Finance DApp已注册');
      } else {
        console.log('⚠️ REF Finance DApp未注册，正在注册...');

        const keyPair = require('near-api-js').utils.KeyPair.fromString(process.env.PRIVATE_KEY || '');
        await keyStore.setKey('mainnet', accountId, keyPair);

        await account.functionCall({
          contractId: 'v2.ref-finance.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000' // 0.125 NEAR
        });
        console.log('✅ REF Finance DApp注册成功');
      }
    } catch (error: any) {
      if (error.message?.includes('not registered')) {
        console.log('⚠️ REF Finance DApp未注册，正在注册...');

        const accountId = process.env.ACCOUNT_ID!;
        const { connect, keyStores } = require('near-api-js');
        const keyStore = new keyStores.InMemoryKeyStore();
        const keyPair = require('near-api-js').utils.KeyPair.fromString(process.env.PRIVATE_KEY || '');
        await keyStore.setKey('mainnet', accountId, keyPair);

        const config = {
          networkId: 'mainnet',
          keyStore,
          nodeUrl: 'https://rpc.mainnet.near.org',
          walletUrl: 'https://wallet.mainnet.near.org',
          helperUrl: 'https://helper.mainnet.near.org',
        };

        const near = await connect(config);
        const account = await near.account(accountId);

        await account.functionCall({
          contractId: 'v2.ref-finance.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000'
        });
        console.log('✅ REF Finance DApp注册成功');
      } else {
        console.warn('⚠️ REF Finance注册检查失败:', error.message);
      }
    }
  }

  /**
   * 🔧 检查所有配置代币的注册状态
   * 在程序启动时确保所有代币都已注册，避免交易时出现问题
   */
  private async checkAllTokenRegistrations(): Promise<void> {
    console.log('🔍 检查所有配置代币的注册状态...');

    const accountId = process.env.ACCOUNT_ID;
    if (!accountId) {
      throw new Error('未设置ACCOUNT_ID环境变量');
    }

    // 收集所有唯一的代币
    const allTokens = new Set<string>();

    // 从交易对配置中收集代币
    const enabledPairs = getEnabledPairs();
    for (const pair of enabledPairs) {
      if (pair.tokenA.id !== 'wrap.near') {
        allTokens.add(pair.tokenA.id);
      }
      if (pair.tokenB.id !== 'wrap.near') {
        allTokens.add(pair.tokenB.id);
      }
    }



    console.log(`📋 需要检查 ${allTokens.size} 个代币的注册状态`);

    let registeredCount = 0;
    let newRegistrations = 0;

    for (const tokenId of allTokens) {
      try {
        const isRegistered = await this.isTokenRegistered(tokenId, accountId);

        if (isRegistered) {
          console.log(`✅ ${tokenId} 已注册`);
          registeredCount++;
        } else {
          console.log(`⚠️ ${tokenId} 未注册，正在注册...`);
          await this.registerToken(tokenId, accountId);
          console.log(`✅ ${tokenId} 注册成功`);
          newRegistrations++;
        }
      } catch (error: any) {
        console.error(`❌ 检查/注册代币 ${tokenId} 失败:`, error.message);
        throw new Error(`代币注册失败: ${tokenId}`);
      }
    }

    console.log(`✅ 代币注册检查完成: ${registeredCount} 个已注册, ${newRegistrations} 个新注册`);
  }

  /**
   * 检查代币是否已注册
   * 🔧 修复：使用storage_balance_of方法，与主程序一致
   */
  private async isTokenRegistered(tokenId: string, accountId: string): Promise<boolean> {
    try {
      const { connect, keyStores } = require('near-api-js');

      const keyStore = new keyStores.InMemoryKeyStore();
      const config = {
        networkId: process.env.NETWORK || 'mainnet',
        keyStore,
        nodeUrl: process.env.RPC_URL || 'https://rpc.mainnet.near.org',
      };

      const near = await connect(config);
      const account = await near.account(accountId);

      // 🔧 正确方法：查询存储余额来检查注册状态
      const result = await account.viewFunction({
        contractId: tokenId,
        methodName: 'storage_balance_of',
        args: { account_id: accountId }
      });

      // 如果返回null或undefined，说明未注册
      // 如果返回存储余额对象，说明已注册
      const isRegistered = result !== null && result !== undefined;

      if (isRegistered) {
        console.log(`✅ ${tokenId} 已注册，存储余额: ${JSON.stringify(result)}`);
      } else {
        console.log(`❌ ${tokenId} 未注册 (storage_balance_of返回null)`);
      }

      return isRegistered;
    } catch (error: any) {
      // 如果查询失败，说明未注册或合约不支持此方法
      console.log(`❌ ${tokenId} 注册检查失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 注册代币
   */
  private async registerToken(tokenId: string, accountId: string): Promise<void> {
    try {
      const { connect, keyStores, utils } = require('near-api-js');

      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(process.env.PRIVATE_KEY || '');
      await keyStore.setKey(process.env.NETWORK || 'mainnet', accountId, keyPair);

      const config = {
        networkId: process.env.NETWORK || 'mainnet',
        keyStore,
        nodeUrl: process.env.RPC_URL || 'https://rpc.mainnet.near.org',
      };

      const near = await connect(config);
      const account = await near.account(accountId);

      // 注册代币存储
      await account.functionCall({
        contractId: tokenId,
        methodName: 'storage_deposit',
        args: { account_id: accountId },
        attachedDeposit: utils.format.parseNearAmount('0.00125') || '0', // 0.00125 NEAR
        gas: '**************' // 30 TGas
      });

    } catch (error: any) {
      console.error(`❌ 代币 ${tokenId} 注册失败:`, error.message);
      throw error;
    }
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ 机器人未在运行');
      return;
    }

    this.isRunning = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('🛑 REF Finance 内部套利机器人已停止');
  }
  
  /**
   * 主监控循环
   */
  private async monitoringLoop(): Promise<void> {
    try {
      // 🔧 执行期间暂停监控日志输出
      if (this.isExecuting) {
        return; // 执行期间跳过监控，避免日志干扰
      }

      // 获取启用的交易对
      const enabledPairs = getEnabledPairs();
      
      // 并行监控所有交易对
      const allOpportunities: InternalArbitrageOpportunity[] = [];
      
      for (const pair of enabledPairs) {
        try {
          const opportunities = await internalMonitor.monitorPair(pair);
          allOpportunities.push(...opportunities);
        } catch (error) {
          console.error(`❌ 监控交易对 ${pair.name} 失败:`, error);
        }
      }
      
      // 处理发现的套利机会
      if (allOpportunities.length > 0) {
        await this.handleOpportunities(allOpportunities);
      }
      
    } catch (error) {
      console.error('❌ 监控循环错误:', error);
    }
  }
  
  /**
   * 处理套利机会
   */
  private async handleOpportunities(opportunities: InternalArbitrageOpportunity[]): Promise<void> {
    // 过滤有利可图的机会
    const profitableOpportunities = opportunities.filter(op => 
      op.profit.profitable && 
      profitCalculator.validateOpportunity(op)
    );
    
    if (profitableOpportunities.length === 0) {
      return;
    }
    
    // 获取最佳机会
    const bestOpportunity = profitCalculator.getBestOpportunity(profitableOpportunities);
    if (!bestOpportunity) {
      return;
    }
    
    console.log(`\n🎯 发现最佳套利机会: ${profitCalculator.formatOpportunity(bestOpportunity)}`);
    
    // 检查是否启用执行
    if (!INTERNAL_ARBITRAGE_CONFIG.executionEnabled || !bestOpportunity.pair.executionEnabled) {
      console.log('💡 自动执行已禁用，仅监控模式');
      return;
    }
    
    // 执行套利
    await this.executeArbitrage(bestOpportunity);
  }
  
  /**
   * 执行套利交易
   */
  private async executeArbitrage(opportunity: InternalArbitrageOpportunity): Promise<void> {
    const lockKey = `${opportunity.pair.name}_${opportunity.direction}`;

    // 检查执行锁
    if (this.executionLocks.get(lockKey)) {
      console.log('⚠️ 该交易对正在执行中，跳过');
      return;
    }

    // 设置执行锁和执行状态
    this.executionLocks.set(lockKey, true);
    this.isExecuting = true; // 🔧 设置执行状态，暂停监控日志

    try {
      console.log(`🚀 开始执行套利: ${opportunity.direction} ${opportunity.pair.name}`);
      console.log(`   预期利润: ${opportunity.profit.absolute.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);

      // 🔧 执行实际的套利交易
      const result = await arbitrageExecutor.executeArbitrage(opportunity);

      // 🔧 改进：基于实际利润和交易状态判断成功/失败
      if (result.success && (result.actualProfit || 0) > 0) {
        console.log(`✅ 套利执行成功!`);
        console.log(`   第一步交易: ${result.firstStepHash}`);
        console.log(`   第二步交易: ${result.secondStepHash}`);
        console.log(`   实际利润: ${result.actualProfit?.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);

        // 记录成功的套利
        this.logSuccessfulArbitrage(result);
      } else {
        console.error(`❌ 套利执行失败!`);
        if (result.error) {
          console.error(`   错误原因: ${result.error}`);
        }
        if (result.actualProfit !== undefined) {
          console.error(`   实际损失: ${result.actualProfit.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);
        }
        if (result.firstStepHash) {
          console.log(`   第一步交易: ${result.firstStepHash}`);
        }
        if (result.secondStepHash) {
          console.log(`   第二步交易: ${result.secondStepHash}`);
        }
      }

    } catch (error) {
      console.error('❌ 套利执行异常:', error);
    } finally {
      // 释放执行锁和执行状态
      this.executionLocks.delete(lockKey);
      this.isExecuting = false; // 🔧 重置执行状态，恢复监控日志
    }
  }

  /**
   * 记录成功的套利
   */
  private logSuccessfulArbitrage(result: ArbitrageExecutionResult): void {
    const { opportunity, actualProfit, firstStepHash, secondStepHash } = result;

    console.log('\n🎉 套利成功记录:');
    console.log(`   交易对: ${opportunity.pair.name}`);
    console.log(`   方向: ${opportunity.direction}`);
    console.log(`   预期利润: ${opportunity.profit.absolute.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);
    console.log(`   实际利润: ${actualProfit?.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);
    console.log(`   第一步: ${firstStepHash}`);
    console.log(`   第二步: ${secondStepHash}`);
    console.log(`   执行时间: ${new Date().toLocaleTimeString()}`);
    console.log('');
  }
  

}

/**
 * 导出单例实例
 */
export const refInternalArbitrageBot = new RefInternalArbitrageBot();

/**
 * 默认导出
 */
export default RefInternalArbitrageBot;
