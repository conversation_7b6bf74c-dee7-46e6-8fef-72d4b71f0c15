{"name": "ref-internal-arbitrage", "version": "1.0.0", "description": "REF Finance 内部套利机器人 - 监控V1和V2系统之间的套利机会", "main": "main.ts", "scripts": {"start": "ts-node main.ts", "dev": "ts-node --watch main.ts", "build": "tsc", "monitor": "NODE_ENV=monitor ts-node main.ts", "test": "NODE_ENV=test ts-node main.ts", "test:execution": "ts-node test-execution.ts", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ref-internal-arbitrage", "pm2:restart": "pm2 restart ref-internal-arbitrage", "pm2:logs": "pm2 logs ref-internal-arbitrage", "pm2:status": "pm2 show ref-internal-arbitrage"}, "keywords": ["arbitrage", "ref-finance", "near", "defi", "trading-bot"], "author": "REF Finance Arbitrage Team", "license": "MIT", "dependencies": {"dotenv": "^16.0.3", "near-api-js": "^2.1.4", "axios": "^1.4.0", "big.js": "^6.2.1"}, "devDependencies": {"@types/node": "^18.16.3", "typescript": "^5.0.4", "ts-node": "^10.9.1"}, "engines": {"node": ">=16.0.0"}}