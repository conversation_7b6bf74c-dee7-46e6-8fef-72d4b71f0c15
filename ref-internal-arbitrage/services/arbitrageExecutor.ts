/**
 * REF Finance 内部套利执行服务
 * 
 * 专门处理两步套利交易的执行：
 * 1. 执行第一步交易
 * 2. 提取实际输出金额
 * 3. 用实际输出执行第二步交易
 * 4. 计算最终利润
 */

import { InternalArbitrageOpportunity, ArbitrageExecutionResult } from '../types';
import { refExecutionService } from './refExecutionService';
import { refQuoteService } from './refQuoteService';

/**
 * 套利执行服务
 */
export class ArbitrageExecutor {
  
  /**
   * 初始化执行服务
   */
  async initialize(): Promise<void> {
    await refExecutionService.initialize();
    console.log('✅ 套利执行服务初始化完成');
  }

  /**
   * 执行套利交易
   *
   * @param opportunity 套利机会
   * @returns 执行结果
   */
  async executeArbitrage(opportunity: InternalArbitrageOpportunity): Promise<ArbitrageExecutionResult> {
    console.log(`🚀 开始执行套利: ${opportunity.direction} ${opportunity.pair.name}`);
    console.log(`   预期利润: ${opportunity.profit.absolute.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);

    // 🔧 简单的利润检查（使用现有配置）
    if (opportunity.profit.absolute < opportunity.pair.minProfitThresholdNear) {
      console.log(`❌ 利润不足: ${opportunity.profit.absolute} < ${opportunity.pair.minProfitThresholdNear} NEAR`);
      return {
        success: false,
        error: `利润不足: ${opportunity.profit.absolute} < ${opportunity.pair.minProfitThresholdNear} NEAR`,
        opportunity,
        timestamp: Date.now()
      };
    }

    console.log(`✅ 利润检查通过，开始执行交易`);

    try {
      // 🔧 修复：代币注册已在启动时检查，这里不再重复检查
      // 第一步：执行第一个交易
      const firstStepResult = await this.executeFirstStep(opportunity);
      if (!firstStepResult.success) {
        return {
          success: false,
          error: `第一步交易失败: ${firstStepResult.error}`,
          opportunity,
          firstStepHash: firstStepResult.transactionHash,
          timestamp: Date.now()
        };
      }

      console.log(`✅ 第一步交易成功: ${firstStepResult.transactionHash}`);
      console.log(`   实际输出: ${firstStepResult.outputAmountWei} wei`);

      // 第二步：用实际输出执行第二个交易
      const secondStepResult = await this.executeSecondStep(opportunity, firstStepResult.outputAmountWei!);
      if (!secondStepResult.success) {
        return {
          success: false,
          error: `第二步交易失败: ${secondStepResult.error}`,
          opportunity,
          firstStepHash: firstStepResult.transactionHash,
          secondStepHash: secondStepResult.transactionHash,
          actualFirstOutput: firstStepResult.outputAmountWei,
          timestamp: Date.now()
        };
      }

      console.log(`✅ 第二步交易成功: ${secondStepResult.transactionHash}`);
      console.log(`   最终输出: ${secondStepResult.outputAmountWei} wei`);

      // 计算实际利润
      const actualProfit = this.calculateActualProfit(
        opportunity,
        secondStepResult.outputAmountWei!
      );

      console.log(`💰 套利执行完成，实际利润: ${actualProfit.toFixed(4)} ${opportunity.pair.tokenA.symbol}`);

      // 🔧 改进：基于实际利润判断成功/失败
      const isReallySuccessful = actualProfit > 0;

      return {
        success: isReallySuccessful, // 🔧 只有实际盈利才算成功
        opportunity,
        firstStepHash: firstStepResult.transactionHash!,
        secondStepHash: secondStepResult.transactionHash!,
        actualFirstOutput: firstStepResult.outputAmountWei!,
        actualFinalOutput: secondStepResult.outputAmountWei!,
        actualProfit,
        executionTime: Date.now(),
        timestamp: Date.now()
      };

    } catch (error: any) {
      console.error('❌ 套利执行异常:', error);
      return {
        success: false,
        error: error.message || '套利执行异常',
        opportunity,
        timestamp: Date.now()
      };
    }
  }

  /**
   * 执行第一步交易
   */
  private async executeFirstStep(opportunity: InternalArbitrageOpportunity) {
    const { firstStep, pair } = opportunity;
    
    // 准备交易参数
    const inputTokenId = pair.tokenA.id;
    const inputAmount = this.toWei(firstStep.inputAmount, pair.tokenA.decimals);
    const minOutputAmount = this.toWei(firstStep.outputAmount, pair.tokenB.decimals);

    if (firstStep.system === 'V1') {
      return await refExecutionService.executeV1Swap(
        firstStep.quote,
        inputTokenId,
        inputAmount,
        minOutputAmount
      );
    } else if (firstStep.system === 'DCL_V2') {
      return await refExecutionService.executeDCLv2Swap(
        firstStep.quote,
        inputTokenId,
        inputAmount,
        minOutputAmount,
        firstStep.quote.poolId!,
        pair.tokenB.id
      );
    } else {
      throw new Error(`不支持的第一步系统: ${firstStep.system}`);
    }
  }

  /**
   * 执行第二步交易（带重试机制）
   */
  private async executeSecondStep(
    opportunity: InternalArbitrageOpportunity,
    actualFirstOutputWei: string
  ) {
    // 🔧 使用现有配置的重试次数
    const maxRetries = opportunity.pair.maxRetries || 3;
    const baseWaitTime = opportunity.pair.retryWaitTime || 1000;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`🔄 第二步交易尝试 ${attempt}/${maxRetries}`);

      try {
        // 重新获取所有REF报价
        const freshQuotes = await this.getAllREFQuotes(actualFirstOutputWei, opportunity);

        if (freshQuotes.length === 0) {
          console.warn('⚠️ 无法获取任何报价');
          if (attempt === maxRetries) {
            return { success: false, error: '无法获取报价' };
          }
          continue;
        }

        // 选择最优报价
        const bestQuote = this.selectBestQuote(freshQuotes);
        if (!bestQuote) {
          console.warn('⚠️ 无法选择最优报价');
          if (attempt === maxRetries) {
            return { success: false, error: '无法选择最优报价' };
          }
          continue;
        }

        console.log(`🎯 选择最优报价: ${bestQuote.system} - ${bestQuote.outputAmount} ${opportunity.pair.tokenA.symbol}`);

        // 执行交易
        const result = await this.executeSecondStepWithQuote(bestQuote, actualFirstOutputWei, opportunity);

        if (result.success) {
          console.log(`✅ 第二步交易成功 (尝试 ${attempt})`);
          return result;
        }

        // 检查是否应该重试
        if (!this.shouldRetrySecondStep(result.error!, opportunity) || attempt === maxRetries) {
          console.log(`❌ 第二步交易最终失败: ${result.error}`);
          return result;
        }

        // 等待后重试
        const waitTime = baseWaitTime * attempt; // 递增等待时间
        console.log(`⏳ 等待 ${waitTime}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));

      } catch (error) {
        console.error(`❌ 第二步交易异常 (尝试 ${attempt}):`, error);
        if (attempt === maxRetries) {
          return { success: false, error: `重试${maxRetries}次后仍失败: ${error}` };
        }
      }
    }

    return { success: false, error: '重试次数耗尽' };
  }

  /**
   * 获取所有REF报价
   */
  private async getAllREFQuotes(
    inputAmountWei: string,
    opportunity: InternalArbitrageOpportunity
  ): Promise<any[]> {
    const quotes: any[] = [];
    const { pair } = opportunity;

    // 转换为人类可读格式用于查询
    const inputAmountHuman = this.fromWei(inputAmountWei, pair.tokenB.decimals);

    try {
      console.log(`🔄 查询第二步报价: ${inputAmountHuman} ${pair.tokenB.symbol} (${inputAmountWei} wei) → ${pair.tokenA.symbol}`);

      // 获取V1报价
      const v1Quote = await refQuoteService.getV1Quote({
        tokenIn: pair.tokenB,
        tokenOut: pair.tokenA,
        amountIn: inputAmountHuman,
        slippage: pair.maxSlippage
      });

      if (v1Quote) {
        quotes.push(v1Quote);
        console.log(`📊 V1报价: ${v1Quote.outputAmount} ${pair.tokenA.symbol}`);
      }

      // 获取DCL v2报价
      const v2Quote = await refQuoteService.getV2BestQuote({
        tokenIn: pair.tokenB,
        tokenOut: pair.tokenA,
        amountIn: inputAmountHuman,
        slippage: pair.maxSlippage
      }, pair.v2PoolIds || []);

      if (v2Quote) {
        quotes.push(v2Quote);
        console.log(`📊 DCL v2报价: ${v2Quote.outputAmount} ${pair.tokenA.symbol}`);
      }

    } catch (error) {
      console.error('❌ 获取报价失败:', error);
    }

    return quotes;
  }

  /**
   * 选择最优报价
   */
  private selectBestQuote(quotes: any[]): any | null {
    if (quotes.length === 0) {
      return null;
    }

    // 按输出金额排序，选择最高的
    const sortedQuotes = quotes.sort((a, b) =>
      parseFloat(b.outputAmount) - parseFloat(a.outputAmount)
    );

    const bestQuote = sortedQuotes[0];
    console.log(`🎯 选择最优报价: ${bestQuote.system} - ${bestQuote.outputAmount}`);

    return bestQuote;
  }

  /**
   * 使用指定报价执行第二步交易
   */
  private async executeSecondStepWithQuote(
    quote: any,
    actualFirstOutputWei: string,
    opportunity: InternalArbitrageOpportunity
  ) {
    const { pair } = opportunity;

    // 计算最小输出金额
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutput = expectedOutput * (1 - pair.maxSlippage);
    const minOutputAmountWei = this.toWei(minOutput.toString(), pair.tokenA.decimals);

    console.log(`🔧 第二步最小输出: ${minOutput} ${pair.tokenA.symbol} (wei: ${minOutputAmountWei})`);

    if (quote.system === 'V1') {
      return await refExecutionService.executeV1Swap(
        quote,
        pair.tokenB.id,
        actualFirstOutputWei,
        minOutputAmountWei
      );
    } else if (quote.system === 'DCL_V2') {
      return await refExecutionService.executeDCLv2Swap(
        quote,
        pair.tokenB.id,
        actualFirstOutputWei,
        minOutputAmountWei,
        quote.poolId!,
        pair.tokenA.id
      );
    } else {
      throw new Error(`不支持的系统: ${quote.system}`);
    }
  }

  /**
   * 判断是否应该重试第二步交易
   */
  private shouldRetrySecondStep(error: string, opportunity: InternalArbitrageOpportunity): boolean {
    const retryableErrors = [
      'E68', // 滑点错误
      'E76', // 参数错误
      'E22', // 余额不足错误 (可通过重新获取报价解决)
      'slippage', // 滑点相关
      'insufficient', // 流动性不足
      'timeout', // 网络超时
      'network', // 网络错误
    ];

    const shouldRetry = retryableErrors.some(err =>
      error.toLowerCase().includes(err.toLowerCase())
    );

    console.log(`🔍 错误分析: "${error}" - ${shouldRetry ? '可重试' : '不可重试'}`);
    return shouldRetry;
  }



  /**
   * 🔧 修复：第二步报价查询 - 遵循用户要求只使用wei精度
   * 执行时不进行任何精度转换，先转换为人类可读格式再查询
   */
  private async getSecondStepMinOutput(
    opportunity: InternalArbitrageOpportunity,
    actualFirstOutputWei: string
  ): Promise<string> {
    try {
      // 🔧 关键修复：将wei格式转换为人类可读格式进行查询
      // 避免getV1Quote内部的重复转换问题
      const actualFirstOutputHuman = this.fromWei(actualFirstOutputWei, opportunity.pair.tokenB.decimals);

      const quoteParams = {
        tokenIn: opportunity.pair.tokenB,
        tokenOut: opportunity.pair.tokenA,
        amountIn: actualFirstOutputHuman, // 🔧 使用人类可读格式查询
        slippage: opportunity.pair.maxSlippage
      };

      console.log(`🔄 查询第二步报价: ${actualFirstOutputHuman} ${opportunity.pair.tokenB.symbol} (${actualFirstOutputWei} wei) → ${opportunity.pair.tokenA.symbol}`);

      let freshQuote;
      if (opportunity.secondStep.system === 'V1') {
        freshQuote = await refQuoteService.getV1Quote(quoteParams);
      } else {
        freshQuote = await refQuoteService.getV2BestQuote(quoteParams, opportunity.pair.v2PoolIds || []);
      }

      if (!freshQuote) {
        console.warn('⚠️ 无法获取第二步新报价，使用原始预期值');
        const expectedOutput = parseFloat(opportunity.secondStep.outputAmount);
        const minOutput = expectedOutput * (1 - opportunity.pair.maxSlippage);
        return this.toWei(minOutput.toString(), opportunity.pair.tokenA.decimals);
      }

      console.log(`✅ 第二步新报价: ${freshQuote.outputAmount} ${opportunity.pair.tokenA.symbol}`);

      // 🔧 应用滑点保护到新报价
      const expectedOutput = parseFloat(freshQuote.outputAmount);
      const minOutput = expectedOutput * (1 - opportunity.pair.maxSlippage);
      const minOutputAmountWei = this.toWei(minOutput.toString(), opportunity.pair.tokenA.decimals);

      console.log(`🔧 第二步最小输出: ${minOutput} ${opportunity.pair.tokenA.symbol} (wei: ${minOutputAmountWei})`);
      return minOutputAmountWei;

    } catch (error) {
      console.error('❌ 查询第二步报价失败，使用原始预期值:', error);
      const expectedOutput = parseFloat(opportunity.secondStep.outputAmount);
      const minOutput = expectedOutput * (1 - opportunity.pair.maxSlippage);
      return this.toWei(minOutput.toString(), opportunity.pair.tokenA.decimals);
    }
  }

  /**
   * 计算实际利润
   */
  private calculateActualProfit(
    opportunity: InternalArbitrageOpportunity,
    actualFinalOutputWei: string
  ): number {
    const initialAmount = parseFloat(opportunity.inputAmount);
    const finalAmount = this.fromWei(actualFinalOutputWei, opportunity.pair.tokenA.decimals);
    return parseFloat(finalAmount) - initialAmount;
  }

  /**
   * 转换为wei格式
   */
  private toWei(amount: string, decimals: number): string {
    if (decimals === 24) {
      // NEAR使用parseNearAmount
      const { parseNearAmount } = require('near-api-js/lib/utils/format');
      return parseNearAmount(amount) || '0';
    } else {
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 从wei格式转换
   */
  private fromWei(amount: string, decimals: number): string {
    if (decimals === 24) {
      // NEAR使用formatNearAmount
      const { formatNearAmount } = require('near-api-js/lib/utils/format');
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      if (amount === '0') return '0';
      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');
      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }


}

/**
 * 导出单例实例
 */
export const arbitrageExecutor = new ArbitrageExecutor();

/**
 * 默认导出
 */
export default ArbitrageExecutor;
