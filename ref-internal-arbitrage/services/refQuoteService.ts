/**
 * REF Finance 独立报价服务
 * 专门为内部套利设计的报价服务
 */

import axios from 'axios';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import { QuoteParams, QuoteResult } from '../types';

/**
 * REF Finance 报价服务
 */
export class RefQuoteService {
  private readonly smartRouterUrl = 'https://smartrouter.ref.finance';
  private readonly rpcUrl = process.env.RPC_URL || 'https://rpc.mainnet.near.org';
  private readonly v1Contract = 'v2.ref-finance.near';
  private readonly v2Contract = 'dclv2.ref-labs.near';
  private readonly requestTimeout = 30000;

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 将可读金额转换为非可分割单位
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ 收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      return parseNearAmount(amount) || '0';
    } else {
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 将非可分割单位转换为可读金额
   */
  private toReadableNumber(amount: string, decimals: number): string {
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ 收到无效wei数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 获取V1 Smart Router报价
   */
  async getV1Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      const { tokenIn, tokenOut, amountIn, slippage = 0.005 } = params;
      
      const amountInNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);
      const slippagePercent = slippage;

      const url = new URL(`${this.smartRouterUrl}/findPath`);
      url.searchParams.set('amountIn', amountInNonDivisible);
      url.searchParams.set('tokenIn', tokenIn.id);
      url.searchParams.set('tokenOut', tokenOut.id);
      url.searchParams.set('pathDeep', '3');
      url.searchParams.set('slippage', slippagePercent.toString());

      const response = await axios.get(url.toString(), {
        timeout: this.requestTimeout,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'REF-Internal-Arbitrage/1.0'
        }
      });

      const data = response.data;

      if (data.result_code !== 0 || !data.result_data?.routes?.length) {
        return null;
      }

      const { result_data } = data;
      const outputAmount = this.toReadableNumber(result_data.amount_out, tokenOut.decimals);

      return {
        system: 'V1',
        contractId: this.v1Contract,
        outputAmount,
        inputAmount: amountIn,
        route: result_data.routes[0], // 🔧 关键修复：与主程序一致，只返回第一条路径
        rawResponse: data
      };

    } catch (error: any) {
      console.error('❌ V1 Smart Router 调用失败:', error.message);
      return null;
    }
  }

  /**
   * 获取V2 DCL报价 (单个池子)
   */
  async getV2Quote(params: QuoteParams, poolId: string): Promise<QuoteResult | null> {
    try {
      const { tokenIn, tokenOut, amountIn } = params;
      
      const inputAmountNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);

      const quoteParams = {
        pool_ids: [poolId],
        input_token: tokenIn.id,
        output_token: tokenOut.id,
        input_amount: inputAmountNonDivisible,
        tag: `${tokenIn.id}|${poolId.split('|')[2]}|${amountIn}`
      };

      const argsBase64 = Buffer.from(JSON.stringify(quoteParams)).toString('base64');

      const rpcParams = {
        request_type: 'call_function',
        finality: 'optimistic',
        account_id: this.v2Contract,
        method_name: 'quote',
        args_base64: argsBase64
      };

      const rpcPayload = {
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'query',
        params: rpcParams
      };

      const response = await axios.post(this.rpcUrl, rpcPayload, {
        timeout: this.requestTimeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = response.data;

      if (data.error) {
        throw new Error(`池子 ${poolId} RPC 错误: ${data.error.message}`);
      }

      const resultBytes = data.result?.result;
      if (!resultBytes || resultBytes.length === 0) {
        return null;
      }

      const resultString = String.fromCharCode(...resultBytes);
      const result = JSON.parse(resultString);

      if (!result || !result.amount || result.amount === '0') {
        return null;
      }

      const outputAmount = this.toReadableNumber(result.amount, tokenOut.decimals);

      return {
        system: 'DCL_V2',
        contractId: this.v2Contract,
        outputAmount,
        inputAmount: amountIn,
        poolId: poolId,
        route: { pool_ids: [poolId], input_token: tokenIn.id, output_token: tokenOut.id, input_amount: amountIn },
        rawResponse: result
      };

    } catch (error: any) {
      console.error(`❌ DCL v2 池子 ${poolId} 调用失败:`, error.message);
      return null;
    }
  }

  /**
   * 获取V2最佳报价 (查询多个池子)
   */
  async getV2BestQuote(params: QuoteParams, poolIds: string[]): Promise<QuoteResult | null> {
    if (!poolIds || poolIds.length === 0) {
      return null;
    }

    // 并行查询所有池子
    const poolQuotes = await Promise.allSettled(
      poolIds.map(poolId => this.getV2Quote(params, poolId))
    );

    // 收集成功的报价
    const validQuotes: QuoteResult[] = [];
    poolQuotes.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        validQuotes.push(result.value);
      }
    });

    if (validQuotes.length === 0) {
      return null;
    }

    // 选择输出金额最大的报价
    const bestQuote = validQuotes.reduce((best, current) => 
      parseFloat(current.outputAmount) > parseFloat(best.outputAmount) ? current : best
    );

    return bestQuote;
  }
}

/**
 * 导出单例实例
 */
export const refQuoteService = new RefQuoteService();

/**
 * 默认导出
 */
export default RefQuoteService;
