/**
 * REF Finance 内部套利利润计算服务
 */

import { 
  InternalArbitrageOpportunity, 
  InternalTradingPair, 
  QuoteResult 
} from '../types';

/**
 * 利润计算服务
 */
export class ProfitCalculator {
  
  /**
   * 计算套利机会
   *
   * 注意：这个方法只是用来显示第一步的报价，真正的套利计算需要第二步的报价
   * 实际的套利逻辑应该在 internalMonitor 中实现
   *
   * @param pair 交易对配置
   * @param v1Quote V1系统第一步报价 (NEAR → TokenB)
   * @param v2Quote V2系统第一步报价 (NEAR → TokenB)
   * @returns 空数组，因为需要第二步报价才能计算真正的套利
   */
  calculateArbitrageOpportunities(
    pair: InternalTradingPair,
    v1Quote: QuoteResult | null,
    v2Quote: QuoteResult | null
  ): InternalArbitrageOpportunity[] {
    // 这个方法现在不应该被使用，因为它没有第二步的报价数据
    console.warn('⚠️ calculateArbitrageOpportunities 方法已废弃，请使用完整的两步套利计算');
    return [];
  }
  
  /**
   * 计算 V1 → V2 套利
   * 第一步: V1 (100 NEAR → A USDT)
   * 第二步: V2 (A USDT → B NEAR)
   * 利润: B - 100
   */
  private calculateV1ToV2Arbitrage(
    pair: InternalTradingPair,
    v1Quote: QuoteResult,
    v2Quote: QuoteResult
  ): InternalArbitrageOpportunity | null {
    try {
      const inputAmount = pair.testAmount;
      
      // 第一步: V1 (NEAR → USDT)
      const firstStepOutput = parseFloat(v1Quote.outputAmount);
      
      // 第二步: V2 (USDT → NEAR) - 需要用第一步的输出作为输入
      // 注意：这里需要重新查询V2，使用实际的USDT数量
      // 暂时使用当前V2报价作为估算
      const secondStepOutput = parseFloat(v2Quote.outputAmount);
      
      // 计算利润
      const initialAmount = parseFloat(inputAmount);
      const finalAmount = secondStepOutput;
      const absoluteProfit = finalAmount - initialAmount;
      const profitPercentage = (absoluteProfit / initialAmount) * 100;
      
      // 检查是否有利可图 (使用绝对利润)
      const profitable = absoluteProfit >= pair.minProfitThresholdNear;
      
      return {
        direction: 'V1_TO_V2',
        pair,
        inputAmount,
        
        firstStep: {
          system: 'V1',
          inputAmount,
          outputAmount: v1Quote.outputAmount,
          quote: v1Quote
        },
        
        secondStep: {
          system: 'DCL_V2',
          inputAmount: v1Quote.outputAmount, // 使用第一步的输出
          outputAmount: v2Quote.outputAmount,
          quote: v2Quote
        },
        
        profit: {
          absolute: absoluteProfit,
          percentage: profitPercentage,
          profitable
        },
        
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('❌ V1→V2套利计算失败:', error);
      return null;
    }
  }
  
  /**
   * 计算 V2 → V1 套利
   * 第一步: V2 (100 NEAR → A USDT)
   * 第二步: V1 (A USDT → B NEAR)
   * 利润: B - 100
   */
  private calculateV2ToV1Arbitrage(
    pair: InternalTradingPair,
    v1Quote: QuoteResult,
    v2Quote: QuoteResult
  ): InternalArbitrageOpportunity | null {
    try {
      const inputAmount = pair.testAmount;
      
      // 第一步: V2 (NEAR → USDT)
      const firstStepOutput = parseFloat(v2Quote.outputAmount);
      
      // 第二步: V1 (USDT → NEAR)
      const secondStepOutput = parseFloat(v1Quote.outputAmount);
      
      // 计算利润
      const initialAmount = parseFloat(inputAmount);
      const finalAmount = secondStepOutput;
      const absoluteProfit = finalAmount - initialAmount;
      const profitPercentage = (absoluteProfit / initialAmount) * 100;
      
      // 检查是否有利可图 (使用绝对利润)
      const profitable = absoluteProfit >= pair.minProfitThresholdNear;
      
      return {
        direction: 'V2_TO_V1',
        pair,
        inputAmount,
        
        firstStep: {
          system: 'DCL_V2',
          inputAmount,
          outputAmount: v2Quote.outputAmount,
          quote: v2Quote
        },
        
        secondStep: {
          system: 'V1',
          inputAmount: v2Quote.outputAmount, // 使用第一步的输出
          outputAmount: v1Quote.outputAmount,
          quote: v1Quote
        },
        
        profit: {
          absolute: absoluteProfit,
          percentage: profitPercentage,
          profitable
        },
        
        timestamp: Date.now()
      };
      
    } catch (error) {
      console.error('❌ V2→V1套利计算失败:', error);
      return null;
    }
  }
  
  /**
   * 格式化套利机会显示
   */
  formatOpportunity(opportunity: InternalArbitrageOpportunity): string {
    const direction = opportunity.direction === 'V1_TO_V2' ? 'V1→V2' : 'V2→V1';
    const absoluteProfit = opportunity.profit.absolute.toFixed(4);
    const percentage = opportunity.profit.percentage.toFixed(2);
    const profitable = opportunity.profit.profitable ? '✅' : '❌';
    const profitSign = opportunity.profit.absolute >= 0 ? '+' : '';

    return `${profitable} ${direction}: ${opportunity.inputAmount}→${opportunity.firstStep.outputAmount}→${opportunity.secondStep.outputAmount} (${profitSign}${absoluteProfit} ${opportunity.pair.tokenA.symbol}, ${percentage}%)`;
  }
  
  /**
   * 获取最佳套利机会
   */
  getBestOpportunity(opportunities: InternalArbitrageOpportunity[]): InternalArbitrageOpportunity | null {
    const profitableOpportunities = opportunities.filter(op => op.profit.profitable);
    
    if (profitableOpportunities.length === 0) {
      return null;
    }
    
    // 返回利润最高的机会
    return profitableOpportunities.reduce((best, current) => 
      current.profit.percentage > best.profit.percentage ? current : best
    );
  }
  
  /**
   * 验证套利机会的有效性
   */
  validateOpportunity(opportunity: InternalArbitrageOpportunity): boolean {
    // 基本验证
    if (!opportunity.firstStep.quote || !opportunity.secondStep.quote) {
      return false;
    }
    
    // 利润验证
    if (!opportunity.profit.profitable) {
      return false;
    }
    
    // 时间验证 (报价不能太旧)
    const maxAge = 30000; // 30秒
    const now = Date.now();
    if (now - opportunity.timestamp > maxAge) {
      return false;
    }
    
    // 金额验证
    const firstOutput = parseFloat(opportunity.firstStep.outputAmount);
    const secondOutput = parseFloat(opportunity.secondStep.outputAmount);
    
    if (firstOutput <= 0 || secondOutput <= 0) {
      return false;
    }
    
    return true;
  }
}

/**
 * 导出单例实例
 */
export const profitCalculator = new ProfitCalculator();

/**
 * 默认导出
 */
export default ProfitCalculator;
