/**
 * REF Finance 内部套利监控服务
 */

import {
  InternalTradingPair,
  InternalArbitrageOpportunity,
  QuoteParams,
  QuoteResult,
  MonitoringStats
} from '../types';
import { profitCalculator } from './profitCalculator';
import { MONITORING_CONFIG } from '../config';
import { refQuoteService } from './refQuoteService';

/**
 * 内部监控服务
 * 负责监控REF V1和V2之间的价格差异
 */
export class InternalMonitorService {
  private stats: MonitoringStats = {
    totalOpportunities: 0,
    profitableOpportunities: 0,
    v1ToV2Opportunities: 0,
    v2ToV1Opportunities: 0,
    averageProfit: 0,
    maxProfit: 0,
    lastUpdateTime: Date.now()
  };
  
  private consecutiveErrors = 0;
  private lastErrorTime = 0;
  
  /**
   * 监控单个交易对的套利机会
   */
  async monitorPair(
    pair: InternalTradingPair
  ): Promise<InternalArbitrageOpportunity[]> {
    try {
      // 检查错误冷却时间
      if (this.isInErrorCooldown()) {
        return [];
      }
      
      // 构建报价参数
      const quoteParams: QuoteParams = {
        tokenIn: pair.tokenA,
        tokenOut: pair.tokenB,
        amountIn: pair.testAmount,
        slippage: pair.maxSlippage
      };
      
      // 分别获取V1和V2报价
      const [v1Quote, v2Quote] = await Promise.allSettled([
        this.getV1Quote(quoteParams),
        this.getV2Quote(quoteParams, pair)
      ]);
      
      // 处理报价结果
      const v1Result = v1Quote.status === 'fulfilled' ? v1Quote.value : null;
      const v2Result = v2Quote.status === 'fulfilled' ? v2Quote.value : null;
      
      if (!v1Result && !v2Result) {
        throw new Error('V1和V2报价都失败了');
      }
      
      if (!v1Result) {
        console.warn(`⚠️ V1报价失败: ${pair.name}`);
        return [];
      }
      
      if (!v2Result) {
        console.warn(`⚠️ V2报价失败: ${pair.name}`);
        return [];
      }
      
      // 🔧 实现正确的两步套利计算
      const opportunities = await this.calculateTwoStepArbitrage(pair, v1Result, v2Result);

      // 更新统计信息
      this.updateStats(opportunities);

      // 显示机会
      this.displayOpportunities(opportunities);

      // 重置错误计数
      this.consecutiveErrors = 0;

      return opportunities;
      
    } catch (error: any) {
      this.handleError(error, pair.name);
      return [];
    }
  }
  
  /**
   * 获取V1报价
   */
  private async getV1Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      const quote = await refQuoteService.getV1Quote(params);
      return quote;
    } catch (error) {
      console.error('❌ V1报价失败:', error);
      return null;
    }
  }
  
  /**
   * 获取V2报价 (查询所有配置的池子，返回最佳报价)
   */
  private async getV2Quote(
    params: QuoteParams,
    pair: InternalTradingPair
  ): Promise<QuoteResult | null> {
    try {
      if (!pair.v2PoolIds || pair.v2PoolIds.length === 0) {
        console.warn(`⚠️ 交易对 ${pair.name} 没有配置V2池子`);
        return null;
      }

      // 使用我们自己的报价服务查询最佳V2报价
      const bestQuote = await refQuoteService.getV2BestQuote(params, pair.v2PoolIds);

      return bestQuote;

    } catch (error) {
      console.error('❌ V2报价失败:', error);
      return null;
    }
  }
  
  /**
   * 显示套利机会
   */
  private displayOpportunities(opportunities: InternalArbitrageOpportunity[]): void {
    if (!MONITORING_CONFIG.showOpportunities) {
      return;
    }
    
    const filteredOpportunities = MONITORING_CONFIG.showOnlyProfitable
      ? opportunities.filter(op => op.profit.profitable)
      : opportunities;
    
    const displayOpportunities = filteredOpportunities.filter(op =>
      Math.abs(op.profit.absolute) >= MONITORING_CONFIG.minDisplayProfitNear
    );
    
    if (displayOpportunities.length === 0) {
      return;
    }
    
    console.log(`\n💰 发现 ${displayOpportunities.length} 个套利机会:`);
    displayOpportunities.forEach(op => {
      const formatted = profitCalculator.formatOpportunity(op);
      console.log(`   ${formatted}`);
    });
  }
  
  /**
   * 更新统计信息
   */
  private updateStats(opportunities: InternalArbitrageOpportunity[]): void {
    this.stats.totalOpportunities += opportunities.length;
    
    const profitableOps = opportunities.filter(op => op.profit.profitable);
    this.stats.profitableOpportunities += profitableOps.length;
    
    // 统计方向
    opportunities.forEach(op => {
      if (op.direction === 'V1_TO_V2') {
        this.stats.v1ToV2Opportunities++;
      } else {
        this.stats.v2ToV1Opportunities++;
      }
    });
    
    // 更新利润统计
    if (profitableOps.length > 0) {
      const profits = profitableOps.map(op => op.profit.percentage);
      const totalProfit = profits.reduce((sum, profit) => sum + profit, 0);
      
      this.stats.averageProfit = totalProfit / profits.length;
      this.stats.maxProfit = Math.max(this.stats.maxProfit, ...profits);
    }
    
    this.stats.lastUpdateTime = Date.now();
  }
  
  /**
   * 处理错误
   */
  private handleError(error: any, pairName: string): void {
    this.consecutiveErrors++;
    this.lastErrorTime = Date.now();
    
    console.error(`❌ 监控错误 [${pairName}] (${this.consecutiveErrors}/${MONITORING_CONFIG.maxConsecutiveErrors}):`, error.message);
    
    if (this.consecutiveErrors >= MONITORING_CONFIG.maxConsecutiveErrors) {
      console.error(`🚨 连续错误次数过多，进入冷却期 ${MONITORING_CONFIG.errorCooldownTime / 1000} 秒`);
    }
  }
  
  /**
   * 检查是否在错误冷却期
   */
  private isInErrorCooldown(): boolean {
    if (this.consecutiveErrors < MONITORING_CONFIG.maxConsecutiveErrors) {
      return false;
    }
    
    const timeSinceLastError = Date.now() - this.lastErrorTime;
    if (timeSinceLastError >= MONITORING_CONFIG.errorCooldownTime) {
      // 冷却期结束，重置错误计数
      this.consecutiveErrors = 0;
      console.log('✅ 错误冷却期结束，恢复监控');
      return false;
    }
    
    return true;
  }
  
  /**
   * 获取统计信息
   */
  getStats(): MonitoringStats {
    return { ...this.stats };
  }
  
  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalOpportunities: 0,
      profitableOpportunities: 0,
      v1ToV2Opportunities: 0,
      v2ToV1Opportunities: 0,
      averageProfit: 0,
      maxProfit: 0,
      lastUpdateTime: Date.now()
    };
  }
  
  /**
   * 🔧 正确的两步套利计算
   *
   * 步骤1: 获取第一步报价 (NEAR → TokenB)
   * 步骤2: 用第一步输出作为输入，获取第二步报价 (TokenB → NEAR)
   * 步骤3: 计算利润 (最终NEAR - 初始NEAR)
   */
  private async calculateTwoStepArbitrage(
    pair: InternalTradingPair,
    v1FirstStep: QuoteResult,
    v2FirstStep: QuoteResult
  ): Promise<InternalArbitrageOpportunity[]> {
    const opportunities: InternalArbitrageOpportunity[] = [];

    try {
      // 方向1: V1 → V2 (V1第一步输出 → V2第二步)
      const route1Opportunity = await this.calculateRoute1(pair, v1FirstStep, v2FirstStep);
      if (route1Opportunity) {
        opportunities.push(route1Opportunity);
      }

      // 方向2: V2 → V1 (V2第一步输出 → V1第二步)
      const route2Opportunity = await this.calculateRoute2(pair, v1FirstStep, v2FirstStep);
      if (route2Opportunity) {
        opportunities.push(route2Opportunity);
      }

      return opportunities;

    } catch (error) {
      console.error('❌ 两步套利计算失败:', error);
      return [];
    }
  }

  /**
   * 计算路径1: V1 → V2
   * 第一步: V1 (NEAR → TokenB)
   * 第二步: V2 (V1输出的TokenB → NEAR)
   */
  private async calculateRoute1(
    pair: InternalTradingPair,
    v1FirstStep: QuoteResult,
    v2FirstStep: QuoteResult
  ): Promise<InternalArbitrageOpportunity | null> {
    try {
      // 用V1第一步的输出作为V2第二步的输入
      const secondStepParams: QuoteParams = {
        tokenIn: pair.tokenB,
        tokenOut: pair.tokenA,
        amountIn: v1FirstStep.outputAmount,
        slippage: pair.maxSlippage
      };

      // 获取V2第二步报价
      const v2SecondStep = await this.getV2Quote(secondStepParams, pair);

      if (!v2SecondStep) {
        return null;
      }

      // 计算利润
      const initialAmount = parseFloat(pair.testAmount);
      const finalAmount = parseFloat(v2SecondStep.outputAmount);
      const absoluteProfit = finalAmount - initialAmount;
      const profitPercentage = (absoluteProfit / initialAmount) * 100;
      const profitable = absoluteProfit >= pair.minProfitThresholdNear;

      // 只在有利可图时输出日志
      if (profitable) {
        console.log(`   路径1 V1→V2: ${v1FirstStep.outputAmount} ${pair.tokenB.symbol} → ${v2SecondStep.outputAmount} ${pair.tokenA.symbol} (利润: ${absoluteProfit.toFixed(4)} ${pair.tokenA.symbol})`);
      }

      return {
        direction: 'V1_TO_V2',
        pair,
        inputAmount: pair.testAmount,

        firstStep: {
          system: 'V1',
          inputAmount: pair.testAmount,
          outputAmount: v1FirstStep.outputAmount,
          quote: v1FirstStep
        },

        secondStep: {
          system: 'DCL_V2',
          inputAmount: v1FirstStep.outputAmount,
          outputAmount: v2SecondStep.outputAmount,
          quote: v2SecondStep
        },

        profit: {
          absolute: absoluteProfit,
          percentage: profitPercentage,
          profitable
        },

        timestamp: Date.now()
      };

    } catch (error) {
      console.error('❌ 路径1计算失败:', error);
      return null;
    }
  }

  /**
   * 计算路径2: V2 → V1
   * 第一步: V2 (NEAR → TokenB)
   * 第二步: V1 (V2输出的TokenB → NEAR)
   */
  private async calculateRoute2(
    pair: InternalTradingPair,
    v1FirstStep: QuoteResult,
    v2FirstStep: QuoteResult
  ): Promise<InternalArbitrageOpportunity | null> {
    try {
      // 用V2第一步的输出作为V1第二步的输入
      const secondStepParams: QuoteParams = {
        tokenIn: pair.tokenB,
        tokenOut: pair.tokenA,
        amountIn: v2FirstStep.outputAmount,
        slippage: pair.maxSlippage
      };

      // 获取V1第二步报价
      const v1SecondStep = await this.getV1Quote(secondStepParams);

      if (!v1SecondStep) {
        return null;
      }

      // 计算利润
      const initialAmount = parseFloat(pair.testAmount);
      const finalAmount = parseFloat(v1SecondStep.outputAmount);
      const absoluteProfit = finalAmount - initialAmount;
      const profitPercentage = (absoluteProfit / initialAmount) * 100;
      const profitable = absoluteProfit >= pair.minProfitThresholdNear;

      // 只在有利可图时输出日志
      if (profitable) {
        console.log(`   路径2 V2→V1: ${v2FirstStep.outputAmount} ${pair.tokenB.symbol} → ${v1SecondStep.outputAmount} ${pair.tokenA.symbol} (利润: ${absoluteProfit.toFixed(4)} ${pair.tokenA.symbol})`);
      }

      return {
        direction: 'V2_TO_V1',
        pair,
        inputAmount: pair.testAmount,

        firstStep: {
          system: 'DCL_V2',
          inputAmount: pair.testAmount,
          outputAmount: v2FirstStep.outputAmount,
          quote: v2FirstStep
        },

        secondStep: {
          system: 'V1',
          inputAmount: v2FirstStep.outputAmount,
          outputAmount: v1SecondStep.outputAmount,
          quote: v1SecondStep
        },

        profit: {
          absolute: absoluteProfit,
          percentage: profitPercentage,
          profitable
        },

        timestamp: Date.now()
      };

    } catch (error) {
      console.error('❌ 路径2计算失败:', error);
      return null;
    }
  }

  /**
   * 显示统计信息
   */
  displayStats(): void {
    const stats = this.getStats();
    const profitableRate = stats.totalOpportunities > 0
      ? (stats.profitableOpportunities / stats.totalOpportunities * 100).toFixed(2)
      : '0.00';

    console.log('\n📊 监控统计信息:');
    console.log(`   总机会数: ${stats.totalOpportunities}`);
    console.log(`   有利可图: ${stats.profitableOpportunities} (${profitableRate}%)`);
    console.log(`   V1→V2: ${stats.v1ToV2Opportunities}, V2→V1: ${stats.v2ToV1Opportunities}`);
    console.log(`   平均利润: ${stats.averageProfit.toFixed(4)}%`);
    console.log(`   最大利润: ${stats.maxProfit.toFixed(4)}%`);
    console.log(`   最后更新: ${new Date(stats.lastUpdateTime).toLocaleTimeString()}`);
  }
}

/**
 * 导出单例实例
 */
export const internalMonitor = new InternalMonitorService();

/**
 * 默认导出
 */
export default InternalMonitorService;
