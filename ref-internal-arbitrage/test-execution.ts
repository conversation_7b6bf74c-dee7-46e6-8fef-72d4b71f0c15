/**
 * 交易执行功能测试脚本
 * 
 * 用于验证交易执行服务是否正常工作
 */

import 'dotenv/config';
import { refExecutionService } from './services/refExecutionService';
import { arbitrageExecutor } from './services/arbitrageExecutor';

async function testExecutionServices() {
  console.log('🧪 开始测试交易执行服务...');
  
  try {
    // 检查环境变量
    if (!process.env.ACCOUNT_ID || !process.env.PRIVATE_KEY) {
      console.error('❌ 缺少必要的环境变量: ACCOUNT_ID 或 PRIVATE_KEY');
      return;
    }
    
    console.log(`📋 测试账户: ${process.env.ACCOUNT_ID}`);
    
    // 测试基础执行服务初始化
    console.log('\n1️⃣ 测试 RefExecutionService 初始化...');
    await refExecutionService.initialize();
    console.log('✅ RefExecutionService 初始化成功');
    
    // 测试套利执行器初始化
    console.log('\n2️⃣ 测试 ArbitrageExecutor 初始化...');
    await arbitrageExecutor.initialize();
    console.log('✅ ArbitrageExecutor 初始化成功');
    
    console.log('\n✅ 所有交易执行服务测试通过！');
    console.log('💡 现在可以运行主程序进行实际套利监控和执行');
    
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
    console.error('🔧 请检查：');
    console.error('   1. .env 文件中的 ACCOUNT_ID 和 PRIVATE_KEY 是否正确');
    console.error('   2. 网络连接是否正常');
    console.error('   3. 私钥格式是否正确 (ed25519:...)');
  }
}

// 运行测试
if (require.main === module) {
  testExecutionServices().catch(error => {
    console.error('❌ 测试异常退出:', error);
    process.exit(1);
  });
}

export default testExecutionServices;
