# REF Finance 内部套利机器人

监控 REF Finance V1 Smart Router 和 DCL v2 系统之间的价格差异，自动执行套利交易。

## 🎯 功能特性

### 核心功能
- **双向套利监控**: 监控 V1→V2 和 V2→V1 两个方向的套利机会
- **实时价格比较**: 分别查询 V1 和 V2 系统的报价
- **利润计算**: 精确计算套利利润 (B - 100)
- **自动执行**: 自动执行有利可图的套利交易
- **风险控制**: 多层风险管理和安全机制

### 套利逻辑
```
方向1: V1 → V2
- 第一步: V1 (100 NEAR → A USDT)
- 第二步: V2 (A USDT → B NEAR)
- 利润: B - 100

方向2: V2 → V1  
- 第一步: V2 (100 NEAR → A USDT)
- 第二步: V1 (A USDT → B NEAR)
- 利润: B - 100
```

## 📋 支持的交易对

| 交易对 | V1支持 | V2支持 | 最小利润阈值 | 状态 |
|--------|--------|--------|--------------|------|
| NEAR-USDT | ✅ | ✅ | 1.5% | 启用 |
| NEAR-USDC | ✅ | ✅ | 1.2% | 观察 |

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
cd ref-internal-arbitrage

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 ACCOUNT_ID 和 PRIVATE_KEY
```

### 2. 配置文件
编辑 `config/internalPairs.ts` 调整交易对配置：
```typescript
{
  name: 'NEAR-USDT',
  testAmount: '100',              // 测试金额
  minProfitThreshold: 0.015,      // 最小1.5%利润
  maxTradeAmount: '1000',         // 最大交易金额
  executionEnabled: true          // 是否启用自动执行
}
```

### 3. 运行方式

#### 开发模式
```bash
# 启动监控（仅监控，不执行）
npm run monitor

# 开发模式（热重载）
npm run dev

# 正常启动
npm start
```

#### 生产模式 (PM2)
```bash
# 启动PM2服务
npm run pm2:start

# 查看状态
npm run pm2:status

# 查看日志
npm run pm2:logs

# 重启服务
npm run pm2:restart

# 停止服务
npm run pm2:stop
```

## ⚙️ 配置说明

### 主要配置 (`config/index.ts`)
```typescript
export const INTERNAL_ARBITRAGE_CONFIG = {
  monitoringInterval: 3000,        // 监控间隔 (3秒)
  executionEnabled: true,          // 启用自动执行
  maxConcurrentTrades: 1,          // 最大并发交易
  globalMinProfit: 0.01,           // 全局最小利润 (1%)
  maxDailyTrades: 50,              // 每日最大交易次数
};
```

### 监控配置 (`config/index.ts`)
```typescript
export const MONITORING_CONFIG = {
  showOpportunities: true,         // 显示套利机会
  showOnlyProfitable: true,        // 只显示有利可图的
  minDisplayProfit: 0.005,         // 最小显示利润 (0.5%)
  statsInterval: 60000,            // 统计显示间隔 (1分钟)
};
```

## 📊 监控输出

### 套利机会显示
```
💰 发现 2 个套利机会:
   ✅ V1→V2: 100-1850.5-101.2 (1.2000%)
   ❌ V2→V1: 100-1845.2-99.8 (-0.2000%)
```

### 统计信息
```
📊 监控统计信息:
   总机会数: 156
   有利可图: 23 (14.74%)
   V1→V2: 78, V2→V1: 78
   平均利润: 0.8234%
   最大利润: 2.1567%
   最后更新: 14:30:25
```

## 🔧 风险控制

### 多层安全机制
1. **利润阈值**: 只执行超过最小利润阈值的交易
2. **交易限制**: 每日最大交易次数限制
3. **执行锁**: 防止同一交易对并发执行
4. **余额检查**: 确保有足够余额执行交易
5. **滑点控制**: 限制最大滑点容忍度

### 错误处理
- **网络错误**: 自动重试和冷却机制
- **合约错误**: 智能错误识别和处理
- **余额不足**: 自动余额管理
- **价格变动**: 实时价格验证

## 📁 项目结构

```
ref-internal-arbitrage/
├── main.ts                     # 主程序入口
├── refInternalArbitrageBot.ts  # 套利机器人主逻辑
├── services/
│   ├── internalMonitor.ts      # 内部价格监控服务
│   ├── profitCalculator.ts     # 利润计算服务
│   └── internalExecutor.ts     # 套利执行服务 (待实现)
├── config/
│   ├── internalPairs.ts        # 交易对配置
│   └── index.ts                # 主配置文件
├── types/
│   └── index.ts                # 类型定义
├── logs/                       # 日志文件目录
├── pids/                       # 进程ID文件目录
├── package.json                # 依赖配置
├── ecosystem.config.js         # PM2配置
└── README.md                   # 使用说明
```

## 🔍 日志管理

### 日志文件
- `logs/ref-internal-arbitrage.log` - 主日志
- `logs/ref-internal-arbitrage-out.log` - 标准输出
- `logs/ref-internal-arbitrage-error.log` - 错误日志

### 日志轮转
- 单文件最大: 100MB
- 保留文件: 10个
- 自动压缩: 是
- 轮转时间: 每天午夜

## 💡 使用建议

### 初次使用
1. 先在测试环境运行，观察套利机会
2. 设置较高的利润阈值，确保稳定盈利
3. 从小金额开始，逐步增加交易规模

### 优化建议
1. 根据市场情况调整监控间隔
2. 定期分析统计数据，优化参数
3. 监控gas费用，确保净利润为正

### 风险提示
- 套利存在市场风险，请谨慎操作
- 建议设置合理的止损机制
- 定期检查账户余额和交易记录

## 🆘 故障排除

### 常见问题
1. **报价失败**: 检查网络连接和RPC配置
2. **执行失败**: 检查账户余额和私钥配置
3. **无套利机会**: 调整利润阈值或监控间隔

### 支持联系
如有问题，请查看日志文件或联系技术支持。
