/**
 * REF Finance 内部套利配置
 */

import { InternalArbitrageConfig } from '../types';

/**
 * 主配置
 */
export const INTERNAL_ARBITRAGE_CONFIG: InternalArbitrageConfig = {
  // 监控配置
  monitoringInterval: 900,        // 每3秒监控一次
  
  // 执行配置
  executionEnabled: true,          // 启用自动执行
  maxConcurrentTrades: 1,          // 最大1个并发交易（避免冲突）
  
  // 风险控制
  globalMinProfitNear: 0.01,       // 全局最小0.01 NEAR利润
  
  // 日志配置
  logLevel: 'info',
  detailedLogging: true
};

/**
 * REF Finance 合约地址
 */
export const CONTRACTS = {
  V1: 'v2.ref-finance.near',
  V2: 'dclv2.ref-labs.near'
};

/**
 * RPC 配置
 */
export const RPC_CONFIG = {
  url: process.env.RPC_URL || 'https://rpc.mainnet.near.org',
  timeout: 30000
};

/**
 * Gas 配置
 */
export const GAS_CONFIG = {
  refSwap: BigInt('300000000000000'),      // 300 TGas
  maxGasPerTrade: BigInt('600000000000000') // 600 TGas (两步交易)
};

/**
 * 存款配置
 */
export const DEPOSIT_CONFIG = {
  refFtTransfer: BigInt('1')  // 1 yoctoNEAR
};

/**
 * 监控配置
 */
export const MONITORING_CONFIG = {
  // 日志输出配置
  showOpportunities: true,         // 显示套利机会
  showOnlyProfitable: true,        // 只显示有利可图的机会
  minDisplayProfitNear: 0.005,     // 最小显示利润阈值 (0.005 NEAR)
  
  // 错误处理
  maxConsecutiveErrors: 10,        // 最大连续错误次数
  errorCooldownTime: 30000         // 错误冷却时间 (30秒)
};

/**
 * 执行配置
 */
export const EXECUTION_CONFIG = {
  // 执行锁定
  lockTimeout: 300000,             // 执行锁定超时 (5分钟)
  
  // 重试配置
  maxRetries: 3,                   // 最大重试次数
  retryDelay: 5000,                // 重试延迟 (5秒)
  
  // 安全配置
  dryRun: false,                   // 是否为模拟运行
  requireConfirmation: false,      // 是否需要确认
  
  // 余额检查
  minBalanceThreshold: '10',       // 最小余额阈值 (10 NEAR)
  autoWrapNear: true               // 自动包装NEAR
};

/**
 * 获取环境配置
 */
export function getEnvironmentConfig() {
  return {
    accountId: process.env.ACCOUNT_ID,
    privateKey: process.env.PRIVATE_KEY,
    rpcUrl: process.env.RPC_URL || RPC_CONFIG.url,
    network: process.env.NETWORK || 'mainnet'
  };
}

/**
 * 验证环境配置
 */
export function validateEnvironmentConfig(): boolean {
  const config = getEnvironmentConfig();
  
  if (!config.accountId) {
    console.error('❌ 缺少环境变量: ACCOUNT_ID');
    return false;
  }
  
  if (!config.privateKey) {
    console.error('❌ 缺少环境变量: PRIVATE_KEY');
    return false;
  }
  
  return true;
}

/**
 * 导出所有配置
 */
export {
  INTERNAL_TRADING_PAIRS,
  getEnabledPairs,
  getPairByName,
  getExecutablePairs,
  validatePairConfig,
  getValidPairs
} from './internalPairs';

/**
 * 默认导出主配置
 */
export default INTERNAL_ARBITRAGE_CONFIG;
