/**
 * REF Finance 内部套利交易对配置
 */

import { InternalTradingPair, TokenMetadata } from '../types';

/**
 * 代币定义
 */
export const TOKENS: Record<string, TokenMetadata> = {
  NEAR: {
    id: 'wrap.near',
    symbol: 'NEAR',
    decimals: 24,
    name: 'NEAR Protocol'
  },
  USDT: {
    id: 'usdt.tether-token.near',
    symbol: 'USDT',
    decimals: 6,
    name: 'Tether USD'
  },
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    symbol: 'USDC',
    decimals: 6,
    name: 'USD Coin'
  },
  USDC_e:{
    id: 'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near',
    symbol: 'USDC.e',
    decimals: 6,
    name: 'USD Coin'
  }
};

/**
 * REF Finance 内部套利交易对配置
 */
export const INTERNAL_TRADING_PAIRS: InternalTradingPair[] = [
  {
    name: 'NEAR-USDT',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDT,
    
    // 交易配置
    testAmount: '15',              // 测试100 NEAR
    minProfitThresholdNear: 0.022,    // 最小1.5 NEAR利润
    
    // 系统启用
    v1Enabled: true,
    v2Enabled: true,
    v2PoolIds: [
      'usdt.tether-token.near|wrap.near|100',  // 0.2% fee
      'usdt.tether-token.near|wrap.near|2000'    // 0.05% fee
    ],
    
    // 风险控制
    maxSlippage: 0.005,              // 最大0.3%滑点
    executionEnabled: true,          // 启用自动执行

    // 重试配置
    maxRetries: 3,                   // 第二步失败最大重试次数
    retryWaitTime: 1000             // 重试等待时间(ms)
  },
  
  {
    name: 'NEAR-USDC',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDC,
    
    // 交易配置
    testAmount: '15',              // 测试10 NEAR
    minProfitThresholdNear: 0.022,    // 最小0.01 NEAR利润 (USDC流动性更好)
    
    // 系统启用
    v1Enabled: true,
    v2Enabled: true,
    v2PoolIds: [
      '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',  // 0.01% fee
      '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|2000'  // 0.01% fee
    ],
    
    // 风险控制
    maxSlippage: 0.005,             // 最大0.3%滑点
    executionEnabled: true,         // 暂时禁用自动执行，先观察

    // 重试配置
    maxRetries: 3,                  // 第二步失败最大重试次数
    retryWaitTime: 1000            // 重试等待时间(ms)
  },

  {
    name: 'NEAR-USDC.e',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDC_e,
    
    // 交易配置
    testAmount: '15',              // 测试10 NEAR
    minProfitThresholdNear: 0.022,    // 最小0.01 NEAR利润 (USDC流动性更好)
    
    // 系统启用
    v1Enabled: true,
    v2Enabled: true,
    v2PoolIds: [
      'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near|wrap.near|2000'  // 0.2% fee
    ],
    
    // 风险控制
    maxSlippage: 0.005,             // 最大0.3%滑点
    executionEnabled: true,         // 暂时禁用自动执行，先观察

    // 重试配置
    maxRetries: 2,                  // USDC.e重试次数较少
    retryWaitTime: 1500            // 重试等待时间稍长
  }
];

/**
 * 获取启用的交易对
 */
export function getEnabledPairs(): InternalTradingPair[] {
  return INTERNAL_TRADING_PAIRS.filter(pair => 
    pair.v1Enabled && pair.v2Enabled
  );
}

/**
 * 根据名称获取交易对
 */
export function getPairByName(name: string): InternalTradingPair | undefined {
  return INTERNAL_TRADING_PAIRS.find(pair => pair.name === name);
}

/**
 * 获取可执行的交易对
 */
export function getExecutablePairs(): InternalTradingPair[] {
  return INTERNAL_TRADING_PAIRS.filter(pair => 
    pair.v1Enabled && 
    pair.v2Enabled && 
    pair.executionEnabled
  );
}

/**
 * 验证交易对配置
 */
export function validatePairConfig(pair: InternalTradingPair): boolean {
  // 基本验证
  if (!pair.name || !pair.tokenA || !pair.tokenB) {
    return false;
  }
  
  // 金额验证
  if (parseFloat(pair.testAmount) <= 0) {
    return false;
  }
  
  // 利润阈值验证
  if (pair.minProfitThresholdNear <= 0) {
    return false;
  }
  
  // 滑点验证
  if (pair.maxSlippage <= 0 || pair.maxSlippage >= 1) {
    return false;
  }
  
  // V2池子验证
  if (pair.v2Enabled && (!pair.v2PoolIds || pair.v2PoolIds.length === 0)) {
    return false;
  }
  
  return true;
}

/**
 * 获取所有有效的交易对
 */
export function getValidPairs(): InternalTradingPair[] {
  return INTERNAL_TRADING_PAIRS.filter(validatePairConfig);
}
