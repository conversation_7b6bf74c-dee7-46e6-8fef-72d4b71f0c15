/**
 * REF Finance 内部套利类型定义
 */

/**
 * 代币元数据
 */
export interface TokenMetadata {
  id: string;
  symbol: string;
  decimals: number;
  name?: string;
}

/**
 * 报价参数
 */
export interface QuoteParams {
  tokenIn: TokenMetadata;
  tokenOut: TokenMetadata;
  amountIn: string;
  slippage?: number;
}

/**
 * 报价结果
 */
export interface QuoteResult {
  system: 'V1' | 'DCL_V2';
  contractId: string;
  outputAmount: string;
  inputAmount: string;
  route?: any;
  poolId?: string;
  rawResponse?: any;
}

/**
 * 内部套利机会
 */
export interface InternalArbitrageOpportunity {
  direction: 'V1_TO_V2' | 'V2_TO_V1';
  pair: InternalTradingPair;
  inputAmount: string;
  
  // 第一步交易
  firstStep: {
    system: 'V1' | 'DCL_V2';
    inputAmount: string;
    outputAmount: string;
    quote: QuoteResult;
  };
  
  // 第二步交易
  secondStep: {
    system: 'V1' | 'DCL_V2';
    inputAmount: string;
    outputAmount: string;
    quote: QuoteResult;
  };
  
  // 利润分析
  profit: {
    absolute: number;      // 绝对利润 (B - 100)
    percentage: number;    // 利润百分比
    profitable: boolean;   // 是否有利可图
  };
  
  timestamp: number;
}

/**
 * 内部交易对配置
 */
export interface InternalTradingPair {
  name: string;
  tokenA: TokenMetadata;  // 基础代币 (如 NEAR)
  tokenB: TokenMetadata;  // 目标代币 (如 USDT)
  
  // 交易配置
  testAmount: string;           // 测试金额 (如 "100")
  minProfitThresholdNear: number; // 最小利润阈值 (NEAR数量，如 1.5)
  
  // 系统启用状态
  v1Enabled: boolean;
  v2Enabled: boolean;
  v2PoolIds?: string[];        // V2池子ID列表
  
  // 风险控制
  maxSlippage: number;         // 最大滑点
  executionEnabled: boolean;   // 是否启用自动执行

  // 重试配置
  maxRetries?: number;         // 第二步失败最大重试次数 (默认3)
  retryWaitTime?: number;      // 重试等待时间(ms) (默认1000)
}

/**
 * 交易结果
 */
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  outputAmount?: string;
  outputAmountWei?: string;
  inputAmount?: string;
  inputAmountWei?: string;
  error?: string;
}



/**
 * 监控统计
 */
export interface MonitoringStats {
  totalOpportunities: number;
  profitableOpportunities: number;
  v1ToV2Opportunities: number;
  v2ToV1Opportunities: number;
  averageProfit: number;
  maxProfit: number;
  lastUpdateTime: number;
}

/**
 * 套利执行结果
 */
export interface ArbitrageExecutionResult {
  success: boolean;
  error?: string;
  opportunity: InternalArbitrageOpportunity;

  // 交易哈希
  firstStepHash?: string;
  secondStepHash?: string;

  // 实际金额（wei格式）
  actualFirstOutput?: string;
  actualFinalOutput?: string;

  // 实际利润
  actualProfit?: number;

  // 执行时间
  executionTime?: number;

  // 时间戳 (可选)
  timestamp?: number;
}

/**
 * 配置接口
 */
export interface InternalArbitrageConfig {
  // 监控配置
  monitoringInterval: number;    // 监控间隔 (毫秒)

  // 执行配置
  executionEnabled: boolean;     // 是否启用自动执行
  maxConcurrentTrades: number;   // 最大并发交易数

  // 风险控制
  globalMinProfitNear: number;   // 全局最小利润阈值 (NEAR数量)

  // 日志配置
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  detailedLogging: boolean;
}

/**
 * 错误类型
 */
export interface ArbitrageError {
  type: 'QUOTE_ERROR' | 'EXECUTION_ERROR' | 'NETWORK_ERROR' | 'VALIDATION_ERROR';
  message: string;
  details?: any;
  timestamp: number;
}
