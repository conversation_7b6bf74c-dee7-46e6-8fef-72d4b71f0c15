/**
 * REF Finance 内部套利机器人主程序
 * 
 * 使用方法：
 * npm start              # 启动机器人
 * npm run monitor        # 仅监控模式
 * npm run test           # 测试模式
 */

import { refInternalArbitrageBot } from './refInternalArbitrageBot';

/**
 * 主程序
 */
async function main() {
  console.log('🎯 REF Finance 内部套利机器人');
  console.log('================================');
  console.log('监控 REF V1 和 DCL v2 系统之间的套利机会');
  console.log('');
  
  try {
    // 初始化机器人
    await refInternalArbitrageBot.initialize();
    
    // 启动监控
    await refInternalArbitrageBot.start();
    
    // 处理优雅退出
    process.on('SIGINT', () => {
      console.log('\n🛑 收到退出信号，正在停止机器人...');
      refInternalArbitrageBot.stop();
      process.exit(0);
    });
    
    process.on('SIGTERM', () => {
      console.log('\n🛑 收到终止信号，正在停止机器人...');
      refInternalArbitrageBot.stop();
      process.exit(0);
    });
    
    // 保持程序运行
    console.log('✅ 机器人运行中... (按 Ctrl+C 退出)');
    
  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

// 运行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序异常退出:', error);
    process.exit(1);
  });
}

export default main;
