#!/bin/bash

# 套利机器人启动脚本
echo "🤖 启动套利机器人..."

# 创建必要的目录
mkdir -p logs
mkdir -p pids

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "❌ 错误：未找到.env文件"
    echo "请确保.env文件存在并包含必要的配置"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
npm install

# 编译TypeScript（可选）
echo "🔨 编译TypeScript..."
npm run build 2>/dev/null || echo "⚠️ 跳过编译步骤"

# 停止现有进程
echo "🛑 停止现有进程..."
pm2 delete arbitrage-bot 2>/dev/null || echo "没有运行中的进程"

# 启动新进程
echo "🚀 启动套利机器人..."
pm2 start ecosystem.config.js --env production

# 显示状态
echo "📊 进程状态:"
pm2 list

echo "📋 有用的命令:"
echo "   pm2 logs arbitrage-bot    - 查看实时日志"
echo "   pm2 monit                 - 监控面板"
echo "   pm2 restart arbitrage-bot - 重启机器人"
echo "   pm2 stop arbitrage-bot    - 停止机器人"
echo "   pm2 delete arbitrage-bot  - 删除进程"

echo "✅ 套利机器人启动完成!"
