# 如何添加和配置交易对

## 📋 概述

现在配置系统已经简化，每个交易对使用**固定的交易金额**，而不是范围。这样更简单、更直观！

## 🎯 配置含义

### **tradeAmount（交易金额）**
- 这是每次套利检查时使用的**固定金额**
- 例如：`tradeAmount: "100"` 表示每次用 100 个基础代币进行套利测试

### **示例说明**
```typescript
{
  id: 'NEAR-USDC',
  tokenA: NEAR,      // 基础代币
  tokenB: USDC,      // 报价代币  
  tradeAmount: '100' // 固定用100 NEAR进行套利测试
}
```

**套利流程：**
1. 用 100 NEAR 在 REF 和 VEAX 获取 USDC 报价
2. 用获得的 USDC 再获取 NEAR 的反向报价
3. 计算是否有套利机会

## 🚀 三种添加方式

### **方式1: 快速设置（推荐新手）**
```bash
npm run setup:quick
```
自动添加常用交易对：
- NEAR-USDC: 100 NEAR
- NEAR-USDT: 100 NEAR  
- USDC-USDT: 1000 USDC

### **方式2: 配置管理工具**
```bash
npm run config:manage
```
提供交互式配置界面

### **方式3: 手动编程添加**
```typescript
import { createTradingPair, tradingPairManager } from './src/config/tradingPairs';

// 添加新交易对
const newPair = createTradingPair('NEAR', 'USDC', {
  enabled: true,
  checkInterval: 1000,  // 1秒检查一次
  tradeAmount: '100',   // 固定100 NEAR
  description: '自定义交易对'
});

tradingPairManager.addPair(newPair);
```

## 📊 推荐配置

### **主要交易对（高流动性）**
```typescript
// NEAR-USDC
{
  tradeAmount: '100',    // 100 NEAR
  checkInterval: 1000,   // 1秒检查
  enabled: true
}

// NEAR-USDT  
{
  tradeAmount: '100',    // 100 NEAR
  checkInterval: 1000,   // 1秒检查
  enabled: true
}
```

### **稳定币对**
```typescript
// USDC-USDT
{
  tradeAmount: '1000',   // 1000 USDC
  checkInterval: 2000,   // 2秒检查
  enabled: true
}
```

### **小流动性对**
```typescript
// NEAR-REF
{
  tradeAmount: '50',     // 50 NEAR
  checkInterval: 5000,   // 5秒检查
  enabled: false         // 默认禁用
}

// WBTC-NEAR
{
  tradeAmount: '0.1',    // 0.1 WBTC
  checkInterval: 5000,   // 5秒检查
  enabled: false         // 默认禁用
}
```

## 🔧 配置参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `tradeAmount` | 固定交易金额 | `"100"` |
| `checkInterval` | 检查间隔（毫秒） | `1000` (1秒) |
| `enabled` | 是否启用监控 | `true` |
| `description` | 描述信息 | `"主要交易对"` |

## 💡 配置建议

### **根据流动性调整**
- **高流动性**：100-1000 个代币
- **中流动性**：50-500 个代币  
- **低流动性**：10-100 个代币

### **根据代币价值调整**
- **NEAR** (约$2): 100 NEAR = $200
- **USDC** (约$1): 1000 USDC = $1000
- **WBTC** (约$40000): 0.1 WBTC = $4000

### **检查频率建议**
- **主要对**: 1000ms (1秒)
- **次要对**: 2000ms (2秒)
- **测试对**: 5000ms (5秒)

## 📝 实际操作示例

### **添加 NEAR-AURORA 交易对**
```typescript
// 1. 先确认代币信息
const AURORA = {
  id: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
  symbol: 'AURORA',
  name: 'Aurora',
  decimals: 18
};

// 2. 创建交易对
const nearAurora = createTradingPair('NEAR', 'AURORA', {
  enabled: true,
  checkInterval: 3000,  // 3秒检查一次
  tradeAmount: '200',   // 固定200 NEAR
  description: 'NEAR/AURORA 交易对'
});

// 3. 添加到管理器
tradingPairManager.addPair(nearAurora);
```

### **修改现有交易对**
```typescript
// 修改 NEAR-USDC 的交易金额
tradingPairManager.updatePair('NEAR-USDC', {
  tradeAmount: '200',   // 改为200 NEAR
  checkInterval: 500    // 改为0.5秒检查
});
```

### **启用/禁用交易对**
```typescript
// 启用交易对
tradingPairManager.togglePair('NEAR-REF', true);

// 禁用交易对  
tradingPairManager.togglePair('WBTC-NEAR', false);
```

## 🎯 完整工作流程

### **1. 设置配置**
```bash
npm run setup:quick
```

### **2. 查看配置**
```bash
npm run config:manage
```

### **3. 测试监控**
```bash
npm run debug:arbitrage
```

### **4. 测试报价**
```bash
npm run debug:veax
```

## ⚠️ 注意事项

1. **交易金额不要过大**：避免价格冲击
2. **检查间隔不要过短**：避免API限制
3. **先禁用自动执行**：手动验证机会
4. **定期检查配置**：根据市场调整

## 🔍 监控建议

- **开始时用小金额**：10-50 个代币
- **观察价格影响**：如果>5%，减少金额
- **监控成功率**：如果报价失败多，增加间隔
- **逐步增加金额**：确认稳定后再增加

现在你可以轻松配置任何交易对了！🚀
