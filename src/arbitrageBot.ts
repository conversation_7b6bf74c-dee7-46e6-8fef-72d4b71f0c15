/**
 * 完整的套利机器人主程序
 * 
 * 功能：
 * 1. 监控模块：每秒获取REF和VEAX报价，检测套利机会
 * 2. 交易执行模块：自动执行套利交易
 * 3. 风险管理：处理交易失败情况
 */

import 'dotenv/config';
import { refQuoteService } from './services/refQuoteService';
import { VeaxQuoteService } from './services/veaxQuoteService';
import RefExecutionServiceCorrect from './services/refExecutionServiceCorrect';
import { VeaxExecutionService } from './services/veaxExecutionService';
import NearWrapService from './services/nearWrapService';
import { AutoBalanceManager } from './services/autoBalanceManager';
import { tradingPairManager, TradingPairConfig } from './config/tradingPairs';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import { dynamicAmountManager } from './services/dynamicAmountManager';

/**
 * 套利机会接口
 */
interface ArbitrageOpportunity {
  direction: 'REF_TO_VEAX' | 'VEAX_TO_REF';
  pair: TradingPairConfig;
  inputAmount: string;
  intermediateAmount: string;
  finalAmount: string;
  profit: number;
}

/**
 * 交易结果接口（修复精度问题）
 */
interface TradeResult {
  success: boolean;
  txHash?: string;
  outputAmount?: string;      // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确）
  error?: string;
}

/**
 * 套利机器人主类
 */
class ArbitrageBot {
  private isRunning: boolean = false;
  private pairIntervals: Map<string, NodeJS.Timeout> = new Map(); // 🔧 改为每个交易对独立的间隔

  // 执行锁机制
  private isExecutingTrade: boolean = false;
  private pendingOpportunities: ArbitrageOpportunity[] = [];

  // 执行服务
  private refExecutionService: RefExecutionServiceCorrect;
  private veaxExecutionService: VeaxExecutionService;
  private nearWrapService: NearWrapService;
  private autoBalanceManager: AutoBalanceManager;

  // 从配置文件获取参数
  private readonly tradingPairs: TradingPairConfig[];
  private readonly arbitrageConfig;
  
  // 统计数据
  private stats = {
    totalChecks: 0,
    opportunitiesFound: 0,
    successfulTrades: 0,
    failedTrades: 0,
    totalProfit: 0,
    startTime: 0
  };

  constructor() {
    // 从配置文件获取交易对和套利配置
    this.tradingPairs = tradingPairManager.getEnabledPairs();
    this.arbitrageConfig = tradingPairManager.getArbitrageConfig();

    // 初始化执行服务
    this.refExecutionService = new RefExecutionServiceCorrect(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    this.veaxExecutionService = new VeaxExecutionService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    this.nearWrapService = new NearWrapService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    // 初始化自动余额管理器
    this.autoBalanceManager = new AutoBalanceManager(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      this.arbitrageConfig.autoBalanceManagement,
      () => this.isExecutingTrade, // 传递交易状态检查函数
      'mainnet'
    );
  }

  /**
   * 启动套利机器人
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 套利机器人已在运行中');
      return;
    }

    console.log('🚀 启动套利机器人');
    console.log(`📊 利润阈值: ${this.arbitrageConfig.minProfitThreshold} NEAR`);
    console.log(`🔄 最大滑点: ${this.arbitrageConfig.maxSlippage * 100}%`);
    console.log(`⏱️ 监控间隔: ${this.arbitrageConfig.monitoringInterval}ms`);
    console.log(`🎯 监控交易对: ${this.tradingPairs.length}个`);

    // 显示所有启用的交易对
    this.tradingPairs.forEach(pair => {
      // 🔧 修复显示逻辑：正确显示动态金额和固定金额
      let amountDisplay: string;
      if (pair.dynamicAmount?.enabled) {
        amountDisplay = `动态(${pair.dynamicAmount.low}-${pair.dynamicAmount.high})`;
      } else if (pair.tradeAmount) {
        amountDisplay = pair.tradeAmount;
      } else {
        amountDisplay = '未配置';
      }
      console.log(`   ${pair.id}: ${amountDisplay} ${pair.tokenA.symbol}`);
    });

    console.log('='.repeat(60));

    // 初始化执行服务
    console.log('🔧 初始化执行服务...');
    await this.refExecutionService.initialize();
    await this.veaxExecutionService.initialize();
    await this.nearWrapService.initialize();
    await this.autoBalanceManager.initialize();

    // 🔧 关键：启动时检查所有注册状态
    console.log('🔍 检查DApp和代币注册状态...');
    await this.checkAllRegistrations();

    this.isRunning = true;
    this.stats.startTime = Date.now();

    // 启动自动余额管理
    this.autoBalanceManager.start();

    // 启动监控循环
    this.startMonitoring();

    console.log('✅ 套利机器人已启动，开始监控...\n');
  }

  /**
   * 🔧 检查所有DApp和代币注册状态
   * 在程序启动时确保所有必要的注册都已完成，避免交易时出现问题
   */
  private async checkAllRegistrations(): Promise<void> {
    try {
      // 1. 检查REF Finance DApp注册
      console.log('📋 检查REF Finance DApp注册...');
      await this.checkRefFinanceRegistration();

      // 2. 检查VEAX DApp注册
      console.log('📋 检查VEAX DApp注册...');
      await this.checkVeaxRegistration();

      // 3. 检查所有配置代币的注册状态
      console.log('📋 检查所有配置代币注册...');
      await this.checkAllTokenRegistrations();

      console.log('✅ 所有注册检查完成');
    } catch (error) {
      console.error('❌ 注册检查失败:', error);
      throw error;
    }
  }

  /**
   * 检查REF Finance DApp注册
   */
  private async checkRefFinanceRegistration(): Promise<void> {
    try {
      // 使用NEAR MCP工具检查REF Finance注册
      const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;

      // 通过VeaxExecutionService获取account实例（它们都使用相同的账户）
      const account = (this.veaxExecutionService as any).account;
      if (!account) {
        throw new Error('账户未初始化');
      }

      const result = await account.viewFunction({
        contractId: 'v2.ref-finance.near',
        methodName: 'get_user_storage_state',
        args: { account_id: accountId }
      });

      if (result) {
        console.log('✅ REF Finance DApp已注册');
      } else {
        console.log('⚠️ REF Finance DApp未注册，正在注册...');
        await account.functionCall({
          contractId: 'v2.ref-finance.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000' // 0.125 NEAR
        });
        console.log('✅ REF Finance DApp注册成功');
      }
    } catch (error: any) {
      if (error.message?.includes('not registered')) {
        console.log('⚠️ REF Finance DApp未注册，正在注册...');
        const account = (this.veaxExecutionService as any).account;
        await account.functionCall({
          contractId: 'v2.ref-finance.near',
          methodName: 'storage_deposit',
          args: {},
          gas: '**************',
          attachedDeposit: '125000000000000000000000'
        });
        console.log('✅ REF Finance DApp注册成功');
      } else {
        console.warn('⚠️ REF Finance注册检查失败:', error.message);
      }
    }
  }

  /**
   * 检查VEAX DApp注册
   */
  private async checkVeaxRegistration(): Promise<void> {
    try {
      const userStatus = await this.veaxExecutionService.checkUserRegistration();

      if (userStatus.isRegistered) {
        console.log('✅ VEAX DApp已注册');
      } else {
        console.log('⚠️ VEAX DApp未注册，正在注册...');
        const registerResult = await this.veaxExecutionService.registerUser();
        if (registerResult.success) {
          console.log('✅ VEAX DApp注册成功');
        } else {
          throw new Error(`VEAX DApp注册失败: ${registerResult.error}`);
        }
      }
    } catch (error) {
      console.error('❌ VEAX注册检查失败:', error);
      throw error;
    }
  }

  /**
   * 检查所有配置代币的注册状态
   */
  private async checkAllTokenRegistrations(): Promise<void> {
    // 收集所有需要检查的代币
    const allTokens = new Set<string>();

    this.tradingPairs.forEach(pair => {
      allTokens.add(pair.tokenA.id);
      allTokens.add(pair.tokenB.id);
    });

    console.log(`📊 需要检查 ${allTokens.size} 个代币的注册状态`);

    let registeredCount = 0;
    let newRegistrations = 0;

    for (const tokenId of allTokens) {
      try {
        // 检查代币在代币合约中的注册
        const tokenStatus = await this.veaxExecutionService.checkTokenRegistration(tokenId);

        if (tokenStatus.isRegistered) {
          console.log(`✅ ${tokenId} 已注册`);
          registeredCount++;
        } else {
          console.log(`⚠️ ${tokenId} 未注册，正在注册...`);
          const registerResult = await this.veaxExecutionService.registerToken(tokenId);
          if (registerResult.success) {
            console.log(`✅ ${tokenId} 注册成功`);
            newRegistrations++;
          } else {
            throw new Error(`代币注册失败: ${tokenId} - ${registerResult.error}`);
          }
        }
      } catch (error: any) {
        console.error(`❌ 检查/注册代币 ${tokenId} 失败:`, error.message);
        throw new Error(`代币注册失败: ${tokenId}`);
      }
    }

    console.log(`✅ 代币注册检查完成: ${registeredCount} 个已注册, ${newRegistrations} 个新注册`);
  }

  /**
   * 启动全局监控循环
   */
  private startMonitoring(): void {
    console.log('🔧 启动全局监控系统...');

    const monitorInterval = setInterval(async () => {
      if (!this.isRunning || this.isExecutingTrade) {
        return; // 如果机器人停止或正在执行交易，则跳过此轮
      }

      try {
        this.stats.totalChecks++;
        this.pendingOpportunities = []; // 清空上一轮的机会

        // 并行检查所有启用的交易对
        await Promise.all(this.tradingPairs.map(pair => this.checkPair(pair)));

        if (this.pendingOpportunities.length > 0) {
          const bestOpportunity = this.selectBestOpportunity();
          await this.executeArbitrage(bestOpportunity);
        } else {
          // 无套利机会时，重置所有动态金额交易对到低档
          this.tradingPairs.forEach(pair => {
            if (pair.dynamicAmount?.enabled) {
              dynamicAmountManager.resetToLowLevel(pair.id, pair);
            }
          });
        }

        // 显示状态
        if (this.stats.totalChecks % this.arbitrageConfig.statusDisplayInterval === 0) {
          const runTime = (Date.now() - this.stats.startTime) / 1000;
          console.log(`⏱️ 监控中... 已运行${Math.floor(runTime)}秒，检查${this.stats.totalChecks}次，发现${this.stats.opportunitiesFound}个机会`);
          dynamicAmountManager.displayStatsSummary();
        }

      } catch (error) {
        console.error('❌ 监控循环出错:', error);
      }
    }, this.arbitrageConfig.monitoringInterval);

    this.pairIntervals.set('__global_monitor__', monitorInterval);
    console.log(`✅ 全局监控已启动，每 ${this.arbitrageConfig.monitoringInterval / 1000} 秒检查一次`);
  }

  /**
   * 停止套利机器人
   */
  stop(): void {
    if (!this.isRunning) {
      console.log('⚠️ 套利机器人未在运行');
      return;
    }

    this.isRunning = false;

    // 停止所有监控间隔
    this.pairIntervals.forEach((interval, pairId) => {
      clearInterval(interval);
      console.log(`   🛑 已停止 ${pairId} 的监控`);
    });
    this.pairIntervals.clear();

    // 停止自动余额管理
    this.autoBalanceManager.stop();

    console.log('\n🛑 套利机器人已停止');
    this.printStats();
  }

  /**
   * 🎯 获取交易金额（支持动态金额和固定金额）
   */
  private getTradeAmount(pair: TradingPairConfig): string {
    if (pair.dynamicAmount?.enabled) {
      return dynamicAmountManager.getCurrentAmount(pair.id, pair);
    }
    return pair.tradeAmount || '1'; // 回退到固定金额或默认值
  }

  /**
   * 检查单个交易对的套利机会
   */
  private async checkPair(pair: TradingPairConfig): Promise<void> {
    try {
      // 🎯 获取当前档位的查询金额
      const queryAmount = this.getTradeAmount(pair);

      // 并行获取两个方向的报价
      const [refQuote, veaxQuote] = await Promise.all([
        this.getREFQuote(pair.tokenA, pair.tokenB, queryAmount),
        this.getVEAXQuote(pair.tokenA, pair.tokenB, queryAmount)
      ]);

      if (!refQuote || !veaxQuote) {
        if (this.arbitrageConfig.debugMode) {
          console.log(`⚠️ ${pair.id} 报价获取失败: REF=${refQuote ? '✅' : '❌'}, VEAX=${veaxQuote ? '✅' : '❌'}`);
        }
        return;
      }

      const veaxQuoteHumanReadable = veaxQuote;

      if (this.arbitrageConfig.debugMode) {
        console.log(`🔧 VEAX输出格式确认: ${veaxQuote} ${pair.tokenB.symbol} (已经是人类可读格式)`);
      }

      const [refReverseQuote, veaxReverseQuote] = await Promise.all([
        this.getREFQuote(pair.tokenB, pair.tokenA, veaxQuoteHumanReadable),
        this.getVEAXQuote(pair.tokenB, pair.tokenA, refQuote)
      ]);

      if (!refReverseQuote || !veaxReverseQuote) {
        if (this.arbitrageConfig.debugMode) {
          console.log(`⚠️ ${pair.id} 反向报价获取失败: REF=${refReverseQuote ? '✅' : '❌'}, VEAX=${veaxReverseQuote ? '✅' : '❌'}`);
        }
        return;
      }

      const refToVeaxProfit = parseFloat(veaxReverseQuote) - parseFloat(queryAmount);
      const veaxToRefProfit = parseFloat(refReverseQuote) - parseFloat(queryAmount);

      if (this.arbitrageConfig.debugMode) {
        console.log(`🔧 利润计算: veaxReverseQuote ${veaxReverseQuote} ${pair.tokenA.symbol} (人类可读格式)`);
        console.log(`🔧 利润计算: refReverseQuote ${refReverseQuote} ${pair.tokenA.symbol} (人类可读格式)`);
      }

      const shouldShowLogs = this.arbitrageConfig.debugMode ||
                             this.arbitrageConfig.verboseLogging ||
                             refToVeaxProfit >= this.arbitrageConfig.minProfitThreshold * 0.8 ||
                             veaxToRefProfit >= this.arbitrageConfig.minProfitThreshold * 0.8;

      if (shouldShowLogs) {
        const refToVeaxProfitRate = (refToVeaxProfit / parseFloat(queryAmount)) * 100;
        const veaxToRefProfitRate = (veaxToRefProfit / parseFloat(queryAmount)) * 100;

        console.log(`📊 ${pair.id}:`);
        console.log(`   REF→VEAX: ${queryAmount}-${refQuote}-${veaxReverseQuote} (利润: ${refToVeaxProfit.toFixed(4)} NEAR, ${refToVeaxProfitRate.toFixed(2)}%)`);
        console.log(`   VEAX→REF: ${queryAmount}-${veaxQuote}-${refReverseQuote} (利润: ${veaxToRefProfit.toFixed(4)} NEAR, ${veaxToRefProfitRate.toFixed(2)}%)`);
      }

      if (refToVeaxProfit >= this.arbitrageConfig.minProfitThreshold) {
        console.log(`💰 发现套利机会! ${pair.id} REF→VEAX 利润: ${refToVeaxProfit.toFixed(4)} NEAR`);
        this.pendingOpportunities.push({
          direction: 'REF_TO_VEAX',
          pair,
          inputAmount: queryAmount,
          intermediateAmount: refQuote,
          finalAmount: veaxReverseQuote,
          profit: refToVeaxProfit
        });
      }

      if (veaxToRefProfit >= this.arbitrageConfig.minProfitThreshold) {
        console.log(`💰 发现套利机会! ${pair.id} VEAX→REF 利润: ${veaxToRefProfit.toFixed(4)} NEAR`);
        this.pendingOpportunities.push({
          direction: 'VEAX_TO_REF',
          pair,
          inputAmount: queryAmount,
          intermediateAmount: veaxQuote,
          finalAmount: refReverseQuote,
          profit: veaxToRefProfit
        });
      }

    } catch (error) {
      if (this.arbitrageConfig.debugMode) {
        console.error(`❌ 检查${pair.id}失败:`, error);
      }
    }
  }

  /**
   * 精确的wei转换（避免浮点数精度问题）
   * 使用NEAR官方方法确保精度
   */
  private toWei(amount: string, decimals: number): string {
    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 精确的wei转人类可读格式（避免浮点数精度问题）
   * 使用NEAR官方方法确保精度，移除千分位分隔符
   */
  private fromWei(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ fromWei收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法并移除千分位分隔符
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      // 对于其他精度，使用精确的字符串操作
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 获取REF报价
   */
  private async getREFQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
    try {
      const result = await refQuoteService.getQuote({
        tokenIn,
        tokenOut,
        amountIn: amount,
        slippage: 0.005
      });
      return result.outputAmount;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取VEAX报价
   */
  private async getVEAXQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
    try {
      // 🔧 验证输入参数
      if (!this.isValidNumber(amount)) {
        console.warn(`⚠️ VEAX报价输入无效: ${amount}`);
        return null;
      }

      const result = await VeaxQuoteService.getQuote(tokenIn.id, tokenOut.id, amount);

      if (!result.success) {
        return null;
      }

      // 🔧 验证VEAX返回的数值
      if (!this.isValidNumber(result.outputAmount)) {
        console.warn(`⚠️ VEAX返回无效数值: ${result.outputAmount}`);
        return null;
      }

      return result.outputAmount;
    } catch (error) {
      console.error(`❌ VEAX报价错误:`, error);
      return null;
    }
  }

  /**
   * 选择最佳套利机会
   */
  private selectBestOpportunity(): ArbitrageOpportunity {
    // 按利润从大到小排序，选择最大的
    const sorted = this.pendingOpportunities.sort((a, b) => b.profit - a.profit);
    const best = sorted[0];

    console.log(`🎯 选择最佳套利机会: ${best.pair.id} ${best.direction} 利润: ${best.profit.toFixed(4)} NEAR`);
    if (sorted.length > 1) {
      console.log(`   跳过其他 ${sorted.length - 1} 个机会`);
    }

    return best;
  }

  /**
   * 执行套利交易（带执行锁）
   */
  private async executeArbitrage(opportunity: ArbitrageOpportunity): Promise<void> {
    // 设置执行锁
    if (this.isExecutingTrade) {
      console.log(`⚠️ 已有交易在执行中，跳过套利机会`);
      return;
    }

    this.isExecutingTrade = true;
    console.log(`🔒 获取执行锁，开始执行套利交易 (${opportunity.direction})`);
    console.log(`   预期利润: ${opportunity.profit.toFixed(4)} NEAR`);

    this.stats.opportunitiesFound++;

    try {
      let step1Result: TradeResult;
      let step2Result: TradeResult;

      if (opportunity.direction === 'REF_TO_VEAX') {
        // 第一步：REF交易 (tokenA -> tokenB)
        step1Result = await this.executeREFTrade(
          opportunity.pair.tokenA,
          opportunity.pair.tokenB,
          opportunity.inputAmount
        );

        if (!step1Result.success) {
          console.log(`❌ 第一步REF交易失败，放弃套利: ${step1Result.error}`);
          this.stats.failedTrades++;
          return;
        }

        console.log(`✅ 第一步REF交易成功: ${step1Result.outputAmount} ${opportunity.pair.tokenB.symbol}`);
        console.log(`🔄 立即执行第二步VEAX交易...`);

        // 🔧 关键修复：使用wei格式传递，避免精度损失
        const step1OutputWei = step1Result.outputAmountWei || step1Result.outputAmount!;
        console.log(`📊 使用wei格式传递: ${step1OutputWei} wei`);

        // 第二步：VEAX交易 (tokenB -> tokenA) - 立即执行，不等待
        step2Result = await this.executeVEAXTrade(
          opportunity.pair.tokenB,
          opportunity.pair.tokenA,
          step1OutputWei // 🔧 使用wei格式
        );

        if (!step2Result.success) {
          console.log(`❌ 第二步VEAX交易失败，启动风险管理: ${step2Result.error}`);
          await this.riskManagement(opportunity.pair.tokenB, opportunity.pair.tokenA, step1Result.outputAmountWei!);
          return;
        }

      } else {
        // VEAX_TO_REF方向
        // 第一步：VEAX交易 (tokenA -> tokenB)
        step1Result = await this.executeVEAXTrade(
          opportunity.pair.tokenA,
          opportunity.pair.tokenB,
          opportunity.inputAmount
        );

        if (!step1Result.success) {
          console.log(`❌ 第一步VEAX交易失败，放弃套利: ${step1Result.error}`);
          this.stats.failedTrades++;
          return;
        }

        console.log(`✅ 第一步VEAX交易成功: ${step1Result.outputAmount} ${opportunity.pair.tokenB.symbol}`);
        console.log(`🔄 立即执行第二步REF交易...`);

        // 🔧 关键修复：使用wei格式传递，避免精度损失
        const step1OutputWei = step1Result.outputAmountWei || step1Result.outputAmount!;
        console.log(`📊 使用wei格式传递: ${step1OutputWei} wei`);

        // 第二步：REF交易 (tokenB -> tokenA) - 立即执行，不等待
        step2Result = await this.executeREFTrade(
          opportunity.pair.tokenB,
          opportunity.pair.tokenA,
          step1OutputWei // 🔧 使用wei格式
        );

        if (!step2Result.success) {
          console.log(`❌ 第二步REF交易失败，启动风险管理: ${step2Result.error}`);
          await this.riskManagement(opportunity.pair.tokenB, opportunity.pair.tokenA, step1Result.outputAmountWei!);
          return;
        }
      }

      // 🔧 修复利润计算的精度问题
      let actualProfit: number;

      // 检查step2Result.outputAmount的格式
      if (step2Result.outputAmountWei) {
        // 如果有wei格式的输出，使用精确转换
        const outputAmountHuman = this.fromWei(step2Result.outputAmountWei, opportunity.pair.tokenA.decimals);
        actualProfit = parseFloat(outputAmountHuman) - parseFloat(opportunity.inputAmount);
        console.log(`🔧 使用wei格式计算利润: ${step2Result.outputAmountWei} wei → ${outputAmountHuman} ${opportunity.pair.tokenA.symbol}`);
      } else if (step2Result.outputAmount && /^\d+$/.test(step2Result.outputAmount) && parseFloat(step2Result.outputAmount) > 1000000) {
        // 如果outputAmount看起来像wei格式（纯数字且很大）
        const outputAmountHuman = this.fromWei(step2Result.outputAmount, opportunity.pair.tokenA.decimals);
        actualProfit = parseFloat(outputAmountHuman) - parseFloat(opportunity.inputAmount);
        console.log(`🔧 检测到wei格式，转换计算利润: ${step2Result.outputAmount} wei → ${outputAmountHuman} ${opportunity.pair.tokenA.symbol}`);
      } else {
        // 否则假设是人类可读格式
        actualProfit = parseFloat(step2Result.outputAmount!) - parseFloat(opportunity.inputAmount);
        console.log(`📊 使用人类可读格式计算利润: ${step2Result.outputAmount} ${opportunity.pair.tokenA.symbol}`);
      }

      console.log(`🎉 套利交易成功完成!`);
      console.log(`   实际利润: ${actualProfit.toFixed(6)} NEAR`);

      this.stats.successfulTrades++;
      this.stats.totalProfit += actualProfit;

      // 🎯 关键修复：根据实际利润调整下次查询金额
      dynamicAmountManager.adjustNextAmount(opportunity.pair.id, actualProfit, opportunity.pair);

    } catch (error) {
      console.error(`❌ 套利执行错误:`, error);
      this.stats.failedTrades++;
    } finally {
      // 释放执行锁
      this.isExecutingTrade = false;
      console.log(`🔓 释放执行锁，继续监控`);
    }
  }

  /**
   * 执行REF交易
   */
  private async executeREFTrade(tokenIn: any, tokenOut: any, amount: string): Promise<TradeResult> {
    try {
      // 🔧 关键修复：检查amount是否为wei格式，避免精度损失
      const isWeiFormat = /^\d+$/.test(amount) && parseFloat(amount) > 1000;
      let humanReadableAmount: string;
      let inputAmountWei: string;

      if (isWeiFormat) {
        // 🔧 关键修复：如果是wei格式，使用精确的fromWei方法转换
        inputAmountWei = amount;
        humanReadableAmount = this.fromWei(amount, tokenIn.decimals);
        console.log(`🔄 检测到wei格式输入: ${amount} wei → ${humanReadableAmount} ${tokenIn.symbol}`);
      } else {
        // 如果是人类可读格式，转换为wei格式
        humanReadableAmount = amount;
        console.log(`📊 使用人类可读格式: ${humanReadableAmount} ${tokenIn.symbol}`);

        // 🔧 使用精确转换
        if (tokenIn.id === 'wrap.near') {
          inputAmountWei = parseNearAmount(humanReadableAmount) || '0';
        } else {
          inputAmountWei = this.toWei(humanReadableAmount, tokenIn.decimals);
        }
      }

      // 如果输入代币是wNEAR，检查并自动包装NEAR
      if (tokenIn.id === 'wrap.near') {
        console.log(`🔍 检查wNEAR余额，交易需要: ${humanReadableAmount} wNEAR`);

        const wrapResult = await this.nearWrapService.checkAndWrapNear(humanReadableAmount, 10);

        if (!wrapResult.success) {
          throw new Error(`自动包装失败: ${wrapResult.error}`);
        }

        if (wrapResult.wrapped) {
          console.log(`✅ 自动包装完成: ${wrapResult.amount} NEAR → wNEAR`);
        }
      }

      // 先获取报价（使用人类可读格式）
      const quote = await refQuoteService.getQuote({
        tokenIn,
        tokenOut,
        amountIn: humanReadableAmount,
        slippage: 0.005
      });

      if (!quote) {
        throw new Error('无法获取REF报价');
      }

      // 计算最小输出金额（考虑滑点）
      const minOutputAmount = (parseFloat(quote.outputAmount) * 0.995).toString(); // 1%滑点保护

      // 🔧 如果还没有wei格式，进行精确转换
      if (!inputAmountWei) {
        if (tokenIn.id === 'wrap.near') {
          // 对于NEAR代币，使用官方parseNearAmount
          inputAmountWei = parseNearAmount(humanReadableAmount) || '0';
          console.log(`🔧 NEAR精确转换: ${humanReadableAmount} → ${inputAmountWei} yoctoNEAR`);
        } else {
          // 对于其他代币，使用精确的字符串计算
          inputAmountWei = this.toWei(humanReadableAmount, tokenIn.decimals);
          console.log(`🔧 代币精确转换: ${humanReadableAmount} ${tokenIn.symbol} → ${inputAmountWei} wei`);
        }
      }

      // 🔧 使用精确转换最小输出金额
      let minOutputAmountWei: string;
      if (tokenOut.id === 'wrap.near') {
        // 对于NEAR代币，使用官方parseNearAmount
        minOutputAmountWei = parseNearAmount(minOutputAmount) || '0';
      } else {
        // 对于其他代币，使用精确的字符串计算
        minOutputAmountWei = this.toWei(minOutputAmount, tokenOut.decimals);
      }

      console.log(`💱 REF交易参数: ${humanReadableAmount} ${tokenIn.symbol} (${inputAmountWei} wei) → 最少 ${minOutputAmountWei} wei (${minOutputAmount} ${tokenOut.symbol})`);

      // 执行交易（使用正确版本的执行服务）
      const result = await this.refExecutionService.executeSwap(
        quote,
        tokenIn.id, // 输入代币合约ID
        inputAmountWei, // wei格式的输入金额
        minOutputAmountWei, // wei格式的最小输出金额
        0.01,
        quote.poolId ? { poolId: quote.poolId, outputToken: tokenOut.id } : undefined
      );

      return {
        success: result.success,
        txHash: result.transactionHash,
        outputAmount: result.outputAmount,
        outputAmountWei: result.outputAmountWei, // 🔧 关键修复：返回wei格式
        error: result.error
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 执行VEAX交易
   */
  private async executeVEAXTrade(tokenIn: any, tokenOut: any, amount: string): Promise<TradeResult> {
    try {
      // 检查amount是否为wei格式（纯数字且较大）还是人类可读格式
      const isWeiFormat = /^\d+$/.test(amount) && parseFloat(amount) > 1000;
      let humanReadableAmount: string;

      if (isWeiFormat) {
        // 🔧 关键修复：如果是wei格式，使用精确的fromWei方法转换
        humanReadableAmount = this.fromWei(amount, tokenIn.decimals);
        console.log(`🔄 检测到wei格式输入: ${amount} wei → ${humanReadableAmount} ${tokenIn.symbol}`);
      } else {
        // 如果已经是人类可读格式，直接使用
        humanReadableAmount = amount;
        console.log(`📊 使用人类可读格式: ${humanReadableAmount} ${tokenIn.symbol}`);
      }

      // 如果输入代币是wNEAR，检查并自动包装NEAR
      if (tokenIn.id === 'wrap.near') {
        console.log(`🔍 检查wNEAR余额，交易需要: ${humanReadableAmount} wNEAR`);

        const wrapResult = await this.nearWrapService.checkAndWrapNear(humanReadableAmount, 10);

        if (!wrapResult.success) {
          throw new Error(`自动包装失败: ${wrapResult.error}`);
        }

        if (wrapResult.wrapped) {
          console.log(`✅ 自动包装完成: ${wrapResult.amount} NEAR → wNEAR`);
        }
      }

      // 先获取VEAX报价来计算最小输出 (使用人类可读格式)
      const quote = await VeaxQuoteService.getQuote(tokenIn.id, tokenOut.id, humanReadableAmount);

      if (!quote.success) {
        throw new Error('无法获取VEAX报价');
      }

      // 🔧 处理wei格式输入，避免重复转换
      let inputAmountWei: string;

      if (isWeiFormat) {
        // 如果输入已经是wei格式，直接使用
        inputAmountWei = amount;
        console.log(`🔧 VEAX直接使用wei格式: ${inputAmountWei} wei`);
      } else {
        // 如果是人类可读格式，进行精确转换
        if (tokenIn.id === 'wrap.near') {
          // 对于NEAR代币，使用官方parseNearAmount
          inputAmountWei = parseNearAmount(humanReadableAmount) || '0';
          console.log(`🔧 VEAX NEAR精确转换: ${humanReadableAmount} → ${inputAmountWei} yoctoNEAR`);
        } else {
          // 对于其他代币，使用精确的字符串计算
          inputAmountWei = this.toWei(humanReadableAmount, tokenIn.decimals);
          console.log(`🔧 VEAX代币精确转换: ${humanReadableAmount} ${tokenIn.symbol} → ${inputAmountWei} wei`);
        }
      }

      // 🔧 关键修复：VEAX API返回的是人类可读格式，需要转换为wei格式
      // 将人类可读格式转换为wei格式用于交易
      const outputAmountWei = this.toWei(quote.outputAmount, tokenOut.decimals);

      if (this.arbitrageConfig.debugMode) {
        console.log(`🔧 VEAX输出格式转换: ${quote.outputAmount} ${tokenOut.symbol} → ${outputAmountWei} wei`);
      }

      // 计算滑点保护（在wei格式上直接计算）
      const outputAmountBigInt = BigInt(outputAmountWei);
      const slippageProtection = BigInt(99); // 99%
      const hundred = BigInt(100);

      // 计算最小输出金额：outputAmount * 99 / 100
      const minOutputAmountBigInt = (outputAmountBigInt * slippageProtection) / hundred;
      const minOutputAmount = minOutputAmountBigInt.toString();

      // 🔧 使用精确的fromWei方法显示人类可读格式
      const minOutputHumanReadable = this.fromWei(minOutputAmount, tokenOut.decimals);
      console.log(`💱 VEAX交易参数: ${humanReadableAmount} ${tokenIn.symbol} (${inputAmountWei} wei) → 最少 ${minOutputAmount} wei (${minOutputHumanReadable} ${tokenOut.symbol})`);

      // 执行交易 (使用wei格式)
      const result = await this.veaxExecutionService.executeSwap(
        tokenIn.id,
        tokenOut.id,
        inputAmountWei,  // 使用wei格式的输入金额
        minOutputAmount  // 使用wei格式的最小输出金额
      );

      return {
        success: result.success,
        txHash: result.transactionHash,
        outputAmount: result.amountOut,
        outputAmountWei: result.outputAmountWei || result.amountOut, // 🔧 关键修复：返回wei格式
        error: result.error
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 风险管理 - 第二步交易失败时的处理
   */
  private async riskManagement(tokenIn: any, tokenOut: any, amount: string): Promise<void> {
    console.log(`🚨 启动风险管理: 尝试在REF卖出 ${amount} ${tokenIn.symbol}`);

    try {
      // 重新获取REF报价
      const emergencyQuote = await this.getREFQuote(tokenIn, tokenOut, amount);
      
      if (!emergencyQuote) {
        console.log(`❌ 风险管理失败: 无法获取REF报价`);
        return;
      }

      // 执行紧急卖出
      const emergencyResult = await this.executeREFTrade(tokenIn, tokenOut, amount);
      
      if (emergencyResult.success) {
        const loss = parseFloat('3') - parseFloat(emergencyResult.outputAmount!); // 假设原始交易金额
        console.log(`✅ 风险管理成功: 紧急卖出完成`);
        console.log(`   损失: ${loss.toFixed(4)} NEAR`);
        this.stats.totalProfit -= loss;
      } else {
        console.log(`❌ 风险管理失败: 紧急卖出失败 - ${emergencyResult.error}`);
      }

    } catch (error) {
      console.error(`❌ 风险管理错误:`, error);
    }
  }

  /**
   * 打印统计信息
   */
  private printStats(): void {
    const runTime = (Date.now() - this.stats.startTime) / 1000;

    console.log('\n📊 套利机器人统计:');
    console.log(`   运行时间: ${runTime.toFixed(1)}秒`);
    console.log(`   总检查次数: ${this.stats.totalChecks}`);
    console.log(`   发现机会: ${this.stats.opportunitiesFound}`);
    console.log(`   成功交易: ${this.stats.successfulTrades}`);
    console.log(`   失败交易: ${this.stats.failedTrades}`);
    console.log(`   总利润: ${this.stats.totalProfit.toFixed(4)} NEAR`);

    if (this.stats.successfulTrades > 0) {
      const avgProfit = this.stats.totalProfit / this.stats.successfulTrades;
      console.log(`   平均利润: ${avgProfit.toFixed(4)} NEAR`);
    }

    // 显示自动余额管理状态
    const balanceStatus = this.autoBalanceManager.getStatus();
    console.log('\n💰 自动余额管理状态:');
    console.log(`   启用状态: ${balanceStatus.enabled ? '✅ 已启用' : '❌ 已禁用'}`);
    if (balanceStatus.enabled) {
      console.log(`   正在检查: ${balanceStatus.isChecking ? '是' : '否'}`);
      if (balanceStatus.lastCheckTime > 0) {
        const lastCheck = new Date(balanceStatus.lastCheckTime).toLocaleTimeString();
        const nextCheck = new Date(balanceStatus.nextCheckTime).toLocaleTimeString();
        console.log(`   上次检查: ${lastCheck}`);
        console.log(`   下次检查: ${nextCheck}`);
      }
    }
  }

  /**
   * 手动触发余额检查
   */
  async manualBalanceCheck(): Promise<void> {
    console.log('🔍 手动触发余额检查...');
    await this.autoBalanceManager.manualCheck();
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      stats: this.stats,
      config: {
        tradingPairs: this.tradingPairs.length,
        arbitrageConfig: this.arbitrageConfig
      }
    };
  }
}

export default ArbitrageBot;
