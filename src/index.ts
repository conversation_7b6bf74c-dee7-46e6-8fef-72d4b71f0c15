/**
 * 套利机器人主程序入口
 *
 * 直接启动套利机器人，使用tradingPairs.ts中的配置
 */

import 'dotenv/config';
import ArbitrageBot from './arbitrageBot';
import { tradingPairManager } from './config/tradingPairs';

/**
 * 检查环境配置
 */
function checkEnvironment(): boolean {
  const required = [
    { key: 'NEAR_RPC_URL', alias: 'RPC_URL' },
    { key: 'NEAR_ACCOUNT_ID', alias: 'ACCOUNT_ID' },
    { key: 'NEAR_PRIVATE_KEY', alias: 'PRIVATE_KEY' }
  ];

  const missing = [];

  for (const { key, alias } of required) {
    if (!process.env[key]) {
      missing.push(key);
    } else {
      // 设置别名以便代码兼容
      process.env[alias] = process.env[key];
    }
  }

  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:');
    missing.forEach(key => console.error(`   ${key}`));
    console.error('\n请检查 .env 文件配置');
    return false;
  }

  return true;
}

/**
 * 显示配置信息
 */
function showConfig() {
  const enabledPairs = tradingPairManager.getEnabledPairs();
  const arbitrageConfig = tradingPairManager.getArbitrageConfig();

  console.log('⚙️ 当前配置:');
  console.log(`📊 利润阈值: ${arbitrageConfig.minProfitThreshold} NEAR`);
  console.log(`🔄 最大滑点: ${arbitrageConfig.maxSlippage * 100}%`);
  console.log(`⏱️ 监控间隔: ${arbitrageConfig.monitoringInterval}ms`);
  console.log(`📊 状态显示: 每${arbitrageConfig.statusDisplayInterval}次检查`);
  console.log(`🔧 调试模式: ${arbitrageConfig.debugMode ? '开启' : '关闭'}`);
  console.log(`📝 详细日志: ${arbitrageConfig.verboseLogging ? '开启' : '关闭'}`);
  console.log(`🎯 监控交易对: ${enabledPairs.length}个`);
  console.log('');

  enabledPairs.forEach(pair => {
    // 🔧 修复显示逻辑：正确显示动态金额和固定金额
    let amountDisplay: string;
    if (pair.dynamicAmount?.enabled) {
      amountDisplay = `动态(${pair.dynamicAmount.low}-${pair.dynamicAmount.high})`;
    } else if (pair.tradeAmount) {
      amountDisplay = pair.tradeAmount;
    } else {
      amountDisplay = '未配置';
    }
    console.log(`   ${pair.id}: ${amountDisplay} ${pair.tokenA.symbol} → ${pair.tokenB.symbol}`);
  });

  console.log('');
  console.log('📋 环境检查:');
  console.log(`  NEAR_RPC_URL: ${process.env.NEAR_RPC_URL ? '✅ 已配置' : '❌ 未配置'} (${process.env.NEAR_RPC_URL || 'https://rpc.mainnet.near.org'})`);
  console.log(`  NEAR_ACCOUNT_ID: ${process.env.NEAR_ACCOUNT_ID ? '✅ 已配置' : '❌ 未配置'} (${process.env.NEAR_ACCOUNT_ID || 'N/A'})`);
  console.log(`  NEAR_PRIVATE_KEY: ${process.env.NEAR_PRIVATE_KEY ? '✅ 已配置' : '❌ 未配置'}`);
  console.log(`  DCL v2 RPC: ${process.env.NEAR_RPC_URL || 'https://rpc.mainnet.near.org'}`);
  console.log('');
}

// 删除交互式查询功能，简化代码

/**
 * 主函数
 */
async function main() {
  console.log('🤖 REF-VEAX 套利机器人');
  console.log('='.repeat(50));

  // 检查环境配置
  if (!checkEnvironment()) {
    process.exit(1);
  }

  // 显示配置
  showConfig();

  // 创建并启动套利机器人
  const bot = new ArbitrageBot();

  // 监听退出信号
  process.on('SIGINT', () => {
    console.log('\n收到停止信号，正在安全停止...');
    bot.stop();
    process.exit(0);
  });

  // 监听未捕获的异常
  process.on('uncaughtException', (error) => {
    console.error('❌ 未捕获的异常:', error);
    bot.stop();
    process.exit(1);
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ 未处理的Promise拒绝:', reason);
    bot.stop();
    process.exit(1);
  });

  // 启动机器人
  try {
    await bot.start();
  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ 程序错误:', error);
    process.exit(1);
  });
}

export { main };
