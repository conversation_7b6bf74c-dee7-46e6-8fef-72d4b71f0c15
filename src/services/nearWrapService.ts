import { Account, connect, keyStores, Near } from 'near-api-js';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';

/**
 * NEAR包装服务
 * 处理NEAR与wNEAR之间的转换
 */
export class NearWrapService {
  private account!: Account;
  private near!: Near;
  private wrapContractId = 'wrap.near';
  private initialized = false;

  constructor(private accountId: string, private privateKey: string, private networkId: string = 'mainnet') {
    // 构造函数不执行异步操作，需要手动调用initialize
  }

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    await this.initializeNear(this.accountId, this.privateKey, this.networkId);
    this.initialized = true;
  }

  /**
   * 内部初始化NEAR连接
   */
  private async initializeNear(accountId: string, privateKey: string, networkId: string) {
    const keyStore = new keyStores.InMemoryKeyStore();
    const keyPair = require('near-api-js').utils.KeyPair.fromString(privateKey);
    await keyStore.setKey(networkId, accountId, keyPair);

    // 🔧 修复：使用环境变量配置的RPC URL，备用fastnear
    const rpcUrl = process.env.NEAR_RPC_URL || (networkId === 'mainnet'
      ? 'https://free.rpc.fastnear.com'
      : 'https://test.rpc.fastnear.com');

    const config = {
      networkId,
      keyStore,
      nodeUrl: rpcUrl,
      walletUrl: networkId === 'mainnet'
        ? 'https://wallet.mainnet.near.org'
        : 'https://wallet.testnet.near.org',
      helperUrl: networkId === 'mainnet'
        ? 'https://helper.mainnet.near.org'
        : 'https://helper.testnet.near.org',
    };

    this.near = await connect(config);
    this.account = await this.near.account(accountId);
  }

  /**
   * 获取账户的NEAR余额
   */
  async getNearBalance(): Promise<string> {
    await this.initialize();
    try {
      const balance = await this.account.getAccountBalance();
      return formatNearAmount(balance.available);
    } catch (error) {
      console.error('获取NEAR余额失败:', error);
      throw error;
    }
  }

  /**
   * 获取账户的wNEAR余额
   */
  async getWNearBalance(): Promise<string> {
    await this.initialize();
    try {
      const result = await this.account.viewFunction({
        contractId: this.wrapContractId,
        methodName: 'ft_balance_of',
        args: { account_id: this.account.accountId }
      });

      // wNEAR使用24位小数，转换为人类可读格式
      const balanceInYocto = result;
      return formatNearAmount(balanceInYocto);
    } catch (error) {
      console.error('获取wNEAR余额失败:', error);
      return '0';
    }
  }

  /**
   * 包装NEAR为wNEAR
   * @param amount 要包装的NEAR数量（人类可读格式，如 "1.5"）
   */
  async wrapNear(amount: string): Promise<{ success: boolean; txHash?: string; error?: string }> {
    await this.initialize();
    try {
      console.log(`🔄 开始包装 ${amount} NEAR → wNEAR...`);

      // 转换为yoctoNEAR
      const amountInYocto = parseNearAmount(amount);
      if (!amountInYocto) {
        throw new Error('无效的NEAR数量');
      }

      // 调用wrap.near合约的near_deposit方法
      const result = await this.account.functionCall({
        contractId: this.wrapContractId,
        methodName: 'near_deposit',
        args: {},
        attachedDeposit: BigInt(amountInYocto),
        gas: BigInt('**************') // 30 TGas
      });

      console.log(`✅ NEAR包装成功: ${result.transaction.hash}`);
      console.log(`📊 包装数量: ${amount} NEAR → ${amount} wNEAR`);

      return {
        success: true,
        txHash: result.transaction.hash
      };
    } catch (error: any) {
      console.error('NEAR包装失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 解包装wNEAR为NEAR
   * @param amount 要解包装的wNEAR数量（人类可读格式，如 "1.5"）
   */
  async unwrapNear(amount: string): Promise<{ success: boolean; txHash?: string; error?: string }> {
    await this.initialize();
    try {
      console.log(`🔄 开始解包装 ${amount} wNEAR → NEAR...`);

      // 转换为yoctoNEAR
      const amountInYocto = parseNearAmount(amount);
      if (!amountInYocto) {
        throw new Error('无效的wNEAR数量');
      }

      // 调用wrap.near合约的near_withdraw方法
      const result = await this.account.functionCall({
        contractId: this.wrapContractId,
        methodName: 'near_withdraw',
        args: { amount: amountInYocto },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('**************') // 30 TGas
      });

      console.log(`✅ wNEAR解包装成功: ${result.transaction.hash}`);
      console.log(`📊 解包装数量: ${amount} wNEAR → ${amount} NEAR`);

      return {
        success: true,
        txHash: result.transaction.hash
      };
    } catch (error: any) {
      console.error('wNEAR解包装失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 检查并自动包装NEAR
   * @param requiredAmount 需要的wNEAR数量（人类可读格式）
   * @param bufferPercent 缓冲百分比，默认10%
   */
  async checkAndWrapNear(
    requiredAmount: string, 
    bufferPercent: number = 10
  ): Promise<{ success: boolean; wrapped?: boolean; amount?: string; error?: string }> {
    try {
      console.log(`🔍 检查wNEAR余额，需要: ${requiredAmount} wNEAR`);
      
      // 获取当前wNEAR余额
      const currentWNearBalance = await this.getWNearBalance();
      const currentBalance = parseFloat(currentWNearBalance);
      const required = parseFloat(requiredAmount);
      
      console.log(`💰 当前wNEAR余额: ${currentWNearBalance}`);
      console.log(`📊 需要wNEAR数量: ${requiredAmount}`);
      
      // 如果余额足够，不需要包装
      if (currentBalance >= required) {
        console.log(`✅ wNEAR余额充足，无需包装`);
        return { success: true, wrapped: false };
      }
      
      // 计算需要包装的数量（包含缓冲）
      const deficit = required - currentBalance;
      const bufferAmount = deficit * (bufferPercent / 100);
      const wrapAmount = deficit + bufferAmount;
      
      console.log(`⚠️ wNEAR余额不足，缺少: ${deficit.toFixed(6)} wNEAR`);
      console.log(`🔄 将包装: ${wrapAmount.toFixed(6)} NEAR (包含${bufferPercent}%缓冲)`);
      
      // 检查NEAR余额是否足够
      const nearBalance = await this.getNearBalance();
      const availableNear = parseFloat(nearBalance);
      
      // 预留一些NEAR用于gas费用
      const gasReserve = 0.1; // 预留0.1 NEAR用于gas
      const availableForWrap = availableNear - gasReserve;
      
      if (availableForWrap < wrapAmount) {
        const error = `NEAR余额不足: 可用 ${availableForWrap.toFixed(6)} NEAR，需要 ${wrapAmount.toFixed(6)} NEAR`;
        console.error(`❌ ${error}`);
        return { success: false, error };
      }
      
      // 执行包装
      const wrapResult = await this.wrapNear(wrapAmount.toFixed(6));
      
      if (!wrapResult.success) {
        return { success: false, error: wrapResult.error };
      }
      
      // 等待一下确保包装完成
      console.log('⏳ 等待包装确认...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 验证包装后的余额
      const newWNearBalance = await this.getWNearBalance();
      console.log(`✅ 包装完成，新的wNEAR余额: ${newWNearBalance}`);
      
      return {
        success: true,
        wrapped: true,
        amount: wrapAmount.toFixed(6)
      };
      
    } catch (error: any) {
      console.error('自动包装检查失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 获取余额信息
   */
  async getBalanceInfo(): Promise<{
    nearBalance: string;
    wNearBalance: string;
    totalBalance: string;
  }> {
    const nearBalance = await this.getNearBalance();
    const wNearBalance = await this.getWNearBalance();
    const totalBalance = (parseFloat(nearBalance) + parseFloat(wNearBalance)).toFixed(6);
    
    return {
      nearBalance,
      wNearBalance,
      totalBalance
    };
  }
}

export default NearWrapService;
