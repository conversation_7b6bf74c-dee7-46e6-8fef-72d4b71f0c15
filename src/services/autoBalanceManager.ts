/**
 * 自动余额管理服务
 * 
 * 功能：
 * 1. 定期检查NEAR余额
 * 2. 当NEAR余额不足时自动解包wNEAR
 * 3. 避免在交易执行期间进行余额操作
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';
import { AutoBalanceConfig } from '../config/tradingPairs';

export class AutoBalanceManager {
  private near: Near | null = null;
  private account: Account | null = null;
  private checkTimer: NodeJS.Timeout | null = null;
  private isChecking = false;
  private lastCheckTime = 0;
  private readonly wrapContractId = 'wrap.near';

  constructor(
    private accountId: string,
    private privateKey: string,
    private config: AutoBalanceConfig,
    private isExecutingTrade: () => boolean, // 检查是否正在执行交易的回调
    private networkId: 'mainnet' | 'testnet' = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      // 🔧 修复：使用环境变量配置的RPC URL
      const rpcUrl = process.env.NEAR_RPC_URL || (this.networkId === 'mainnet'
        ? 'https://free.rpc.fastnear.com'
        : 'https://test.rpc.fastnear.com');

      const nearConfig = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: rpcUrl,
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(nearConfig);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ 自动余额管理服务初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ 自动余额管理服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 启动自动余额管理
   */
  start(): void {
    if (!this.config.enabled) {
      console.log('⚠️ 自动余额管理已禁用');
      return;
    }

    console.log(`🚀 启动自动余额管理服务`);
    console.log(`   检查间隔: ${this.config.checkInterval / 1000 / 60} 分钟`);
    console.log(`   最小NEAR余额: ${this.config.minNearBalance} NEAR`);
    console.log(`   自动解包数量: ${this.config.unwrapAmount} wNEAR`);
    console.log(`   预留wNEAR数量: ${this.config.reserveAmount} wNEAR`);

    // 立即执行一次检查
    this.performBalanceCheck();

    // 设置定时器
    this.checkTimer = setInterval(() => {
      this.performBalanceCheck();
    }, this.config.checkInterval);
  }

  /**
   * 停止自动余额管理
   */
  stop(): void {
    if (this.checkTimer) {
      clearInterval(this.checkTimer);
      this.checkTimer = null;
      console.log('🛑 自动余额管理服务已停止');
    }
  }

  /**
   * 执行余额检查
   */
  private async performBalanceCheck(): Promise<void> {
    // 避免重复检查
    if (this.isChecking) {
      return;
    }

    // 检查是否正在执行交易
    if (this.isExecutingTrade()) {
      console.log('🔒 正在执行交易，跳过余额检查');
      return;
    }

    this.isChecking = true;
    this.lastCheckTime = Date.now();

    try {
      await this.checkAndManageBalance();
    } catch (error) {
      console.error('❌ 余额检查失败:', error);
    } finally {
      this.isChecking = false;
    }
  }

  /**
   * 检查并管理余额
   */
  private async checkAndManageBalance(): Promise<void> {
    if (!this.account) {
      throw new Error('账户未初始化');
    }

    try {
      // 获取NEAR余额
      const nearBalance = await this.getNearBalance();
      const nearBalanceNum = parseFloat(nearBalance);

      console.log(`💰 当前NEAR余额: ${nearBalance} NEAR`);

      // 检查是否需要解包
      if (nearBalanceNum < this.config.minNearBalance) {
        console.log(`⚠️ NEAR余额不足 (${nearBalance} < ${this.config.minNearBalance})`);
        
        // 获取wNEAR余额
        const wNearBalance = await this.getWNearBalance();
        const wNearBalanceNum = parseFloat(wNearBalance);
        
        console.log(`💰 当前wNEAR余额: ${wNearBalance} wNEAR`);

        // 检查wNEAR余额是否足够（包含预留）
        const requiredWNear = this.config.unwrapAmount + this.config.reserveAmount;
        
        if (wNearBalanceNum >= requiredWNear) {
          console.log(`🔄 开始自动解包 ${this.config.unwrapAmount} wNEAR → NEAR`);
          await this.unwrapNear(this.config.unwrapAmount.toString());
        } else {
          console.log(`❌ wNEAR余额不足，无法解包`);
          console.log(`   需要: ${requiredWNear} wNEAR (${this.config.unwrapAmount} 解包 + ${this.config.reserveAmount} 预留用于交易)`);
          console.log(`   可用: ${wNearBalanceNum} wNEAR`);
        }
      } else {
        console.log(`✅ NEAR余额充足 (${nearBalance} ≥ ${this.config.minNearBalance})`);
      }

    } catch (error) {
      console.error('❌ 余额管理失败:', error);
    }
  }

  /**
   * 获取NEAR余额
   */
  private async getNearBalance(): Promise<string> {
    if (!this.account) {
      throw new Error('账户未初始化');
    }

    try {
      const balance = await this.account.getAccountBalance();
      return formatNearAmount(balance.available);
    } catch (error) {
      console.error('获取NEAR余额失败:', error);
      throw error;
    }
  }

  /**
   * 获取wNEAR余额
   */
  private async getWNearBalance(): Promise<string> {
    if (!this.account) {
      throw new Error('账户未初始化');
    }

    try {
      const result = await this.account.viewFunction({
        contractId: this.wrapContractId,
        methodName: 'ft_balance_of',
        args: { account_id: this.account.accountId }
      });

      return formatNearAmount(result);
    } catch (error) {
      console.error('获取wNEAR余额失败:', error);
      return '0';
    }
  }

  /**
   * 解包wNEAR为NEAR
   */
  private async unwrapNear(amount: string): Promise<void> {
    if (!this.account) {
      throw new Error('账户未初始化');
    }

    try {
      console.log(`🔄 解包 ${amount} wNEAR → NEAR...`);

      // 使用NEAR官方精确转换
      const amountInYocto = parseNearAmount(amount);
      if (!amountInYocto) {
        throw new Error('无效的wNEAR数量');
      }

      // 调用wrap.near合约的near_withdraw方法
      const result = await this.account.functionCall({
        contractId: this.wrapContractId,
        methodName: 'near_withdraw',
        args: { amount: amountInYocto },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('**************') // 50 TGas
      });

      console.log(`✅ wNEAR解包成功: ${result.transaction.hash}`);
      console.log(`📊 解包数量: ${amount} wNEAR → ${amount} NEAR`);

      // 等待一下确保解包完成
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 验证解包后的余额
      const newNearBalance = await this.getNearBalance();
      console.log(`💰 解包后NEAR余额: ${newNearBalance} NEAR`);

    } catch (error: any) {
      console.error('❌ wNEAR解包失败:', error);
      throw error;
    }
  }

  /**
   * 获取状态信息
   */
  getStatus(): {
    enabled: boolean;
    isChecking: boolean;
    lastCheckTime: number;
    nextCheckTime: number;
  } {
    return {
      enabled: this.config.enabled,
      isChecking: this.isChecking,
      lastCheckTime: this.lastCheckTime,
      nextCheckTime: this.lastCheckTime + this.config.checkInterval
    };
  }

  /**
   * 手动触发余额检查
   */
  async manualCheck(): Promise<void> {
    console.log('🔍 手动触发余额检查...');
    await this.performBalanceCheck();
  }
}

export default AutoBalanceManager;
