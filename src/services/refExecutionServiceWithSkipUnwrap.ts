/**
 * REF Finance 交易执行服务 - 保留skip_unwrap_near版本
 * 
 * 测试保留skip_unwrap_near参数是否仍然有效
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import Big from 'big.js';
import { QuoteResult } from '../types';

// 交易结果接口
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  outputAmount?: string;
  inputAmount?: string;
}

// V1交易动作接口
interface V1SwapAction {
  pool_id: number;
  token_in: string;
  token_out: string;
  amount_in?: string;
  min_amount_out: string;
}

// DCL v2交易参数接口
interface DCLv2SwapParams {
  pool_ids: string[];
  output_token: string;
  min_output_amount: string;
  skip_unwrap_near?: boolean;
}

/**
 * REF Finance 交易执行服务 - 保留skip_unwrap_near版本
 */
export class RefExecutionServiceWithSkipUnwrap {
  private near: Near | null = null;
  private account: Account | null = null;

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: string = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      const config = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: this.networkId === 'mainnet' 
          ? 'https://rpc.mainnet.near.org'
          : 'https://rpc.testnet.near.org',
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(config);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ REF执行服务(保留skip_unwrap_near)初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ REF执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行V1系统交易 - 保留skip_unwrap_near版本
   */
  async executeV1Swap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005,
    skipUnwrapNear: boolean = false
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'V1' || !quoteResult.rawResponse) {
      throw new Error('无效的V1报价结果');
    }

    try {
      console.log(`🚀 开始执行V1交易(保留skip_unwrap_near=${skipUnwrapNear}): ${inputAmount} → ${minOutputAmount}`);
      console.log(`📋 输入代币合约: ${inputTokenId}`);

      // 构建交易动作
      const swapActions = this.buildV1SwapActions(
        quoteResult.rawResponse.result_data,
        minOutputAmount
      );

      console.log(`🔧 构建了 ${swapActions.length} 个交易动作`);

      // 构建msg - 可选择是否包含skip_unwrap_near
      const msg: any = {
        force: 0,
        actions: swapActions
      };

      // 根据参数决定是否添加skip_unwrap_near
      if (skipUnwrapNear !== undefined) {
        msg.skip_unwrap_near = skipUnwrapNear;
        console.log(`📋 包含skip_unwrap_near: ${skipUnwrapNear}`);
      } else {
        console.log(`📋 不包含skip_unwrap_near参数`);
      }

      console.log('📋 交易消息:', JSON.stringify(msg, null, 2));

      // 验证交易动作格式
      swapActions.forEach((action, index) => {
        console.log(`🔍 动作${index + 1}:`);
        console.log(`   pool_id: ${action.pool_id} (${typeof action.pool_id})`);
        console.log(`   token_in: ${action.token_in}`);
        console.log(`   token_out: ${action.token_out}`);
        console.log(`   min_amount_out: ${action.min_amount_out}`);
        if (action.amount_in) {
          console.log(`   amount_in: ${action.amount_in}`);
        }
      });

      // 调用输入代币合约的ft_transfer_call
      const result = await this.account.functionCall({
        contractId: inputTokenId,
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'v2.ref-finance.near',
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`✅ V1交易成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: quoteResult.outputAmount,
        inputAmount: inputAmount
      };

    } catch (error: any) {
      console.error('❌ V1交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 执行DCL v2系统交易 - 保留skip_unwrap_near版本
   */
  async executeDCLv2Swap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    poolId: string,
    outputToken: string,
    skipUnwrapNear: boolean = true
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'DCL_V2') {
      throw new Error('无效的DCL v2报价结果');
    }

    try {
      console.log(`🚀 开始执行DCL v2交易(skip_unwrap_near=${skipUnwrapNear}): ${inputAmount} → ${minOutputAmount}`);
      console.log(`📋 输入代币合约: ${inputTokenId}`);
      console.log(`📋 池子ID: ${poolId}`);

      // 构建DCL v2参数
      const swapParams: DCLv2SwapParams = {
        pool_ids: [poolId],
        output_token: outputToken,
        min_output_amount: minOutputAmount,
        skip_unwrap_near: skipUnwrapNear
      };

      const msg = {
        Swap: swapParams
      };

      console.log('📋 交易消息:', JSON.stringify(msg, null, 2));

      // 调用输入代币合约的ft_transfer_call
      const result = await this.account.functionCall({
        contractId: inputTokenId,
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'dclv2.ref-labs.near',
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`✅ DCL v2交易成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: quoteResult.outputAmount,
        inputAmount: inputAmount
      };

    } catch (error: any) {
      console.error('❌ DCL v2交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 构建V1交易动作
   */
  private buildV1SwapActions(
    routeData: any,
    minOutputAmount: string
  ): V1SwapAction[] {
    const routes = routeData.routes || [];
    if (routes.length === 0) {
      throw new Error('没有可用的交易路径');
    }

    console.log(`📊 处理 ${routes.length} 条路径`);

    const actions: V1SwapAction[] = [];

    routes.forEach((route: any, routeIndex: number) => {
      console.log(`📋 处理路径 ${routeIndex + 1}: ${route.pools.length} 个池子`);
      
      route.pools.forEach((pool: any, poolIndex: number) => {
        const action: V1SwapAction = {
          pool_id: parseInt(pool.pool_id.toString()),
          token_in: pool.token_in,
          token_out: pool.token_out,
          min_amount_out: "0"
        };

        // 只有第一个池子设置amount_in
        if (routeIndex === 0 && poolIndex === 0 && pool.amount_in) {
          action.amount_in = pool.amount_in;
          console.log(`   第一个池子设置 amount_in = ${pool.amount_in}`);
        }

        // 只有最后一个池子设置min_amount_out
        if (routeIndex === routes.length - 1 && poolIndex === route.pools.length - 1) {
          action.min_amount_out = minOutputAmount;
          console.log(`   最后池子设置 min_amount_out = ${minOutputAmount}`);
        }

        actions.push(action);
        console.log(`   ✅ 动作${actions.length}: 池子${action.pool_id}`);
      });
    });

    console.log(`✅ 构建了 ${actions.length} 个交易动作`);
    return actions;
  }

  /**
   * 测试不同的skip_unwrap_near设置
   */
  async testSkipUnwrapNearOptions(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string
  ): Promise<void> {
    console.log('\n🧪 测试不同的skip_unwrap_near设置...');

    const testCases = [
      { skipUnwrapNear: false, description: 'skip_unwrap_near: false' },
      { skipUnwrapNear: true, description: 'skip_unwrap_near: true' },
      { skipUnwrapNear: undefined, description: '不包含skip_unwrap_near' }
    ];

    for (const testCase of testCases) {
      console.log(`\n📋 测试: ${testCase.description}`);
      
      try {
        if (quoteResult.system === 'V1') {
          const result = await this.executeV1Swap(
            quoteResult,
            inputTokenId,
            inputAmount,
            minOutputAmount,
            0.01,
            testCase.skipUnwrapNear as any
          );
          
          if (result.success) {
            console.log(`✅ ${testCase.description} - 成功`);
          } else {
            console.log(`❌ ${testCase.description} - 失败: ${result.error}`);
          }
        }
      } catch (error: any) {
        console.log(`❌ ${testCase.description} - 异常: ${error.message}`);
      }
      
      // 等待一下避免过快的请求
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
}

export default RefExecutionServiceWithSkipUnwrap;
