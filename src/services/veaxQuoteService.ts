/**
 * VEAX DEX 报价服务
 * 
 * VEAX使用HTTP API获取报价，比REF Finance的智能合约调用更简单
 * API端点: https://veax-estimation-service.veax.com/v1/rpc
 * 方法: estimate_swap_exact_in
 */

import axios from 'axios';

// VEAX API配置
const VEAX_API_URL = 'https://veax-estimation-service.veax.com/v1/rpc';

// VEAX API请求接口
interface VeaxSwapRequest {
  jsonrpc: string;
  method: string;
  params: {
    token_a: string;
    token_b: string;
    amount_a: string;
    slippage_tolerance: number;
  };
  id: number;
}

// VEAX API响应接口
interface VeaxSwapResponse {
  jsonrpc: string;
  id: number;
  result?: {
    amount_b_expected: string;
    amount_b_bound: string;
    price_impact: string;
    swap_price: string;
    fee: string;
    fee_amount: string;
    gas_fee: string;
    pool_exists: boolean;
    storage_cost: {
      init_account: string;
      register_token: string;
      create_pool: string;
      open_position: string;
    };
  };
  error?: {
    code: number;
    message: string;
    data?: {
      details: string;
      request_id: string;
    };
  };
}

// 报价结果接口
export interface VeaxQuoteResult {
  outputAmount: string;
  priceImpact: string;
  fee: string;
  poolExists: boolean;
  success: boolean;
  error?: string;
}

/**
 * VEAX报价服务类
 */
export class VeaxQuoteService {
  private static readonly DEFAULT_SLIPPAGE = 0.005; // 0.5%
  private static readonly REQUEST_TIMEOUT = 10000; // 10秒超时

  /**
   * 获取VEAX交换报价
   * @param tokenA 输入代币地址
   * @param tokenB 输出代币地址  
   * @param amountA 输入代币数量（字符串格式）
   * @param slippageTolerance 滑点容忍度，默认0.5%
   * @returns 报价结果
   */
  static async getQuote(
    tokenA: string,
    tokenB: string,
    amountA: string,
    slippageTolerance: number = VeaxQuoteService.DEFAULT_SLIPPAGE
  ): Promise<VeaxQuoteResult> {
    try {
      // 构建请求数据
      const requestData: VeaxSwapRequest = {
        jsonrpc: '2.0',
        method: 'estimate_swap_exact_in',
        params: {
          token_a: tokenA,
          token_b: tokenB,
          amount_a: amountA,
          slippage_tolerance: slippageTolerance
        },
        id: 0
      };

      // 静默获取VEAX报价

      // 发送HTTP请求
      const response = await axios.post<VeaxSwapResponse>(
        VEAX_API_URL,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          timeout: VeaxQuoteService.REQUEST_TIMEOUT
        }
      );

      // 静默处理API响应

      // 检查API错误
      if (response.data.error) {
        const errorMsg = `VEAX API错误: ${response.data.error.message}`;
        // 只对流动性不足以外的错误显示日志
        if (!response.data.error.data?.details?.includes('insufficient_liquidity')) {
          console.error(`[VEAX] ${errorMsg}`, response.data.error);
        }
        
        return {
          outputAmount: '0',
          priceImpact: '0',
          fee: '0',
          poolExists: false,
          success: false,
          error: errorMsg
        };
      }

      // 检查结果
      if (!response.data.result) {
        const errorMsg = 'VEAX API返回空结果';
        console.error(`[VEAX] ${errorMsg}`);
        
        return {
          outputAmount: '0',
          priceImpact: '0',
          fee: '0',
          poolExists: false,
          success: false,
          error: errorMsg
        };
      }

      const result = response.data.result;
      
      // 静默处理成功结果

      return {
        outputAmount: result.amount_b_expected,
        priceImpact: result.price_impact,
        fee: result.fee,
        poolExists: result.pool_exists,
        success: true
      };

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '未知错误';
      console.error(`[VEAX] 请求失败:`, errorMsg);
      
      return {
        outputAmount: '0',
        priceImpact: '0',
        fee: '0',
        poolExists: false,
        success: false,
        error: `网络请求失败: ${errorMsg}`
      };
    }
  }

  /**
   * 批量获取多个交易对的报价
   * @param quotes 报价请求数组
   * @returns 报价结果数组
   */
  static async getBatchQuotes(
    quotes: Array<{
      tokenA: string;
      tokenB: string;
      amountA: string;
      slippageTolerance?: number;
    }>
  ): Promise<VeaxQuoteResult[]> {
    console.log(`[VEAX] 批量获取 ${quotes.length} 个报价`);
    
    // 并发请求所有报价
    const promises = quotes.map(quote => 
      VeaxQuoteService.getQuote(
        quote.tokenA,
        quote.tokenB,
        quote.amountA,
        quote.slippageTolerance
      )
    );

    const results = await Promise.all(promises);
    
    const successCount = results.filter(r => r.success).length;
    console.log(`[VEAX] 批量报价完成: ${successCount}/${quotes.length} 成功`);
    
    return results;
  }

  /**
   * 检查交易对是否存在流动性池
   * @param tokenA 代币A地址
   * @param tokenB 代币B地址
   * @returns 是否存在池子
   */
  static async checkPoolExists(tokenA: string, tokenB: string): Promise<boolean> {
    try {
      // 用最小数量测试是否存在池子
      const result = await VeaxQuoteService.getQuote(tokenA, tokenB, '1');
      return result.poolExists;
    } catch (error) {
      console.error(`[VEAX] 检查池子存在性失败:`, error);
      return false;
    }
  }
}

export default VeaxQuoteService;
