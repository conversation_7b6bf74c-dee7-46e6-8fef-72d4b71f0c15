/**
 * 三档位动态金额管理器
 * 
 * 功能：根据实际交易利润动态调整下次查询的交易金额
 * 核心逻辑：一次查询，一次执行，基于实际利润调整下次金额
 */

import { TradingPairConfig, DynamicAmountConfig } from '../config/tradingPairs';

// 利润阈值常量
export const PROFIT_THRESHOLDS = {
  MEDIUM: 0.052,  // 中档阈值：>= 0.052 NEAR
  HIGH: 0.1       // 高档阈值：>= 0.1 NEAR
} as const;

// 档位类型
export type AmountLevel = 'low' | 'medium' | 'high';

// 动态金额统计接口
export interface DynamicAmountStats {
  pairId: string;
  currentLevel: AmountLevel;
  currentAmount: string;
  totalTrades: number;
  levelDistribution: {
    low: number;
    medium: number;
    high: number;
  };
  averageProfit: number;
  lastAdjustment: number;
}

/**
 * 动态金额管理器类
 */
export class DynamicAmountManager {
  private currentAmounts: Map<string, string> = new Map();
  private currentLevels: Map<string, AmountLevel> = new Map();
  private stats: Map<string, DynamicAmountStats> = new Map();
  private consecutiveNoOpportunity: Map<string, number> = new Map();

  constructor() {
    console.log('🎯 三档位动态金额管理器已初始化');
  }

  /**
   * 获取当前查询金额
   */
  getCurrentAmount(pairId: string, pair: TradingPairConfig): string {
    // 检查是否启用动态金额
    if (!pair.dynamicAmount?.enabled) {
      // 回退到固定金额模式
      return pair.tradeAmount || '1';
    }

    const current = this.currentAmounts.get(pairId);
    if (!current) {
      // 首次查询，使用低档金额
      const defaultAmount = pair.dynamicAmount.low;
      this.currentAmounts.set(pairId, defaultAmount);
      this.currentLevels.set(pairId, 'low');
      this.initializeStats(pairId, defaultAmount);
      
      console.log(`🎯 ${pairId}: 初始化为低档 (${defaultAmount} NEAR)`);
      return defaultAmount;
    }
    
    return current;
  }

  /**
   * 根据实际利润调整下次查询金额（渐进式调整）
   *
   * 核心理念：基于当前档位的利润表现来渐进式调整下次档位
   * - 避免激进的档位跳跃
   * - 逐步试探市场，控制风险
   * - 稳定的档位切换策略
   */
  adjustNextAmount(pairId: string, actualProfit: number, pair: TradingPairConfig): void {
    if (!pair.dynamicAmount?.enabled) {
      return; // 未启用动态金额，跳过调整
    }

    const { dynamicAmount } = pair;
    const currentLevel = this.currentLevels.get(pairId) || 'low';
    const currentAmount = this.currentAmounts.get(pairId) || dynamicAmount.low;

    let nextLevel: AmountLevel;
    let nextAmount: string;

    // 🎯 渐进式调整逻辑：基于当前档位和利润表现
    if (actualProfit >= PROFIT_THRESHOLDS.HIGH) {
      // 高利润 (>= 0.1 NEAR)：保持当前档位或渐进升档
      if (currentLevel === 'low') {
        nextLevel = 'medium';  // 低档 → 中档（渐进升档，不直接跳高档）
      } else if (currentLevel === 'medium') {
        nextLevel = 'high';    // 中档 → 高档
      } else {
        nextLevel = 'high';    // 高档 → 保持高档
      }
    } else if (actualProfit >= PROFIT_THRESHOLDS.MEDIUM) {
      // 中等利润 (>= 0.052 NEAR)：目标是中档
      if (currentLevel === 'low') {
        nextLevel = 'medium';  // 低档 → 中档
      } else if (currentLevel === 'medium') {
        nextLevel = 'medium';  // 中档 → 保持中档
      } else {
        nextLevel = 'medium';  // 高档 → 中档（渐进降档）
      }
    } else {
      // 低利润 (< 0.052 NEAR)：逐步降档到低档
      if (currentLevel === 'low') {
        nextLevel = 'low';     // 低档 → 保持低档
      } else if (currentLevel === 'medium') {
        nextLevel = 'low';     // 中档 → 低档
      } else {
        nextLevel = 'medium';  // 高档 → 中档（渐进降档，不直接跳低档）
      }
    }

    // 根据档位设置金额
    switch (nextLevel) {
      case 'high':
        nextAmount = dynamicAmount.high;
        break;
      case 'medium':
        nextAmount = dynamicAmount.medium;
        break;
      case 'low':
      default:
        nextAmount = dynamicAmount.low;
        break;
    }

    // 更新当前金额和档位
    this.currentAmounts.set(pairId, nextAmount);
    this.currentLevels.set(pairId, nextLevel);

    // 重置无机会计数器
    this.consecutiveNoOpportunity.set(pairId, 0);

    // 更新统计
    this.updateStats(pairId, currentLevel, nextLevel, actualProfit);

    // 记录档位变化
    const levelNames = { low: '低档', medium: '中档', high: '高档' };
    if (currentAmount !== nextAmount) {
      console.log(`📊 ${pairId}: 利润${actualProfit.toFixed(4)} NEAR → ${levelNames[currentLevel]}→${levelNames[nextLevel]} (${currentAmount}→${nextAmount} NEAR)`);
    } else {
      console.log(`📊 ${pairId}: 利润${actualProfit.toFixed(4)} NEAR → 保持${levelNames[nextLevel]} (${nextAmount} NEAR)`);
    }
  }

  /**
   * 处理无套利机会的情况（渐进式调整下已不需要强制重置）
   *
   * 注意：在渐进式调整逻辑下，档位会在下次有利润的交易中自然调整，
   * 不需要强制重置机制。低利润会自然导致档位逐步降低。
   */
  handleNoOpportunity(pairId: string, pair: TradingPairConfig): void {
    if (!pair.dynamicAmount?.enabled) {
      return;
    }

    // 仅用于统计，不再执行强制重置
    const count = (this.consecutiveNoOpportunity.get(pairId) || 0) + 1;
    this.consecutiveNoOpportunity.set(pairId, count);

    // 可选：记录连续无机会的次数（用于调试）
    if (count % 20 === 0) {
      console.log(`📊 ${pairId}: 连续${count}次无套利机会（渐进式调整下无需重置档位）`);
    }
  }

  /**
   * 重置到低档（无套利机会时使用）
   *
   * 核心逻辑：无套利机会时直接重置到低档，避免档位卡死
   * 这样确保下次查询使用保守的低档金额
   */
  resetToLowLevel(pairId: string, pair: TradingPairConfig): void {
    if (!pair.dynamicAmount?.enabled) {
      return;
    }

    const lowAmount = pair.dynamicAmount.low;
    const currentAmount = this.currentAmounts.get(pairId);
    const currentLevel = this.currentLevels.get(pairId) || 'low';

    // 只有当前不是低档时才需要重置
    if (currentLevel !== 'low') {
      this.currentAmounts.set(pairId, lowAmount);
      this.currentLevels.set(pairId, 'low');
      this.consecutiveNoOpportunity.set(pairId, 0);

      const levelNames = { low: '低档', medium: '中档', high: '高档' };
      console.log(`🔄 ${pairId}: 无套利机会，${levelNames[currentLevel]}→低档 (${currentAmount}→${lowAmount} NEAR)`);
    }
  }

  /**
   * 重置到默认档位（保留方法以备特殊情况使用）
   */
  resetToDefault(pairId: string, pair: TradingPairConfig): void {
    if (!pair.dynamicAmount?.enabled) {
      return;
    }

    const defaultAmount = pair.dynamicAmount.low;
    const currentAmount = this.currentAmounts.get(pairId);

    if (currentAmount !== defaultAmount) {
      this.currentAmounts.set(pairId, defaultAmount);
      this.currentLevels.set(pairId, 'low');
      this.consecutiveNoOpportunity.set(pairId, 0);

      console.log(`🔄 ${pairId}: 手动重置到低档 (${defaultAmount} NEAR)`);
    }
  }

  /**
   * 获取当前档位
   */
  getCurrentLevel(pairId: string): AmountLevel {
    return this.currentLevels.get(pairId) || 'low';
  }

  /**
   * 获取档位统计
   */
  getStats(pairId: string): DynamicAmountStats | undefined {
    return this.stats.get(pairId);
  }

  /**
   * 获取所有统计信息
   */
  getAllStats(): Map<string, DynamicAmountStats> {
    return new Map(this.stats);
  }

  /**
   * 显示档位统计摘要
   */
  displayStatsSummary(): void {
    console.log('\n📈 动态金额档位统计:');
    
    for (const [pairId, stats] of this.stats) {
      const { levelDistribution, totalTrades, averageProfit, currentLevel } = stats;
      
      if (totalTrades > 0) {
        const lowPct = ((levelDistribution.low / totalTrades) * 100).toFixed(1);
        const mediumPct = ((levelDistribution.medium / totalTrades) * 100).toFixed(1);
        const highPct = ((levelDistribution.high / totalTrades) * 100).toFixed(1);
        
        const levelNames = { low: '低档', medium: '中档', high: '高档' };
        
        console.log(`   ${pairId}: 当前${levelNames[currentLevel]} | 低档${lowPct}% | 中档${mediumPct}% | 高档${highPct}% | 平均利润${averageProfit.toFixed(4)} NEAR`);
      }
    }
  }

  /**
   * 初始化统计信息
   */
  private initializeStats(pairId: string, initialAmount: string): void {
    this.stats.set(pairId, {
      pairId,
      currentLevel: 'low',
      currentAmount: initialAmount,
      totalTrades: 0,
      levelDistribution: {
        low: 0,
        medium: 0,
        high: 0
      },
      averageProfit: 0,
      lastAdjustment: Date.now()
    });
  }

  /**
   * 更新统计信息
   */
  private updateStats(pairId: string, currentLevel: AmountLevel, nextLevel: AmountLevel, profit: number): void {
    const stats = this.stats.get(pairId);
    if (!stats) return;

    stats.totalTrades++;
    stats.levelDistribution[currentLevel]++;
    stats.currentLevel = nextLevel;
    stats.currentAmount = this.currentAmounts.get(pairId) || '0';
    
    // 更新平均利润（简单移动平均）
    stats.averageProfit = (stats.averageProfit * (stats.totalTrades - 1) + profit) / stats.totalTrades;
    stats.lastAdjustment = Date.now();
  }
}

// 全局动态金额管理器实例
export const dynamicAmountManager = new DynamicAmountManager();

export default dynamicAmountManager;
