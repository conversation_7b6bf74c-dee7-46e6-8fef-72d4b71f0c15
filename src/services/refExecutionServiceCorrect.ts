/**
 * REF Finance 交易执行服务 - 正确版本
 * 
 * 基于REF Finance官方SDK文档的正确实现
 * 参考：https://github.com/ref-finance/ref-sdk
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import Big from 'big.js';
import { QuoteResult } from '../types';

// 交易结果接口（修复精度问题）
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  outputAmount?: string;      // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmount?: string;       // 人类可读格式
  inputAmountWei?: string;    // wei格式（精确，新增）
}

// V1交易动作接口（按照官方SDK格式）
interface V1SwapAction {
  pool_id: number;
  token_in: string;
  token_out: string;
  amount_in?: string;
  min_amount_out: string;
}

// DCL v2交易参数接口
interface DCLv2SwapParams {
  pool_ids: string[];
  output_token: string;
  min_output_amount: string;
  skip_unwrap_near?: boolean;
}

/**
 * REF Finance 交易执行服务 - 正确版本
 * 基于官方SDK文档实现
 */
export class RefExecutionServiceCorrect {
  private near: Near | null = null;
  private account: Account | null = null;

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: string = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      // 🔧 修复：使用环境变量配置的RPC URL，备用fastnear
      const rpcUrl = process.env.NEAR_RPC_URL || (this.networkId === 'mainnet'
        ? 'https://free.rpc.fastnear.com'
        : 'https://test.rpc.fastnear.com');

      const config = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: rpcUrl,
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(config);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ REF执行服务(正确版)初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ REF执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行V1系统交易 - 正确版本
   * 按照官方SDK格式构建交易
   */
  async executeV1Swap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'V1' || !quoteResult.rawResponse) {
      throw new Error('无效的V1报价结果');
    }

    try {
      console.log(`🚀 开始执行V1交易(正确版): ${inputAmount} → ${minOutputAmount}`);
      console.log(`📋 输入代币合约: ${inputTokenId}`);

      // 🔧 按照官方SDK格式构建交易动作，传递实际输入金额
      const swapActions = this.buildCorrectV1SwapActions(
        quoteResult.rawResponse.result_data,
        minOutputAmount,
        inputAmount  // 🔧 传递实际的输入金额
      );

      console.log(`🔧 构建了 ${swapActions.length} 个交易动作`);

      // 🔧 关键修复：按照官方SDK格式构建msg
      const msg = {
        force: 0,
        actions: swapActions
        // 注意：不包含skip_unwrap_near，这可能是导致E76错误的原因
      };

      console.log('📋 交易消息(官方格式):', JSON.stringify(msg, null, 2));

      // 🔧 验证交易动作格式
      swapActions.forEach((action, index) => {
        console.log(`🔍 动作${index + 1}:`);
        console.log(`   pool_id: ${action.pool_id} (${typeof action.pool_id})`);
        console.log(`   token_in: ${action.token_in}`);
        console.log(`   token_out: ${action.token_out}`);
        console.log(`   min_amount_out: ${action.min_amount_out}`);
        if (action.amount_in) {
          console.log(`   amount_in: ${action.amount_in}`);
        }
      });

      // 🔧 关键修复：调用输入代币合约的ft_transfer_call
      const result = await this.account.functionCall({
        contractId: inputTokenId, // 正确：调用输入代币合约
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'v2.ref-finance.near', // REF合约作为接收者
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`📋 V1交易已提交: ${result.transaction.hash}`);

      // 🔧 关键修复：检查交易是否真正成功
      const successCheck = this.checkTransactionSuccess(result);
      if (!successCheck.success) {
        console.error(`❌ V1交易执行失败: ${successCheck.error}`);
        return {
          success: false,
          error: successCheck.error
        };
      }

      console.log(`✅ V1交易真正成功: ${result.transaction.hash}`);

      // 从交易结果中提取实际输出金额
      const actualOutputAmount = this.extractOutputAmountFromResult(result);

      // 🔧 修复：如果提取到实际金额，使用实际值；否则使用预估值
      let finalOutputAmount: string;
      let finalOutputAmountWei: string | undefined;

      if (actualOutputAmount.wei) {
        // 提取成功，使用实际值
        finalOutputAmountWei = actualOutputAmount.wei;
        // 这里需要知道输出代币的精度来正确转换，暂时使用wei格式
        finalOutputAmount = actualOutputAmount.wei;
        console.log(`📊 使用实际输出金额: ${finalOutputAmountWei} wei`);
      } else {
        // 提取失败，使用预估值
        finalOutputAmount = quoteResult.outputAmount;
        finalOutputAmountWei = undefined;
        console.log(`⚠️ 未能提取实际金额，使用预估值: ${finalOutputAmount}`);
      }

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: finalOutputAmount,
        outputAmountWei: finalOutputAmountWei,
        inputAmount: inputAmount,
        inputAmountWei: inputAmount // 🔧 关键修复：输入也是wei格式
      };

    } catch (error: any) {
      console.error('❌ V1交易失败:', error);

      // 🔧 关键修复：只对网络错误进行交易状态检测
      if (error.context?.transactionHash && this.isNetworkError(error)) {
        const txHash = error.context.transactionHash;
        console.log(`🔍 检测到网络错误且有交易哈希: ${txHash}，查询交易状态...`);

        try {
          const txResult = await this.checkTransactionStatusWithHash(txHash, inputAmount);
          if (txResult.success) {
            console.log(`✅ V1交易实际成功: ${txHash}`);
            return txResult;
          } else {
            console.log(`❌ V1交易确认失败: ${txHash}`);
          }
        } catch (checkError) {
          console.error('❌ 查询V1交易状态失败:', checkError);
        }
      }

      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 执行DCL v2系统交易 - 正确版本
   */
  async executeDCLv2Swap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    poolId: string,
    outputToken: string
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'DCL_V2') {
      throw new Error('无效的DCL v2报价结果');
    }

    try {
      console.log(`🚀 开始执行DCL v2交易(正确版): ${inputAmount} → ${minOutputAmount}`);
      console.log(`📋 输入代币合约: ${inputTokenId}`);
      console.log(`📋 池子ID: ${poolId}`);

      // 🔧 智能设置skip_unwrap_near参数
      // 当输出代币是wrap.near时，设置skip_unwrap_near: true确保返回wNEAR
      const shouldSkipUnwrap = outputToken === 'wrap.near';

      console.log(`🔧 输出代币: ${outputToken}, skip_unwrap_near: ${shouldSkipUnwrap}`);

      // 🔧 按照官方SDK格式构建DCL v2参数
      const swapParams: DCLv2SwapParams = {
        pool_ids: [poolId],
        output_token: outputToken,
        min_output_amount: minOutputAmount,
        skip_unwrap_near: shouldSkipUnwrap  // 🔧 关键修复：根据输出代币智能设置
      };

      const msg = {
        Swap: swapParams
      };

      console.log('📋 交易消息(官方格式):', JSON.stringify(msg, null, 2));

      // 🔧 关键修复：调用输入代币合约的ft_transfer_call
      const result = await this.account.functionCall({
        contractId: inputTokenId, // 正确：调用输入代币合约
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'dclv2.ref-labs.near', // DCL v2合约作为接收者
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR（按照官方SDK）
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`📋 DCL v2交易已提交: ${result.transaction.hash}`);

      // 🔧 关键修复：检查交易是否真正成功
      const successCheck = this.checkTransactionSuccess(result);
      if (!successCheck.success) {
        console.error(`❌ DCL v2交易执行失败: ${successCheck.error}`);
        return {
          success: false,
          error: successCheck.error
        };
      }

      console.log(`✅ DCL v2交易真正成功: ${result.transaction.hash}`);

      // 从交易结果中提取实际输出金额
      const actualOutputAmount = this.extractOutputAmountFromResult(result);

      // 🔧 修复：如果提取到实际金额，使用实际值；否则使用预估值
      let finalOutputAmount: string;
      let finalOutputAmountWei: string | undefined;

      if (actualOutputAmount.wei) {
        // 提取成功，使用实际值
        finalOutputAmountWei = actualOutputAmount.wei;
        // 这里需要知道输出代币的精度来正确转换，暂时使用wei格式
        finalOutputAmount = actualOutputAmount.wei;
        console.log(`📊 使用实际输出金额: ${finalOutputAmountWei} wei`);
      } else {
        // 提取失败，使用预估值
        finalOutputAmount = quoteResult.outputAmount;
        finalOutputAmountWei = undefined;
        console.log(`⚠️ 未能提取实际金额，使用预估值: ${finalOutputAmount}`);
      }

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: finalOutputAmount,
        outputAmountWei: finalOutputAmountWei,
        inputAmount: inputAmount,
        inputAmountWei: inputAmount // 🔧 关键修复：输入也是wei格式
      };

    } catch (error: any) {
      console.error('❌ DCL v2交易失败:', error);

      // 🔧 关键修复：只对网络错误进行交易状态检测
      if (error.context?.transactionHash && this.isNetworkError(error)) {
        const txHash = error.context.transactionHash;
        console.log(`🔍 检测到网络错误且有交易哈希: ${txHash}，查询交易状态...`);

        try {
          const txResult = await this.checkTransactionStatusWithHash(txHash, inputAmount);
          if (txResult.success) {
            console.log(`✅ DCL v2交易实际成功: ${txHash}`);
            return txResult;
          } else {
            console.log(`❌ DCL v2交易确认失败: ${txHash}`);
          }
        } catch (checkError) {
          console.error('❌ 查询DCL v2交易状态失败:', checkError);
        }
      }

      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 构建V1交易动作 - 直接使用Smart Router返回的pools
   * 完全基于Smart Router的精确计算，无需自定义逻辑
   */
  private buildCorrectV1SwapActions(
    routeData: any,
    minOutputAmount: string,
    actualInputAmount: string
  ): V1SwapAction[] {
    const routes = routeData.routes || [];
    if (routes.length === 0) {
      throw new Error('没有可用的交易路径');
    }

    console.log(`📊 Smart Router返回 ${routes.length} 条路径`);

    // 🎯 关键修复：直接使用Smart Router返回的所有pools作为actions
    const actions: V1SwapAction[] = routes.flatMap((route: any, routeIndex: number) => {
      const pools = route.pools || [];

      console.log(`📋 路径 ${routeIndex + 1}: ${pools.length} 个池子，分配金额: ${route.amount_in}`);

      return pools.map((pool: any, poolIndex: number) => {
        const action: V1SwapAction = {
          pool_id: parseInt(pool.pool_id.toString()),
          token_in: pool.token_in,
          token_out: pool.token_out,
          min_amount_out: pool.min_amount_out || "0"  // 直接使用Smart Router计算的值
        };

        // 🎯 直接使用Smart Router分配的amount_in（如果不为0）
        if (pool.amount_in && pool.amount_in !== "0") {
          action.amount_in = pool.amount_in;
          console.log(`   池子${pool.pool_id}: amount_in=${pool.amount_in} (Smart Router分配)`);
        } else {
          console.log(`   池子${pool.pool_id}: 链式交易中间步骤，无需amount_in`);
        }

        console.log(`   池子${pool.pool_id}: min_amount_out=${action.min_amount_out}`);

        return action;
      });
    });

    console.log(`✅ 直接使用Smart Router数据，构建了 ${actions.length} 个交易动作`);

    // 验证Smart Router的计算
    this.validateSmartRouterCalculation(routes, actualInputAmount);

    return actions;
  }

  /**
   * 验证Smart Router的计算是否正确
   */
  private validateSmartRouterCalculation(routes: any[], actualInputAmount: string): void {
    // 验证路径级别的金额分配
    const totalRouteAllocated = routes.reduce((sum, route) => {
      const routeAmount = route.amount_in ? parseFloat(route.amount_in) : 0;
      return sum + routeAmount;
    }, 0);

    // 验证池子级别的金额分配
    const totalPoolAllocated = routes.reduce((sum, route) => {
      return sum + route.pools.reduce((poolSum: number, pool: any) => {
        const poolAmount = pool.amount_in ? parseFloat(pool.amount_in) : 0;
        return poolSum + poolAmount;
      }, 0);
    }, 0);

    const actualAmount = parseFloat(actualInputAmount);
    const routeDifference = Math.abs(totalRouteAllocated - actualAmount);
    const tolerance = actualAmount * 0.001; // 0.1%容差

    console.log(`🔍 Smart Router计算验证:`);
    console.log(`   实际输入: ${actualInputAmount}`);
    console.log(`   路径分配总计: ${totalRouteAllocated}`);
    console.log(`   池子分配总计: ${totalPoolAllocated}`);
    console.log(`   路径差异: ${routeDifference}`);

    if (routeDifference > tolerance) {
      console.warn(`⚠️ Smart Router路径分配不匹配，差异: ${routeDifference}`);
    } else {
      console.log(`✅ Smart Router计算验证通过`);
    }

    // 验证每个路径的池子分配
    routes.forEach((route: any, index: number) => {
      const routeAmount = parseFloat(route.amount_in || '0');
      const firstPoolAmount = parseFloat(route.pools[0]?.amount_in || '0');

      if (routeAmount > 0 && firstPoolAmount > 0) {
        const poolDifference = Math.abs(routeAmount - firstPoolAmount);
        if (poolDifference > routeAmount * 0.001) {
          console.warn(`⚠️ 路径${index + 1}的池子分配不匹配: 路径=${routeAmount}, 池子=${firstPoolAmount}`);
        }
      }
    });
  }

  /**
   * 统一执行接口 - 正确版本
   */
  async executeSwap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005,
    additionalParams?: any
  ): Promise<TransactionResult> {
    if (quoteResult.system === 'V1') {
      return this.executeV1Swap(quoteResult, inputTokenId, inputAmount, minOutputAmount, slippage);
    } else if (quoteResult.system === 'DCL_V2') {
      if (!additionalParams?.poolId || !additionalParams?.outputToken) {
        throw new Error('DCL v2交易需要poolId和outputToken参数');
      }
      return this.executeDCLv2Swap(
        quoteResult,
        inputTokenId,
        inputAmount,
        minOutputAmount,
        additionalParams.poolId,
        additionalParams.outputToken
      );
    } else {
      throw new Error(`不支持的交易系统: ${quoteResult.system}`);
    }
  }

  /**
   * 判断是否为网络错误
   */
  private isNetworkError(error: any): boolean {
    const errorMessage = error.message || '';
    const errorString = errorMessage.toLowerCase();

    // 网络相关的错误关键词
    const networkErrorKeywords = [
      '502 bad gateway',
      '503 service unavailable',
      '504 gateway timeout',
      'network timeout',
      'connection reset',
      'connection refused',
      'timeout',
      'network error',
      'fetch failed',
      'cloudflare',
      'bad gateway'
    ];

    // 检查是否包含网络错误关键词
    const isNetworkError = networkErrorKeywords.some(keyword =>
      errorString.includes(keyword)
    );

    if (isNetworkError) {
      console.log(`🌐 识别为网络错误: ${errorMessage}`);
      return true;
    }

    // 合约执行错误不应该触发检测
    const contractErrorKeywords = [
      'smart contract panicked',
      'execution error',
      'insufficient balance',
      'slippage',
      'invalid token',
      'function call error'
    ];

    const isContractError = contractErrorKeywords.some(keyword =>
      errorString.includes(keyword)
    );

    if (isContractError) {
      console.log(`⚙️ 识别为合约执行错误，不进行状态检测: ${errorMessage}`);
      return false;
    }

    // 对于未知错误，保守处理：如果有HTML标签，可能是网络错误
    if (errorMessage.includes('<html>') || errorMessage.includes('<body>')) {
      console.log(`🌐 识别为HTML格式的网络错误: ${errorMessage.substring(0, 100)}...`);
      return true;
    }

    console.log(`❓ 未知错误类型，不进行状态检测: ${errorMessage}`);
    return false;
  }

  /**
   * 查询交易状态（通过交易哈希）
   */
  async checkTransactionStatusWithHash(txHash: string, inputAmount: string): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔍 查询REF交易状态: ${txHash}`);

      // 查询交易状态
      const txResult = await this.account.connection.provider.txStatus(txHash, this.accountId, 'FINAL');

      // 检查交易是否成功
      const successCheck = this.checkTransactionSuccess(txResult);
      if (!successCheck.success) {
        console.log(`❌ REF交易确认失败: ${txHash} - ${successCheck.error}`);
        return {
          success: false,
          error: successCheck.error
        };
      }

      console.log(`✅ REF交易确认成功: ${txHash}`);

      // 从交易结果中提取实际输出金额
      const actualOutputAmount = this.extractOutputAmountFromResult(txResult);

      // 🔧 修复：如果提取到实际金额，使用实际值；否则使用默认值
      let finalOutputAmount: string;
      let finalOutputAmountWei: string | undefined;

      if (actualOutputAmount.wei) {
        // 提取成功，使用实际值
        finalOutputAmountWei = actualOutputAmount.wei;
        finalOutputAmount = actualOutputAmount.wei;
        console.log(`📊 使用实际输出金额: ${finalOutputAmountWei} wei`);
      } else {
        // 提取失败，使用默认值
        finalOutputAmount = '0';
        finalOutputAmountWei = undefined;
        console.log(`⚠️ 未能提取实际金额，使用默认值: ${finalOutputAmount}`);
      }

      return {
        success: true,
        transactionHash: txHash,
        outputAmount: finalOutputAmount,
        outputAmountWei: finalOutputAmountWei,
        inputAmount: inputAmount,
        inputAmountWei: inputAmount
      };
    } catch (error: any) {
      console.error(`❌ 查询REF交易状态失败: ${txHash}`, error);
      return {
        success: false,
        error: error.message || '查询交易状态失败'
      };
    }
  }

  /**
   * 检查交易是否真正成功（检测FunctionCallError）
   */
  private checkTransactionSuccess(result: any): { success: boolean; error?: string } {
    try {
      // 检查所有receipts中是否有执行错误
      const allReceipts = result.receipts_outcome || [];

      for (const receipt of allReceipts) {
        const status = receipt.outcome?.status;

        // 检查是否有Failure状态
        if (status && status.Failure) {
          const failure = status.Failure;

          // 检查是否是ActionError
          if (failure.ActionError) {
            const actionError = failure.ActionError;

            // 检查是否是FunctionCallError
            if (actionError.kind && actionError.kind.FunctionCallError) {
              const functionCallError = actionError.kind.FunctionCallError;

              // 检查是否是ExecutionError
              if (functionCallError.ExecutionError) {
                const errorMessage = functionCallError.ExecutionError;
                console.error(`🚨 合约执行失败: ${errorMessage}`);

                // 解析具体错误类型
                if (errorMessage.includes('E22: not enough tokens in deposit')) {
                  return { success: false, error: 'REF合约存款不足 (E22)' };
                } else if (errorMessage.includes('E76')) {
                  return { success: false, error: 'REF交易滑点过大 (E76)' };
                } else {
                  return { success: false, error: `REF合约错误: ${errorMessage}` };
                }
              }
            }
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error('检查交易状态失败:', error);
      return { success: false, error: '无法检查交易状态' };
    }
  }

  /**
   * 从交易结果中提取实际输出金额（wei格式）
   */
  private extractOutputAmountFromResult(result: any): { humanReadable: string | null; wei: string | null } {
    try {
      // 查找所有receipts中的ft_transfer事件
      const allReceipts = result.receipts_outcome || [];

      for (const receipt of allReceipts) {
        const logs = receipt.outcome?.logs || [];

        for (const log of logs) {
          try {
            // 🔧 关键修复：首先查找EVENT_JSON日志
            if (log.includes('EVENT_JSON:')) {
              const eventStr = log.split('EVENT_JSON:')[1];
              const event = JSON.parse(eventStr);

              // 查找ft_transfer事件
              if (event.standard === 'nep141' && event.event === 'ft_transfer') {
                const transferData = event.data?.[0];

                // 检查是否是转给我们账户的输出代币
                if (transferData &&
                    transferData.new_owner_id === this.account?.accountId &&
                    transferData.amount) {

                  const weiAmount = transferData.amount;
                  console.log(`📊 从EVENT_JSON提取实际输出: ${weiAmount} wei`);

                  // 🔧 修复：正确转换为人类可读格式，避免使用预估值
                  // 需要从合约地址推断代币精度，这里先返回wei格式，在上层处理转换
                  return {
                    wei: weiAmount,
                    humanReadable: weiAmount // 🔧 临时方案：返回wei格式，让上层根据代币信息转换
                  };
                }
              }
            } else {
              // 🔧 关键修复：查找普通Transfer日志格式
              // 格式: "Transfer AMOUNT from FROM_ACCOUNT to TO_ACCOUNT"
              const transferMatch = log.match(/Transfer (\d+) from ([\w\.-]+) to ([\w\.-]+)/);
              if (transferMatch) {
                const [, amount, from, to] = transferMatch;

                // 检查是否是转给我们账户的
                if (to === this.account?.accountId) {
                  console.log(`📊 从普通日志提取实际输出: ${amount} wei (from ${from} to ${to})`);

                  return {
                    wei: amount,
                    humanReadable: amount // 🔧 临时方案：返回wei格式，让上层根据代币信息转换
                  };
                }
              }
            }
          } catch (parseError) {
            // 忽略解析错误，继续查找
            continue;
          }
        }
      }

      console.log('❌ 未找到匹配的transfer事件');
      return { humanReadable: null, wei: null };
    } catch (error) {
      console.error('❌ 提取输出金额失败:', error);
      return { humanReadable: null, wei: null };
    }
  }
}

export default RefExecutionServiceCorrect;
