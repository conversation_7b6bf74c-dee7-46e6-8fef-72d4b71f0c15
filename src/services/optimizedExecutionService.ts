/**
 * 优化的交易执行服务
 * 
 * 使用不同的执行级别来优化套利交易速度
 */

import { Account } from 'near-api-js';
import { functionCall } from 'near-api-js/lib/transaction';
import { TxExecutionStatus } from '@near-js/types';

export interface OptimizedExecutionResult {
  success: boolean;
  transactionHash: string;
  outputAmount?: string;
  executionTime: number;
  executionLevel: TxExecutionStatus;
  error?: string;
}

export class OptimizedExecutionService {
  constructor(private account: Account) {}

  /**
   * 执行第一步交易（需要完整receipts）
   * 使用 EXECUTED_OPTIMISTIC 确保获得完整的交易结果
   */
  async executeFirstStep(
    contractId: string,
    methodName: string,
    args: any,
    attachedDeposit: bigint,
    gas: bigint
  ): Promise<OptimizedExecutionResult> {
    const startTime = Date.now();
    
    try {
      console.log(`🚀 执行第一步交易 (EXECUTED_OPTIMISTIC): ${contractId}.${methodName}`);
      
      // 使用默认的 EXECUTED_OPTIMISTIC 级别
      const result = await this.account.functionCall({
        contractId,
        methodName,
        args,
        attachedDeposit,
        gas
      });

      const executionTime = Date.now() - startTime;
      
      // 从receipts中提取输出金额
      const outputAmount = this.extractOutputAmount(result);
      
      console.log(`✅ 第一步完成: ${executionTime}ms, 输出: ${outputAmount}`);
      
      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount,
        executionTime,
        executionLevel: 'EXECUTED_OPTIMISTIC',
      };
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ 第一步失败: ${executionTime}ms`, error);
      
      return {
        success: false,
        transactionHash: '',
        executionTime,
        executionLevel: 'EXECUTED_OPTIMISTIC',
        error: error.message
      };
    }
  }

  /**
   * 执行第二步交易（可以使用更快的级别）
   * 使用 INCLUDED 级别来加速执行，因为我们主要关心交易是否成功
   */
  async executeSecondStep(
    contractId: string,
    methodName: string,
    args: any,
    attachedDeposit: bigint,
    gas: bigint,
    useFastMode: boolean = true
  ): Promise<OptimizedExecutionResult> {
    const startTime = Date.now();
    
    try {
      if (useFastMode) {
        console.log(`⚡ 执行第二步交易 (INCLUDED - 快速模式): ${contractId}.${methodName}`);
        
        // 构建交易
        const actions = [functionCall(methodName, args, gas, attachedDeposit)];
        
        // 签名交易
        const signedTx = await this.account.signTransaction({
          receiverId: contractId,
          actions
        });

        // 使用 INCLUDED 级别快速执行
        const result = await this.account.connection.provider.sendTransactionUntil(
          signedTx,
          'INCLUDED' as TxExecutionStatus
        );

        const executionTime = Date.now() - startTime;
        
        console.log(`✅ 第二步完成 (快速): ${executionTime}ms`);
        
        return {
          success: true,
          transactionHash: result.transaction.hash,
          executionTime,
          executionLevel: 'INCLUDED',
        };
        
      } else {
        console.log(`🚀 执行第二步交易 (EXECUTED_OPTIMISTIC - 标准模式): ${contractId}.${methodName}`);
        
        // 使用标准的 EXECUTED_OPTIMISTIC 级别
        const result = await this.account.functionCall({
          contractId,
          methodName,
          args,
          attachedDeposit,
          gas
        });

        const executionTime = Date.now() - startTime;
        
        // 从receipts中提取输出金额
        const outputAmount = this.extractOutputAmount(result);
        
        console.log(`✅ 第二步完成 (标准): ${executionTime}ms, 输出: ${outputAmount}`);
        
        return {
          success: true,
          transactionHash: result.transaction.hash,
          outputAmount,
          executionTime,
          executionLevel: 'EXECUTED_OPTIMISTIC',
        };
      }
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ 第二步失败: ${executionTime}ms`, error);
      
      return {
        success: false,
        transactionHash: '',
        executionTime,
        executionLevel: useFastMode ? 'INCLUDED' : 'EXECUTED_OPTIMISTIC',
        error: error.message
      };
    }
  }

  /**
   * 监控模式：使用最快的执行级别来获取交易状态
   * 用于快速检测套利机会或监控交易状态
   */
  async executeMonitoringTransaction(
    contractId: string,
    methodName: string,
    args: any,
    attachedDeposit: bigint,
    gas: bigint
  ): Promise<OptimizedExecutionResult> {
    const startTime = Date.now();
    
    try {
      console.log(`👁️ 执行监控交易 (NONE - 最快模式): ${contractId}.${methodName}`);
      
      // 构建交易
      const actions = [functionCall(methodName, args, gas, attachedDeposit)];
      
      // 签名交易
      const signedTx = await this.account.signTransaction({
        receiverId: contractId,
        actions
      });

      // 使用 NONE 级别最快执行
      const result = await this.account.connection.provider.sendTransactionUntil(
        signedTx,
        'NONE' as TxExecutionStatus
      );

      const executionTime = Date.now() - startTime;
      
      console.log(`✅ 监控交易提交: ${executionTime}ms`);
      console.log(`⚠️ 注意：NONE级别只确认提交，不保证执行完成`);
      
      return {
        success: true,
        transactionHash: result.transaction.hash,
        executionTime,
        executionLevel: 'NONE',
      };
      
    } catch (error: any) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ 监控交易失败: ${executionTime}ms`, error);
      
      return {
        success: false,
        transactionHash: '',
        executionTime,
        executionLevel: 'NONE',
        error: error.message
      };
    }
  }

  /**
   * 从交易结果中提取输出金额
   */
  private extractOutputAmount(result: any): string | undefined {
    try {
      const allReceipts = result.receipts_outcome || [];
      
      for (const receipt of allReceipts) {
        const logs = receipt.outcome?.logs || [];
        
        for (const log of logs) {
          try {
            if (log.includes('EVENT_JSON:')) {
              const eventStr = log.split('EVENT_JSON:')[1];
              const event = JSON.parse(eventStr);
              
              // 查找ft_transfer事件
              if (event.standard === 'nep141' && event.event === 'ft_transfer') {
                const transferData = event.data?.[0];
                if (transferData?.new_owner_id === this.account.accountId && transferData.amount) {
                  return transferData.amount;
                }
              }
              
              // 查找VEAX swap事件
              if (event.standard === 'veax' && event.event === 'swap') {
                if (event.data?.amounts?.[1]) {
                  return event.data.amounts[1];
                }
              }
            }
          } catch (parseError) {
            continue;
          }
        }
      }
      
      return undefined;
    } catch (error) {
      console.error('提取输出金额失败:', error);
      return undefined;
    }
  }

  /**
   * 获取执行级别的性能特征
   */
  static getExecutionLevelInfo(level: TxExecutionStatus) {
    const info = {
      'NONE': { speed: '最快', safety: '最低', receipts: '无' },
      'INCLUDED': { speed: '快', safety: '低', receipts: '部分' },
      'EXECUTED_OPTIMISTIC': { speed: '中等', safety: '中等', receipts: '完整' },
      'INCLUDED_FINAL': { speed: '慢', safety: '高', receipts: '完整' },
      'EXECUTED': { speed: '很慢', safety: '很高', receipts: '完整+最终化' },
      'FINAL': { speed: '最慢', safety: '最高', receipts: '完整+完全最终化' }
    };
    
    return info[level] || info['EXECUTED_OPTIMISTIC'];
  }
}

export default OptimizedExecutionService;
