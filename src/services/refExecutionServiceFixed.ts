/**
 * REF Finance 交易执行服务 - 修复版本
 * 
 * 修复了原版本的关键问题：
 * 1. 正确调用输入代币合约的ft_transfer_call
 * 2. 完整实现Smart Router的actions结构
 * 3. 正确处理amount_in的分配逻辑
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import Big from 'big.js';
import { QuoteResult } from '../types';

// 交易结果接口
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  outputAmount?: string;
  inputAmount?: string;
}

// V1交易参数接口
interface V1SwapAction {
  pool_id: number;
  token_in: string;
  token_out: string;
  amount_in?: string;
  amount_out: string;
  min_amount_out: string;
}

// DCL v2交易参数接口
interface DCLv2SwapParams {
  pool_ids: string[];
  output_token: string;
  min_output_amount: string;
  skip_unwrap_near: boolean;
}

/**
 * REF Finance 交易执行服务 - 修复版本
 */
export class RefExecutionServiceFixed {
  private near: Near | null = null;
  private account: Account | null = null;

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: string = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      // 🔧 修复：使用环境变量配置的RPC URL，备用fastnear
      const rpcUrl = process.env.NEAR_RPC_URL || (this.networkId === 'mainnet'
        ? 'https://free.rpc.fastnear.com'
        : 'https://test.rpc.fastnear.com');

      const config = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: rpcUrl,
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(config);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ REF执行服务(修复版)初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ REF执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行V1系统交易 - 修复版本
   */
  async executeV1Swap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'V1' || !quoteResult.rawResponse) {
      throw new Error('无效的V1报价结果');
    }

    try {
      console.log(`🚀 开始执行V1交易(修复版): ${inputAmount} → ${minOutputAmount}`);
      console.log(`📋 输入代币合约: ${inputTokenId}`);

      // 从Smart Router响应构建交易参数
      const swapActions = this.buildV1SwapActionsFixed(
        quoteResult.rawResponse.result_data,
        minOutputAmount
      );

      console.log(`🔧 构建了 ${swapActions.length} 个交易动作`);

      const msg = {
        force: 0,
        actions: swapActions,
        skip_unwrap_near: false
      };

      console.log('📋 交易消息:', JSON.stringify(msg, null, 2));

      // 🔧 验证 pool_id 类型
      swapActions.forEach((action, index) => {
        console.log(`🔍 动作${index + 1}: pool_id = ${action.pool_id} (类型: ${typeof action.pool_id})`);
        if (typeof action.pool_id !== 'number') {
          console.error(`❌ 动作${index + 1}的pool_id不是数字类型!`);
        }
      });

      // 🔧 关键修复：调用输入代币合约的ft_transfer_call
      const result = await this.account.functionCall({
        contractId: inputTokenId, // 修复：调用输入代币合约
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'v2.ref-finance.near', // REF合约作为接收者
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`✅ V1交易成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: quoteResult.outputAmount,
        inputAmount: inputAmount
      };

    } catch (error: any) {
      console.error('❌ V1交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 执行DCL v2系统交易 - 修复版本
   */
  async executeDCLv2Swap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    poolId: string,
    outputToken: string
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'DCL_V2') {
      throw new Error('无效的DCL v2报价结果');
    }

    try {
      console.log(`🚀 开始执行DCL v2交易(修复版): ${inputAmount} → ${minOutputAmount}`);
      console.log(`📋 输入代币合约: ${inputTokenId}`);
      console.log(`📋 池子ID: ${poolId}`);

      const swapParams: DCLv2SwapParams = {
        pool_ids: [poolId],
        output_token: outputToken,
        min_output_amount: minOutputAmount,
        skip_unwrap_near: true
      };

      const msg = {
        Swap: swapParams
      };

      console.log('📋 交易消息:', JSON.stringify(msg, null, 2));

      // 🔧 关键修复：调用输入代币合约的ft_transfer_call
      const result = await this.account.functionCall({
        contractId: inputTokenId, // 修复：调用输入代币合约
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'dclv2.ref-labs.near', // DCL v2合约作为接收者
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`✅ DCL v2交易成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: quoteResult.outputAmount,
        inputAmount: inputAmount
      };

    } catch (error: any) {
      console.error('❌ DCL v2交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 构建V1交易动作 - 修复版本
   * 完全按照Smart Router的返回结果构建
   */
  private buildV1SwapActionsFixed(
    routeData: any,
    minOutputAmount: string
  ): V1SwapAction[] {
    const routes = routeData.routes || [];
    if (routes.length === 0) {
      throw new Error('没有可用的交易路径');
    }

    console.log(`📊 处理 ${routes.length} 条路径`);

    const actions: V1SwapAction[] = [];

    // 🔧 修复：处理所有路径的所有池子
    routes.forEach((route: any, routeIndex: number) => {
      console.log(`📋 处理路径 ${routeIndex + 1}: ${route.pools.length} 个池子`);
      
      route.pools.forEach((pool: any, poolIndex: number) => {
        const action: V1SwapAction = {
          pool_id: parseInt(pool.pool_id.toString()), // 🔧 关键修复：确保pool_id是数字类型
          token_in: pool.token_in,
          token_out: pool.token_out,
          amount_out: "0",
          min_amount_out: "0" // 默认为0，最后一个池子会设置实际值
        };

        // 🔧 修复：只有当pool中有amount_in时才设置
        if (pool.amount_in) {
          action.amount_in = pool.amount_in;
          console.log(`   池子${poolIndex + 1}: amount_in = ${pool.amount_in}`);
        }

        // 🔧 修复：为最后一个动作设置最小输出金额
        if (routeIndex === routes.length - 1 && poolIndex === route.pools.length - 1) {
          action.min_amount_out = minOutputAmount;
          console.log(`   最后池子设置 min_amount_out = ${minOutputAmount}`);
        }

        actions.push(action);
        console.log(`   ✅ 动作${actions.length}: 池子${action.pool_id} (数字类型)`);
      });
    });

    console.log(`✅ 构建了 ${actions.length} 个交易动作`);
    return actions;
  }

  /**
   * 统一执行接口 - 修复版本
   */
  async executeSwap(
    quoteResult: QuoteResult,
    inputTokenId: string,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005,
    additionalParams?: any
  ): Promise<TransactionResult> {
    if (quoteResult.system === 'V1') {
      return this.executeV1Swap(quoteResult, inputTokenId, inputAmount, minOutputAmount, slippage);
    } else if (quoteResult.system === 'DCL_V2') {
      if (!additionalParams?.poolId || !additionalParams?.outputToken) {
        throw new Error('DCL v2交易需要poolId和outputToken参数');
      }
      return this.executeDCLv2Swap(
        quoteResult,
        inputTokenId,
        inputAmount,
        minOutputAmount,
        additionalParams.poolId,
        additionalParams.outputToken
      );
    } else {
      throw new Error(`不支持的交易系统: ${quoteResult.system}`);
    }
  }
}

export default RefExecutionServiceFixed;
