import Big from 'big.js';
import { QuoteParams, QuoteResult, QuoteError } from '../types';
import { v1SmartRouter } from './v1SmartRouter';
import { dclv2Contract } from './dclv2Contract';

/**
 * REF Finance 统一报价服务
 * 整合 V1 Smart Router 和 DCL v2 系统
 */
export class RefQuoteService {
  
  /**
   * 获取最佳报价（并行调用两套系统）
   */
  async getBestQuote(params: QuoteParams): Promise<QuoteResult> {
    // 静默获取报价
    
    const errors: QuoteError[] = [];
    
    try {
      // 并行调用两套系统（DCL v2只查询主要池子）
      const [v1Result, dclv2Result] = await Promise.allSettled([
        v1SmartRouter.getV1Quote(params),
        dclv2Contract.getDCLv2Quote(params) // 优化版本，只查询USDC-NEAR和USDT-NEAR池子
      ]);

      // 处理 V1 结果
      let v1Quote: QuoteResult | null = null;
      if (v1Result.status === 'fulfilled') {
        v1Quote = v1Result.value;
      } else {
        errors.push({
          system: 'V1',
          message: v1Result.reason?.message || 'V1 系统调用失败',
          originalError: v1Result.reason
        });
        console.error('❌ V1 系统错误:', v1Result.reason?.message);
      }

      // 处理 DCL v2 结果
      let dclv2Quote: QuoteResult | null = null;
      if (dclv2Result.status === 'fulfilled') {
        dclv2Quote = dclv2Result.value;
      } else {
        errors.push({
          system: 'DCL_V2',
          message: dclv2Result.reason?.message || 'DCL v2 系统调用失败',
          originalError: dclv2Result.reason
        });
        console.error('❌ DCL v2 系统错误:', dclv2Result.reason?.message);
      }

      // 选择最佳报价
      const bestQuote = this.selectBestQuote(v1Quote, dclv2Quote);
      
      if (!bestQuote) {
        const errorMessage = errors.length > 0 
          ? `所有系统都失败了: ${errors.map(e => e.message).join(', ')}`
          : '未找到可用的报价路径';
        throw new Error(errorMessage);
      }

      // 静默处理结果
      
      return bestQuote;

    } catch (error: any) {
      console.error('❌ 报价查询失败:', error.message);
      throw error;
    }
  }

  /**
   * 选择最佳报价
   */
  private selectBestQuote(v1Quote: QuoteResult | null, dclv2Quote: QuoteResult | null): QuoteResult | null {
    // 如果只有一个系统有结果，直接返回
    if (v1Quote && !dclv2Quote) return v1Quote;
    if (!v1Quote && dclv2Quote) return dclv2Quote;
    if (!v1Quote && !dclv2Quote) return null;

    // 两个系统都有结果，比较输出金额
    const v1Output = new Big(v1Quote!.outputAmount);
    const dclv2Output = new Big(dclv2Quote!.outputAmount);

    // 返回输出金额更大的报价
    return v1Output.gte(dclv2Output) ? v1Quote! : dclv2Quote!;
  }

  /**
   * 输出报价对比日志
   */
  private logQuoteComparison(
    v1Quote: QuoteResult | null, 
    dclv2Quote: QuoteResult | null, 
    bestQuote: QuoteResult
  ): void {
    console.log('\n📊 报价对比结果:');
    console.log('┌─────────────────────────────────────────────────────────┐');
    
    if (v1Quote) {
      const isWinner = bestQuote.system === 'V1';
      console.log(`│ V1 Smart Router: ${v1Quote.outputAmount.padEnd(15)} ${isWinner ? '🏆' : '  '} │`);
      console.log(`│ 路径: ${v1SmartRouter.getRouteDetails(v1Quote).padEnd(25)} │`);
    } else {
      console.log('│ V1 Smart Router: 无可用报价                              │');
    }
    
    if (dclv2Quote) {
      const isWinner = bestQuote.system === 'DCL_V2';
      console.log(`│ DCL v2 Contract: ${dclv2Quote.outputAmount.padEnd(15)} ${isWinner ? '🏆' : '  '} │`);
      console.log(`│ 池子: ${dclv2Contract.getBestPoolInfo(dclv2Quote).padEnd(25)} │`);
    } else {
      console.log('│ DCL v2 Contract: 无可用报价                              │');
    }
    
    console.log('└─────────────────────────────────────────────────────────┘');
    console.log(`🏆 最佳报价: ${bestQuote.system} 系统 - ${bestQuote.outputAmount}`);
    
    // 计算价格差异
    if (v1Quote && dclv2Quote) {
      const priceDiff = this.calculatePriceDifference(v1Quote, dclv2Quote);
      console.log(`📈 价格差异: ${priceDiff}%`);
    }
  }

  /**
   * 计算两个报价之间的价格差异
   */
  private calculatePriceDifference(quote1: QuoteResult, quote2: QuoteResult): string {
    try {
      const output1 = new Big(quote1.outputAmount);
      const output2 = new Big(quote2.outputAmount);
      
      const diff = output1.minus(output2).abs();
      const avg = output1.plus(output2).div(2);
      const percentage = diff.div(avg).times(100);
      
      return percentage.toFixed(4);
    } catch {
      return 'N/A';
    }
  }

  /**
   * 获取报价详情
   */
  getQuoteDetails(quote: QuoteResult): any {
    return {
      system: quote.system,
      contractId: quote.contractId,
      inputAmount: quote.inputAmount,
      outputAmount: quote.outputAmount,
      priceImpact: quote.priceImpact || 'N/A',
      route: quote.system === 'V1' 
        ? v1SmartRouter.getRouteDetails(quote)
        : dclv2Contract.getBestPoolInfo(quote),
      rawResponse: quote.rawResponse
    };
  }

  /**
   * 验证报价参数
   */
  private validateQuoteParams(params: QuoteParams): void {
    if (!params.tokenIn?.id || !params.tokenOut?.id) {
      throw new Error('代币信息不完整');
    }
    
    if (!params.amountIn || parseFloat(params.amountIn) <= 0) {
      throw new Error('输入金额必须大于 0');
    }
    
    if (params.tokenIn.id === params.tokenOut.id) {
      throw new Error('输入和输出代币不能相同');
    }
    
    if (params.slippage && (params.slippage < 0 || params.slippage > 1)) {
      throw new Error('滑点容忍度必须在 0-1 之间');
    }
  }

  /**
   * 获取报价（带参数验证）
   */
  async getQuote(params: QuoteParams): Promise<QuoteResult> {
    this.validateQuoteParams(params);
    return this.getBestQuote(params);
  }
}

/**
 * 导出单例实例
 */
export const refQuoteService = new RefQuoteService();
