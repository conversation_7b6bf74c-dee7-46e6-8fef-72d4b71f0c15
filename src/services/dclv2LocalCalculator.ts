/**
 * DCL v2 本地计算服务
 * 
 * 基于池子状态信息本地计算报价，大幅减少RPC调用
 */

import { TokenMetadata, QuoteParams, QuoteResult } from '../types';
import { getConfig } from '../config/index';
import axios from 'axios';

/**
 * DCL池子信息接口
 */
interface DCLPoolInfo {
  pool_id: string;
  token_x: string;
  token_y: string;
  fee: number;
  point_delta: number;
  current_point: number;
  liquidity: string;
  liquidity_x: string;
  max_liquidity_per_point: string;
  total_fee_x_charged: string;
  total_fee_y_charged: string;
  volume_x_in: string;
  volume_y_in: string;
  volume_x_out: string;
  volume_y_out: string;
  total_liquidity: string;
  total_order_x: string;
  total_order_y: string;
  total_x: string;
  total_y: string;
  state: string;
}

/**
 * DCL v2 本地计算服务
 */
class DCLv2LocalCalculatorService {
  private config = getConfig();
  private poolsCache: DCLPoolInfo[] = [];
  private poolsCacheTime: number = 0;
  private readonly CACHE_DURATION = 30 * 1000; // 30秒缓存
  
  /**
   * 代币对到池子的映射
   */
  private tokenPairToPoolsMap: Map<string, DCLPoolInfo[]> = new Map();

  /**
   * 获取所有池子信息（带缓存）
   */
  async getAllPoolsInfo(): Promise<DCLPoolInfo[]> {
    const now = Date.now();
    
    // 检查缓存
    if (this.poolsCache.length > 0 && (now - this.poolsCacheTime) < this.CACHE_DURATION) {
      return this.poolsCache;
    }

    try {
      console.log('🔄 获取DCL v2池子状态信息...');
      
      const rpcPayload = {
        jsonrpc: '2.0',
        id: Date.now(),
        method: 'query',
        params: {
          request_type: 'call_function',
          finality: 'optimistic',
          account_id: this.config.contracts.dclv2,
          method_name: 'list_pools',
          args_base64: Buffer.from(JSON.stringify({})).toString('base64')
        }
      };

      const response = await axios.post(this.config.rpcUrl, rpcPayload, {
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      });

      const data = response.data;
      if (data.error) {
        throw new Error(`RPC错误: ${data.error.message}`);
      }

      const resultBytes = data.result?.result;
      if (!resultBytes || resultBytes.length === 0) {
        throw new Error('无法获取池子信息');
      }

      const resultString = String.fromCharCode(...resultBytes);
      const pools: DCLPoolInfo[] = JSON.parse(resultString);

      // 更新缓存
      this.poolsCache = pools;
      this.poolsCacheTime = now;
      
      // 构建索引
      this.buildTokenPairIndex(pools);

      console.log(`✅ 成功获取 ${pools.length} 个DCL v2池子状态信息`);
      return pools;

    } catch (error: any) {
      console.error('❌ 获取池子信息失败:', error.message);
      throw error;
    }
  }

  /**
   * 构建代币对索引
   */
  private buildTokenPairIndex(pools: DCLPoolInfo[]): void {
    this.tokenPairToPoolsMap.clear();
    
    for (const pool of pools) {
      if (pool.state !== 'Running') continue; // 只使用运行中的池子
      
      const { token_x, token_y } = pool;
      
      // 两种顺序都要索引
      const pair1 = `${token_x}:${token_y}`;
      const pair2 = `${token_y}:${token_x}`;
      
      if (!this.tokenPairToPoolsMap.has(pair1)) {
        this.tokenPairToPoolsMap.set(pair1, []);
      }
      if (!this.tokenPairToPoolsMap.has(pair2)) {
        this.tokenPairToPoolsMap.set(pair2, []);
      }
      
      this.tokenPairToPoolsMap.get(pair1)!.push(pool);
      this.tokenPairToPoolsMap.get(pair2)!.push(pool);
    }
    
    console.log(`📋 构建代币对索引: ${this.tokenPairToPoolsMap.size} 个代币对`);
  }

  /**
   * 查找代币对的所有池子
   */
  private findPoolsForPair(tokenIn: TokenMetadata, tokenOut: TokenMetadata): DCLPoolInfo[] {
    const pairKey = `${tokenIn.id}:${tokenOut.id}`;
    return this.tokenPairToPoolsMap.get(pairKey) || [];
  }

  /**
   * 本地计算DCL v2报价
   */
  async getLocalQuote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      // 1. 确保池子信息已加载
      await this.getAllPoolsInfo();
      
      // 2. 查找相关池子
      const pools = this.findPoolsForPair(params.tokenIn, params.tokenOut);
      
      if (pools.length === 0) {
        console.log(`⚠️ 没有找到 ${params.tokenIn.symbol}-${params.tokenOut.symbol} 的DCL v2池子`);
        return null;
      }

      console.log(`🔄 本地计算 ${pools.length} 个池子的报价...`);

      // 3. 计算每个池子的报价
      const quotes = pools.map(pool => this.calculatePoolQuote(pool, params));
      
      // 4. 选择最佳报价
      const validQuotes = quotes.filter(q => q !== null);
      if (validQuotes.length === 0) {
        console.log(`❌ 所有池子都无法提供有效报价`);
        return null;
      }

      // 选择输出金额最大的池子
      const bestQuote = validQuotes.reduce((best, current) => 
        parseFloat(current!.outputAmount) > parseFloat(best!.outputAmount) ? current : best
      );

      const pool = pools.find(p => p.pool_id === bestQuote!.poolId);
      const feePercent = (pool!.fee / 10000).toFixed(2);
      console.log(`🏆 选择最优DCL v2池子: ${bestQuote!.poolId} (费率${feePercent}%, 输出${bestQuote!.outputAmount} ${params.tokenOut.symbol})`);

      return bestQuote;

    } catch (error: any) {
      console.error('❌ 本地计算DCL v2报价失败:', error.message);
      return null;
    }
  }

  /**
   * 计算单个池子的报价
   */
  private calculatePoolQuote(pool: DCLPoolInfo, params: QuoteParams): QuoteResult | null {
    try {
      const { tokenIn, tokenOut, amountIn } = params;
      
      // 检查池子状态
      if (pool.state !== 'Running') {
        return null;
      }

      // 检查流动性
      const liquidity = parseFloat(pool.liquidity);
      if (liquidity <= 0) {
        return null;
      }

      // 确定代币方向
      const isTokenXToY = pool.token_x === tokenIn.id;
      const inputAmount = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);
      
      // 使用简化的DCL计算公式
      const outputAmount = this.calculateDCLSwap(pool, inputAmount, isTokenXToY);
      
      if (!outputAmount || outputAmount === '0') {
        return null;
      }

      // 转换为可读格式
      const readableOutput = this.toReadableNumber(outputAmount, tokenOut.decimals);

      const feePercent = (pool.fee / 10000).toFixed(2);
      console.log(`   ✅ 池子 ${pool.pool_id} (费率${feePercent}%): ${readableOutput} ${tokenOut.symbol}`);

      return {
        system: 'DCL_V2_LOCAL',
        contractId: this.config.contracts.dclv2,
        outputAmount: readableOutput,
        inputAmount: amountIn,
        poolId: pool.pool_id,
        route: {
          pool_ids: [pool.pool_id],
          input_token: tokenIn.id,
          output_token: tokenOut.id,
          input_amount: amountIn
        },
        rawResponse: { pool, calculatedLocally: true }
      };

    } catch (error: any) {
      console.log(`   ❌ 池子 ${pool.pool_id}: 计算失败 - ${error.message}`);
      return null;
    }
  }

  /**
   * DCL交换计算（改进版本）
   * 基于池子实际储备和当前价格进行更准确的计算
   */
  private calculateDCLSwap(pool: DCLPoolInfo, inputAmount: string, isTokenXToY: boolean): string {
    try {
      const input = parseFloat(inputAmount);
      const fee = pool.fee;

      // 获取池子储备
      const totalX = parseFloat(pool.total_x);
      const totalY = parseFloat(pool.total_y);

      if (totalX <= 0 || totalY <= 0) {
        return '0';
      }

      // 计算费用
      const feeRate = fee / 1000000; // fee是基点，转换为小数
      const inputAfterFee = input * (1 - feeRate);

      let outputAmount: number;

      if (isTokenXToY) {
        // X -> Y: 类似于恒定乘积公式的简化版本
        // 使用当前储备比例估算
        const k = totalX * totalY; // 恒定乘积
        const newTotalX = totalX + inputAfterFee;
        const newTotalY = k / newTotalX;
        outputAmount = totalY - newTotalY;
      } else {
        // Y -> X: 反向计算
        const k = totalX * totalY;
        const newTotalY = totalY + inputAfterFee;
        const newTotalX = k / newTotalY;
        outputAmount = totalX - newTotalX;
      }

      // 确保输出为正数且不超过储备
      if (outputAmount <= 0) {
        return '0';
      }

      // 限制最大输出为储备的50%（安全限制）
      const maxOutput = isTokenXToY ? totalY * 0.5 : totalX * 0.5;
      outputAmount = Math.min(outputAmount, maxOutput);

      return Math.floor(outputAmount).toString();

    } catch (error) {
      return '0';
    }
  }

  /**
   * 转换为非可分割数字
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    const factor = Math.pow(10, decimals);
    return Math.floor(parseFloat(amount) * factor).toString();
  }

  /**
   * 转换为可读数字
   */
  private toReadableNumber(amount: string, decimals: number): string {
    const factor = Math.pow(10, decimals);
    return (parseFloat(amount) / factor).toString();
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return {
      poolsCount: this.poolsCache.length,
      cacheAge: Date.now() - this.poolsCacheTime,
      tokenPairsCount: this.tokenPairToPoolsMap.size
    };
  }
}

/**
 * 导出单例实例
 */
export const dclv2LocalCalculator = new DCLv2LocalCalculatorService();
