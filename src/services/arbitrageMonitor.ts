/**
 * 套利监控服务
 * 
 * 实现你提到的监控逻辑：
 * 1. 同时获取REF和VEAX的near兑换usdt报价
 * 2. 用获取到的usdt再获取对near的报价
 * 3. 计算套利机会和利润
 */

import { refQuoteService } from './refQuoteService';
import { VeaxQuoteService } from './veaxQuoteService';
import { TradingPairConfig, ArbitrageConfig, tradingPairManager } from '../config/tradingPairs';
import { QuoteParams } from '../types';
import { dynamicAmountManager, AmountLevel } from './dynamicAmountManager';

// 套利机会接口
export interface ArbitrageOpportunity {
  pairId: string;
  direction: 'REF_TO_VEAX' | 'VEAX_TO_REF';
  inputAmount: string;
  
  // 第一步：tokenA -> tokenB
  step1: {
    dex: 'REF' | 'VEAX';
    inputToken: string;
    outputToken: string;
    inputAmount: string;
    outputAmount: string;
    priceImpact?: string;
    fee?: string;
  };
  
  // 第二步：tokenB -> tokenA
  step2: {
    dex: 'REF' | 'VEAX';
    inputToken: string;
    outputToken: string;
    inputAmount: string;
    outputAmount: string;
    priceImpact?: string;
    fee?: string;
  };
  
  // 利润分析
  profit: {
    absoluteProfit: string;    // 绝对利润
    profitPercentage: number;  // 利润百分比
    estimatedGasCost: string;  // 预估Gas费用
    netProfit: string;         // 净利润
  };
  
  timestamp: number;
  confidence: 'HIGH' | 'MEDIUM' | 'LOW';
}

// 监控统计接口
export interface MonitorStats {
  totalChecks: number;
  opportunitiesFound: number;
  successfulTrades: number;
  tradesExecuted: number;  // 🎯 新增：已执行交易数
  totalProfit: string;
  averageCheckTime: number;
  lastCheckTime: number;
  errors: number;
}

/**
 * 套利监控服务类
 */
export class ArbitrageMonitorService {
  private isRunning: boolean = false;
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private stats: MonitorStats = {
    totalChecks: 0,
    opportunitiesFound: 0,
    successfulTrades: 0,
    tradesExecuted: 0,  // 🎯 初始化已执行交易数
    totalProfit: '0',
    averageCheckTime: 0,
    lastCheckTime: 0,
    errors: 0
  };

  private opportunities: ArbitrageOpportunity[] = [];
  private maxOpportunityHistory = 100; // 最多保存100个机会记录

  constructor() {
    // 使用单例的refQuoteService
  }

  /**
   * 开始监控所有启用的交易对
   */
  async startMonitoring(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ 监控已在运行中');
      return;
    }

    this.isRunning = true;
    console.log('🚀 开始套利监控');

    const enabledPairs = tradingPairManager.getEnabledPairs();
    console.log(`📊 监控 ${enabledPairs.length} 个交易对`);

    for (const pair of enabledPairs) {
      this.startPairMonitoring(pair);
    }
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (!this.isRunning) {
      console.log('⚠️ 监控未在运行');
      return;
    }

    this.isRunning = false;
    
    // 清除所有定时器
    this.intervals.forEach((interval, pairId) => {
      clearInterval(interval);
      console.log(`⏹️ 停止监控交易对: ${pairId}`);
    });
    
    this.intervals.clear();
    console.log('🛑 套利监控已停止');
  }

  /**
   * 开始监控单个交易对
   */
  private startPairMonitoring(pair: TradingPairConfig): void {
    console.log(`▶️ 开始监控: ${pair.id} (间隔: ${pair.checkInterval}ms)`);

    const interval = setInterval(async () => {
      try {
        await this.checkArbitrageOpportunity(pair);
      } catch (error) {
        console.error(`❌ 监控错误 ${pair.id}:`, error);
        this.stats.errors++;
      }
    }, pair.checkInterval);

    this.intervals.set(pair.id, interval);
  }

  /**
   * 检查单个交易对的套利机会
   */
  private async checkArbitrageOpportunity(pair: TradingPairConfig): Promise<void> {
    const startTime = Date.now();
    this.stats.totalChecks++;

    try {
      // 🎯 获取当前档位的查询金额
      const queryAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
      const currentLevel = dynamicAmountManager.getCurrentLevel(pair.id);

      console.log(`\n🔍 检查套利机会: ${pair.id} (${currentLevel}档: ${queryAmount} NEAR)`);

      // 步骤1: 并行获取两个DEX的报价 (tokenA -> tokenB)
      const [refQuote, veaxQuote] = await Promise.all([
        this.getREFQuote(pair.tokenA.id, pair.tokenB.id, queryAmount),
        this.getVEAXQuote(pair.tokenA.id, pair.tokenB.id, queryAmount)
      ]);

      if (!refQuote.success || !veaxQuote.success) {
        console.log(`⚠️ ${pair.id} 报价获取失败`);
        return;
      }

      // 步骤2: 用获得的tokenB数量获取反向报价 (tokenB -> tokenA)
      const [refReverseQuote, veaxReverseQuote] = await Promise.all([
        this.getREFQuote(pair.tokenB.id, pair.tokenA.id, refQuote.outputAmount),
        this.getVEAXQuote(pair.tokenB.id, pair.tokenA.id, veaxQuote.outputAmount)
      ]);

      if (!refReverseQuote.success || !veaxReverseQuote.success) {
        console.log(`⚠️ ${pair.id} 反向报价获取失败`);
        return;
      }

      // 显示简化的套利路径
      console.log(`REF-VEAX: ${queryAmount} ${pair.tokenA.symbol} → ${refQuote.outputAmount} ${pair.tokenB.symbol} → ${veaxReverseQuote.outputAmount} ${pair.tokenA.symbol}`);
      console.log(`VEAX-REF: ${queryAmount} ${pair.tokenA.symbol} → ${veaxQuote.outputAmount} ${pair.tokenB.symbol} → ${refReverseQuote.outputAmount} ${pair.tokenA.symbol}`);

      // 步骤3: 分析套利机会
      const opportunities = this.analyzeArbitrageOpportunities(
        pair,
        queryAmount,
        {
          refForward: refQuote,
          veaxForward: veaxQuote,
          refReverse: refReverseQuote,
          veaxReverse: veaxReverseQuote
        }
      );

      // 步骤4: 处理发现的机会
      if (opportunities.length > 0) {
        for (const opportunity of opportunities) {
          this.handleArbitrageOpportunity(opportunity);
        }
      } else {
        // 🎯 无套利机会时的处理
        dynamicAmountManager.handleNoOpportunity(pair.id, pair);
      }

    } finally {
      // 更新统计信息
      const checkTime = Date.now() - startTime;
      this.stats.averageCheckTime = (this.stats.averageCheckTime + checkTime) / 2;
      this.stats.lastCheckTime = Date.now();
    }
  }

  /**
   * 获取REF Finance报价
   */
  private async getREFQuote(tokenIn: string, tokenOut: string, amount: string) {
    try {
      // 构建代币元数据（简化版本，实际应该从配置中获取）
      const tokenInMeta = this.getTokenMetadata(tokenIn);
      const tokenOutMeta = this.getTokenMetadata(tokenOut);

      const quoteParams: QuoteParams = {
        tokenIn: tokenInMeta,
        tokenOut: tokenOutMeta,
        amountIn: amount,
        slippage: 0.005 // 0.5%滑点
      };

      const result = await refQuoteService.getQuote(quoteParams);

      return {
        success: true,
        outputAmount: result.outputAmount,
        priceImpact: result.priceImpact || '0',
        fee: result.fee || '0'
      };
    } catch (error) {
      console.error(`REF报价失败: ${tokenIn}->${tokenOut}`, error);
      return {
        success: false,
        outputAmount: '0',
        priceImpact: '0',
        fee: '0'
      };
    }
  }

  /**
   * 获取代币元数据（简化版本）
   */
  private getTokenMetadata(tokenId: string) {
    // 常用代币映射
    const tokenMap: Record<string, any> = {
      'wrap.near': { id: 'wrap.near', symbol: 'NEAR', name: 'NEAR Protocol', decimals: 24 },
      '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1': {
        id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
        symbol: 'USDC',
        name: 'USD Coin',
        decimals: 6
      },
      'usdt.tether-token.near': {
        id: 'usdt.tether-token.near',
        symbol: 'USDT',
        name: 'Tether USD',
        decimals: 6
      }
    };

    return tokenMap[tokenId] || {
      id: tokenId,
      symbol: tokenId.slice(-4),
      name: tokenId,
      decimals: 18
    };
  }

  /**
   * 获取VEAX报价
   */
  private async getVEAXQuote(tokenIn: string, tokenOut: string, amount: string) {
    try {
      const result = await VeaxQuoteService.getQuote(tokenIn, tokenOut, amount);
      return result;
    } catch (error) {
      console.error(`VEAX报价失败: ${tokenIn}->${tokenOut}`, error);
      return {
        success: false,
        outputAmount: '0',
        priceImpact: '0',
        fee: '0'
      };
    }
  }

  /**
   * 分析套利机会
   */
  private analyzeArbitrageOpportunities(
    pair: TradingPairConfig,
    queryAmount: string,
    quotes: {
      refForward: any;
      veaxForward: any;
      refReverse: any;
      veaxReverse: any;
    }
  ): ArbitrageOpportunity[] {
    const opportunities: ArbitrageOpportunity[] = [];
    const arbitrageConfig = tradingPairManager.getArbitrageConfig();

    // 路径1: REF -> VEAX (REF买tokenB，VEAX卖tokenB)
    const refToVeaxProfit = this.calculateProfit(
      queryAmount,
      quotes.refForward.outputAmount,
      quotes.veaxReverse.outputAmount
    );

    if (parseFloat(refToVeaxProfit.absoluteProfit) > 0.02) {
      opportunities.push({
        pairId: pair.id,
        direction: 'REF_TO_VEAX',
        inputAmount: queryAmount,
        step1: {
          dex: 'REF',
          inputToken: pair.tokenA.id,
          outputToken: pair.tokenB.id,
          inputAmount: queryAmount,
          outputAmount: quotes.refForward.outputAmount,
          priceImpact: quotes.refForward.priceImpact,
          fee: quotes.refForward.fee
        },
        step2: {
          dex: 'VEAX',
          inputToken: pair.tokenB.id,
          outputToken: pair.tokenA.id,
          inputAmount: quotes.refForward.outputAmount,
          outputAmount: quotes.veaxReverse.outputAmount,
          priceImpact: quotes.veaxReverse.priceImpact,
          fee: quotes.veaxReverse.fee
        },
        profit: refToVeaxProfit,
        timestamp: Date.now(),
        confidence: this.assessConfidence(parseFloat(refToVeaxProfit.absoluteProfit))
      });
    }

    // 路径2: VEAX -> REF (VEAX买tokenB，REF卖tokenB)
    const veaxToRefProfit = this.calculateProfit(
      queryAmount,
      quotes.veaxForward.outputAmount,
      quotes.refReverse.outputAmount
    );

    if (parseFloat(veaxToRefProfit.absoluteProfit) > 0.02) {
      opportunities.push({
        pairId: pair.id,
        direction: 'VEAX_TO_REF',
        inputAmount: queryAmount,
        step1: {
          dex: 'VEAX',
          inputToken: pair.tokenA.id,
          outputToken: pair.tokenB.id,
          inputAmount: queryAmount,
          outputAmount: quotes.veaxForward.outputAmount,
          priceImpact: quotes.veaxForward.priceImpact,
          fee: quotes.veaxForward.fee
        },
        step2: {
          dex: 'REF',
          inputToken: pair.tokenB.id,
          outputToken: pair.tokenA.id,
          inputAmount: quotes.veaxForward.outputAmount,
          outputAmount: quotes.refReverse.outputAmount,
          priceImpact: quotes.refReverse.priceImpact,
          fee: quotes.refReverse.fee
        },
        profit: veaxToRefProfit,
        timestamp: Date.now(),
        confidence: this.assessConfidence(parseFloat(veaxToRefProfit.absoluteProfit))
      });
    }

    return opportunities;
  }

  /**
   * 计算利润
   */
  private calculateProfit(inputAmount: string, intermediateAmount: string, finalAmount: string) {
    const input = parseFloat(inputAmount);
    const final = parseFloat(finalAmount);
    const absoluteProfit = final - input;
    const profitPercentage = (absoluteProfit / input) * 100;
    
    // 简单的Gas费用估算
    const estimatedGasCost = '0.01'; // 假设0.01 NEAR的Gas费用
    const netProfit = (absoluteProfit - parseFloat(estimatedGasCost)).toString();

    return {
      absoluteProfit: absoluteProfit.toString(),
      profitPercentage,
      estimatedGasCost,
      netProfit
    };
  }

  /**
   * 评估机会置信度 (基于绝对利润NEAR)
   */
  private assessConfidence(absoluteProfit: number): 'HIGH' | 'MEDIUM' | 'LOW' {
    if (absoluteProfit > 0.1) return 'HIGH';    // 大于0.1 NEAR
    if (absoluteProfit > 0.05) return 'MEDIUM'; // 大于0.05 NEAR
    return 'LOW';                               // 0.02-0.05 NEAR
  }

  /**
   * 处理套利机会
   */
  private handleArbitrageOpportunity(opportunity: ArbitrageOpportunity): void {
    console.log(`💰 发现套利机会!`);
    console.log(`   交易对: ${opportunity.pairId}`);
    console.log(`   方向: ${opportunity.direction}`);
    console.log(`   绝对利润: ${opportunity.profit.absoluteProfit} NEAR`);
    console.log(`   利润率: ${opportunity.profit.profitPercentage.toFixed(2)}%`);
    console.log(`   置信度: ${opportunity.confidence}`);

    // 保存机会记录
    this.opportunities.unshift(opportunity);
    if (this.opportunities.length > this.maxOpportunityHistory) {
      this.opportunities.pop();
    }

    this.stats.opportunitiesFound++;

    // 这里可以添加自动执行逻辑
    const arbitrageConfig = tradingPairManager.getArbitrageConfig();
    const absoluteProfit = parseFloat(opportunity.profit.absoluteProfit);

    if (arbitrageConfig.enabled && absoluteProfit > arbitrageConfig.minProfitThreshold) {
      console.log(`🤖 满足执行条件 (利润${absoluteProfit.toFixed(4)} NEAR > ${arbitrageConfig.minProfitThreshold} NEAR)，准备自动执行套利交易...`);

      // 🎯 模拟交易执行并调整动态金额
      this.simulateTradeExecution(opportunity);
    }
  }

  /**
   * 🎯 模拟交易执行（用于测试动态金额调整）
   */
  private simulateTradeExecution(opportunity: ArbitrageOpportunity): void {
    console.log(`🚀 模拟执行套利交易: ${opportunity.direction}`);

    // 模拟交易执行时间
    setTimeout(() => {
      // 模拟实际利润（添加一些随机性）
      const expectedProfit = parseFloat(opportunity.profit.absoluteProfit);
      const actualProfit = expectedProfit * (0.9 + Math.random() * 0.2); // 90%-110%的预期利润

      console.log(`✅ 模拟交易完成: 预期利润${expectedProfit.toFixed(4)} NEAR → 实际利润${actualProfit.toFixed(4)} NEAR`);

      // 🎯 根据实际利润调整下次查询金额
      const pair = tradingPairManager.getPair(opportunity.pairId);
      if (pair) {
        dynamicAmountManager.adjustNextAmount(opportunity.pairId, actualProfit, pair);
      }

      // 更新统计
      this.stats.tradesExecuted = (this.stats.tradesExecuted || 0) + 1;

    }, 1000 + Math.random() * 2000); // 1-3秒的模拟执行时间
  }

  /**
   * 获取监控统计
   */
  getStats(): MonitorStats {
    return { ...this.stats };
  }

  /**
   * 获取最近的套利机会
   */
  getRecentOpportunities(limit: number = 10): ArbitrageOpportunity[] {
    return this.opportunities.slice(0, limit);
  }

  /**
   * 获取监控状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      monitoredPairs: this.intervals.size,
      stats: this.getStats(),
      recentOpportunities: this.getRecentOpportunities(5)
    };
  }
}

export default ArbitrageMonitorService;
