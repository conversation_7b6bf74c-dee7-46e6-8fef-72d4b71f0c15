import axios from 'axios';
import Big from 'big.js';
import {
  SmartRouterResponse,
  QuoteParams,
  QuoteResult,
  QuoteError,
  TokenMetadata
} from '../types';
import { getConfig, DEFAULT_SLIPPAGE, DEFAULT_PATH_DEEP, REQUEST_TIMEOUT } from '../config';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';

/**
 * V1 Smart Router API 服务
 */
export class V1SmartRouterService {
  private config = getConfig();

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 将可读金额转换为非可分割单位（使用精确方法）
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ REF Smart Router收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 将非可分割单位转换为可读金额（使用精确方法）
   */
  private toReadableNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ REF Smart Router收到无效wei数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法并移除千分位分隔符
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      // 对于其他精度，使用精确的字符串操作
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  /**
   * 构建 Smart Router API URL
   */
  private buildSmartRouterUrl(params: QuoteParams): string {
    const { tokenIn, tokenOut, amountIn, slippage = DEFAULT_SLIPPAGE } = params;
    
    const amountInNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);
    const slippagePercent = slippage;
    
    const url = new URL(`${this.config.smartRouterUrl}/findPath`);
    url.searchParams.set('amountIn', amountInNonDivisible);
    url.searchParams.set('tokenIn', tokenIn.id);
    url.searchParams.set('tokenOut', tokenOut.id);
    url.searchParams.set('pathDeep', DEFAULT_PATH_DEEP.toString());
    url.searchParams.set('slippage', slippagePercent.toString());
    
    return url.toString();
  }

  /**
   * 调用 V1 Smart Router API
   */
  async getV1Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      const url = this.buildSmartRouterUrl(params);
      
      // 静默调用V1 API
      
      const response = await axios.get<SmartRouterResponse>(url, {
        timeout: REQUEST_TIMEOUT,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'REF-VEAX-Arbitrage-Bot/1.0'
        },
        // // 🔧 临时解决方案：忽略SSL证书验证（仅用于证书过期问题）
        // httpsAgent: new (require('https').Agent)({
        //   rejectUnauthorized: false
        // })
      });

      const data = response.data;
      
      // 检查响应状态
      if (data.result_code !== 0 || !data.result_data?.routes?.length) {
        console.log('❌ V1 Smart Router 未找到可用路径');
        return null;
      }

      const { result_data } = data;
      const outputAmount = this.toReadableNumber(result_data.amount_out, params.tokenOut.decimals);
      
      // 静默处理V1成功结果
      
      return {
        system: 'V1',
        contractId: this.config.contracts.v1,
        outputAmount,
        inputAmount: params.amountIn,
        route: result_data.routes[0], // 使用第一条路径作为主要路径
        rawResponse: data
      };

    } catch (error: any) {
      console.error('❌ V1 Smart Router 调用失败:', error.message);
      
      if (error.code === 'ECONNABORTED') {
        throw new Error('V1 Smart Router API 请求超时');
      }
      
      if (error.response?.status === 404) {
        throw new Error('V1 Smart Router API 未找到');
      }
      
      throw new Error(`V1 Smart Router API 调用失败: ${error.message}`);
    }
  }

  /**
   * 获取路径详情
   */
  getRouteDetails(quoteResult: QuoteResult): string {
    if (quoteResult.system !== 'V1' || !quoteResult.route) {
      return 'N/A';
    }

    const route = quoteResult.route as any;
    if (!route.pools) {
      return 'N/A';
    }

    const pools = route.pools.map((pool: any) => `Pool#${pool.pool_id}`).join(' → ');
    return pools;
  }

  /**
   * 计算价格影响
   */
  calculatePriceImpact(inputAmount: string, outputAmount: string, marketPrice?: string): string {
    if (!marketPrice) return 'N/A';
    
    try {
      const actualPrice = new Big(outputAmount).div(inputAmount);
      const market = new Big(marketPrice);
      const impact = actualPrice.minus(market).div(market).abs().times(100);
      return `${impact.toFixed(4)}%`;
    } catch {
      return 'N/A';
    }
  }
}

/**
 * 导出单例实例
 */
export const v1SmartRouter = new V1SmartRouterService();
