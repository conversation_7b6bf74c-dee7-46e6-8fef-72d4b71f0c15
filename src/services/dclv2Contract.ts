import axios from 'axios';
import Big from 'big.js';
import {
  DCLv2QuoteParams,
  DCLv2QuoteResponse,
  QuoteParams,
  QuoteResult,
  NearRPCParams,
  TokenMetadata
} from '../types';
import { getConfig, DCL_V2_FEE_LEVELS, REQUEST_TIMEOUT } from '../config';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';

/**
 * 主要的DCL v2池子（高流动性，只查询这些池子）
 */
const MAIN_DCL_POOLS = {
  'USDC-NEAR': '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',
  'USDT-NEAR': 'usdt.tether-token.near|wrap.near|100'
};

/**
 * DCL v2 合约调用服务
 */
export class DCLv2ContractService {
  private config = getConfig();

  /**
   * 验证数值格式是否有效
   */
  private isValidNumber(value: any): boolean {
    if (value === null || value === undefined || value === '') {
      return false;
    }

    const str = String(value).trim();
    if (str === '' || str === 'null' || str === 'undefined' || str === 'NaN' || str === 'Infinity') {
      return false;
    }

    // 检查是否为纯数字字符串（允许小数点）
    return /^\d+(\.\d+)?$/.test(str);
  }

  /**
   * 将可读金额转换为非可分割单位（使用精确方法）
   */
  private toNonDivisibleNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ DCL v2收到无效数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法
      return parseNearAmount(amount) || '0';
    } else {
      // 对于其他精度，使用精确的字符串操作
      const [integer, decimal = ''] = amount.split('.');
      const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
      return (integer || '0') + paddedDecimal;
    }
  }

  /**
   * 将非可分割单位转换为可读金额（使用精确方法）
   */
  private toReadableNumber(amount: string, decimals: number): string {
    // 🔧 添加数值验证
    if (!this.isValidNumber(amount)) {
      console.warn(`⚠️ DCL v2收到无效wei数值: ${amount}, 返回'0'`);
      return '0';
    }

    if (decimals === 24) {
      // 对于24位精度（NEAR），使用官方方法并移除千分位分隔符
      return formatNearAmount(amount).replace(/,/g, '');
    } else {
      // 对于其他精度，使用精确的字符串操作
      if (amount === '0') return '0';

      const paddedAmount = amount.padStart(decimals + 1, '0');
      const integerPart = paddedAmount.slice(0, -decimals) || '0';
      const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');

      return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
    }
  }

  // 移除复杂的缓存机制，简化为直接查询主要池子

  // 移除复杂的池子缓存和索引逻辑，简化为直接主要池子查询

  /**
   * 获取主要池子ID（简化版本）
   */
  private getMainPoolId(tokenIn: TokenMetadata, tokenOut: TokenMetadata): string | null {
    // USDC-NEAR交易对
    if ((tokenIn.id === '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1' && tokenOut.id === 'wrap.near') ||
        (tokenIn.id === 'wrap.near' && tokenOut.id === '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1')) {
      return MAIN_DCL_POOLS['USDC-NEAR'];
    }

    // USDT-NEAR交易对
    if ((tokenIn.id === 'usdt.tether-token.near' && tokenOut.id === 'wrap.near') ||
        (tokenIn.id === 'wrap.near' && tokenOut.id === 'usdt.tether-token.near')) {
      return MAIN_DCL_POOLS['USDT-NEAR'];
    }

    return null; // 不是主要交易对
  }



  /**
   * 构建单个池子的 RPC 调用参数
   */
  private buildSinglePoolRPCParams(
    poolId: string,
    tokenIn: TokenMetadata,
    tokenOut: TokenMetadata,
    amountIn: string
  ): NearRPCParams {
    // 🔧 修复：使用精度单位的金额（模仿前端）
    const inputAmountNonDivisible = this.toNonDivisibleNumber(amountIn, tokenIn.decimals);

    const quoteParams: DCLv2QuoteParams = {
      pool_ids: [poolId], // 只查询单个池子
      input_token: tokenIn.id,
      output_token: tokenOut.id,
      input_amount: inputAmountNonDivisible, // 使用精度单位
      tag: `${tokenIn.id}|${poolId.split('|')[2]}|${amountIn}`
    };

    const argsBase64 = Buffer.from(JSON.stringify(quoteParams)).toString('base64');

    return {
      request_type: 'call_function',
      finality: 'optimistic',
      account_id: this.config.contracts.dclv2,
      method_name: 'quote',
      args_base64: argsBase64
    };
  }

  /**
   * 获取 DCL v2 报价（优化版本 - 只查询主要池子）
   */
  async getDCLv2Quote(params: QuoteParams): Promise<QuoteResult | null> {
    try {
      // 1. 检查是否为主要交易对
      const mainPoolId = this.getMainPoolId(params.tokenIn, params.tokenOut);

      if (!mainPoolId) {
        return null; // 不是主要交易对，直接返回
      }

      // 2. 查询主要池子（1次RPC调用）
      const result = await this.querySinglePool(mainPoolId, params);

      if (!result || !result.amount || result.amount === '0') {
        return null;
      }

      // 3. 格式化结果
      const outputAmount = this.toReadableNumber(result.amount, params.tokenOut.decimals);

      return {
        system: 'DCL_V2',
        contractId: this.config.contracts.dclv2,
        outputAmount,
        inputAmount: params.amountIn,
        poolId: mainPoolId,
        route: {
          pool_ids: [mainPoolId],
          input_token: params.tokenIn.id,
          output_token: params.tokenOut.id,
          input_amount: params.amountIn
        },
        rawResponse: result
      };

    } catch (error: any) {
      console.error('❌ DCL v2 报价失败:', error.message);
      return null;
    }
  }



  /**
   * 查询单个池子
   */
  private async querySinglePool(poolId: string, params: QuoteParams): Promise<any> {
    const rpcParams = this.buildSinglePoolRPCParams(poolId, params.tokenIn, params.tokenOut, params.amountIn);

    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: rpcParams
    };

    const response = await axios.post(this.config.rpcUrl, rpcPayload, {
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;

    if (data.error) {
      throw new Error(`池子 ${poolId} RPC 错误: ${data.error.message}`);
    }

    const resultBytes = data.result?.result;
    if (!resultBytes || resultBytes.length === 0) {
      return null;
    }

    const resultString = String.fromCharCode(...resultBytes);
    return JSON.parse(resultString);
  }

  /**
   * 获取最佳池子信息
   */
  getBestPoolInfo(quoteResult: QuoteResult): string {
    if (quoteResult.system !== 'DCL_V2' || !quoteResult.poolId) {
      return 'N/A';
    }

    const parts = quoteResult.poolId.split('|');
    if (parts.length === 3) {
      const fee = parts[2];
      const feePercent = (parseInt(fee) / 10000).toFixed(2);
      return `DCL Pool (${feePercent}% fee)`;
    }

    return 'DCL Pool';
  }
}

/**
 * 导出单例实例
 */
export const dclv2Contract = new DCLv2ContractService();
