/**
 * VEAX DEX 交易执行服务
 * 
 * 支持VEAX的swap_exact_in交易执行
 * 使用near-api-js和私钥进行签名
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';

// 交易结果接口（修复精度问题）
export interface VeaxTransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  amountIn?: string;          // 人类可读格式
  amountOut?: string;         // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmountWei?: string;    // wei格式（精确，新增）
}

// VEAX用户注册状态
export interface VeaxUserStatus {
  isRegistered: boolean;
  storageBalance?: {
    total: string;
    available: string;
  };
}

// VEAX代币注册状态 (钱包在代币合约中的注册状态)
export interface VeaxTokenStatus {
  isRegistered: boolean;
  balance?: string;
}

/**
 * VEAX DEX 交易执行服务
 */
export class VeaxExecutionService {
  private near: Near | null = null;
  private account: Account | null = null;
  private readonly contractId = 'veax.near';

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: string = 'mainnet'
  ) {}

  /**
   * 检查账户余额 (原生NEAR)
   */
  async checkAccountBalance(): Promise<{ balance: string; balanceNear: number }> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      const accountState = await this.account.state();
      const balanceYocto = accountState.amount;
      const balanceNear = parseFloat(balanceYocto) / Math.pow(10, 24);

      return {
        balance: balanceYocto,
        balanceNear: balanceNear
      };
    } catch (error) {
      console.error('❌ 获取账户余额失败:', error);
      throw error;
    }
  }

  /**
   * 检查wNEAR余额 (用于交易)
   */
  async checkWNearBalance(): Promise<{ balance: string; balanceWNear: number }> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      // 查询wNEAR合约中的余额
      const result = await this.account.viewFunction({
        contractId: 'wrap.near',
        methodName: 'ft_balance_of',
        args: {
          account_id: this.accountId
        }
      });

      const balanceWNear = parseFloat(result) / Math.pow(10, 24);

      return {
        balance: result,
        balanceWNear: balanceWNear
      };
    } catch (error) {
      console.log('ℹ️ 获取wNEAR余额失败，可能未注册wNEAR');
      return {
        balance: '0',
        balanceWNear: 0
      };
    }
  }

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      // 🔧 修复：使用环境变量配置的RPC URL，而不是硬编码fastnear
      const rpcUrl = process.env.NEAR_RPC_URL || (this.networkId === 'mainnet'
        ? 'https://rpc.mainnet.near.org'
        : 'https://rpc.testnet.near.org');

      const config = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: rpcUrl,
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(config);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ VEAX执行服务初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ VEAX执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查用户注册状态
   */
  async checkUserRegistration(): Promise<VeaxUserStatus> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      const result = await this.account.viewFunction({
        contractId: this.contractId,
        methodName: 'storage_balance_of',
        args: { account_id: this.accountId }
      });

      if (result) {
        return {
          isRegistered: true,
          storageBalance: {
            total: result.total,
            available: result.available
          }
        };
      } else {
        return { isRegistered: false };
      }
    } catch (error) {
      console.log(`ℹ️ 用户 ${this.accountId} 未在VEAX注册`);
      return { isRegistered: false };
    }
  }

  /**
   * 注册用户到VEAX
   */
  async registerUser(): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔄 注册用户到VEAX: ${this.accountId}`);

      const result = await this.account.functionCall({
        contractId: this.contractId,
        methodName: 'storage_deposit',
        args: {
          account_id: this.accountId,
          registration_only: false
        },
        attachedDeposit: BigInt('1250000000000000000000000'), // 0.00125 NEAR (更保守)
        gas: BigInt('**************') // 30 TGas
      });

      console.log(`✅ 用户注册成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ 用户注册失败:', error);
      return {
        success: false,
        error: error.message || '用户注册失败'
      };
    }
  }

  /**
   * 检查代币注册状态 (检查钱包是否在代币合约中注册)
   */
  async checkTokenRegistration(tokenId: string): Promise<VeaxTokenStatus> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      // 检查钱包在代币合约中的存储余额
      const result = await this.account.viewFunction({
        contractId: tokenId, // 直接查询代币合约
        methodName: 'storage_balance_of',
        args: {
          account_id: this.accountId
        }
      });

      if (result) {
        return {
          isRegistered: true,
          balance: result.total || '0'
        };
      } else {
        return { isRegistered: false };
      }
    } catch (error) {
      console.log(`ℹ️ 代币 ${tokenId} 未为用户 ${this.accountId} 注册`);
      return { isRegistered: false };
    }
  }

  /**
   * 检查代币是否在VEAX合约中注册
   */
  async checkTokenRegistrationInVeax(tokenId: string): Promise<VeaxTokenStatus> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      // 检查代币是否在VEAX合约中为用户注册
      const result = await this.account.viewFunction({
        contractId: this.contractId, // veax.near
        methodName: 'token_register_of',
        args: {
          account_id: this.accountId,
          token_id: tokenId
        }
      });

      return {
        isRegistered: result === true,
        balance: '0'
      };
    } catch (error) {
      console.log(`ℹ️ 代币 ${tokenId} 未在VEAX合约中为用户 ${this.accountId} 注册`);
      return { isRegistered: false };
    }
  }

  /**
   * 注册代币 (在代币合约中为钱包注册存储)
   */
  async registerToken(tokenId: string): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔄 在代币合约中注册钱包: ${tokenId}`);

      // 调用代币合约的storage_deposit方法
      const result = await this.account.functionCall({
        contractId: tokenId, // 调用代币合约，不是VEAX合约
        methodName: 'storage_deposit',
        args: {
          account_id: this.accountId,
          registration_only: false
        },
        attachedDeposit: BigInt('1250000000000000000000'), // 约0.00125 NEAR (更保守)
        gas: BigInt('**************') // 30 TGas
      });

      console.log(`✅ 代币注册成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ 代币注册失败:', error);
      return {
        success: false,
        error: error.message || '代币注册失败'
      };
    }
  }

  /**
   * 增加存储余额
   */
  async addStorageDeposit(amount: string = '10000000000000000000000'): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔄 增加存储余额: ${amount} yoctoNEAR`);

      // 调用storage_deposit增加存储余额
      const result = await this.account.functionCall({
        contractId: this.contractId, // veax.near
        methodName: 'storage_deposit',
        args: {
          account_id: this.accountId,
          registration_only: false // 存入所有附加的NEAR
        },
        attachedDeposit: BigInt(amount), // 0.01 NEAR
        gas: BigInt('**************') // 30 TGas
      });

      console.log(`✅ 存储余额增加成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ 存储余额增加失败:', error);
      return {
        success: false,
        error: error.message || '存储余额增加失败'
      };
    }
  }

  /**
   * 在VEAX合约中注册代币 (使用官方API)
   */
  async registerTokensInVeax(tokenIds: string[]): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      // 先检查存储余额是否足够
      const userStatus = await this.checkUserRegistration();
      const availableBalance = userStatus.storageBalance ? parseInt(userStatus.storageBalance.available) : 0;

      // 每个代币需要约284字节存储，按1 NEAR = 10^24 yoctoNEAR计算
      // 每字节约需要10^19 yoctoNEAR，所以每个代币约需要2.84 * 10^21 yoctoNEAR
      const requiredPerToken = 2840000000000000000000; // 约0.00284 NEAR
      const totalRequired = requiredPerToken * tokenIds.length;

      if (availableBalance < totalRequired) {
        console.log(`⚠️ 存储余额不足，需要 ${totalRequired}，当前 ${availableBalance}`);
        console.log(`🔄 正在增加存储余额...`);

        const depositAmount = BigInt(totalRequired - availableBalance + 5000000000000000000000).toString(); // 多加0.005 NEAR作为缓冲
        const depositResult = await this.addStorageDeposit(depositAmount);

        if (!depositResult.success) {
          throw new Error(`存储余额增加失败: ${depositResult.error}`);
        }
      }

      console.log(`🔄 在VEAX合约中注册代币: ${tokenIds.join(', ')}`);

      // 调用VEAX的register_tokens方法
      const result = await this.account.functionCall({
        contractId: this.contractId, // veax.near
        methodName: 'register_tokens',
        args: {
          token_ids: tokenIds
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR (根据文档)
        gas: BigInt('**************') // 30 TGas
      });

      console.log(`✅ VEAX代币注册成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash
      };

    } catch (error: any) {
      console.error('❌ VEAX代币注册失败:', error);
      return {
        success: false,
        error: error.message || 'VEAX代币注册失败'
      };
    }
  }

  /**
   * 执行VEAX交易 (使用正确的ft_transfer_call格式)
   */
  async executeSwap(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    minAmountOut: string
  ): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🚀 开始执行VEAX交易: ${amountIn} ${tokenIn} → ${tokenOut}`);
      console.log(`📊 最小输出: ${minAmountOut}`);

      // 检查用户注册状态
      const userStatus = await this.checkUserRegistration();
      if (!userStatus.isRegistered) {
        console.log('⚠️ 用户未注册，正在注册...');
        const registerResult = await this.registerUser();
        if (!registerResult.success) {
          throw new Error(`用户注册失败: ${registerResult.error}`);
        }
      }

      // 检查代币在代币合约中的注册状态
      const tokenInStatus = await this.checkTokenRegistration(tokenIn);
      if (!tokenInStatus.isRegistered) {
        console.log(`⚠️ 输入代币 ${tokenIn} 未在代币合约中注册，正在注册...`);
        const registerResult = await this.registerToken(tokenIn);
        if (!registerResult.success) {
          throw new Error(`输入代币注册失败: ${registerResult.error}`);
        }
      }

      const tokenOutStatus = await this.checkTokenRegistration(tokenOut);
      if (!tokenOutStatus.isRegistered) {
        console.log(`⚠️ 输出代币 ${tokenOut} 未在代币合约中注册，正在注册...`);
        const registerResult = await this.registerToken(tokenOut);
        if (!registerResult.success) {
          throw new Error(`输出代币注册失败: ${registerResult.error}`);
        }
      }

      // 检查代币在VEAX合约中的注册状态
      const tokenInVeaxStatus = await this.checkTokenRegistrationInVeax(tokenIn);
      if (!tokenInVeaxStatus.isRegistered) {
        console.log(`⚠️ 输入代币 ${tokenIn} 未在VEAX合约中注册，正在注册...`);
        const registerResult = await this.registerTokensInVeax([tokenIn]);
        if (!registerResult.success) {
          throw new Error(`输入代币VEAX注册失败: ${registerResult.error}`);
        }
      }

      const tokenOutVeaxStatus = await this.checkTokenRegistrationInVeax(tokenOut);
      if (!tokenOutVeaxStatus.isRegistered) {
        console.log(`⚠️ 输出代币 ${tokenOut} 未在VEAX合约中注册，正在注册...`);
        const registerResult = await this.registerTokensInVeax([tokenOut]);
        if (!registerResult.success) {
          throw new Error(`输出代币VEAX注册失败: ${registerResult.error}`);
        }
      }

      // 构建VEAX交易消息 (正确格式)
      const swapMsg = [
        "Deposit",
        {
          "SwapExactIn": {
            "token_in": tokenIn,
            "token_out": tokenOut,
            "amount": amountIn,
            "amount_limit": minAmountOut // 确保这是整数字符串
          }
        },
        {
          "Withdraw": [tokenIn, "0", null]
        },
        {
          "Withdraw": [tokenOut, "0", null]
        }
      ];

      // 执行ft_transfer_call到输入代币合约
      const result = await this.account.functionCall({
        contractId: tokenIn, // 调用输入代币合约
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: this.contractId, // veax.near
          amount: amountIn,
          msg: JSON.stringify(swapMsg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('**************0') // 300 TGas
      });

      console.log(`✅ VEAX交易成功: ${result.transaction.hash}`);

      // 从交易结果中提取实际输出金额（wei格式）
      const logs = result.receipts_outcome
        .flatMap(receipt => receipt.outcome.logs)
        .filter(log => log.includes('EVENT_JSON'));

      let actualAmountOutWei = minAmountOut; // 默认使用最小输出金额
      for (const log of logs) {
        try {
          const eventData = JSON.parse(log.replace('EVENT_JSON:', ''));
          if (eventData.event === 'swap' && eventData.data.amounts) {
            actualAmountOutWei = eventData.data.amounts[1]; // 输出金额（wei格式）
            console.log(`📊 VEAX交易实际输出: ${actualAmountOutWei} wei`);
            break;
          }
        } catch (e) {
          // 忽略解析错误
        }
      }

      return {
        success: true,
        transactionHash: result.transaction.hash,
        amountIn: amountIn,
        amountOut: actualAmountOutWei, // 保持原有字段兼容性
        outputAmountWei: actualAmountOutWei, // 🔧 关键修复：明确的wei格式输出
        inputAmountWei: amountIn // 🔧 关键修复：输入也是wei格式
      };

    } catch (error: any) {
      console.error('❌ VEAX交易失败:', error);

      // 🔧 关键修复：只对网络错误进行交易状态检测
      if (error.context?.transactionHash && this.isNetworkError(error)) {
        const txHash = error.context.transactionHash;
        console.log(`🔍 检测到网络错误且有交易哈希: ${txHash}，查询交易状态...`);

        try {
          const txResult = await this.checkTransactionStatus(txHash);
          if (txResult.success) {
            console.log(`✅ 交易实际成功: ${txHash}`);
            return txResult;
          } else {
            console.log(`❌ 交易确认失败: ${txHash}`);
          }
        } catch (checkError) {
          console.error('❌ 查询交易状态失败:', checkError);
        }
      }

      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 判断是否为网络错误
   */
  private isNetworkError(error: any): boolean {
    const errorMessage = error.message || '';
    const errorString = errorMessage.toLowerCase();

    // 网络相关的错误关键词
    const networkErrorKeywords = [
      '502 bad gateway',
      '503 service unavailable',
      '504 gateway timeout',
      'network timeout',
      'connection reset',
      'connection refused',
      'timeout',
      'network error',
      'fetch failed',
      'cloudflare',
      'bad gateway'
    ];

    // 检查是否包含网络错误关键词
    const isNetworkError = networkErrorKeywords.some(keyword =>
      errorString.includes(keyword)
    );

    if (isNetworkError) {
      console.log(`🌐 识别为网络错误: ${errorMessage}`);
      return true;
    }

    // 合约执行错误不应该触发检测
    const contractErrorKeywords = [
      'smart contract panicked',
      'execution error',
      'insufficient balance',
      'slippage',
      'invalid token',
      'function call error'
    ];

    const isContractError = contractErrorKeywords.some(keyword =>
      errorString.includes(keyword)
    );

    if (isContractError) {
      console.log(`⚙️ 识别为合约执行错误，不进行状态检测: ${errorMessage}`);
      return false;
    }

    // 对于未知错误，保守处理：如果有HTML标签，可能是网络错误
    if (errorMessage.includes('<html>') || errorMessage.includes('<body>')) {
      console.log(`🌐 识别为HTML格式的网络错误: ${errorMessage.substring(0, 100)}...`);
      return true;
    }

    console.log(`❓ 未知错误类型，不进行状态检测: ${errorMessage}`);
    return false;
  }

  /**
   * 查询交易状态
   */
  async checkTransactionStatus(txHash: string): Promise<VeaxTransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    try {
      console.log(`🔍 查询交易状态: ${txHash}`);

      // 查询交易状态
      const txResult = await this.account.connection.provider.txStatus(txHash, this.accountId, 'FINAL');

      // 检查交易是否成功
      if (txResult.status && typeof txResult.status === 'object' && 'SuccessValue' in txResult.status) {
        console.log(`✅ 交易确认成功: ${txHash}`);

        // 从交易结果中提取实际输出金额（wei格式）
        const logs = txResult.receipts_outcome
          .flatMap(receipt => receipt.outcome.logs)
          .filter(log => log.includes('EVENT_JSON'));

        let actualAmountOutWei = '0';
        for (const log of logs) {
          try {
            const eventData = JSON.parse(log.replace('EVENT_JSON:', ''));
            if (eventData.event === 'swap' && eventData.data.amounts) {
              actualAmountOutWei = eventData.data.amounts[1]; // 输出金额（wei格式）
              console.log(`📊 VEAX交易实际输出: ${actualAmountOutWei} wei`);

              // 转换为人类可读格式用于显示
              const outputTokenId = eventData.data.tokens[1];
              let decimals = 6; // 默认USDT精度
              if (outputTokenId === 'wrap.near') decimals = 24;
              else if (outputTokenId.includes('usdt')) decimals = 6;
              else if (outputTokenId.includes('usdc')) decimals = 6;

              const humanReadable = (parseFloat(actualAmountOutWei) / Math.pow(10, decimals)).toString();
              console.log(`📊 VEAX交易实际输出(可读): ${humanReadable} ${outputTokenId.split('.')[0].toUpperCase()}`);
              break;
            }
          } catch (e) {
            // 忽略解析错误
          }
        }

        return {
          success: true,
          transactionHash: txHash,
          amountOut: actualAmountOutWei,
          outputAmountWei: actualAmountOutWei,
          inputAmountWei: '0' // 无法从交易结果中获取输入金额
        };
      } else {
        console.log(`❌ 交易失败: ${txHash}`, txResult.status);
        return {
          success: false,
          error: `交易失败: ${JSON.stringify(txResult.status)}`
        };
      }
    } catch (error: any) {
      console.error(`❌ 查询交易状态失败: ${txHash}`, error);
      return {
        success: false,
        error: error.message || '查询交易状态失败'
      };
    }
  }

  /**
   * 批量注册代币
   */
  async registerTokens(tokenIds: string[]): Promise<VeaxTransactionResult[]> {
    const results: VeaxTransactionResult[] = [];

    for (const tokenId of tokenIds) {
      const status = await this.checkTokenRegistration(tokenId);
      if (!status.isRegistered) {
        const result = await this.registerToken(tokenId);
        results.push(result);
      } else {
        console.log(`✅ 代币 ${tokenId} 已注册`);
        results.push({ success: true });
      }
    }

    return results;
  }
}

export default VeaxExecutionService;
