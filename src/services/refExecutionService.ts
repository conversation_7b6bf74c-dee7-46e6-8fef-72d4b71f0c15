/**
 * REF Finance 交易执行服务
 * 
 * 支持V1和DCL v2系统的交易执行
 * 使用near-api-js和私钥进行签名
 */

import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import { QuoteResult } from '../types';

// 交易结果接口
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  outputAmount?: string;
}

// V1交易参数接口
interface V1SwapAction {
  pool_id: number;
  token_in: string;
  token_out: string;
  amount_in?: string;
  amount_out: string;
  min_amount_out: string;
}

// DCL v2交易参数接口
interface DCLv2SwapParams {
  pool_ids: string[];
  output_token: string;
  min_output_amount: string;
  skip_unwrap_near: boolean;
}

/**
 * REF Finance 交易执行服务
 */
export class RefExecutionService {
  private near: Near | null = null;
  private account: Account | null = null;

  constructor(
    private accountId: string,
    private privateKey: string,
    private networkId: string = 'mainnet'
  ) {}

  /**
   * 初始化NEAR连接
   */
  async initialize(): Promise<void> {
    try {
      const keyStore = new keyStores.InMemoryKeyStore();
      const keyPair = utils.KeyPair.fromString(this.privateKey as any);
      await keyStore.setKey(this.networkId, this.accountId, keyPair);

      const config = {
        networkId: this.networkId,
        keyStore,
        nodeUrl: this.networkId === 'mainnet' 
          ? 'https://rpc.mainnet.near.org'
          : 'https://rpc.testnet.near.org',
        walletUrl: this.networkId === 'mainnet'
          ? 'https://wallet.mainnet.near.org'
          : 'https://wallet.testnet.near.org',
        helperUrl: this.networkId === 'mainnet'
          ? 'https://helper.mainnet.near.org'
          : 'https://helper.testnet.near.org',
      };

      this.near = await connect(config);
      this.account = await this.near.account(this.accountId);
      
      console.log(`✅ REF执行服务初始化成功: ${this.accountId}`);
    } catch (error) {
      console.error('❌ REF执行服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 执行V1系统交易
   */
  async executeV1Swap(
    quoteResult: QuoteResult,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'V1' || !quoteResult.rawResponse) {
      throw new Error('无效的V1报价结果');
    }

    try {
      console.log(`🚀 开始执行V1交易: ${inputAmount} → ${minOutputAmount}`);

      // 从Smart Router响应构建交易参数
      const swapActions = this.buildV1SwapActions(
        quoteResult.rawResponse.result_data,
        inputAmount,
        minOutputAmount,
        slippage
      );

      const msg = {
        force: 0,
        actions: swapActions,
        skip_unwrap_near: false
      };

      // 执行ft_transfer_call
      const result = await this.account.functionCall({
        contractId: 'v2.ref-finance.near',
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'v2.ref-finance.near',
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('1'), // 1 yoctoNEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`✅ V1交易成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: quoteResult.outputAmount
      };

    } catch (error: any) {
      console.error('❌ V1交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 执行DCL v2系统交易
   */
  async executeDCLv2Swap(
    quoteResult: QuoteResult,
    inputAmount: string,
    minOutputAmount: string,
    poolId: string,
    outputToken: string
  ): Promise<TransactionResult> {
    if (!this.account) {
      throw new Error('服务未初始化，请先调用initialize()');
    }

    if (quoteResult.system !== 'DCL_V2') {
      throw new Error('无效的DCL v2报价结果');
    }

    try {
      console.log(`🚀 开始执行DCL v2交易: ${inputAmount} → ${minOutputAmount}`);

      const swapParams: DCLv2SwapParams = {
        pool_ids: [poolId],
        output_token: outputToken,
        min_output_amount: minOutputAmount,
        skip_unwrap_near: true
      };

      const msg = {
        Swap: swapParams
      };

      // 执行ft_transfer_call到DCL v2合约
      const result = await this.account.functionCall({
        contractId: 'dclv2.ref-labs.near',
        methodName: 'ft_transfer_call',
        args: {
          receiver_id: 'dclv2.ref-labs.near',
          amount: inputAmount,
          msg: JSON.stringify(msg)
        },
        attachedDeposit: BigInt('200000000000000000000000000'), // 0.2 NEAR
        gas: BigInt('***************') // 300 TGas
      });

      console.log(`✅ DCL v2交易成功: ${result.transaction.hash}`);

      return {
        success: true,
        transactionHash: result.transaction.hash,
        outputAmount: quoteResult.outputAmount
      };

    } catch (error: any) {
      console.error('❌ DCL v2交易失败:', error);
      return {
        success: false,
        error: error.message || '交易执行失败'
      };
    }
  }

  /**
   * 构建V1交易动作
   */
  private buildV1SwapActions(
    routeData: any,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number
  ): V1SwapAction[] {
    const routes = routeData.routes || [];
    if (routes.length === 0) {
      throw new Error('没有可用的交易路径');
    }

    const actions: V1SwapAction[] = [];
    const route = routes[0]; // 使用第一条路径

    route.pools.forEach((pool: any, index: number) => {
      const action: V1SwapAction = {
        pool_id: pool.pool_id,
        token_in: pool.token_in,
        token_out: pool.token_out,
        amount_out: "0",
        min_amount_out: index === route.pools.length - 1 ? minOutputAmount : "0"
      };

      // 只有第一个池子需要设置amount_in
      if (index === 0) {
        action.amount_in = pool.amount_in || inputAmount;
      }

      actions.push(action);
    });

    return actions;
  }

  /**
   * 统一执行接口
   */
  async executeSwap(
    quoteResult: QuoteResult,
    inputAmount: string,
    minOutputAmount: string,
    slippage: number = 0.005,
    additionalParams?: any
  ): Promise<TransactionResult> {
    if (quoteResult.system === 'V1') {
      return this.executeV1Swap(quoteResult, inputAmount, minOutputAmount, slippage);
    } else if (quoteResult.system === 'DCL_V2') {
      if (!additionalParams?.poolId || !additionalParams?.outputToken) {
        throw new Error('DCL v2交易需要poolId和outputToken参数');
      }
      return this.executeDCLv2Swap(
        quoteResult,
        inputAmount,
        minOutputAmount,
        additionalParams.poolId,
        additionalParams.outputToken
      );
    } else {
      throw new Error(`不支持的交易系统: ${quoteResult.system}`);
    }
  }
}

export default RefExecutionService;
