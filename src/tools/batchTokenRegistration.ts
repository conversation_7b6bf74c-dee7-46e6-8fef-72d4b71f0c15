/**
 * 批量代币注册工具
 * 
 * 为所有配置的交易对代币进行批量注册
 * 包括代币合约注册和VEAX合约注册
 */

import 'dotenv/config';
import VeaxExecutionService from '../services/veaxExecutionService';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import { TOKENS } from '../config/tradingPairs';

/**
 * 批量注册所有代币
 */
async function batchRegisterAllTokens() {
  console.log('🚀 批量代币注册工具');
  console.log('='.repeat(60));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    return;
  }

  console.log(`✅ 环境变量配置正确`);
  console.log(`📋 账户: ${EXECUTION_CONFIG.ACCOUNT_ID}`);
  console.log(`🌐 网络: ${EXECUTION_CONFIG.NETWORK_ID}`);

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 初始化服务
    console.log('\n1️⃣ 初始化VEAX服务...');
    await veaxExecution.initialize();
    console.log('✅ 服务初始化成功');

    // 检查用户注册状态
    console.log('\n2️⃣ 检查用户VEAX注册状态...');
    const userStatus = await veaxExecution.checkUserRegistration();
    
    if (!userStatus.isRegistered) {
      console.log('❌ 用户未在VEAX注册，正在注册...');
      const registerResult = await veaxExecution.registerUser();
      if (!registerResult.success) {
        console.error(`❌ 用户注册失败: ${registerResult.error}`);
        return;
      }
      console.log(`✅ 用户注册成功: ${registerResult.transactionHash}`);
    } else {
      console.log('✅ 用户已在VEAX注册');
      if (userStatus.storageBalance) {
        console.log(`💰 存储余额: ${userStatus.storageBalance.available} / ${userStatus.storageBalance.total}`);
      }
    }

    // 获取所有代币列表
    const allTokens = Object.values(TOKENS);
    console.log(`\n3️⃣ 准备注册 ${allTokens.length} 个代币...`);
    
    // 显示代币列表
    console.log('\n📋 代币列表:');
    allTokens.forEach((token, index) => {
      console.log(`   ${index + 1}. ${token.symbol} (${token.name})`);
      console.log(`      地址: ${token.id}`);
    });

    console.log('\n⚠️ 这将执行真实的注册交易！');
    console.log('💡 如果您不想继续，请按 Ctrl+C 退出');
    
    // 等待3秒给用户取消的机会
    console.log('倒计时: 3...');
    await sleep(1000);
    console.log('倒计时: 2...');
    await sleep(1000);
    console.log('倒计时: 1...');
    await sleep(1000);

    // 开始批量注册
    console.log('\n4️⃣ 开始批量注册...');
    
    let successCount = 0;
    let failCount = 0;
    const results: Array<{token: string, status: string, hash?: string, error?: string}> = [];

    for (let i = 0; i < allTokens.length; i++) {
      const token = allTokens[i];
      console.log(`\n🪙 [${i + 1}/${allTokens.length}] 处理 ${token.symbol}:`);

      try {
        // 检查代币合约注册
        const tokenStatus = await veaxExecution.checkTokenRegistration(token.id);
        
        if (!tokenStatus.isRegistered) {
          console.log(`   🔄 在代币合约中注册 ${token.symbol}...`);
          const registerResult = await veaxExecution.registerToken(token.id);
          
          if (registerResult.success) {
            console.log(`   ✅ 代币合约注册成功: ${registerResult.transactionHash}`);
          } else {
            console.log(`   ❌ 代币合约注册失败: ${registerResult.error}`);
            results.push({
              token: token.symbol,
              status: 'failed_token_contract',
              error: registerResult.error
            });
            failCount++;
            continue;
          }
        } else {
          console.log(`   ✅ ${token.symbol} 已在代币合约注册`);
        }

        // 检查VEAX合约注册
        const veaxTokenStatus = await veaxExecution.checkTokenRegistrationInVeax(token.id);
        
        if (!veaxTokenStatus.isRegistered) {
          console.log(`   🔄 在VEAX合约中注册 ${token.symbol}...`);
          const registerResult = await veaxExecution.registerTokensInVeax([token.id]);
          
          if (registerResult.success) {
            console.log(`   ✅ VEAX合约注册成功: ${registerResult.transactionHash}`);
            results.push({
              token: token.symbol,
              status: 'success',
              hash: registerResult.transactionHash
            });
            successCount++;
          } else {
            console.log(`   ❌ VEAX合约注册失败: ${registerResult.error}`);
            results.push({
              token: token.symbol,
              status: 'failed_veax_contract',
              error: registerResult.error
            });
            failCount++;
          }
        } else {
          console.log(`   ✅ ${token.symbol} 已在VEAX合约注册`);
          results.push({
            token: token.symbol,
            status: 'already_registered'
          });
          successCount++;
        }

        // 添加延迟避免过于频繁的请求
        if (i < allTokens.length - 1) {
          console.log('   ⏳ 等待2秒...');
          await sleep(2000);
        }

      } catch (error) {
        console.error(`   ❌ 处理 ${token.symbol} 时发生错误:`, error);
        results.push({
          token: token.symbol,
          status: 'error',
          error: error instanceof Error ? error.message : String(error)
        });
        failCount++;
      }
    }

    // 显示最终结果
    console.log('\n' + '='.repeat(60));
    console.log('📊 批量注册完成！');
    console.log(`✅ 成功: ${successCount} 个代币`);
    console.log(`❌ 失败: ${failCount} 个代币`);
    console.log(`📋 总计: ${allTokens.length} 个代币`);

    // 详细结果
    console.log('\n📋 详细结果:');
    results.forEach(result => {
      const statusIcon = result.status === 'success' || result.status === 'already_registered' ? '✅' : '❌';
      console.log(`${statusIcon} ${result.token}: ${result.status}`);
      if (result.hash) {
        console.log(`   🔗 交易哈希: ${result.hash}`);
      }
      if (result.error) {
        console.log(`   ❌ 错误: ${result.error}`);
      }
    });

    if (successCount === allTokens.length) {
      console.log('\n🎉 所有代币注册成功！现在可以进行套利交易了。');
    } else if (successCount > 0) {
      console.log('\n⚠️ 部分代币注册成功，可以使用已注册的代币进行交易。');
    } else {
      console.log('\n❌ 所有代币注册失败，请检查错误信息并重试。');
    }

  } catch (error) {
    console.error('\n❌ 批量注册过程中发生错误:', error);
    
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
  }
}

/**
 * 睡眠函数
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行批量注册
if (require.main === module) {
  batchRegisterAllTokens().catch(console.error);
}

export { batchRegisterAllTokens };
