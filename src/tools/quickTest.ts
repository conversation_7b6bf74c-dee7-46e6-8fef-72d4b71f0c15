import { testTradingPair, testMultipleAmounts } from './customQuoteTester';

/**
 * 快速测试脚本 - 预设一些常见的测试用例
 */

async function quickTests() {
  console.log('🚀 REF Finance 快速测试套件\n');

  // 测试 1: NEAR → USDT 不同金额
  console.log('📊 测试 1: NEAR → USDT 价格趋势');
  await testMultipleAmounts('NEAR', 'USDT', ['1', '10', '100', '1000']);

  // 测试 2: USDT → NEAR 反向测试
  console.log('\n📊 测试 2: USDT → NEAR 反向测试');
  await testMultipleAmounts('USDT', 'NEAR', ['10', '100', '1000', '5000']);

  // 测试 3: 稳定币对
  console.log('\n📊 测试 3: 稳定币对 USDT → USDC');
  await testTradingPair('USDT', 'USDC', '1000');

  // 测试 4: ETH 相关
  console.log('\n📊 测试 4: WETH → NEAR');
  await testTradingPair('WETH', 'NEAR', '1');

  // 测试 5: REF 代币
  console.log('\n📊 测试 5: REF → USDT');
  await testTradingPair('REF', 'USDT', '1000');

  console.log('\n✅ 快速测试完成！');
}

if (require.main === module) {
  quickTests().catch(console.error);
}
