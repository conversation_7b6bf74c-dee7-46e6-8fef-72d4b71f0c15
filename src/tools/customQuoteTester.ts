import { refQuoteService } from '../services/refQuoteService';
import { TokenMetadata, QuoteParams } from '../types';
import { COMMON_TOKENS } from '../config';

/**
 * 自定义交易对测试工具
 */

// 扩展的代币列表
const EXTENDED_TOKENS: Record<string, TokenMetadata> = {
  // 主要代币
  NEAR: {
    id: 'wrap.near',
    name: 'Wrapped NEAR',
    symbol: 'NEAR',
    decimals: 24
  },
  USDT: {
    id: 'usdt.tether-token.near',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6
  },
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    name: 'USD Coin',
    symbol: 'USDC',
    decimals: 6
  },
  REF: {
    id: 'token.v2.ref-finance.near',
    name: 'Ref Finance Token',
    symbol: 'REF',
    decimals: 18
  },
  AURORA: {
    id: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
    name: '<PERSON>',
    symbol: 'AURORA',
    decimals: 18
  },
  // 更多代币
  WETH: {
    id: 'c02aaa39b223fe8d0a0e5c4f27ead9083c756cc2.factory.bridge.near',
    name: 'Wrapped Ethereum',
    symbol: 'WETH',
    decimals: 18
  },
  WBTC: {
    id: '2260fac5e5542a773aa44fbcfedf7c193bc2c599.factory.bridge.near',
    name: 'Wrapped Bitcoin',
    symbol: 'WBTC',
    decimals: 8
  },
  DAI: {
    id: '6b175474e89094c44da98b954eedeac495271d0f.factory.bridge.near',
    name: 'Dai Stablecoin',
    symbol: 'DAI',
    decimals: 18
  },
  USDD: {
    id: 'usdd.tether-token.near',
    name: 'Decentralized USD',
    symbol: 'USDD',
    decimals: 18
  },
  FRAX: {
    id: '853d955acef822db058eb8505911ed77f175b99e.factory.bridge.near',
    name: 'Frax',
    symbol: 'FRAX',
    decimals: 18
  }
};

/**
 * 显示可用代币列表
 */
function showAvailableTokens(): void {
  console.log('\n📋 可用代币列表:');
  console.log('┌─────────┬──────────────────────────────────────────────────────────┐');
  console.log('│ 符号    │ 名称                                                     │');
  console.log('├─────────┼──────────────────────────────────────────────────────────┤');
  
  Object.entries(EXTENDED_TOKENS).forEach(([symbol, token]) => {
    console.log(`│ ${symbol.padEnd(7)} │ ${token.name.padEnd(56)} │`);
  });
  
  console.log('└─────────┴──────────────────────────────────────────────────────────┘');
}

/**
 * 测试单个交易对
 */
async function testTradingPair(
  tokenInSymbol: string,
  tokenOutSymbol: string,
  amount: string,
  slippage: number = 0.005
): Promise<void> {
  const tokenIn = EXTENDED_TOKENS[tokenInSymbol.toUpperCase()];
  const tokenOut = EXTENDED_TOKENS[tokenOutSymbol.toUpperCase()];

  if (!tokenIn) {
    throw new Error(`不支持的输入代币: ${tokenInSymbol}`);
  }
  
  if (!tokenOut) {
    throw new Error(`不支持的输出代币: ${tokenOutSymbol}`);
  }

  if (tokenIn.id === tokenOut.id) {
    throw new Error('输入和输出代币不能相同');
  }

  console.log('\n' + '='.repeat(80));
  console.log(`🔍 测试交易对: ${amount} ${tokenIn.symbol} → ${tokenOut.symbol}`);
  console.log(`💧 滑点容忍度: ${(slippage * 100).toFixed(2)}%`);
  console.log('='.repeat(80));

  try {
    const params: QuoteParams = {
      tokenIn,
      tokenOut,
      amountIn: amount,
      slippage
    };

    const quote = await refQuoteService.getQuote(params);
    
    console.log('\n📊 报价详情:');
    const details = refQuoteService.getQuoteDetails(quote);
    
    // 格式化输出
    console.log('┌─────────────────────────────────────────────────────────────────┐');
    console.log(`│ 🏆 最佳系统: ${details.system.padEnd(10)} 合约: ${details.contractId.substring(0, 20)}... │`);
    console.log(`│ 💰 输入金额: ${details.inputAmount.padEnd(15)} ${tokenIn.symbol.padEnd(8)} │`);
    console.log(`│ 💎 输出金额: ${details.outputAmount.padEnd(15)} ${tokenOut.symbol.padEnd(8)} │`);
    console.log(`│ 📈 价格影响: ${details.priceImpact.padEnd(15)} │`);
    console.log(`│ 🛣️  路径信息: ${details.route.substring(0, 45).padEnd(45)} │`);
    console.log('└─────────────────────────────────────────────────────────────────┘');

    // 计算价格
    const price = parseFloat(details.outputAmount) / parseFloat(details.inputAmount);
    console.log(`\n💱 交换价格: 1 ${tokenIn.symbol} = ${price.toFixed(6)} ${tokenOut.symbol}`);

  } catch (error: any) {
    console.error(`❌ 测试失败: ${error.message}`);
  }
}

/**
 * 批量测试多个金额
 */
async function testMultipleAmounts(
  tokenInSymbol: string,
  tokenOutSymbol: string,
  amounts: string[],
  slippage: number = 0.005
): Promise<void> {
  console.log('\n' + '='.repeat(80));
  console.log(`📊 批量测试: ${tokenInSymbol} → ${tokenOutSymbol}`);
  console.log('='.repeat(80));

  const results: Array<{
    amount: string;
    outputAmount: string;
    system: string;
    price: number;
  }> = [];

  for (const amount of amounts) {
    try {
      const tokenIn = EXTENDED_TOKENS[tokenInSymbol.toUpperCase()];
      const tokenOut = EXTENDED_TOKENS[tokenOutSymbol.toUpperCase()];

      const quote = await refQuoteService.getQuote({
        tokenIn,
        tokenOut,
        amountIn: amount,
        slippage
      });

      const details = refQuoteService.getQuoteDetails(quote);
      const price = parseFloat(details.outputAmount) / parseFloat(amount);

      results.push({
        amount,
        outputAmount: details.outputAmount,
        system: details.system,
        price
      });

      console.log(`✅ ${amount.padEnd(10)} ${tokenInSymbol} → ${details.outputAmount.padEnd(15)} ${tokenOutSymbol} (${details.system})`);

    } catch (error: any) {
      console.log(`❌ ${amount.padEnd(10)} ${tokenInSymbol} → 失败: ${error.message}`);
    }
  }

  // 显示价格趋势
  if (results.length > 1) {
    console.log('\n📈 价格趋势分析:');
    console.log('┌─────────────┬─────────────────┬─────────┬──────────────────┐');
    console.log('│ 金额        │ 单价            │ 系统    │ 价格变化         │');
    console.log('├─────────────┼─────────────────┼─────────┼──────────────────┤');
    
    results.forEach((result, index) => {
      const priceChange = index > 0 
        ? ((result.price - results[0].price) / results[0].price * 100).toFixed(4) + '%'
        : 'baseline';
      
      console.log(`│ ${result.amount.padEnd(11)} │ ${result.price.toFixed(6).padEnd(15)} │ ${result.system.padEnd(7)} │ ${priceChange.padEnd(16)} │`);
    });
    
    console.log('└─────────────┴─────────────────┴─────────┴──────────────────┘');
  }
}

/**
 * 交互式测试
 */
async function interactiveTest(): Promise<void> {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt: string): Promise<string> => {
    return new Promise((resolve) => {
      rl.question(prompt, resolve);
    });
  };

  try {
    console.log('🎯 REF Finance 自定义交易对测试工具');
    showAvailableTokens();

    while (true) {
      console.log('\n' + '='.repeat(60));
      console.log('选择测试模式:');
      console.log('1. 单次测试');
      console.log('2. 批量金额测试');
      console.log('3. 显示代币列表');
      console.log('4. 退出');
      
      const mode = await question('\n请选择 (1-4): ');

      if (mode === '4') {
        console.log('👋 再见！');
        break;
      }

      if (mode === '3') {
        showAvailableTokens();
        continue;
      }

      const tokenIn = await question('输入代币符号 (如 NEAR): ');
      const tokenOut = await question('输出代币符号 (如 USDT): ');

      if (mode === '1') {
        const amount = await question('输入金额: ');
        const slippageInput = await question('滑点容忍度 % (默认 0.5): ');
        const slippage = slippageInput ? parseFloat(slippageInput) / 100 : 0.005;

        await testTradingPair(tokenIn, tokenOut, amount, slippage);

      } else if (mode === '2') {
        const amountsInput = await question('输入测试金额 (用逗号分隔，如: 1,10,100,1000): ');
        const amounts = amountsInput.split(',').map(a => a.trim());
        const slippageInput = await question('滑点容忍度 % (默认 0.5): ');
        const slippage = slippageInput ? parseFloat(slippageInput) / 100 : 0.005;

        await testMultipleAmounts(tokenIn, tokenOut, amounts, slippage);
      }

      const continueTest = await question('\n继续测试? (y/n): ');
      if (continueTest.toLowerCase() !== 'y') {
        console.log('👋 测试完成！');
        break;
      }
    }

  } catch (error: any) {
    console.error('❌ 测试过程中出错:', error.message);
  } finally {
    rl.close();
  }
}

/**
 * 命令行参数测试
 */
async function cliTest(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.length < 3) {
    console.log('📖 使用方法:');
    console.log('  npm run test:custom <输入代币> <输出代币> <金额> [滑点%]');
    console.log('');
    console.log('📝 示例:');
    console.log('  npm run test:custom NEAR USDT 1000');
    console.log('  npm run test:custom USDC NEAR 100 0.5');
    console.log('');
    showAvailableTokens();
    return;
  }

  const [tokenIn, tokenOut, amount, slippageStr] = args;
  const slippage = slippageStr ? parseFloat(slippageStr) / 100 : 0.005;

  await testTradingPair(tokenIn, tokenOut, amount, slippage);
}

// 主函数
async function main(): Promise<void> {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    await interactiveTest();
  } else {
    await cliTest();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { testTradingPair, testMultipleAmounts, EXTENDED_TOKENS };
