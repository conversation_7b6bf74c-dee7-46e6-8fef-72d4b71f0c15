/**
 * 检查账户余额和DCL v2交易成本分析
 */

import 'dotenv/config';
import { Account, connect, keyStores, Near, utils } from 'near-api-js';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 检查账户余额
 */
async function checkAccountBalance() {
  console.log('💰 账户余额检查');
  console.log('='.repeat(50));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量');
    return;
  }

  try {
    // 初始化NEAR连接
    const keyStore = new keyStores.InMemoryKeyStore();
    const keyPair = utils.KeyPair.fromString(EXECUTION_CONFIG.PRIVATE_KEY as any);
    await keyStore.setKey(EXECUTION_CONFIG.NETWORK_ID, EXECUTION_CONFIG.ACCOUNT_ID, keyPair);

    const config = {
      networkId: EXECUTION_CONFIG.NETWORK_ID,
      keyStore,
      nodeUrl: EXECUTION_CONFIG.NETWORK_ID === 'mainnet' 
        ? 'https://rpc.mainnet.near.org'
        : 'https://rpc.testnet.near.org',
      walletUrl: EXECUTION_CONFIG.NETWORK_ID === 'mainnet'
        ? 'https://wallet.mainnet.near.org'
        : 'https://wallet.testnet.near.org',
      helperUrl: EXECUTION_CONFIG.NETWORK_ID === 'mainnet'
        ? 'https://helper.mainnet.near.org'
        : 'https://helper.testnet.near.org',
    };

    const near = await connect(config);
    const account = await near.account(EXECUTION_CONFIG.ACCOUNT_ID);

    // 1. 检查NEAR余额
    console.log('\n1️⃣ NEAR账户余额:');
    const accountState = await account.state();
    const balanceNear = new Big(accountState.amount).div(new Big(10).pow(24));
    const storageNear = new Big(accountState.storage_usage).times(new Big(10).pow(19)).div(new Big(10).pow(24));
    const availableNear = balanceNear.minus(storageNear);

    console.log(`   总余额: ${balanceNear.toString()} NEAR`);
    console.log(`   存储费用: ${storageNear.toString()} NEAR`);
    console.log(`   可用余额: ${availableNear.toString()} NEAR`);

    // 2. 分析DCL v2交易成本
    console.log('\n2️⃣ DCL v2交易成本分析:');
    
    const tradingAmount = '0.1'; // NEAR
    const tradingAmountWei = new Big(tradingAmount).times(new Big(10).pow(24));
    const attachedDeposit = new Big('200000000000000000000000000'); // 0.2 NEAR
    const estimatedGas = new Big('***************'); // 300 TGas
    const gasPrice = new Big('*********'); // 估计的gas价格 (100 Ggas)
    const estimatedGasCost = estimatedGas.times(gasPrice).div(new Big(10).pow(24));

    console.log(`   交易金额: ${tradingAmount} NEAR`);
    console.log(`   附加存款: ${attachedDeposit.div(new Big(10).pow(24)).toString()} NEAR`);
    console.log(`   估计Gas费: ${estimatedGasCost.toString()} NEAR`);
    
    const totalCost = tradingAmountWei.plus(attachedDeposit).plus(estimatedGasCost.times(new Big(10).pow(24)));
    const totalCostNear = totalCost.div(new Big(10).pow(24));
    
    console.log(`   总成本: ${totalCostNear.toString()} NEAR`);
    console.log(`   账户余额: ${balanceNear.toString()} NEAR`);
    console.log(`   余额充足: ${balanceNear.gte(totalCostNear) ? '✅ 是' : '❌ 否'}`);

    if (!balanceNear.gte(totalCostNear)) {
      const shortage = totalCostNear.minus(balanceNear);
      console.log(`   缺少: ${shortage.toString()} NEAR`);
    }

    // 3. 检查代币余额
    console.log('\n3️⃣ 代币余额检查:');
    
    try {
      // 检查NEAR代币余额
      const nearBalance = await account.viewFunction({
        contractId: 'wrap.near',
        methodName: 'ft_balance_of',
        args: { account_id: EXECUTION_CONFIG.ACCOUNT_ID }
      });
      
      const nearBalanceFormatted = new Big(nearBalance).div(new Big(10).pow(24));
      console.log(`   wNEAR余额: ${nearBalanceFormatted.toString()} NEAR`);
      console.log(`   交易金额: ${tradingAmount} NEAR`);
      console.log(`   wNEAR充足: ${nearBalanceFormatted.gte(new Big(tradingAmount)) ? '✅ 是' : '❌ 否'}`);
      
    } catch (error) {
      console.log(`   ❌ 无法获取wNEAR余额: ${error}`);
    }

    // 4. 分析错误信息
    console.log('\n4️⃣ 错误分析:');
    console.log('   错误信息: "Sender does not have enough balance 2.24562634086935922451537 for operation costing 200.188100107584189326618148"');
    
    const reportedBalance = new Big('2245626340869359224515370').div(new Big(10).pow(24));
    const reportedCost = new Big('200188100107584189326618148').div(new Big(10).pow(24));
    
    console.log(`   报告余额: ${reportedBalance.toString()} NEAR`);
    console.log(`   报告成本: ${reportedCost.toString()} NEAR`);
    console.log(`   成本异常: ${reportedCost.gt(new Big(1)) ? '✅ 是，成本过高' : '❌ 否'}`);

    // 5. 建议
    console.log('\n5️⃣ 建议:');
    if (reportedCost.gt(new Big(1))) {
      console.log('   ❌ DCL v2交易成本异常高 (>200 NEAR)');
      console.log('   💡 可能的原因:');
      console.log('      1. attachedDeposit设置错误');
      console.log('      2. Gas估算错误');
      console.log('      3. 交易参数错误');
      console.log('   🔧 建议:');
      console.log('      1. 降低attachedDeposit到1 yoctoNEAR');
      console.log('      2. 检查交易消息格式');
      console.log('      3. 使用V1系统进行测试');
    }

    if (!balanceNear.gte(new Big(1))) {
      console.log('   ❌ 账户余额不足');
      console.log('   💡 建议充值至少1 NEAR用于测试');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  }
}

/**
 * 分析DCL v2参数问题
 */
function analyzeDCLv2Issue() {
  console.log('\n🔍 DCL v2参数问题分析:');
  console.log('='.repeat(50));
  
  console.log('\n问题: attachedDeposit过高');
  console.log('当前设置: 200000000000000000000000000 (200 NEAR)');
  console.log('建议设置: 1 (1 yoctoNEAR)');
  
  console.log('\n修复建议:');
  console.log('```typescript');
  console.log('// ❌ 当前错误设置');
  console.log('attachedDeposit: BigInt("200000000000000000000000000"), // 200 NEAR');
  console.log('');
  console.log('// ✅ 正确设置');
  console.log('attachedDeposit: BigInt("1"), // 1 yoctoNEAR');
  console.log('```');
}

// 运行检查
if (require.main === module) {
  Promise.all([
    checkAccountBalance(),
    analyzeDCLv2Issue()
  ]).catch(console.error);
}

export { checkAccountBalance, analyzeDCLv2Issue };
