/**
 * V1系统高滑点测试 - 解决多跳交易问题
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { v1SmartRouter } from '../services/v1SmartRouter';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

/**
 * V1系统高滑点测试
 */
async function testV1HighSlippage() {
  console.log('🚀 V1系统高滑点测试 - 解决多跳交易问题');
  console.log('='.repeat(70));

  // 验证环境变量
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 环境变量配置不完整');
    return;
  }

  const refExecution = new RefExecutionServiceFixed(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化
    console.log('\n1️⃣ 初始化服务...');
    await refExecution.initialize();

    // 2. 获取V1报价
    console.log('\n2️⃣ 获取V1报价...');
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.01', // 🔧 减少交易金额到0.01 NEAR
      slippage: 0.005
    };

    const v1Quote = await v1SmartRouter.getV1Quote(quoteParams);
    
    if (!v1Quote) {
      console.error('❌ V1报价失败');
      return;
    }
    
    console.log(`📊 V1报价: ${v1Quote.outputAmount} USDC`);
    console.log(`🔧 系统: ${v1Quote.system}`);

    if (!v1Quote.rawResponse) {
      console.error('❌ V1报价没有rawResponse');
      return;
    }

    // 3. 分析交易路径
    console.log('\n3️⃣ 分析交易路径...');
    const routeData = v1Quote.rawResponse.result_data;
    const route = routeData.routes[0];
    
    console.log(`📋 交易路径分析:`);
    route.pools.forEach((pool: any, index: number) => {
      const tokenInSymbol = pool.token_in === 'wrap.near' ? 'NEAR' : 
                           pool.token_in.includes('usdt') ? 'USDT' : 
                           pool.token_in.includes('17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1') ? 'USDC' : 'Unknown';
      const tokenOutSymbol = pool.token_out === 'wrap.near' ? 'NEAR' : 
                            pool.token_out.includes('usdt') ? 'USDT' : 
                            pool.token_out.includes('17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1') ? 'USDC' : 'Unknown';
      
      console.log(`   步骤${index + 1}: ${tokenInSymbol} → ${tokenOutSymbol} (池子${pool.pool_id})`);
      if (pool.amount_in) {
        console.log(`           输入: ${pool.amount_in} wei`);
      }
    });

    // 4. 使用高滑点设置
    console.log('\n4️⃣ 使用高滑点设置...');
    const inputAmount = toWei('0.01', TOKENS.NEAR.decimals);
    const expectedOutputUsdc = parseFloat(v1Quote.outputAmount);
    
    // 🔧 关键修复：使用20%滑点应对多跳交易
    const slippagePercent = 20; // 20% 滑点
    const slippage = slippagePercent / 100;
    const minOutputUsdc = expectedOutputUsdc * (1 - slippage);
    const minOutputAmountWei = toWei(minOutputUsdc.toString(), TOKENS.USDC.decimals);

    console.log(`📊 输入金额: 0.01 NEAR (${inputAmount} wei)`);
    console.log(`📊 预期输出: ${expectedOutputUsdc} USDC`);
    console.log(`📊 滑点设置: ${slippagePercent}% (应对多跳交易)`);
    console.log(`📊 最小输出: ${minOutputUsdc.toFixed(6)} USDC (${minOutputAmountWei} wei)`);

    // 5. 对比不同滑点设置
    console.log('\n5️⃣ 滑点设置对比...');
    const slippageComparison = [
      { name: '之前失败', percent: 5, value: expectedOutputUsdc * 0.95 },
      { name: '当前设置', percent: 20, value: expectedOutputUsdc * 0.8 },
      { name: '极端保守', percent: 30, value: expectedOutputUsdc * 0.7 }
    ];

    slippageComparison.forEach(setting => {
      const wei = toWei(setting.value.toString(), TOKENS.USDC.decimals);
      console.log(`   ${setting.name}: ${setting.percent}% → ${setting.value.toFixed(6)} USDC (${wei} wei)`);
    });

    // 6. 执行交易
    console.log('\n6️⃣ 执行交易...');
    
    if (!EXECUTION_CONFIG.SAFETY.ENABLE_REAL_TRADING) {
      console.log('🔒 安全模式 - 模拟执行');
      console.log('💡 要执行真实交易，请设置 ENABLE_REAL_TRADING=true');
      
      console.log('\n📋 将要执行的交易参数:');
      console.log(`   输入: ${inputAmount} wei NEAR (0.01 NEAR)`);
      console.log(`   最小输出: ${minOutputAmountWei} wei USDC`);
      console.log(`   滑点: ${slippagePercent}% (高滑点应对多跳)`);
      
      return;
    }

    // 真实交易执行
    console.log('🚀 执行真实V1交易（高滑点设置）...');
    const executionResult = await refExecution.executeV1Swap(
      v1Quote,
      TOKENS.NEAR.id,
      inputAmount,
      minOutputAmountWei,
      slippage
    );

    // 7. 分析结果
    console.log('\n7️⃣ 交易结果分析...');
    
    if (executionResult.success) {
      console.log('🎉 V1交易执行成功!');
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 预期输出: ${v1Quote.outputAmount} USDC`);
      
      console.log('\n✅ 高滑点策略验证:');
      console.log('   ✅ 20%滑点成功应对多跳交易');
      console.log('   ✅ 小额交易减少价格影响');
      console.log('   ✅ 复合滑点问题解决');
      console.log('   ✅ V1系统完全可用');

      console.log('\n🎯 生产环境建议:');
      console.log('   - 小额交易: 10-15% 滑点');
      console.log('   - 中额交易: 15-20% 滑点');
      console.log('   - 大额交易: 20-25% 滑点');
      console.log('   - 多跳路径: 额外增加5-10%');

    } else {
      console.log('❌ V1交易执行失败');
      console.log(`🔍 错误信息: ${executionResult.error}`);
      
      if (executionResult.error?.includes('E68: slippage error')) {
        console.log('⚠️ 仍然是滑点错误，建议:');
        console.log('   1. 进一步增加滑点到 30%');
        console.log('   2. 使用更小的交易金额 (0.001 NEAR)');
        console.log('   3. 等待市场流动性改善');
        console.log('   4. 考虑使用单跳路径');
      } else {
        console.log('✅ 滑点问题已修复');
        console.log('🔧 可能是其他问题（余额、权限等）');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 多跳交易滑点理论分析
 */
function analyzeMultiHopSlippage() {
  console.log('\n📚 多跳交易滑点理论分析');
  console.log('='.repeat(50));
  
  console.log('\n🔬 滑点累积公式:');
  console.log('单跳滑点: S1, S2');
  console.log('总滑点: 1 - (1-S1) × (1-S2)');
  console.log('');
  console.log('示例计算:');
  console.log('第一跳滑点: 2% (NEAR → USDT)');
  console.log('第二跳滑点: 4% (USDT → USDC)');
  console.log('总滑点: 1 - (1-0.02) × (1-0.04) = 1 - 0.98 × 0.96 = 5.92%');
  console.log('');
  console.log('💡 这解释了为什么5%滑点不够！');
  
  console.log('\n🎯 滑点设置策略:');
  console.log('保守估计: 总滑点 = 单跳滑点之和');
  console.log('NEAR → USDT: 3%');
  console.log('USDT → USDC: 5%');
  console.log('建议总滑点: 8% + 安全边际 = 15-20%');
}

// 运行测试
if (require.main === module) {
  Promise.all([
    testV1HighSlippage(),
    analyzeMultiHopSlippage()
  ]).catch(console.error);
}

export { testV1HighSlippage };
