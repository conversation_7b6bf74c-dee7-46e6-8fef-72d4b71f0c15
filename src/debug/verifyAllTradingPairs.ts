/**
 * 验证VEAX-REF之间所有交易对的报价检测
 * 
 * 全面测试套利监控系统中的报价获取逻辑
 */

import 'dotenv/config';
import { refQuoteService } from '../services/refQuoteService';
import { VeaxQuoteService } from '../services/veaxQuoteService';
import { tradingPairManager } from '../config/tradingPairs';
import { TOKENS } from '../config/tradingPairs';

interface QuoteTestResult {
  pair: string;
  direction: string;
  amount: string;
  refQuote: {
    success: boolean;
    outputAmount: string;
    system?: string;
    error?: string;
  };
  veaxQuote: {
    success: boolean;
    outputAmount: string;
    error?: string;
  };
  crossCheck: {
    refRate: number;
    veaxRate: number;
    priceDifference: number;
    percentageDiff: number;
  };
}

/**
 * 验证所有交易对的报价
 */
async function verifyAllTradingPairs() {
  console.log('🔍 验证VEAX-REF之间所有交易对的报价检测');
  console.log('='.repeat(80));

  const tradingPairs = tradingPairManager.getAllPairs();
  const results: QuoteTestResult[] = [];

  console.log(`📊 发现 ${tradingPairs.length} 个交易对配置`);
  
  for (let i = 0; i < tradingPairs.length; i++) {
    const pair = tradingPairs[i];
    console.log(`\n${i + 1}️⃣ 测试交易对: ${pair.id}`);
    console.log('-'.repeat(60));

    // 测试两个方向
    // 🔧 修复显示逻辑：正确获取交易金额
    let testAmount: string;
    if (pair.dynamicAmount?.enabled) {
      testAmount = pair.dynamicAmount.low; // 使用低档金额进行测试
    } else if (pair.tradeAmount) {
      testAmount = pair.tradeAmount;
    } else {
      testAmount = '1'; // 默认值
    }

    const directions = [
      { from: pair.tokenA, to: pair.tokenB, amount: testAmount, label: `${pair.tokenA.symbol}→${pair.tokenB.symbol}` },
      { from: pair.tokenB, to: pair.tokenA, amount: '1', label: `${pair.tokenB.symbol}→${pair.tokenA.symbol}` }
    ];

    for (const direction of directions) {
      console.log(`\n🔄 测试方向: ${direction.label} (${direction.amount})`);
      
      const result = await testSingleDirection(
        pair.id,
        direction.from,
        direction.to,
        direction.amount,
        direction.label
      );
      
      results.push(result);
      
      // 显示结果摘要
      displayQuoteResult(result);
    }
  }

  // 生成总结报告
  generateSummaryReport(results);
  
  return results;
}

/**
 * 测试单个方向的报价
 */
async function testSingleDirection(
  pairId: string,
  tokenFrom: any,
  tokenTo: any,
  amount: string,
  direction: string
): Promise<QuoteTestResult> {
  
  const result: QuoteTestResult = {
    pair: pairId,
    direction,
    amount,
    refQuote: { success: false, outputAmount: '0' },
    veaxQuote: { success: false, outputAmount: '0' },
    crossCheck: { refRate: 0, veaxRate: 0, priceDifference: 0, percentageDiff: 0 }
  };

  try {
    // 并行获取REF和VEAX报价
    const [refResult, veaxResult] = await Promise.allSettled([
      getREFQuote(tokenFrom, tokenTo, amount),
      getVEAXQuote(tokenFrom, tokenTo, amount)
    ]);

    // 处理REF报价结果
    if (refResult.status === 'fulfilled') {
      result.refQuote = refResult.value;
    } else {
      result.refQuote = {
        success: false,
        outputAmount: '0',
        error: refResult.reason?.message || 'Unknown error'
      };
    }

    // 处理VEAX报价结果
    if (veaxResult.status === 'fulfilled') {
      result.veaxQuote = veaxResult.value;
    } else {
      result.veaxQuote = {
        success: false,
        outputAmount: '0',
        error: veaxResult.reason?.message || 'Unknown error'
      };
    }

    // 交叉验证价格
    if (result.refQuote.success && result.veaxQuote.success) {
      result.crossCheck = calculatePriceDifference(
        amount,
        result.refQuote.outputAmount,
        result.veaxQuote.outputAmount
      );
    }

  } catch (error) {
    console.error(`❌ 测试失败:`, error);
  }

  return result;
}

/**
 * 获取REF报价
 */
async function getREFQuote(tokenFrom: any, tokenTo: any, amount: string) {
  try {
    const quoteParams = {
      tokenIn: tokenFrom,
      tokenOut: tokenTo,
      amountIn: amount,
      slippage: 0.005
    };

    const quote = await refQuoteService.getQuote(quoteParams);
    
    return {
      success: true,
      outputAmount: quote.outputAmount,
      system: quote.system,
      priceImpact: quote.priceImpact,
      fee: quote.fee
    };
  } catch (error: any) {
    return {
      success: false,
      outputAmount: '0',
      error: error.message
    };
  }
}

/**
 * 获取VEAX报价
 */
async function getVEAXQuote(tokenFrom: any, tokenTo: any, amount: string) {
  try {
    const result = await VeaxQuoteService.getQuote(tokenFrom.id, tokenTo.id, amount);
    return result;
  } catch (error: any) {
    return {
      success: false,
      outputAmount: '0',
      error: error.message
    };
  }
}

/**
 * 计算价格差异
 */
function calculatePriceDifference(inputAmount: string, refOutput: string, veaxOutput: string) {
  const input = parseFloat(inputAmount);
  const refOut = parseFloat(refOutput);
  const veaxOut = parseFloat(veaxOutput);
  
  const refRate = refOut / input;
  const veaxRate = veaxOut / input;
  const priceDifference = Math.abs(refOut - veaxOut);
  const percentageDiff = Math.abs((refRate - veaxRate) / Math.max(refRate, veaxRate)) * 100;
  
  return {
    refRate,
    veaxRate,
    priceDifference,
    percentageDiff
  };
}

/**
 * 显示单个报价结果
 */
function displayQuoteResult(result: QuoteTestResult) {
  console.log(`\n📋 ${result.direction} 报价结果:`);
  
  // REF结果
  if (result.refQuote.success) {
    console.log(`   ✅ REF: ${result.refQuote.outputAmount} (${result.refQuote.system || 'Unknown'})`);
  } else {
    console.log(`   ❌ REF: 失败 - ${result.refQuote.error || 'Unknown error'}`);
  }
  
  // VEAX结果
  if (result.veaxQuote.success) {
    console.log(`   ✅ VEAX: ${result.veaxQuote.outputAmount}`);
  } else {
    console.log(`   ❌ VEAX: 失败 - ${result.veaxQuote.error || 'Unknown error'}`);
  }
  
  // 价格比较
  if (result.refQuote.success && result.veaxQuote.success) {
    console.log(`   💱 汇率比较:`);
    console.log(`      REF汇率: 1 = ${result.crossCheck.refRate.toFixed(6)}`);
    console.log(`      VEAX汇率: 1 = ${result.crossCheck.veaxRate.toFixed(6)}`);
    console.log(`      价格差异: ${result.crossCheck.percentageDiff.toFixed(2)}%`);
    
    if (result.crossCheck.percentageDiff > 5) {
      console.log(`   ⚠️ 价格差异较大 (>${result.crossCheck.percentageDiff.toFixed(2)}%)`);
    }
  }
}

/**
 * 生成总结报告
 */
function generateSummaryReport(results: QuoteTestResult[]) {
  console.log('\n📊 总结报告');
  console.log('='.repeat(80));
  
  const stats = {
    total: results.length,
    refSuccess: 0,
    veaxSuccess: 0,
    bothSuccess: 0,
    bothFailed: 0,
    largePriceDiff: 0
  };
  
  const priceDiffs: number[] = [];
  
  for (const result of results) {
    if (result.refQuote.success) stats.refSuccess++;
    if (result.veaxQuote.success) stats.veaxSuccess++;
    if (result.refQuote.success && result.veaxQuote.success) {
      stats.bothSuccess++;
      priceDiffs.push(result.crossCheck.percentageDiff);
      if (result.crossCheck.percentageDiff > 5) {
        stats.largePriceDiff++;
      }
    }
    if (!result.refQuote.success && !result.veaxQuote.success) stats.bothFailed++;
  }
  
  console.log(`📈 统计数据:`);
  console.log(`   总测试数: ${stats.total}`);
  console.log(`   REF成功率: ${stats.refSuccess}/${stats.total} (${(stats.refSuccess/stats.total*100).toFixed(1)}%)`);
  console.log(`   VEAX成功率: ${stats.veaxSuccess}/${stats.total} (${(stats.veaxSuccess/stats.total*100).toFixed(1)}%)`);
  console.log(`   双方成功: ${stats.bothSuccess}/${stats.total} (${(stats.bothSuccess/stats.total*100).toFixed(1)}%)`);
  console.log(`   双方失败: ${stats.bothFailed}/${stats.total} (${(stats.bothFailed/stats.total*100).toFixed(1)}%)`);
  
  if (priceDiffs.length > 0) {
    const avgDiff = priceDiffs.reduce((a, b) => a + b, 0) / priceDiffs.length;
    const maxDiff = Math.max(...priceDiffs);
    console.log(`\n💰 价格差异分析:`);
    console.log(`   平均价格差异: ${avgDiff.toFixed(2)}%`);
    console.log(`   最大价格差异: ${maxDiff.toFixed(2)}%`);
    console.log(`   大差异数量 (>5%): ${stats.largePriceDiff}`);
  }
  
  // 显示失败的交易对
  const failures = results.filter(r => !r.refQuote.success || !r.veaxQuote.success);
  if (failures.length > 0) {
    console.log(`\n❌ 失败的交易对:`);
    failures.forEach(failure => {
      console.log(`   ${failure.pair} ${failure.direction}:`);
      if (!failure.refQuote.success) {
        console.log(`      REF: ${failure.refQuote.error}`);
      }
      if (!failure.veaxQuote.success) {
        console.log(`      VEAX: ${failure.veaxQuote.error}`);
      }
    });
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await verifyAllTradingPairs();
  } catch (error) {
    console.error('❌ 验证失败:', error);
  }
}

// 运行验证
if (require.main === module) {
  main().catch(console.error);
}

export { verifyAllTradingPairs };
