/**
 * 测试主程序逻辑
 * 
 * 验证套利机器人的核心功能（不执行真实交易）
 */

import 'dotenv/config';
import { refQuoteService } from '../services/refQuoteService';
import { VeaxQuoteService } from '../services/veaxQuoteService';
import { TOKENS } from '../config/tradingPairs';

/**
 * 模拟套利机器人测试
 */
class ArbitrageBotTest {
  private readonly PROFIT_THRESHOLD = 0.015; // 0.015 NEAR利润阈值
  private readonly TRADE_AMOUNT = '3'; // 3 NEAR交易金额
  
  private stats = {
    totalChecks: 0,
    opportunitiesFound: 0,
    refToVeaxOpportunities: 0,
    veaxToRefOpportunities: 0
  };

  /**
   * 运行测试
   */
  async runTest(cycles: number = 10): Promise<void> {
    console.log('🧪 套利机器人逻辑测试');
    console.log(`💰 交易金额: ${this.TRADE_AMOUNT} NEAR`);
    console.log(`📊 利润阈值: ${this.PROFIT_THRESHOLD} NEAR`);
    console.log(`🔄 测试周期: ${cycles}次`);
    console.log('='.repeat(60));

    for (let i = 1; i <= cycles; i++) {
      console.log(`\n📋 第${i}次检查:`);
      
      // 检查NEAR-USDC
      await this.checkPair(TOKENS.NEAR, TOKENS.USDC, 'NEAR-USDC');
      
      // 检查NEAR-USDT  
      await this.checkPair(TOKENS.NEAR, TOKENS.USDT, 'NEAR-USDT');
      
      // 等待1秒
      if (i < cycles) {
        await this.sleep(1000);
      }
    }

    this.printFinalStats();
  }

  /**
   * 检查单个交易对
   */
  private async checkPair(tokenA: any, tokenB: any, pairName: string): Promise<void> {
    this.stats.totalChecks++;

    try {
      console.log(`🔍 检查 ${pairName}...`);

      // 1. 获取正向报价
      const startTime = Date.now();
      
      const [refQuote, veaxQuote] = await Promise.all([
        this.getREFQuote(tokenA, tokenB, this.TRADE_AMOUNT),
        this.getVEAXQuote(tokenA, tokenB, this.TRADE_AMOUNT)
      ]);

      if (!refQuote || !veaxQuote) {
        console.log(`   ❌ 正向报价获取失败`);
        return;
      }

      // 2. 获取反向报价
      const [refReverseQuote, veaxReverseQuote] = await Promise.all([
        this.getREFQuote(tokenB, tokenA, refQuote),
        this.getVEAXQuote(tokenB, tokenA, veaxQuote)
      ]);

      if (!refReverseQuote || !veaxReverseQuote) {
        console.log(`   ❌ 反向报价获取失败`);
        return;
      }

      const queryTime = Date.now() - startTime;

      // 3. 计算利润
      const refToVeaxProfit = parseFloat(veaxReverseQuote) - parseFloat(this.TRADE_AMOUNT);
      const veaxToRefProfit = parseFloat(refReverseQuote) - parseFloat(this.TRADE_AMOUNT);

      // 4. 显示结果
      console.log(`   REF→VEAX: ${this.TRADE_AMOUNT} NEAR → ${refQuote} ${tokenB.symbol} → ${veaxReverseQuote} NEAR`);
      console.log(`   利润: ${refToVeaxProfit.toFixed(6)} NEAR ${refToVeaxProfit >= this.PROFIT_THRESHOLD ? '🟢 触发!' : '🔴'}`);
      
      console.log(`   VEAX→REF: ${this.TRADE_AMOUNT} NEAR → ${veaxQuote} ${tokenB.symbol} → ${refReverseQuote} NEAR`);
      console.log(`   利润: ${veaxToRefProfit.toFixed(6)} NEAR ${veaxToRefProfit >= this.PROFIT_THRESHOLD ? '🟢 触发!' : '🔴'}`);
      
      console.log(`   查询耗时: ${queryTime}ms`);

      // 5. 记录套利机会
      if (refToVeaxProfit >= this.PROFIT_THRESHOLD) {
        this.stats.opportunitiesFound++;
        this.stats.refToVeaxOpportunities++;
        console.log(`   💰 REF→VEAX套利机会! 利润: ${refToVeaxProfit.toFixed(4)} NEAR`);
        this.simulateTradeExecution('REF_TO_VEAX', pairName, refToVeaxProfit);
      }

      if (veaxToRefProfit >= this.PROFIT_THRESHOLD) {
        this.stats.opportunitiesFound++;
        this.stats.veaxToRefOpportunities++;
        console.log(`   💰 VEAX→REF套利机会! 利润: ${veaxToRefProfit.toFixed(4)} NEAR`);
        this.simulateTradeExecution('VEAX_TO_REF', pairName, veaxToRefProfit);
      }

    } catch (error) {
      console.log(`   ❌ 检查${pairName}失败:`, error);
    }
  }

  /**
   * 模拟交易执行
   */
  private simulateTradeExecution(direction: string, pairName: string, profit: number): void {
    console.log(`   🔄 模拟执行${direction}套利交易:`);
    
    if (direction === 'REF_TO_VEAX') {
      console.log(`      1. REF交易: ${this.TRADE_AMOUNT} NEAR → ${pairName.split('-')[1]}`);
      console.log(`      2. VEAX交易: ${pairName.split('-')[1]} → NEAR`);
    } else {
      console.log(`      1. VEAX交易: ${this.TRADE_AMOUNT} NEAR → ${pairName.split('-')[1]}`);
      console.log(`      2. REF交易: ${pairName.split('-')[1]} → NEAR`);
    }
    
    console.log(`      预期利润: ${profit.toFixed(4)} NEAR`);
    console.log(`      ✅ 模拟交易完成`);
  }

  /**
   * 获取REF报价
   */
  private async getREFQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
    try {
      const result = await refQuoteService.getQuote({
        tokenIn,
        tokenOut,
        amountIn: amount,
        slippage: 0.005
      });
      return result.outputAmount;
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取VEAX报价
   */
  private async getVEAXQuote(tokenIn: any, tokenOut: any, amount: string): Promise<string | null> {
    try {
      const result = await VeaxQuoteService.getQuote(tokenIn.id, tokenOut.id, amount);
      return result.success ? result.outputAmount : null;
    } catch (error) {
      return null;
    }
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 打印最终统计
   */
  private printFinalStats(): void {
    console.log('\n📊 测试结果统计:');
    console.log('='.repeat(60));
    console.log(`总检查次数: ${this.stats.totalChecks}`);
    console.log(`发现套利机会: ${this.stats.opportunitiesFound}`);
    console.log(`REF→VEAX机会: ${this.stats.refToVeaxOpportunities}`);
    console.log(`VEAX→REF机会: ${this.stats.veaxToRefOpportunities}`);
    
    if (this.stats.totalChecks > 0) {
      const opportunityRate = (this.stats.opportunitiesFound / this.stats.totalChecks * 100).toFixed(2);
      console.log(`机会发现率: ${opportunityRate}%`);
    }

    console.log('\n💡 测试结论:');
    if (this.stats.opportunitiesFound > 0) {
      console.log('✅ 套利机器人逻辑正常，发现了套利机会');
      console.log('✅ 可以考虑启动生产模式');
    } else {
      console.log('⚠️ 测试期间未发现套利机会');
      console.log('⚠️ 可能需要调整利润阈值或等待市场波动');
    }
  }
}

/**
 * 运行测试
 */
async function runMainProgramTest() {
  const test = new ArbitrageBotTest();
  
  // 获取命令行参数
  const cycles = parseInt(process.argv[3]) || 5;
  
  console.log('🚀 开始测试套利机器人主程序逻辑\n');
  
  try {
    await test.runTest(cycles);
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  runMainProgramTest().catch(console.error);
}

export { ArbitrageBotTest };
