/**
 * 测试余额计算
 */

const availableWNearWei = BigInt('5619215072614511486231018');
const requiredWNearWei = BigInt('100000000000000000000000000');

console.log('可用:', availableWNearWei.toString());
console.log('需要:', requiredWNearWei.toString());
console.log('可用 > 需要:', availableWNearWei > requiredWNearWei);
console.log('可用 < 需要:', availableWNearWei < requiredWNearWei);

// 转换为人类可读
const availableWNear = Number(availableWNearWei) / Math.pow(10, 24);
const requiredWNear = Number(requiredWNearWei) / Math.pow(10, 24);

console.log('可用 wNEAR:', availableWNear);
console.log('需要 wNEAR:', requiredWNear);
