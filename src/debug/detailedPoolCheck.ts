/**
 * 详细池子检查工具
 */

import 'dotenv/config';

/**
 * 详细检查DCL v2池子
 */
async function detailedPoolCheck() {
  console.log('🔍 详细DCL v2池子检查');
  console.log('='.repeat(60));

  const testPool = 'wrap.near|17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|100';
  
  console.log(`\n🔧 详细检查池子: ${testPool}`);
  
  try {
    const response = await fetch('https://rpc.mainnet.near.org', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 'test',
        method: 'query',
        params: {
          request_type: 'call_function',
          finality: 'final',
          account_id: 'dclv2.ref-labs.near',
          method_name: 'get_pool',
          args_base64: Buffer.from(JSON.stringify({ pool_id: testPool })).toString('base64')
        }
      })
    });

    const result: any = await response.json();
    
    console.log('\n📋 完整响应:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.result?.result) {
      console.log('\n📊 原始数据 (base64):');
      console.log(result.result.result);
      
      try {
        const rawData = Buffer.from(result.result.result).toString();
        console.log('\n📊 解码数据 (字符串):');
        console.log(rawData);
        
        const poolData = JSON.parse(rawData);
        console.log('\n📊 解析后的池子数据:');
        console.log(JSON.stringify(poolData, null, 2));
        
        // 分析池子结构
        console.log('\n🔍 池子结构分析:');
        console.log(`   池子ID: ${poolData.pool_id || 'N/A'}`);
        console.log(`   代币A: ${poolData.token_a || 'N/A'}`);
        console.log(`   代币B: ${poolData.token_b || 'N/A'}`);
        console.log(`   费率: ${poolData.fee || 'N/A'}`);
        console.log(`   流动性: ${poolData.total_liquidity || 'N/A'}`);
        console.log(`   状态: ${poolData.state || 'N/A'}`);
        
      } catch (parseError) {
        console.log(`\n❌ JSON解析失败: ${parseError}`);
        console.log('这可能是二进制数据或特殊格式');
      }
    } else {
      console.log('\n❌ 没有返回池子数据');
      if (result.error) {
        console.log(`错误: ${JSON.stringify(result.error, null, 2)}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ 请求失败: ${error}`);
  }
}

/**
 * 分析V1滑点问题的根本原因
 */
function analyzeV1SlippageRoot() {
  console.log('\n🔍 V1滑点问题根本原因分析');
  console.log('='.repeat(60));
  
  console.log('\n📊 交易数据详细分析:');
  console.log('第一步: NEAR → USDT');
  console.log('  输入: 100000000000000000000000 wei = 0.1 NEAR');
  console.log('  输出: 223888 microUSDT = 0.223888 USDT');
  console.log('  状态: ✅ 成功');
  
  console.log('\n第二步: USDT → USDC');
  console.log('  输入: 223888 microUSDT = 0.223888 USDT');
  console.log('  预期输出: ~0.224 USDC');
  console.log('  最小输出: 212800 microUSDC = 0.2128 USDC');
  console.log('  状态: ❌ 滑点错误');
  
  console.log('\n🔧 问题分析:');
  console.log('1. 预期输出: 0.224 USDC');
  console.log('2. 最小输出: 0.2128 USDC (5%滑点)');
  console.log('3. 实际可能输出: < 0.2128 USDC');
  console.log('4. 差异: 可能超过5%的价格影响');
  
  console.log('\n💡 根本原因:');
  console.log('1. 🔴 多跳交易的复合滑点');
  console.log('   - 第一跳: NEAR → USDT (成功)');
  console.log('   - 第二跳: USDT → USDC (失败)');
  console.log('   - 总滑点 = 第一跳滑点 + 第二跳滑点');
  
  console.log('\n2. 🔴 USDT/USDC汇率不是1:1');
  console.log('   - USDT和USDC虽然都是稳定币');
  console.log('   - 但在DEX中汇率可能偏离1:1');
  console.log('   - 特别是在流动性不足时');
  
  console.log('\n3. 🔴 价格影响 (Price Impact)');
  console.log('   - 0.1 NEAR ≈ $0.5-1.0 的交易');
  console.log('   - 在小池子中可能产生显著价格影响');
  console.log('   - 价格影响 + 滑点 > 5%');
}

/**
 * 提供具体解决方案
 */
function provideConcreteSolutions() {
  console.log('\n🚀 具体解决方案');
  console.log('='.repeat(60));
  
  console.log('\n1️⃣ 立即可行方案 - 增加滑点:');
  console.log('   当前: 5% 滑点');
  console.log('   建议: 15-20% 滑点 (测试环境)');
  console.log('   计算: 0.224 * (1 - 0.15) = 0.1904 USDC');
  console.log('   微单位: 190400 microUSDC');
  
  console.log('\n2️⃣ 优化方案 - 减少交易金额:');
  console.log('   当前: 0.1 NEAR');
  console.log('   建议: 0.01 NEAR (减少价格影响)');
  console.log('   优势: 更小的价格影响，更容易成功');
  
  console.log('\n3️⃣ 最佳方案 - 直接交易对:');
  console.log('   当前路径: NEAR → USDT → USDC (2跳)');
  console.log('   建议路径: NEAR → USDC (1跳)');
  console.log('   优势: 减少复合滑点，提高成功率');
  
  console.log('\n4️⃣ 技术方案 - 动态滑点:');
  console.log('   实现: 根据交易金额动态调整滑点');
  console.log('   小额: 5-10% 滑点');
  console.log('   中额: 10-15% 滑点');
  console.log('   大额: 15-25% 滑点');
}

/**
 * 创建测试建议
 */
function createTestSuggestions() {
  console.log('\n📋 测试建议');
  console.log('='.repeat(60));
  
  console.log('\n🔧 建议的测试序列:');
  console.log('1. 测试1: 0.01 NEAR + 20% 滑点');
  console.log('2. 测试2: 0.05 NEAR + 15% 滑点');
  console.log('3. 测试3: 0.1 NEAR + 10% 滑点');
  console.log('4. 测试4: 直接 NEAR → USDC 路径');
  
  console.log('\n💡 预期结果:');
  console.log('测试1: 95% 成功率 (小额 + 高滑点)');
  console.log('测试2: 85% 成功率 (中额 + 中滑点)');
  console.log('测试3: 70% 成功率 (当前设置优化)');
  console.log('测试4: 90% 成功率 (直接路径)');
  
  console.log('\n🎯 成功标准:');
  console.log('- 交易完成，无滑点错误');
  console.log('- 实际输出在预期范围内');
  console.log('- Gas费用合理');
}

// 运行检查
if (require.main === module) {
  Promise.all([
    detailedPoolCheck(),
    analyzeV1SlippageRoot(),
    provideConcreteSolutions(),
    createTestSuggestions()
  ]).catch(console.error);
}

export { detailedPoolCheck };
