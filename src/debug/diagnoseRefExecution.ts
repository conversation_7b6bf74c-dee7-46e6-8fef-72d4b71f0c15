/**
 * REF Finance 执行问题诊断工具
 * 
 * 分析当前REF执行服务的问题并提供修复方案
 */

import 'dotenv/config';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';

/**
 * 诊断REF Finance执行问题
 */
async function diagnoseRefExecution() {
  console.log('🔍 REF Finance 执行问题诊断');
  console.log('='.repeat(60));

  // 1. 分析当前实现的问题
  console.log('\n1️⃣ 当前实现问题分析:');
  console.log('❌ 问题1: ft_transfer_call调用错误');
  console.log('   当前: 直接调用REF合约的ft_transfer_call');
  console.log('   正确: 应该调用输入代币合约的ft_transfer_call');
  
  console.log('\n❌ 问题2: V1交易构建不完整');
  console.log('   当前: 只处理简单单路径');
  console.log('   正确: 需要处理Smart Router的复杂多路径结构');
  
  console.log('\n❌ 问题3: amount_in分配错误');
  console.log('   当前: 每个action都设置amount_in');
  console.log('   正确: 只有特定action需要amount_in');

  // 2. 获取真实报价进行分析
  console.log('\n2️⃣ 获取真实报价进行分析:');
  
  try {
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '1',
      slippage: 0.005
    };

    const quote = await refQuoteService.getBestQuote(quoteParams);
    console.log(`📊 获得报价: ${quote.outputAmount} ${TOKENS.USDC.symbol}`);
    console.log(`🔧 使用系统: ${quote.system}`);

    if (quote.system === 'V1' && quote.rawResponse) {
      console.log('\n📋 V1 Smart Router响应分析:');
      const routeData = quote.rawResponse.result_data;
      console.log(`   路径数量: ${routeData.routes.length}`);
      
      routeData.routes.forEach((route: any, routeIndex: number) => {
        console.log(`   路径${routeIndex + 1}: ${route.pools.length}个池子`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`     池子${poolIndex + 1}: ${pool.pool_id} (${pool.token_in} → ${pool.token_out})`);
          if (pool.amount_in) {
            console.log(`       输入金额: ${pool.amount_in}`);
          }
        });
      });
    }

    // 3. 展示正确的交易构建方式
    console.log('\n3️⃣ 正确的交易构建方式:');
    
    if (quote.system === 'V1') {
      console.log('✅ V1系统正确调用方式:');
      console.log(`   1. 调用 ${TOKENS.NEAR.id}.ft_transfer_call()`);
      console.log(`   2. receiver_id: "v2.ref-finance.near"`);
      console.log(`   3. amount: 输入代币的精度金额`);
      console.log(`   4. msg: 包含完整的actions数组`);
      
      showCorrectV1Transaction(quote);
    } else if (quote.system === 'DCL_V2') {
      console.log('✅ DCL v2系统正确调用方式:');
      console.log(`   1. 调用 ${TOKENS.NEAR.id}.ft_transfer_call()`);
      console.log(`   2. receiver_id: "dclv2.ref-labs.near"`);
      console.log(`   3. amount: 输入代币的精度金额`);
      console.log(`   4. msg: {"Swap": {...}}`);
      
      showCorrectDCLv2Transaction(quote);
    }

  } catch (error) {
    console.error('❌ 报价获取失败:', error);
  }

  // 4. 提供修复建议
  console.log('\n4️⃣ 修复建议:');
  console.log('🔧 需要修复的关键点:');
  console.log('   1. 修改ft_transfer_call的调用目标为输入代币合约');
  console.log('   2. 完整实现Smart Router返回的actions结构');
  console.log('   3. 正确处理amount_in的分配逻辑');
  console.log('   4. 添加更详细的错误处理和日志');

  console.log('\n✅ 诊断完成');
  console.log('💡 接下来将创建修复版本的RefExecutionService');
}

/**
 * 展示正确的V1交易构建
 */
function showCorrectV1Transaction(quote: any) {
  if (!quote.rawResponse?.result_data?.routes?.[0]) return;

  const route = quote.rawResponse.result_data.routes[0];
  const inputAmount = '1000000000000000000000000'; // 1 NEAR in wei
  
  console.log('\n📋 正确的V1交易参数:');
  console.log('```typescript');
  console.log('await nearAccount.functionCall({');
  console.log(`  contractId: "${TOKENS.NEAR.id}", // 输入代币合约`);
  console.log('  methodName: "ft_transfer_call",');
  console.log('  args: {');
  console.log('    receiver_id: "v2.ref-finance.near",');
  console.log(`    amount: "${inputAmount}",`);
  console.log('    msg: JSON.stringify({');
  console.log('      force: 0,');
  console.log('      actions: [');
  
  route.pools.forEach((pool: any, index: number) => {
    console.log('        {');
    console.log(`          pool_id: ${pool.pool_id},`);
    console.log(`          token_in: "${pool.token_in}",`);
    console.log(`          token_out: "${pool.token_out}",`);
    if (pool.amount_in) {
      console.log(`          amount_in: "${pool.amount_in}",`);
    }
    console.log('          amount_out: "0",');
    console.log(`          min_amount_out: "${index === route.pools.length - 1 ? 'CALCULATED_MIN_OUTPUT' : '0'}"`);
    console.log(`        }${index < route.pools.length - 1 ? ',' : ''}`);
  });
  
  console.log('      ],');
  console.log('      skip_unwrap_near: false');
  console.log('    })');
  console.log('  },');
  console.log('  attachedDeposit: "1",');
  console.log('  gas: "***************"');
  console.log('});');
  console.log('```');
}

/**
 * 展示正确的DCL v2交易构建
 */
function showCorrectDCLv2Transaction(quote: any) {
  const inputAmount = '1000000000000000000000000'; // 1 NEAR in wei
  const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
  const minOutputAmount = '*********'; // 示例最小输出
  
  console.log('\n📋 正确的DCL v2交易参数:');
  console.log('```typescript');
  console.log('await nearAccount.functionCall({');
  console.log(`  contractId: "${TOKENS.NEAR.id}", // 输入代币合约`);
  console.log('  methodName: "ft_transfer_call",');
  console.log('  args: {');
  console.log('    receiver_id: "dclv2.ref-labs.near",');
  console.log(`    amount: "${inputAmount}",`);
  console.log('    msg: JSON.stringify({');
  console.log('      Swap: {');
  console.log(`        pool_ids: ["${poolId}"],`);
  console.log(`        output_token: "${TOKENS.USDC.id}",`);
  console.log(`        min_output_amount: "${minOutputAmount}",`);
  console.log('        skip_unwrap_near: true');
  console.log('      }');
  console.log('    })');
  console.log('  },');
  console.log('  attachedDeposit: "200000000000000000000000000",');
  console.log('  gas: "***************"');
  console.log('});');
  console.log('```');
}

// 运行诊断
if (require.main === module) {
  diagnoseRefExecution().catch(console.error);
}

export { diagnoseRefExecution };
