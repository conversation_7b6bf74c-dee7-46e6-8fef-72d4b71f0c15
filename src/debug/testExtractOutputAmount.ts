/**
 * 测试从真实交易中提取输出金额
 * 使用交易哈希: 8BeGdw67i7QmMwaPdVZodx5Ed3tok3ZozajoRjgUcubA
 */

import axios from 'axios';

/**
 * 模拟REF执行服务的提取方法
 */
function extractOutputAmountFromResult(result: any, accountId: string): { humanReadable: string | null; wei: string | null } {
  try {
    console.log('🔍 开始提取输出金额...');
    
    // 查找所有receipts中的ft_transfer事件
    const allReceipts = result.receipts_outcome || [];
    console.log(`📋 找到 ${allReceipts.length} 个receipts`);

    for (let i = 0; i < allReceipts.length; i++) {
      const receipt = allReceipts[i];
      const logs = receipt.outcome?.logs || [];
      console.log(`📋 Receipt ${i + 1}: ${logs.length} 个日志`);

      for (let j = 0; j < logs.length; j++) {
        const log = logs[j];
        console.log(`   日志 ${j + 1}: ${log}`);
        
        try {
          // 查找EVENT_JSON日志
          if (log.includes('EVENT_JSON:')) {
            console.log('   🔍 找到EVENT_JSON日志');
            const eventStr = log.split('EVENT_JSON:')[1];
            const event = JSON.parse(eventStr);
            console.log('   📊 解析的事件:', JSON.stringify(event, null, 2));

            // 查找ft_transfer事件
            if (event.standard === 'nep141' && event.event === 'ft_transfer') {
              console.log('   ✅ 找到ft_transfer事件');
              const transferData = event.data?.[0];
              console.log('   📊 转账数据:', JSON.stringify(transferData, null, 2));

              // 检查是否是转给我们账户的输出代币
              if (transferData &&
                  transferData.new_owner_id === accountId &&
                  transferData.amount) {

                const weiAmount = transferData.amount;
                console.log(`   ✅ 找到匹配的转账: ${weiAmount} wei`);

                // 返回wei格式和人类可读格式
                return {
                  wei: weiAmount,
                  humanReadable: null // 暂时不转换，避免精度损失
                };
              } else {
                console.log('   ❌ 转账不匹配我们的账户');
                console.log(`   预期账户: ${accountId}`);
                console.log(`   实际账户: ${transferData?.new_owner_id}`);
              }
            } else {
              console.log(`   ❌ 不是ft_transfer事件: ${event.event}`);
            }
          } else {
            console.log('   ❌ 不是EVENT_JSON日志');
          }
        } catch (parseError) {
          console.log(`   ❌ 解析日志失败: ${parseError}`);
          continue;
        }
      }
    }

    console.log('❌ 未找到匹配的ft_transfer事件');
    return { humanReadable: null, wei: null };
  } catch (error) {
    console.error('❌ 提取输出金额失败:', error);
    return { humanReadable: null, wei: null };
  }
}

/**
 * 尝试从普通日志中提取金额
 */
function extractFromRegularLogs(result: any, accountId: string): { humanReadable: string | null; wei: string | null } {
  try {
    console.log('\n🔍 尝试从普通日志中提取金额...');
    
    const allReceipts = result.receipts_outcome || [];

    for (let i = 0; i < allReceipts.length; i++) {
      const receipt = allReceipts[i];
      const logs = receipt.outcome?.logs || [];

      for (let j = 0; j < logs.length; j++) {
        const log = logs[j];
        
        // 查找Transfer日志格式: "Transfer AMOUNT from FROM to TO"
        const transferMatch = log.match(/Transfer (\d+) from ([\w\.-]+) to ([\w\.-]+)/);
        if (transferMatch) {
          const [, amount, from, to] = transferMatch;
          console.log(`   🔍 找到Transfer日志: ${amount} from ${from} to ${to}`);
          
          if (to === accountId) {
            console.log(`   ✅ 找到转给我们账户的金额: ${amount} wei`);
            return {
              wei: amount,
              humanReadable: null
            };
          }
        }
      }
    }

    console.log('❌ 未找到匹配的Transfer日志');
    return { humanReadable: null, wei: null };
  } catch (error) {
    console.error('❌ 从普通日志提取失败:', error);
    return { humanReadable: null, wei: null };
  }
}

/**
 * 查询并测试交易
 */
async function testTransactionExtraction() {
  const txHash = '8BeGdw67i7QmMwaPdVZodx5Ed3tok3ZozajoRjgUcubA';
  const accountId = 'whatdoyoumean.near'; // 从日志中看到的账户ID
  
  console.log('🧪 测试从真实交易中提取输出金额');
  console.log('='.repeat(60));
  console.log(`交易哈希: ${txHash}`);
  console.log(`账户ID: ${accountId}`);
  console.log('='.repeat(60));

  try {
    // 查询交易状态
    const response = await axios.post('https://rpc.mainnet.near.org', {
      jsonrpc: '2.0',
      id: 'dontcare',
      method: 'tx',
      params: [txHash, accountId]
    });

    if (response.data.error) {
      console.error('❌ RPC错误:', response.data.error);
      return;
    }

    const txResult = response.data.result;
    
    console.log('📊 交易基本信息:');
    console.log(`   交易哈希: ${txResult.transaction.hash}`);
    console.log(`   发送者: ${txResult.transaction.signer_id}`);
    console.log(`   接收者: ${txResult.transaction.receiver_id}`);
    console.log(`   状态: ${JSON.stringify(txResult.status)}`);

    // 显示所有日志
    console.log('\n📋 所有交易日志:');
    const allReceipts = txResult.receipts_outcome || [];
    allReceipts.forEach((receipt: any, receiptIndex: number) => {
      const logs = receipt.outcome?.logs || [];
      console.log(`\nReceipt ${receiptIndex + 1}:`);
      logs.forEach((log: string, logIndex: number) => {
        console.log(`   ${logIndex + 1}. ${log}`);
      });
    });

    // 测试我们当前的提取方法
    console.log('\n🔧 测试当前的提取方法 (EVENT_JSON):');
    console.log('='.repeat(50));
    const currentResult = extractOutputAmountFromResult(txResult, accountId);
    console.log('📊 当前方法结果:');
    console.log(`   wei: ${currentResult.wei}`);
    console.log(`   humanReadable: ${currentResult.humanReadable}`);

    // 测试从普通日志提取
    console.log('\n🔧 测试从普通日志提取:');
    console.log('='.repeat(50));
    const regularResult = extractFromRegularLogs(txResult, accountId);
    console.log('📊 普通日志方法结果:');
    console.log(`   wei: ${regularResult.wei}`);
    console.log(`   humanReadable: ${regularResult.humanReadable}`);

    // 对比真实的区块链输出
    console.log('\n📊 对比分析:');
    console.log('='.repeat(50));
    console.log('从日志中我们知道的真实输出:');
    console.log('   REF实际输出: 1060173820656998309173701887367336 wei');
    console.log('   机器人显示: **********.507499803153604415250318 BLACKDRAGON');
    console.log('   机器人wei:  **********507499803153604415250318 wei');
    
    if (currentResult.wei) {
      console.log(`\n当前方法提取: ${currentResult.wei} wei`);
      const matches = currentResult.wei === '1060173820656998309173701887367336';
      console.log(`匹配真实输出: ${matches ? '✅' : '❌'}`);
    } else {
      console.log('\n❌ 当前方法未能提取到金额');
    }

    if (regularResult.wei) {
      console.log(`\n普通日志提取: ${regularResult.wei} wei`);
      const matches = regularResult.wei === '1060173820656998309173701887367336';
      console.log(`匹配真实输出: ${matches ? '✅' : '❌'}`);
    } else {
      console.log('\n❌ 普通日志方法未能提取到金额');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始测试输出金额提取');
  
  await testTransactionExtraction();
  
  console.log('\n🎉 测试完成!');
  console.log('现在我们知道了提取方法的问题所在！');
}

// 运行测试
main();
