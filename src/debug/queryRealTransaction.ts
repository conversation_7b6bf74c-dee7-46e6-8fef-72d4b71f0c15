/**
 * 查询真实的交易状态
 */

import axios from 'axios';

/**
 * 查询NEAR交易状态
 */
async function queryNearTransaction(txHash: string, accountId: string = 'wrap.near') {
  console.log(`🔍 查询NEAR交易: ${txHash}`);
  console.log('='.repeat(60));

  try {
    // 使用NEAR RPC查询交易状态
    const response = await axios.post('https://rpc.mainnet.near.org', {
      jsonrpc: '2.0',
      id: 'dontcare',
      method: 'tx',
      params: [txHash, accountId]
    });

    if (response.data.error) {
      console.error('❌ RPC错误:', response.data.error);
      return;
    }

    const txResult = response.data.result;
    
    console.log('📊 交易基本信息:');
    console.log(`   交易哈希: ${txResult.transaction.hash}`);
    console.log(`   发送者: ${txResult.transaction.signer_id}`);
    console.log(`   接收者: ${txResult.transaction.receiver_id}`);
    console.log(`   状态: ${JSON.stringify(txResult.status)}`);

    // 检查交易是否成功
    const isSuccess = txResult.status && typeof txResult.status === 'object' && 'SuccessValue' in txResult.status;
    console.log(`   是否成功: ${isSuccess ? '✅ 成功' : '❌ 失败'}`);

    // 查找交易日志
    console.log('\n📋 交易日志:');
    const allReceipts = txResult.receipts_outcome || [];
    
    let foundSwapEvent = false;
    for (const receipt of allReceipts) {
      const logs = receipt.outcome?.logs || [];
      
      for (const log of logs) {
        console.log(`   日志: ${log}`);
        
        // 查找VEAX swap事件
        if (log.includes('EVENT_JSON:')) {
          try {
            const eventStr = log.split('EVENT_JSON:')[1];
            const event = JSON.parse(eventStr);
            
            if (event.event === 'swap' && event.data.amounts) {
              foundSwapEvent = true;
              console.log('\n🎯 找到VEAX swap事件:');
              console.log(`   输入金额: ${event.data.amounts[0]} wei`);
              console.log(`   输出金额: ${event.data.amounts[1]} wei`);
              console.log(`   输入代币: ${event.data.tokens[0]}`);
              console.log(`   输出代币: ${event.data.tokens[1]}`);
              
              // 转换为人类可读格式
              const inputAmount = parseFloat(event.data.amounts[0]) / Math.pow(10, 24); // NEAR是24位精度
              const outputAmount = parseFloat(event.data.amounts[1]) / Math.pow(10, 6);  // USDT是6位精度
              
              console.log(`   输入金额(可读): ${inputAmount} NEAR`);
              console.log(`   输出金额(可读): ${outputAmount} USDT`);
            }
          } catch (parseError) {
            // 忽略解析错误
          }
        }
        
        // 查找ft_transfer事件
        if (log.includes('EVENT_JSON:') && log.includes('ft_transfer')) {
          try {
            const eventStr = log.split('EVENT_JSON:')[1];
            const event = JSON.parse(eventStr);
            
            if (event.standard === 'nep141' && event.event === 'ft_transfer') {
              const transferData = event.data?.[0];
              if (transferData) {
                console.log('\n💰 找到ft_transfer事件:');
                console.log(`   从: ${transferData.old_owner_id}`);
                console.log(`   到: ${transferData.new_owner_id}`);
                console.log(`   金额: ${transferData.amount} wei`);
                
                // 尝试转换为人类可读格式（假设是USDT 6位精度）
                const readableAmount = parseFloat(transferData.amount) / Math.pow(10, 6);
                console.log(`   金额(可读): ${readableAmount} USDT`);
              }
            }
          } catch (parseError) {
            // 忽略解析错误
          }
        }
      }
    }

    if (!foundSwapEvent) {
      console.log('\n⚠️ 未找到swap事件，可能不是VEAX交易或交易失败');
    }

    // 显示完整的交易结果（用于调试）
    console.log('\n🔧 完整交易结果（调试用）:');
    console.log(JSON.stringify(txResult, null, 2));

  } catch (error) {
    console.error('❌ 查询失败:', error);
  }
}

/**
 * 查询多个交易
 */
async function queryMultipleTransactions() {
  const transactions = [
    {
      hash: 'DCk88G9cDCGwwd64qVnUD7Z5wFDeavf5pGanGpYbtCxE',
      description: '您提到的VEAX交易'
    }
    // 可以添加更多交易哈希
  ];

  for (const tx of transactions) {
    console.log(`\n🔍 查询: ${tx.description}`);
    await queryNearTransaction(tx.hash);
    console.log('\n' + '='.repeat(80));
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始查询真实交易状态');
  console.log('='.repeat(80));
  
  try {
    await queryMultipleTransactions();
    
    console.log('\n🎉 查询完成!');
    console.log('现在我们知道了真实的交易输出金额！');
    
  } catch (error) {
    console.error('❌ 查询失败:', error);
    process.exit(1);
  }
}

// 运行查询
main();
