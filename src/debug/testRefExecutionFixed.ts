/**
 * REF Finance 修复版本执行测试
 *
 * 测试修复后的REF执行服务
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

function fromWei(amount: string, decimals: number): string {
  return new Big(amount).div(new Big(10).pow(decimals)).toString();
}

/**
 * REF Finance 修复版本执行测试
 */
async function testRefExecutionFixed() {
  console.log('🚀 开始REF Finance修复版本交易执行测试');
  console.log('='.repeat(60));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请在.env文件中设置这些变量');
    return;
  }

  const refExecution = new RefExecutionServiceFixed(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化REF执行服务(修复版)...');
    await refExecution.initialize();

    // 2. 获取报价
    console.log('\n2️⃣ 获取REF Finance报价...');
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1', // 0.1 NEAR
      slippage: 0.005
    };

    const quote = await refQuoteService.getBestQuote(quoteParams);
    console.log(`📊 获得报价: ${quote.outputAmount} ${TOKENS.USDC.symbol}`);
    console.log(`🔧 使用系统: ${quote.system}`);

    // 3. 分析报价结构
    console.log('\n3️⃣ 分析报价结构...');
    if (quote.system === 'V1' && quote.rawResponse) {
      console.log('📋 V1 Smart Router结构分析:');
      const routeData = quote.rawResponse.result_data;
      console.log(`   路径数量: ${routeData.routes.length}`);
      
      routeData.routes.forEach((route: any, routeIndex: number) => {
        console.log(`   路径${routeIndex + 1}:`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`     池子${poolIndex + 1}: ${pool.pool_id}`);
          console.log(`       ${pool.token_in} → ${pool.token_out}`);
          if (pool.amount_in) {
            console.log(`       amount_in: ${pool.amount_in}`);
          }
          if (pool.min_amount_out) {
            console.log(`       min_amount_out: ${pool.min_amount_out}`);
          }
        });
      });
    }

    // 4. 计算交易参数
    console.log('\n4️⃣ 计算交易参数...');
    const inputAmount = quoteParams.amountIn;
    const inputAmountWei = toWei(inputAmount, TOKENS.NEAR.decimals);
    
    const slippage = 0.01; // 1%
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDC.decimals);

    console.log(`📊 输入金额: ${inputAmount} NEAR (${inputAmountWei} wei)`);
    console.log(`📊 预期输出: ${quote.outputAmount} USDC`);
    console.log(`📊 最小输出: ${minOutputAmount} USDC (${minOutputAmountWei} wei)`);
    console.log(`📊 滑点容忍: ${slippage * 100}%`);

    // 5. 展示正确的交易构建
    console.log('\n5️⃣ 展示正确的交易构建...');
    console.log('🔧 修复版本的关键改进:');
    console.log(`   ✅ 调用输入代币合约: ${TOKENS.NEAR.id}`);
    console.log(`   ✅ receiver_id: ${quote.system === 'V1' ? 'v2.ref-finance.near' : 'dclv2.ref-labs.near'}`);
    console.log(`   ✅ 正确的消息格式`);

    // 6. 模拟交易执行（不执行真实交易）
    console.log('\n6️⃣ 模拟交易执行...');
    console.log('⚠️ 这是模拟执行，不会发送真实交易');

    if (quote.system === 'V1') {
      console.log('📋 V1交易模拟:');
      console.log(`   合约调用: ${TOKENS.NEAR.id}.ft_transfer_call()`);
      console.log(`   参数:`);
      console.log(`     receiver_id: "v2.ref-finance.near"`);
      console.log(`     amount: "${inputAmountWei}"`);
      console.log(`     msg: {包含${quote.rawResponse?.result_data?.routes?.[0]?.pools?.length || 0}个动作的消息}`);
    } else if (quote.system === 'DCL_V2') {
      const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
      console.log('📋 DCL v2交易模拟:');
      console.log(`   合约调用: ${TOKENS.NEAR.id}.ft_transfer_call()`);
      console.log(`   参数:`);
      console.log(`     receiver_id: "dclv2.ref-labs.near"`);
      console.log(`     amount: "${inputAmountWei}"`);
      console.log(`     msg: {"Swap": {"pool_ids": ["${poolId}"], ...}}`);
    }

    // 7. 真实交易执行（需要手动启用）
    console.log('\n7️⃣ 真实交易执行...');
    console.log('⚠️ 要执行真实交易，请取消注释下面的代码');
    console.log('💡 确保账户有足够的余额和权限');

    /*
    // 取消注释以执行真实交易
    let executionResult;
    if (quote.system === 'V1') {
      executionResult = await refExecution.executeV1Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        slippage
      );
    } else if (quote.system === 'DCL_V2') {
      const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
      executionResult = await refExecution.executeDCLv2Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        poolId,
        TOKENS.USDC.id
      );
    }

    if (executionResult?.success) {
      console.log(`✅ 交易执行成功!`);
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 输出金额: ${executionResult.outputAmount}`);
    } else {
      console.log(`❌ 交易执行失败: ${executionResult?.error}`);
    }
    */

    console.log('\n✅ REF Finance修复版本测试完成');
    console.log('🎉 主要修复内容:');
    console.log('   1. ✅ 修复ft_transfer_call调用目标');
    console.log('   2. ✅ 完整实现Smart Router actions结构');
    console.log('   3. ✅ 正确处理amount_in分配逻辑');
    console.log('   4. ✅ 添加详细的调试日志');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 对比原版本和修复版本的差异
 */
function showDifferences() {
  console.log('\n📊 原版本 vs 修复版本对比:');
  console.log('='.repeat(60));
  
  console.log('\n❌ 原版本问题:');
  console.log('1. 错误调用: refContract.ft_transfer_call()');
  console.log('2. 简单构建: 只处理单一路径');
  console.log('3. 错误分配: 每个action都设置amount_in');
  
  console.log('\n✅ 修复版本改进:');
  console.log('1. 正确调用: inputTokenContract.ft_transfer_call()');
  console.log('2. 完整构建: 处理Smart Router的所有路径');
  console.log('3. 正确分配: 只在需要时设置amount_in');
  
  console.log('\n🔧 关键代码差异:');
  console.log('原版本:');
  console.log('```typescript');
  console.log('await this.account.functionCall({');
  console.log('  contractId: "v2.ref-finance.near", // ❌ 错误');
  console.log('  methodName: "ft_transfer_call",');
  console.log('  // ...');
  console.log('});');
  console.log('```');
  
  console.log('\n修复版本:');
  console.log('```typescript');
  console.log('await this.account.functionCall({');
  console.log('  contractId: inputTokenId, // ✅ 正确');
  console.log('  methodName: "ft_transfer_call",');
  console.log('  args: {');
  console.log('    receiver_id: "v2.ref-finance.near", // ✅ REF作为接收者');
  console.log('    // ...');
  console.log('  }');
  console.log('});');
  console.log('```');
}

// 运行测试
if (require.main === module) {
  Promise.all([
    testRefExecutionFixed(),
    showDifferences()
  ]).catch(console.error);
}

export { testRefExecutionFixed, showDifferences };
