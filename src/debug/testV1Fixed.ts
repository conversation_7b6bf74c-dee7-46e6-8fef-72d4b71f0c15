/**
 * 强制V1系统测试 - 验证pool_id修复
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { refQuoteService } from '../services/refQuoteService';
import { v1SmartRouter } from '../services/v1SmartRouter';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

/**
 * 强制V1系统测试
 */
async function testV1Fixed() {
  console.log('🚀 强制V1系统测试 - 验证pool_id修复');
  console.log('='.repeat(60));

  // 验证环境变量
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 环境变量配置不完整');
    return;
  }

  const refExecution = new RefExecutionServiceFixed(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化
    console.log('\n1️⃣ 初始化服务...');
    await refExecution.initialize();

    // 2. 强制获取V1报价
    console.log('\n2️⃣ 强制获取V1报价...');
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1',
      slippage: 0.005
    };

    // 直接调用V1系统
    const v1Quote = await v1SmartRouter.getV1Quote(quoteParams);

    if (!v1Quote) {
      console.error('❌ V1报价失败');
      return;
    }

    console.log(`📊 V1报价: ${v1Quote.outputAmount} USDC`);
    console.log(`🔧 系统: ${v1Quote.system}`);

    if (!v1Quote.rawResponse) {
      console.error('❌ V1报价没有rawResponse');
      return;
    }

    // 3. 分析V1报价结构
    console.log('\n3️⃣ 分析V1报价结构...');
    const routeData = v1Quote.rawResponse.result_data;
    console.log(`📋 路径数量: ${routeData.routes.length}`);
    
    routeData.routes.forEach((route: any, routeIndex: number) => {
      console.log(`\n路径${routeIndex + 1}:`);
      route.pools.forEach((pool: any, poolIndex: number) => {
        console.log(`  池子${poolIndex + 1}:`);
        console.log(`    pool_id: ${pool.pool_id} (原始类型: ${typeof pool.pool_id})`);
        console.log(`    ${pool.token_in.slice(-10)} → ${pool.token_out.slice(-10)}`);
        if (pool.amount_in) {
          console.log(`    amount_in: ${pool.amount_in}`);
        }
      });
    });

    // 4. 测试交易构建
    console.log('\n4️⃣ 测试交易构建...');
    const inputAmount = toWei('0.1', TOKENS.NEAR.decimals);
    const minOutputAmount = toWei('0.2', TOKENS.USDC.decimals); // 保守的最小输出

    console.log(`📊 输入金额: ${inputAmount} wei`);
    console.log(`📊 最小输出: ${minOutputAmount} wei`);

    // 5. 执行交易（如果启用）
    console.log('\n5️⃣ 执行交易...');
    
    if (!EXECUTION_CONFIG.SAFETY.ENABLE_REAL_TRADING) {
      console.log('🔒 安全模式 - 模拟执行');
      console.log('💡 要执行真实交易，请设置 ENABLE_REAL_TRADING=true');
      
      // 模拟构建交易消息
      console.log('\n📋 模拟交易构建:');
      const route = routeData.routes[0];
      const actions = route.pools.map((pool: any, index: number) => {
        const action = {
          pool_id: parseInt(pool.pool_id.toString()), // 🔧 关键修复
          token_in: pool.token_in,
          token_out: pool.token_out,
          amount_out: "0",
          min_amount_out: index === route.pools.length - 1 ? minOutputAmount : "0"
        };
        
        if (pool.amount_in) {
          (action as any).amount_in = pool.amount_in;
        }
        
        console.log(`  动作${index + 1}: pool_id = ${action.pool_id} (类型: ${typeof action.pool_id})`);
        return action;
      });

      const msg = {
        force: 0,
        actions: actions,
        skip_unwrap_near: false
      };

      console.log('\n📋 完整交易消息:');
      console.log(JSON.stringify(msg, null, 2));
      
      // 验证序列化后的类型
      const serialized = JSON.stringify(msg);
      const parsed = JSON.parse(serialized);
      
      console.log('\n🔍 序列化验证:');
      parsed.actions.forEach((action: any, index: number) => {
        const isNumber = typeof action.pool_id === 'number';
        console.log(`  动作${index + 1}: pool_id = ${action.pool_id} (${typeof action.pool_id}) ${isNumber ? '✅' : '❌'}`);
      });

      return;
    }

    // 真实交易执行
    console.log('🚀 执行真实V1交易...');
    const executionResult = await refExecution.executeV1Swap(
      v1Quote,
      TOKENS.NEAR.id,
      inputAmount,
      minOutputAmount,
      0.02 // 2% 滑点
    );

    // 6. 分析结果
    console.log('\n6️⃣ 交易结果分析...');
    
    if (executionResult.success) {
      console.log('🎉 V1交易执行成功!');
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 预期输出: ${v1Quote.outputAmount} USDC`);
      
      console.log('\n✅ 关键修复验证:');
      console.log('   ✅ pool_id数字类型修复成功');
      console.log('   ✅ ft_transfer_call调用方式正确');
      console.log('   ✅ 交易消息格式正确');
      console.log('   ✅ V1系统交易执行成功');

    } else {
      console.log('❌ V1交易执行失败');
      console.log(`🔍 错误信息: ${executionResult.error}`);
      
      if (executionResult.error?.includes('E28: Illegal msg')) {
        console.log('❌ 仍然存在消息格式问题');
        console.log('🔧 需要进一步检查pool_id类型转换');
      } else {
        console.log('✅ 消息格式问题已修复');
        console.log('🔧 可能是其他问题（余额、权限等）');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 显示V1修复总结
 */
function showV1FixSummary() {
  console.log('\n📊 V1修复总结:');
  console.log('='.repeat(50));
  
  console.log('\n🔧 关键修复点:');
  console.log('1. ✅ pool_id类型: 字符串 → 数字');
  console.log('2. ✅ 合约调用: REF合约 → 输入代币合约');
  console.log('3. ✅ 交易构建: 简单路径 → 完整多路径');
  console.log('4. ✅ 错误处理: 基础 → 详细日志');
  
  console.log('\n🎯 预期结果:');
  console.log('- ❌ 修复前: E28: Illegal msg in ft_transfer_call');
  console.log('- ✅ 修复后: 交易成功执行或其他非格式错误');
  
  console.log('\n💡 如果仍然失败:');
  console.log('1. 检查账户余额是否充足');
  console.log('2. 检查代币是否已注册');
  console.log('3. 检查网络连接状态');
  console.log('4. 但不应该再出现E28错误');
}

// 运行测试
if (require.main === module) {
  Promise.all([
    testV1Fixed(),
    showV1FixSummary()
  ]).catch(console.error);
}

export { testV1Fixed };
