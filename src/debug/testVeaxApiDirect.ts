/**
 * 直接测试VEAX API返回值格式
 */

import 'dotenv/config';
import axios from 'axios';

/**
 * 直接调用VEAX API测试返回格式
 */
async function testVeaxApiDirect() {
  console.log('🔍 直接测试VEAX API返回值格式');
  console.log('='.repeat(60));

  const testCases = [
    {
      name: '7 NEAR → USDT',
      tokenA: 'wrap.near',
      tokenB: 'usdt.tether-token.near',
      amount: '7'
    },
    {
      name: '0.1 NEAR → USDT',
      tokenA: 'wrap.near',
      tokenB: 'usdt.tether-token.near',
      amount: '0.1'
    },
    {
      name: '1 NEAR → USDT',
      tokenA: 'wrap.near',
      tokenB: 'usdt.tether-token.near',
      amount: '1'
    },
    {
      name: '100 USDT → NEAR',
      tokenA: 'usdt.tether-token.near',
      tokenB: 'wrap.near',
      amount: '100'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📊 测试: ${testCase.name}`);
    console.log(`   输入: ${testCase.amount} ${testCase.tokenA === 'wrap.near' ? 'NEAR' : 'USDT'}`);
    
    try {
      // 构建VEAX API请求
      const requestData = {
        jsonrpc: '2.0',
        id: 1,
        method: 'estimate_swap_exact_in',
        params: {
          token_a: testCase.tokenA,
          token_b: testCase.tokenB,
          amount_a: testCase.amount,
          slippage_tolerance: 0.005
        }
      };

      console.log(`   请求参数: ${JSON.stringify(requestData.params, null, 2)}`);

      // 发送请求
      const response = await axios.post('https://api.veax.io/rpc', requestData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.data.error) {
        console.log(`   ❌ API错误: ${response.data.error.message}`);
        if (response.data.error.data?.details) {
          console.log(`   详细信息: ${response.data.error.data.details}`);
        }
      } else if (response.data.result) {
        const result = response.data.result;
        
        console.log(`   ✅ API成功响应:`);
        console.log(`   amount_b_expected: ${result.amount_b_expected}`);
        console.log(`   amount_b_bound: ${result.amount_b_bound}`);
        console.log(`   price_impact: ${result.price_impact}`);
        console.log(`   swap_price: ${result.swap_price}`);
        console.log(`   fee: ${result.fee}`);
        console.log(`   pool_exists: ${result.pool_exists}`);

        // 分析数值格式
        const outputValue = result.amount_b_expected;
        console.log(`\n   🔍 输出值分析:`);
        console.log(`   原始值: "${outputValue}"`);
        console.log(`   类型: ${typeof outputValue}`);
        console.log(`   长度: ${outputValue.length}`);
        console.log(`   包含小数点: ${outputValue.includes('.')}`);
        console.log(`   是否为整数字符串: ${/^\d+$/.test(outputValue)}`);
        console.log(`   是否为小数字符串: ${/^\d+\.\d+$/.test(outputValue)}`);

        // 判断格式类型
        if (testCase.tokenB === 'usdt.tether-token.near') {
          // 输出是USDT (6位精度)
          const expectedWeiLength = testCase.amount.length + 6; // 大概估算
          if (outputValue.includes('.')) {
            console.log(`   📊 格式判断: 人类可读格式 (包含小数点)`);
          } else if (outputValue.length > 10) {
            console.log(`   📊 格式判断: 可能是wei格式 (长度${outputValue.length})`);
          } else {
            console.log(`   📊 格式判断: 可能是人类可读格式 (长度${outputValue.length})`);
          }
        } else {
          // 输出是NEAR (24位精度)
          if (outputValue.includes('.')) {
            console.log(`   📊 格式判断: 人类可读格式 (包含小数点)`);
          } else if (outputValue.length > 15) {
            console.log(`   📊 格式判断: 可能是wei格式 (长度${outputValue.length})`);
          } else {
            console.log(`   📊 格式判断: 可能是人类可读格式 (长度${outputValue.length})`);
          }
        }

        // 尝试转换验证
        try {
          const floatValue = parseFloat(outputValue);
          console.log(`   转换为浮点数: ${floatValue}`);
          
          if (testCase.tokenB === 'usdt.tether-token.near') {
            // USDT应该在合理范围内
            if (floatValue > 0 && floatValue < 1000000) {
              console.log(`   💡 推测: 人类可读格式 (USDT值在合理范围)`);
            } else {
              console.log(`   💡 推测: 可能是wei格式 (值过大)`);
            }
          } else {
            // NEAR应该在合理范围内
            if (floatValue > 0 && floatValue < 10000) {
              console.log(`   💡 推测: 人类可读格式 (NEAR值在合理范围)`);
            } else {
              console.log(`   💡 推测: 可能是wei格式 (值过大)`);
            }
          }
        } catch (error) {
          console.log(`   ❌ 无法转换为数字`);
        }
      } else {
        console.log(`   ❌ 无响应结果`);
      }

    } catch (error: any) {
      console.log(`   ❌ 请求失败: ${error.message}`);
    }
  }
}

/**
 * 对比我们的VeaxQuoteService
 */
async function testVeaxQuoteService() {
  console.log('\n🔄 对比我们的VeaxQuoteService');
  console.log('='.repeat(60));

  // 动态导入避免编译错误
  const { VeaxQuoteService } = await import('../services/veaxQuoteService');

  const testCases = [
    {
      name: '7 NEAR → USDT',
      tokenA: 'wrap.near',
      tokenB: 'usdt.tether-token.near',
      amount: '7'
    },
    {
      name: '0.1 NEAR → USDT',
      tokenA: 'wrap.near',
      tokenB: 'usdt.tether-token.near',
      amount: '0.1'
    },
    {
      name: '1 NEAR → DOGSHIT',
      tokenA: 'wrap.near',
      tokenB: 'dogshit-1408.meme-cooking.near',
      amount: '1'
    },
    {
      name: '1 NEAR → BLACKDRAGON',
      tokenA: 'wrap.near',
      tokenB: 'blackdragon.tkn.near',
      amount: '1'
    },
    {
      name: '1000000 DOGSHIT → NEAR',
      tokenA: 'dogshit-1408.meme-cooking.near',
      tokenB: 'wrap.near',
      amount: '1000000'
    },
    {
      name: '1000000 BLACKDRAGON → NEAR',
      tokenA: 'blackdragon.tkn.near',
      tokenB: 'wrap.near',
      amount: '1000000'
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n📊 测试: ${testCase.name}`);
    
    try {
      const result = await VeaxQuoteService.getQuote(
        testCase.tokenA,
        testCase.tokenB,
        testCase.amount
      );

      if (result.success) {
        console.log(`   ✅ VeaxQuoteService成功:`);
        console.log(`   outputAmount: ${result.outputAmount}`);
        console.log(`   类型: ${typeof result.outputAmount}`);
        console.log(`   长度: ${result.outputAmount.length}`);
        console.log(`   包含小数点: ${result.outputAmount.includes('.')}`);
        
        const floatValue = parseFloat(result.outputAmount);
        console.log(`   转换为浮点数: ${floatValue}`);
      } else {
        console.log(`   ❌ VeaxQuoteService失败: ${result.error}`);
      }
    } catch (error: any) {
      console.log(`   ❌ VeaxQuoteService错误: ${error.message}`);
    }
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试VEAX API返回值格式');
  console.log('='.repeat(80));
  
  try {
    // 直接测试VEAX API
    await testVeaxApiDirect();
    
    // 对比我们的服务
    await testVeaxQuoteService();
    
    console.log('\n🎉 测试完成!');
    console.log('='.repeat(80));
    console.log('📊 请根据上述结果判断VEAX返回的是人类可读格式还是wei格式');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
runTests();
