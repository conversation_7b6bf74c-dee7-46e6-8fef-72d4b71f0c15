/**
 * 测试wei格式精度修复
 */

import 'dotenv/config';
import { formatNearAmount, parseNearAmount } from 'near-api-js/lib/utils/format';

/**
 * 精确的wei转人类可读格式
 */
function fromWei(amount: string, decimals: number): string {
  if (decimals === 24) {
    return formatNearAmount(amount).replace(/,/g, '');
  } else {
    if (amount === '0') return '0';
    
    const paddedAmount = amount.padStart(decimals + 1, '0');
    const integerPart = paddedAmount.slice(0, -decimals) || '0';
    const decimalPart = paddedAmount.slice(-decimals).replace(/0+$/, '');
    
    return decimalPart ? `${integerPart}.${decimalPart}` : integerPart;
  }
}

/**
 * 测试精度损失问题
 */
function testPrecisionLoss() {
  console.log('🧪 测试wei格式精度损失问题');
  console.log('='.repeat(60));

  const testCases = [
    {
      name: '问题案例',
      amount: '6331830778580438089728',
      decimals: 18,
      symbol: '0xSHITZU'
    },
    {
      name: 'NEAR案例',
      amount: '100000000000000000000000',
      decimals: 24,
      symbol: 'NEAR'
    },
    {
      name: '大数值案例',
      amount: '2518779270443279475429878220322304',
      decimals: 24,
      symbol: 'BLACKDRAGON'
    }
  ];

  testCases.forEach(testCase => {
    console.log(`\n📊 测试: ${testCase.name} (${testCase.symbol})`);
    console.log(`   原始wei: ${testCase.amount}`);
    
    // 错误的方法（使用parseFloat）
    const amountFloat = parseFloat(testCase.amount);
    const wrongResult = (amountFloat / Math.pow(10, testCase.decimals)).toString();
    
    // 正确的方法（使用fromWei）
    const correctResult = fromWei(testCase.amount, testCase.decimals);
    
    console.log(`   ❌ 错误方法: ${wrongResult}`);
    console.log(`   ✅ 正确方法: ${correctResult}`);
    
    // 检查精度损失
    const hasLoss = wrongResult !== correctResult;
    console.log(`   精度损失: ${hasLoss ? '❌ 有损失' : '✅ 无损失'}`);
    
    if (hasLoss) {
      // 计算损失的wei数量
      const wrongWei = parseFloat(wrongResult) * Math.pow(10, testCase.decimals);
      const originalWei = parseFloat(testCase.amount);
      const lossWei = originalWei - wrongWei;
      console.log(`   损失wei: ${lossWei.toFixed(0)}`);
    }
  });
}

/**
 * 测试REF Smart Router的amount_in精度
 */
function testRefSmartRouterPrecision() {
  console.log('\n🔧 测试REF Smart Router的amount_in精度');
  console.log('='.repeat(60));

  const originalWei = '6331830778580438089728';
  const decimals = 18;
  
  console.log(`📊 原始wei: ${originalWei}`);
  
  // 模拟错误的处理流程
  console.log('\n❌ 错误的处理流程:');
  const step1_wrong = parseFloat(originalWei);
  console.log(`   1. parseFloat: ${step1_wrong}`);
  
  const step2_wrong = (step1_wrong / Math.pow(10, decimals)).toString();
  console.log(`   2. 转人类可读: ${step2_wrong}`);
  
  // 模拟传递给REF Smart Router
  const step3_wrong = parseFloat(step2_wrong) * Math.pow(10, decimals);
  console.log(`   3. REF内部转wei: ${step3_wrong}`);
  console.log(`   4. 最终amount_in: "${step3_wrong.toFixed(0)}"`);
  
  // 模拟正确的处理流程
  console.log('\n✅ 正确的处理流程:');
  const step1_correct = fromWei(originalWei, decimals);
  console.log(`   1. fromWei: ${step1_correct}`);
  
  // 模拟传递给REF Smart Router（使用精确方法）
  const [integer, decimal = ''] = step1_correct.split('.');
  const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
  const step2_correct = (integer || '0') + paddedDecimal;
  console.log(`   2. REF内部精确转wei: ${step2_correct}`);
  console.log(`   3. 最终amount_in: "${step2_correct}"`);
  
  // 对比结果
  console.log('\n📊 对比结果:');
  console.log(`   原始: ${originalWei}`);
  console.log(`   错误: ${step3_wrong.toFixed(0)}`);
  console.log(`   正确: ${step2_correct}`);
  console.log(`   匹配: ${originalWei === step2_correct ? '✅' : '❌'}`);
}

/**
 * 测试不同精度的代币
 */
function testDifferentDecimals() {
  console.log('\n🪙 测试不同精度的代币');
  console.log('='.repeat(60));

  const testCases = [
    { name: 'USDT', decimals: 6, wei: '1234567' },
    { name: 'USDC', decimals: 6, wei: '9876543' },
    { name: 'ETH类', decimals: 18, wei: '1234567890123456789' },
    { name: '0xSHITZU', decimals: 18, wei: '6331830778580438089728' },
    { name: 'NEAR', decimals: 24, wei: '100000000000000000000000' },
    { name: 'BLACKDRAGON', decimals: 24, wei: '2518779270443279475429878220322304' }
  ];

  testCases.forEach(testCase => {
    console.log(`\n📊 ${testCase.name} (${testCase.decimals}位精度):`);
    console.log(`   wei: ${testCase.wei}`);
    
    // 错误方法
    const wrongFloat = parseFloat(testCase.wei);
    const wrongHuman = (wrongFloat / Math.pow(10, testCase.decimals)).toString();
    
    // 正确方法
    const correctHuman = fromWei(testCase.wei, testCase.decimals);
    
    console.log(`   ❌ 错误转换: ${wrongHuman}`);
    console.log(`   ✅ 正确转换: ${correctHuman}`);
    console.log(`   精度保持: ${wrongHuman === correctHuman ? '✅' : '❌'}`);
  });
}

/**
 * 主测试函数
 */
function runTests() {
  console.log('🚀 开始测试wei格式精度修复');
  console.log('='.repeat(80));
  
  try {
    // 测试精度损失
    testPrecisionLoss();
    
    // 测试REF Smart Router精度
    testRefSmartRouterPrecision();
    
    // 测试不同精度代币
    testDifferentDecimals();
    
    console.log('\n🎉 所有测试完成!');
    console.log('='.repeat(80));
    console.log('✅ 精度损失问题已识别');
    console.log('✅ 修复方案已验证');
    console.log('✅ fromWei方法保持完整精度');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
runTests();
