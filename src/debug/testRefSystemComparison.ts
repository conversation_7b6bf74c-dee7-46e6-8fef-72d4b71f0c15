/**
 * 测试REF系统对比
 * 验证V1 Smart Router和DCL v2是否都在工作
 */

import 'dotenv/config';
import { refQuoteService } from '../services/refQuoteService';
import { v1SmartRouter } from '../services/v1SmartRouter';
import { dclv2Contract } from '../services/dclv2Contract';
import { TOKENS } from '../config/tradingPairs';

async function testRefSystemComparison() {
  console.log('🔍 测试REF系统对比');
  console.log('='.repeat(60));

  try {
    // 测试代币
    const tokenA = TOKENS.NEAR;
    const tokenB = TOKENS.USDT;

    const testAmount = '5';

    console.log(`📊 测试交易对: ${tokenA.symbol} → ${tokenB.symbol}`);
    console.log(`💰 测试金额: ${testAmount} ${tokenA.symbol}`);
    console.log('');

    // 1. 分别测试V1和DCL v2系统
    console.log('1️⃣ 分别测试两个系统...');
    
    const params = {
      tokenIn: tokenA,
      tokenOut: tokenB,
      amountIn: testAmount,
      slippage: 0.005
    };

    // 测试V1 Smart Router
    console.log('\n🔄 测试V1 Smart Router...');
    try {
      const v1Quote = await v1SmartRouter.getV1Quote(params);
      if (v1Quote) {
        console.log(`✅ V1 Smart Router成功:`);
        console.log(`   输出金额: ${v1Quote.outputAmount} ${tokenB.symbol}`);
        console.log(`   价格影响: ${v1Quote.priceImpact || 'N/A'}`);
        console.log(`   路径详情: ${v1SmartRouter.getRouteDetails(v1Quote)}`);
      } else {
        console.log(`❌ V1 Smart Router返回null`);
      }
    } catch (error) {
      console.log(`❌ V1 Smart Router失败: ${error}`);
    }

    // 测试DCL v2
    console.log('\n🔄 测试DCL v2...');
    try {
      const dclv2Quote = await dclv2Contract.getDCLv2Quote(params);
      if (dclv2Quote) {
        console.log(`✅ DCL v2成功:`);
        console.log(`   输出金额: ${dclv2Quote.outputAmount} ${tokenB.symbol}`);
        console.log(`   价格影响: ${dclv2Quote.priceImpact || 'N/A'}`);
        console.log(`   池子详情: ${dclv2Contract.getBestPoolInfo(dclv2Quote)}`);
      } else {
        console.log(`❌ DCL v2返回null`);
      }
    } catch (error) {
      console.log(`❌ DCL v2失败: ${error}`);
    }

    // 2. 测试统一报价服务
    console.log('\n2️⃣ 测试统一报价服务...');
    
    const unifiedQuote = await refQuoteService.getQuote(params);
    
    console.log(`🏆 最佳报价系统: ${unifiedQuote.system}`);
    console.log(`💰 最佳输出金额: ${unifiedQuote.outputAmount} ${tokenB.symbol}`);
    console.log(`📊 价格影响: ${unifiedQuote.priceImpact || 'N/A'}`);
    
    if (unifiedQuote.system === 'V1') {
      console.log(`🛣️ V1路径: ${v1SmartRouter.getRouteDetails(unifiedQuote)}`);
    } else {
      console.log(`🏊 DCL v2池子: ${dclv2Contract.getBestPoolInfo(unifiedQuote)}`);
    }

    // 3. 测试反向交易
    console.log('\n3️⃣ 测试反向交易...');
    
    const reverseParams = {
      tokenIn: tokenB,
      tokenOut: tokenA,
      amountIn: unifiedQuote.outputAmount,
      slippage: 0.005
    };

    const reverseQuote = await refQuoteService.getQuote(reverseParams);
    
    console.log(`🔄 反向交易系统: ${reverseQuote.system}`);
    console.log(`💰 反向输出金额: ${reverseQuote.outputAmount} ${tokenA.symbol}`);
    
    // 计算往返损失
    const originalAmount = parseFloat(testAmount);
    const finalAmount = parseFloat(reverseQuote.outputAmount);
    const roundTripLoss = originalAmount - finalAmount;
    const lossPercentage = (roundTripLoss / originalAmount) * 100;
    
    console.log(`📊 往返分析:`);
    console.log(`   原始金额: ${originalAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   最终金额: ${finalAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   往返损失: ${roundTripLoss.toFixed(6)} ${tokenA.symbol} (${lossPercentage.toFixed(4)}%)`);

    // 4. 系统使用统计
    console.log('\n4️⃣ 系统使用统计...');
    
    const systems = [unifiedQuote.system, reverseQuote.system];
    const v1Count = systems.filter(s => s === 'V1').length;
    const dclv2Count = systems.filter(s => s === 'DCL_V2').length;
    
    console.log(`📊 本次测试中:`);
    console.log(`   V1 Smart Router: ${v1Count}/2 次 (${(v1Count/2*100).toFixed(1)}%)`);
    console.log(`   DCL v2: ${dclv2Count}/2 次 (${(dclv2Count/2*100).toFixed(1)}%)`);

    if (v1Count === 0) {
      console.log(`⚠️ 注意: V1 Smart Router在本次测试中未被选择`);
      console.log(`   可能原因:`);
      console.log(`   1. DCL v2提供了更好的价格`);
      console.log(`   2. V1系统在该交易对上流动性不足`);
      console.log(`   3. V1系统暂时不可用`);
    }

    if (dclv2Count === 0) {
      console.log(`⚠️ 注意: DCL v2在本次测试中未被选择`);
      console.log(`   可能原因:`);
      console.log(`   1. V1 Smart Router提供了更好的价格`);
      console.log(`   2. DCL v2系统在该交易对上流动性不足`);
      console.log(`   3. DCL v2系统暂时不可用`);
    }

    console.log('\n✅ REF系统对比测试完成!');
    console.log('📋 结论: 系统会自动选择提供最佳价格的路径');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testRefSystemComparison().catch(console.error);
