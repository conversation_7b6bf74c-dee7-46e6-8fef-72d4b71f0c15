/**
 * REF Finance 真实交易测试 - 修复版本
 *
 * 使用修复版本的RefExecutionService进行真实交易测试
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

function fromWei(amount: string, decimals: number): string {
  return new Big(amount).div(new Big(10).pow(decimals)).toString();
}

/**
 * REF Finance 真实交易测试 - 修复版本
 */
async function testRefRealTradeFixed() {
  console.log('🚀 REF Finance 真实交易测试 - 修复版本');
  console.log('='.repeat(60));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请运行 npm run setup:env 设置环境变量');
    return;
  }

  // 安全检查
  if (!EXECUTION_CONFIG.SAFETY.ENABLE_REAL_TRADING) {
    console.log('⚠️ 真实交易已禁用');
    console.log('💡 要启用真实交易，请在.env文件中设置 ENABLE_REAL_TRADING=true');
    console.log('🔧 当前为安全模式，只会显示交易参数');
  }

  const refExecution = new RefExecutionServiceFixed(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化REF执行服务...');
    await refExecution.initialize();

    // 2. 获取报价
    console.log('\n2️⃣ 获取REF Finance报价...');
    const testAmount = '0.1'; // 0.1 NEAR - 小额测试
    
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: testAmount,
      slippage: 0.005
    };

    const quote = await refQuoteService.getBestQuote(quoteParams);
    console.log(`📊 报价结果:`);
    console.log(`   输入: ${testAmount} ${TOKENS.NEAR.symbol}`);
    console.log(`   输出: ${quote.outputAmount} ${TOKENS.USDC.symbol}`);
    console.log(`   系统: ${quote.system}`);
    console.log(`   合约: ${quote.contractId}`);

    // 3. 计算交易参数
    console.log('\n3️⃣ 计算交易参数...');
    const inputAmountWei = toWei(testAmount, TOKENS.NEAR.decimals);
    
    const slippage = 0.02; // 2% 滑点
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDC.decimals);

    console.log(`📊 交易参数:`);
    console.log(`   输入金额(wei): ${inputAmountWei}`);
    console.log(`   最小输出: ${minOutputAmount} USDC`);
    console.log(`   最小输出(wei): ${minOutputAmountWei}`);
    console.log(`   滑点容忍: ${slippage * 100}%`);

    // 4. 展示正确的交易构建
    console.log('\n4️⃣ 交易构建分析...');
    console.log('🔧 修复版本的正确调用方式:');
    console.log(`   合约: ${TOKENS.NEAR.id}.ft_transfer_call()`);
    console.log(`   接收者: ${quote.contractId}`);
    console.log(`   金额: ${inputAmountWei}`);

    if (quote.system === 'V1' && quote.rawResponse) {
      console.log('\n📋 V1交易详细分析:');
      const routeData = quote.rawResponse.result_data;
      console.log(`   路径数量: ${routeData.routes.length}`);
      
      routeData.routes.forEach((route: any, routeIndex: number) => {
        console.log(`   路径${routeIndex + 1}:`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`     动作${poolIndex + 1}: 池子${pool.pool_id}`);
          console.log(`       ${pool.token_in.slice(-10)} → ${pool.token_out.slice(-10)}`);
          if (pool.amount_in && pool.amount_in !== "0") {
            console.log(`       amount_in: ${pool.amount_in}`);
          }
        });
      });
    }

    // 5. 执行交易
    console.log('\n5️⃣ 执行交易...');
    
    if (!EXECUTION_CONFIG.SAFETY.ENABLE_REAL_TRADING) {
      console.log('🔒 安全模式 - 模拟执行');
      console.log('📋 将要执行的交易:');
      console.log(`   调用: ${TOKENS.NEAR.id}.ft_transfer_call()`);
      console.log(`   参数: {`);
      console.log(`     receiver_id: "${quote.contractId}",`);
      console.log(`     amount: "${inputAmountWei}",`);
      console.log(`     msg: "...交易消息..."`);
      console.log(`   }`);
      console.log('💡 要执行真实交易，请设置 ENABLE_REAL_TRADING=true');
      return;
    }

    // 真实交易执行
    console.log('🚀 执行真实交易...');
    console.log('⚠️ 这将消耗真实的代币和Gas费用！');

    let executionResult;
    
    if (quote.system === 'V1') {
      console.log('📋 执行V1交易...');
      executionResult = await refExecution.executeV1Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        slippage
      );
    } else if (quote.system === 'DCL_V2') {
      console.log('📋 执行DCL v2交易...');
      const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
      executionResult = await refExecution.executeDCLv2Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        poolId,
        TOKENS.USDC.id
      );
    }

    // 6. 分析结果
    console.log('\n6️⃣ 交易结果分析...');
    
    if (executionResult?.success) {
      console.log('🎉 交易执行成功!');
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 预期输出: ${quote.outputAmount} USDC`);
      
      if (executionResult.outputAmount) {
        console.log(`📊 实际输出: ${executionResult.outputAmount} USDC`);
      }

      // 计算实际汇率
      const actualRate = parseFloat(quote.outputAmount) / parseFloat(testAmount);
      console.log(`📈 实际汇率: 1 NEAR = ${actualRate.toFixed(6)} USDC`);

      console.log('\n✅ 修复版本交易成功执行!');
      console.log('🎯 关键修复点验证:');
      console.log('   ✅ 正确调用输入代币合约');
      console.log('   ✅ 正确设置receiver_id');
      console.log('   ✅ 正确构建交易消息');

    } else {
      console.log('❌ 交易执行失败');
      console.log(`🔍 错误信息: ${executionResult?.error}`);
      
      console.log('\n🔧 故障排除建议:');
      console.log('1. 检查账户余额是否充足');
      console.log('2. 检查代币是否已注册');
      console.log('3. 检查网络连接状态');
      console.log('4. 检查滑点设置是否合理');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
    
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查环境变量配置');
    console.log('2. 检查网络连接');
    console.log('3. 检查账户权限');
    console.log('4. 检查代币余额');
  }
}

/**
 * 安全检查函数
 */
function performSafetyChecks(): boolean {
  console.log('\n🔒 安全检查...');
  
  const checks = [
    {
      name: '环境变量配置',
      check: () => EXECUTION_CONFIG.ACCOUNT_ID && EXECUTION_CONFIG.PRIVATE_KEY,
      message: '请设置NEAR_ACCOUNT_ID和NEAR_PRIVATE_KEY'
    },
    {
      name: '交易金额限制',
      check: () => parseFloat(EXECUTION_CONFIG.SAFETY.MAX_TRADE_AMOUNT_NEAR) >= 0.1,
      message: '测试金额超出限制'
    },
    {
      name: '网络配置',
      check: () => EXECUTION_CONFIG.NETWORK_ID === 'mainnet',
      message: '请确认网络配置正确'
    }
  ];

  let allPassed = true;
  
  checks.forEach(check => {
    const passed = check.check();
    console.log(`   ${passed ? '✅' : '❌'} ${check.name}`);
    if (!passed) {
      console.log(`      💡 ${check.message}`);
      allPassed = false;
    }
  });

  return allPassed;
}

// 运行测试
if (require.main === module) {
  if (performSafetyChecks()) {
    testRefRealTradeFixed().catch(console.error);
  } else {
    console.log('\n❌ 安全检查未通过，请修复问题后重试');
  }
}

export { testRefRealTradeFixed };
