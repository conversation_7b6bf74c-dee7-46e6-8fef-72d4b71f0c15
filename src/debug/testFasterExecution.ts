/**
 * 测试更快的交易执行级别
 * 
 * 使用 NEAR RPC API 的不同执行级别来优化交易速度
 */

import 'dotenv/config';
import { Near, Account, keyStores } from 'near-api-js';
import { parseNearAmount } from 'near-api-js/lib/utils/format';
import { functionCall } from 'near-api-js/lib/transaction';
import { TxExecutionStatus } from '@near-js/types';

async function testFasterExecution() {
  console.log('⚡ 测试更快的交易执行级别');
  console.log('='.repeat(60));

  try {
    // 1. 初始化NEAR连接
    const keyStore = new keyStores.InMemoryKeyStore();
    const privateKey = process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!;
    const accountId = process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!;
    
    await keyStore.setKey('mainnet', accountId, privateKey);

    const near = new Near({
      networkId: 'mainnet',
      keyStore,
      nodeUrl: 'https://rpc.mainnet.near.org',
      walletUrl: 'https://wallet.mainnet.near.org',
      helperUrl: 'https://helper.mainnet.near.org'
    });

    const account = await near.account(accountId);

    // 2. 测试不同的执行级别
    const testAmount = parseNearAmount('0.001')!; // 0.001 NEAR

    console.log('\n📊 测试不同执行级别的性能...');

    // 测试 EXECUTED_OPTIMISTIC (默认)
    console.log('\n1️⃣ 测试 EXECUTED_OPTIMISTIC (默认)...');
    const startOptimistic = Date.now();
    
    try {
      const resultOptimistic = await account.functionCall({
        contractId: 'wrap.near',
        methodName: 'near_deposit',
        args: {},
        attachedDeposit: BigInt(testAmount),
        gas: BigInt('**************')
      });
      
      const endOptimistic = Date.now();
      console.log(`✅ EXECUTED_OPTIMISTIC 完成: ${endOptimistic - startOptimistic}ms`);
      console.log(`📊 交易哈希: ${resultOptimistic.transaction.hash}`);
    } catch (error) {
      console.log(`❌ EXECUTED_OPTIMISTIC 失败:`, error);
    }

    // 等待一下避免nonce冲突
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试 INCLUDED (更快)
    console.log('\n2️⃣ 测试 INCLUDED (更快)...');
    const startIncluded = Date.now();
    
    try {
      // 构建交易
      const actions = [functionCall(
        'near_deposit',
        {},
        BigInt('**************'),
        BigInt(testAmount)
      )];

      // 使用 provider 的 sendTransactionUntil 方法
      const signedTx = await account.signTransaction({
        receiverId: 'wrap.near',
        actions
      });

      const resultIncluded = await account.connection.provider.sendTransactionUntil(
        signedTx,
        'INCLUDED' as TxExecutionStatus
      );
      
      const endIncluded = Date.now();
      console.log(`✅ INCLUDED 完成: ${endIncluded - startIncluded}ms`);
      console.log(`📊 交易哈希: ${resultIncluded.transaction.hash}`);
      
      // 注意：INCLUDED 级别可能没有完整的 receipts
      console.log(`📋 Receipts 数量: ${resultIncluded.receipts_outcome?.length || 0}`);
      
    } catch (error) {
      console.log(`❌ INCLUDED 失败:`, error);
    }

    // 等待一下避免nonce冲突
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 测试 NONE (最快，但风险最高)
    console.log('\n3️⃣ 测试 NONE (最快，但风险最高)...');
    const startNone = Date.now();
    
    try {
      // 构建交易
      const actions = [functionCall(
        'near_deposit',
        {},
        BigInt('**************'),
        BigInt(testAmount)
      )];

      // 使用 provider 的 sendTransactionUntil 方法
      const signedTx = await account.signTransaction({
        receiverId: 'wrap.near',
        actions
      });

      const resultNone = await account.connection.provider.sendTransactionUntil(
        signedTx,
        'NONE' as TxExecutionStatus
      );
      
      const endNone = Date.now();
      console.log(`✅ NONE 完成: ${endNone - startNone}ms`);
      console.log(`📊 交易哈希: ${resultNone.transaction.hash}`);
      
      // 注意：NONE 级别几乎没有执行信息
      console.log(`📋 Receipts 数量: ${resultNone.receipts_outcome?.length || 0}`);
      console.log(`⚠️ 注意：NONE 级别只确认交易被提交，不保证执行完成`);
      
    } catch (error) {
      console.log(`❌ NONE 失败:`, error);
    }

    console.log('\n📊 性能对比总结:');
    console.log('┌─────────────────────────────────────────────────────────┐');
    console.log('│ 执行级别           │ 速度   │ 安全性 │ Receipt完整性    │');
    console.log('├─────────────────────────────────────────────────────────┤');
    console.log('│ NONE              │ 最快   │ 最低   │ 无               │');
    console.log('│ INCLUDED          │ 快     │ 低     │ 部分             │');
    console.log('│ EXECUTED_OPTIMISTIC│ 中等   │ 中等   │ 完整             │');
    console.log('│ EXECUTED          │ 慢     │ 高     │ 完整+最终化      │');
    console.log('│ FINAL             │ 最慢   │ 最高   │ 完整+完全最终化  │');
    console.log('└─────────────────────────────────────────────────────────┘');

    console.log('\n💡 套利优化建议:');
    console.log('1. 第一步交易：使用 EXECUTED_OPTIMISTIC (需要完整receipts)');
    console.log('2. 第二步交易：可以使用 INCLUDED (更快，风险可控)');
    console.log('3. 监控模式：可以使用 NONE (最快获取交易哈希)');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testFasterExecution().catch(console.error);
