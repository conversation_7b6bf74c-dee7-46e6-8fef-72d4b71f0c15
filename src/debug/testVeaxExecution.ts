/**
 * VEAX DEX 交易执行测试
 *
 * 测试VEAX的swap_exact_in交易执行功能
 */

import 'dotenv/config';
import VeaxExecutionService from '../services/veaxExecutionService';
import VeaxQuoteService from '../services/veaxQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';

/**
 * VEAX 执行测试
 */
async function testVeaxExecution() {
  console.log('🚀 开始VEAX交易执行测试');

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请在.env文件中设置这些变量');
    return;
  }

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化VEAX执行服务...');
    await veaxExecution.initialize();

    // 1.5. 检查账户余额
    console.log('\n💰 检查账户余额...');
    const balanceInfo = await veaxExecution.checkAccountBalance();
    const wNearBalanceInfo = await veaxExecution.checkWNearBalance();

    console.log(`💳 NEAR余额: ${balanceInfo.balanceNear.toFixed(6)} NEAR (支付费用)`);
    console.log(`🔄 wNEAR余额: ${wNearBalanceInfo.balanceWNear.toFixed(6)} wNEAR (交易用)`);

    if (balanceInfo.balanceNear < 2) {
      console.log('⚠️ NEAR余额不足，可能无法支付交易费用');
    }

    if (wNearBalanceInfo.balanceWNear < 1) {
      console.log('⚠️ wNEAR余额不足，需要先包装NEAR为wNEAR');
      console.log('💡 访问 https://app.ref.finance 进行包装');
    }

    // 2. 检查用户注册状态
    console.log('\n2️⃣ 检查用户注册状态...');
    const userStatus = await veaxExecution.checkUserRegistration();
    console.log(`👤 用户注册状态: ${userStatus.isRegistered ? '已注册' : '未注册'}`);
    
    if (userStatus.isRegistered && userStatus.storageBalance) {
      console.log(`💰 存储余额: ${userStatus.storageBalance.available} / ${userStatus.storageBalance.total}`);
    }

    // 3. 检查代币注册状态
    console.log('\n3️⃣ 检查代币注册状态...');
    const tokenInStatus = await veaxExecution.checkTokenRegistration(TOKENS.NEAR.id);
    const tokenOutStatus = await veaxExecution.checkTokenRegistration(TOKENS.USDC.id);
    
    console.log(`🪙 ${TOKENS.NEAR.symbol} 注册状态: ${tokenInStatus.isRegistered ? '已注册' : '未注册'}`);
    console.log(`🪙 ${TOKENS.USDC.symbol} 注册状态: ${tokenOutStatus.isRegistered ? '已注册' : '未注册'}`);

    // 4. 获取VEAX报价
    console.log('\n4️⃣ 获取VEAX报价...');
    const quoteResult = await VeaxQuoteService.getQuote(
      TOKENS.NEAR.id,
      TOKENS.USDC.id,
      '0.1', // 1 NEAR
      0.005 // 0.5% 滑点
    );

    if (quoteResult.success) {
      console.log(`📊 VEAX报价: 1 ${TOKENS.NEAR.symbol} → ${quoteResult.outputAmount} ${TOKENS.USDC.symbol}`);
      console.log(`💥 价格影响: ${quoteResult.priceImpact}`);
      console.log(`💸 手续费: ${quoteResult.fee}`);
    } else {
      console.log(`❌ VEAX报价失败: ${quoteResult.error}`);
      return;
    }

    // 5. 计算最小输出金额 (使用BigInt避免精度问题)
    const slippage = 0.01; // 1%

    // 使用BigInt进行精确计算
    const outputAmountBigInt = BigInt(quoteResult.outputAmount);
    const slippageProtection = BigInt(99); // 99% (1 - 1% slippage)
    const hundred = BigInt(100);

    // 计算最小输出金额：outputAmount * 99 / 100
    const minOutputAmountBigInt = (outputAmountBigInt * slippageProtection) / hundred;
    const minOutputAmount = minOutputAmountBigInt.toString();

    console.log(`📉 最小输出金额: ${minOutputAmount} ${TOKENS.USDC.symbol}`);

    // 6. 执行交易（注意：这是真实交易，请谨慎！）
    console.log('\n5️⃣ 准备执行交易...');
    console.log('⚠️ 这将执行真实的交易！请确认你想要继续。');
    console.log('💡 如果只是测试，请注释掉下面的执行代码。');

    // 取消注释下面的代码来执行真实交易

    // 使用正确的金额格式 (wei格式)
    const inputAmountWei = '100000000000000000000000000'; // 0.1 wNEAR
    // 直接使用计算好的minOutputAmount，避免再次转换
    const minAmountOutWei = minOutputAmount;

    const executionResult = await veaxExecution.executeSwap(
      TOKENS.NEAR.id, // wrap.near
      TOKENS.USDC.id,  // 17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1
      inputAmountWei,
      minAmountOutWei
    );

    if (executionResult.success) {
      console.log(`✅ 交易执行成功!`);
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 输入金额: ${executionResult.amountIn} ${TOKENS.NEAR.symbol}`);
      console.log(`📊 输出金额: ${executionResult.amountOut} ${TOKENS.USDC.symbol}`);
    } else {
      console.log(`❌ 交易执行失败: ${executionResult.error}`);
    }

    console.log('\n✅ VEAX执行测试完成');
    console.log('💡 要执行真实交易，请取消注释执行代码并提供正确的账户信息');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 测试用户和代币注册流程
 */
async function testRegistrationFlow() {
  console.log('\n🔧 测试注册流程...');

  // 注意：这里需要替换为实际的账户ID和私钥
  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    await veaxExecution.initialize();

    // 测试用户注册
    console.log('\n📝 测试用户注册检查...');
    const userStatus = await veaxExecution.checkUserRegistration();
    
    if (!userStatus.isRegistered) {
      console.log('⚠️ 用户未注册，需要注册');
      // 在真实环境中，这里会执行注册
      const registerResult = await veaxExecution.registerUser();
    } else {
      console.log('✅ 用户已注册');
    }

    // 测试代币注册
    console.log('\n🪙 测试代币注册检查...');
    const tokensToCheck = [TOKENS.NEAR.id, TOKENS.USDC.id, TOKENS.USDT.id];
    
    for (const tokenId of tokensToCheck) {
      const tokenStatus = await veaxExecution.checkTokenRegistration(tokenId);
      const tokenSymbol = Object.values(TOKENS).find(t => t.id === tokenId)?.symbol || tokenId;
      
      if (!tokenStatus.isRegistered) {
        console.log(`⚠️ 代币 ${tokenSymbol} 未注册，需要注册`);
        // 在真实环境中，这里会执行注册
        const registerResult = await veaxExecution.registerToken(tokenId);
      } else {
        console.log(`✅ 代币 ${tokenSymbol} 已注册，余额: ${tokenStatus.balance || '0'}`);
      }
    }

    console.log('✅ 注册流程测试完成');

  } catch (error) {
    console.error('❌ 注册流程测试失败:', error);
  }
}

/**
 * 测试批量代币注册
 */
async function testBatchTokenRegistration() {
  console.log('\n🔧 测试批量代币注册...');

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    await veaxExecution.initialize();

    const tokensToRegister = [
      TOKENS.NEAR.id,
      TOKENS.USDC.id,
      TOKENS.USDT.id,
      TOKENS.BLACKDRAGON.id,
      TOKENS.SHITZU.id
    ];

    console.log(`📋 准备批量注册 ${tokensToRegister.length} 个代币...`);

    // 在真实环境中执行批量注册
    // const results = await veaxExecution.registerTokens(tokensToRegister);
    // console.log(`✅ 批量注册完成，成功: ${results.filter(r => r.success).length}/${results.length}`);

    console.log('💡 批量注册测试完成（未执行真实交易）');

  } catch (error) {
    console.error('❌ 批量注册测试失败:', error);
  }
}

/**
 * 测试交易参数构建
 */
async function testTransactionBuilding() {
  console.log('\n🔧 测试VEAX交易参数构建...');

  const swapParams = {
    tokens: [TOKENS.NEAR.id, TOKENS.USDC.id],
    amount_in: "1000000000000000000000000000", // 1 NEAR in wei
    min_amount_out: "2200000" // 2.2 USDC in wei
  };

  console.log('📋 VEAX交易参数:');
  console.log(`代币对: [${TOKENS.NEAR.symbol}, ${TOKENS.USDC.symbol}]`);
  console.log(`输入金额: ${swapParams.amount_in} (wei)`);
  console.log(`最小输出: ${swapParams.min_amount_out} (wei)`);
  console.log(`合约方法: swap_exact_in`);
  console.log(`合约地址: veax.near`);

  console.log('✅ 交易参数构建测试完成');
}

// 运行测试
if (require.main === module) {
  Promise.all([
    testVeaxExecution(),
    testRegistrationFlow(),
    testBatchTokenRegistration(),
    testTransactionBuilding()
  ]).catch(console.error);
}

export { 
  testVeaxExecution, 
  testRegistrationFlow, 
  testBatchTokenRegistration,
  testTransactionBuilding 
};
