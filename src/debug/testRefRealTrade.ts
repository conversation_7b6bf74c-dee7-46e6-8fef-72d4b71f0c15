/**
 * REF Finance 真实交易测试
 * 
 * 使用0.1 NEAR进行真实交易测试
 */

import 'dotenv/config';
import RefExecutionService from '../services/refExecutionService';
import { refQuoteService } from '../services/refQuoteService';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import { TOKENS } from '../config/tradingPairs';
import Big from 'big.js';

/**
 * 执行0.1 NEAR的真实REF交易测试
 */
async function testRefRealTrade() {
  console.log('🚀 REF Finance 真实交易测试 - 0.1 NEAR');
  console.log('='.repeat(50));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    return;
  }

  console.log(`✅ 环境变量配置正确`);
  console.log(`📋 账户: ${EXECUTION_CONFIG.ACCOUNT_ID}`);

  const refExecution = new RefExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 步骤1: 初始化服务
    console.log('\n1️⃣ 初始化REF服务...');
    await refExecution.initialize();
    console.log('✅ 服务初始化成功');

    // 步骤2: 检查余额
    console.log('\n2️⃣ 检查账户余额...');
    const balanceInfo = await refExecution.checkAccountBalance();
    const wNearBalance = await refExecution.checkWNearBalance();
    
    console.log(`💳 NEAR余额: ${balanceInfo.balanceNear.toFixed(6)} NEAR`);
    console.log(`🔄 wNEAR余额: ${wNearBalance.balanceWNear.toFixed(6)} wNEAR`);
    
    if (wNearBalance.balanceWNear < 0.1) {
      console.log('❌ wNEAR余额不足0.1，无法进行测试');
      console.log('💡 请先在 https://app.ref.finance 包装一些NEAR为wNEAR');
      return;
    }

    // 步骤3: 获取REF报价
    console.log('\n3️⃣ 获取REF Finance报价...');
    
    const quoteParams = {
      inputToken: TOKENS.NEAR.id,
      outputToken: TOKENS.USDC.id,
      inputAmount: '0.1',
      slippage: 0.005 // 0.5% 滑点
    };

    const quoteResult = await refQuoteService.getQuote(quoteParams);

    if (!quoteResult.success) {
      console.log(`❌ REF报价失败: ${quoteResult.error}`);
      return;
    }

    console.log(`📊 REF Finance报价成功:`);
    console.log(`   输入: 0.1 NEAR`);
    console.log(`   输出: ${quoteResult.outputAmount} USDC`);
    console.log(`   系统: ${quoteResult.system}`);
    console.log(`   路径: ${quoteResult.path?.join(' → ') || 'Direct'}`);
    if (quoteResult.priceImpact) {
      console.log(`   价格影响: ${quoteResult.priceImpact}`);
    }

    // 步骤4: 计算交易参数
    console.log('\n4️⃣ 计算交易参数...');
    
    // 输入金额 (0.1 NEAR = 0.1 * 10^24 yoctoNEAR)
    const inputAmount = new Big('0.1').times(new Big(10).pow(24)).toFixed(0);
    
    // 最小输出金额 (考虑滑点)
    const expectedOutput = new Big(quoteResult.outputAmount);
    const slippageTolerance = new Big('0.005'); // 0.5%
    const minOutput = expectedOutput.times(new Big(1).minus(slippageTolerance));
    const minOutputWei = minOutput.times(new Big(10).pow(6)).toFixed(0); // USDC是6位小数

    console.log(`📋 交易参数:`);
    console.log(`   输入金额: ${inputAmount} yoctoNEAR (0.1 NEAR)`);
    console.log(`   最小输出: ${minOutputWei} microUSDC (${minOutput.toFixed(6)} USDC)`);
    console.log(`   使用系统: ${quoteResult.system}`);

    // 步骤5: 确认执行
    console.log('\n5️⃣ 准备执行真实交易...');
    console.log('⚠️ 这将使用真实的0.1 NEAR进行交易！');
    console.log('💡 如果您不想执行真实交易，请按 Ctrl+C 退出');
    
    // 等待3秒给用户取消的机会
    console.log('倒计时: 3...');
    await sleep(1000);
    console.log('倒计时: 2...');
    await sleep(1000);
    console.log('倒计时: 1...');
    await sleep(1000);

    // 步骤6: 执行交易
    console.log('\n6️⃣ 执行REF Finance交易...');
    
    let executionResult;
    
    if (quoteResult.system === 'V1') {
      // 使用V1系统执行
      executionResult = await refExecution.executeV1Swap(
        quoteResult,
        inputAmount,
        minOutputWei,
        0.005
      );
    } else if (quoteResult.system === 'DCL v2') {
      // 使用DCL v2系统执行
      executionResult = await refExecution.executeDCLv2Swap(
        quoteResult,
        inputAmount,
        minOutputWei
      );
    } else {
      console.log('❌ 未知的交易系统');
      return;
    }

    if (executionResult.success) {
      console.log('\n✅ 交易执行成功!');
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 输入金额: ${executionResult.amountIn} yoctoNEAR`);
      console.log(`📊 输出金额: ${executionResult.amountOut} microUSDC`);
      
      // 计算实际汇率
      const actualInputNear = new Big(executionResult.amountIn || inputAmount).div(new Big(10).pow(24));
      const actualOutputUsdc = new Big(executionResult.amountOut || minOutputWei).div(new Big(10).pow(6));
      const actualRate = actualOutputUsdc.div(actualInputNear);
      
      console.log(`💱 实际汇率: 1 NEAR = ${actualRate.toFixed(6)} USDC`);
      console.log(`🏭 执行系统: ${quoteResult.system}`);
      
      // 检查交易后余额
      console.log('\n📊 检查交易后余额...');
      const newWNearBalance = await refExecution.checkWNearBalance();
      console.log(`🔄 新的wNEAR余额: ${newWNearBalance.balanceWNear.toFixed(6)} wNEAR`);
      
    } else {
      console.log('\n❌ 交易执行失败!');
      console.log(`🔍 错误信息: ${executionResult.error}`);
      
      // 提供调试建议
      console.log('\n🔧 调试建议:');
      console.log('1. 检查错误信息中的具体错误代码');
      console.log('2. 查看交易哈希（如果有）在 NEAR Explorer');
      console.log('3. 确认账户有足够的wNEAR余额');
      console.log('4. 尝试减少交易金额或增加滑点容忍度');
      console.log('5. 检查REF Finance合约状态');
    }

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error);
    
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
    }
  }
}

/**
 * 测试不同的REF系统
 */
async function testDifferentRefSystems() {
  console.log('\n🧪 测试不同REF系统...');
  
  const testPairs = [
    { tokenIn: TOKENS.NEAR.id, tokenOut: TOKENS.USDC.id, symbol: 'NEAR → USDC' },
    { tokenIn: TOKENS.NEAR.id, tokenOut: TOKENS.USDT.id, symbol: 'NEAR → USDT' },
    { tokenIn: TOKENS.USDC.id, tokenOut: TOKENS.NEAR.id, symbol: 'USDC → NEAR' }
  ];

  for (const pair of testPairs) {
    console.log(`\n📊 测试 ${pair.symbol}:`);
    
    try {
      const quoteParams = {
        inputToken: pair.tokenIn,
        outputToken: pair.tokenOut,
        inputAmount: '0.1',
        slippage: 0.005
      };

      const quoteResult = await refQuoteService.getQuote(quoteParams);
      
      if (quoteResult.success) {
        console.log(`   ✅ 报价成功: ${quoteResult.outputAmount}`);
        console.log(`   🏭 系统: ${quoteResult.system}`);
        if (quoteResult.priceImpact) {
          console.log(`   💥 价格影响: ${quoteResult.priceImpact}`);
        }
      } else {
        console.log(`   ❌ 报价失败: ${quoteResult.error}`);
      }
    } catch (error) {
      console.log(`   ❌ 测试失败: ${error}`);
    }
  }
}

/**
 * 睡眠函数
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 运行测试
if (require.main === module) {
  // 可以选择运行哪个测试
  const args = process.argv.slice(2);
  
  if (args.includes('--systems')) {
    testDifferentRefSystems().catch(console.error);
  } else {
    testRefRealTrade().catch(console.error);
  }
}

export { testRefRealTrade, testDifferentRefSystems };
