import axios from 'axios';
import { getConfig } from '../config';

/**
 * 测试不同金额的 DCL v2 报价
 */
async function testDifferentAmounts() {
  const config = getConfig();
  
  // 测试不同的金额
  const testAmounts = [
    "1",      // 1 NEAR
    "10",     // 10 NEAR  
    "100",    // 100 NEAR
    "1000",   // 1000 NEAR
    "0.1",    // 0.1 NEAR
    "0.01"    // 0.01 NEAR
  ];
  
  // 使用实际存在的池子
  const poolId = "usdt.tether-token.near|wrap.near|2000"; // 0.2% 费用
  
  console.log(`🧪 测试池子: ${poolId}`);
  console.log(`💰 流动性: 87340889145032 (从之前的查询结果)`);
  console.log('='.repeat(80));
  
  for (const amount of testAmounts) {
    await testSingleAmount(poolId, amount);
    console.log('-'.repeat(40));
  }
  
  // 测试反向交换
  console.log('\n🔄 测试反向交换 (USDT → NEAR)');
  console.log('='.repeat(80));
  
  const usdtAmounts = ["1", "10", "100", "1000", "0.1"];
  
  for (const amount of usdtAmounts) {
    await testSingleAmount(poolId, amount, "usdt.tether-token.near", "wrap.near");
    console.log('-'.repeat(40));
  }
}

async function testSingleAmount(
  poolId: string, 
  amount: string, 
  inputToken: string = "wrap.near",
  outputToken: string = "usdt.tether-token.near"
) {
  const config = getConfig();
  
  try {
    console.log(`📊 测试: ${amount} ${inputToken === "wrap.near" ? "NEAR" : "USDT"} → ${outputToken === "wrap.near" ? "NEAR" : "USDT"}`);
    
    const quoteParams = {
      pool_ids: [poolId],
      input_token: inputToken,
      output_token: outputToken,
      input_amount: amount
    };
    
    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: {
        request_type: 'call_function',
        finality: 'optimistic',
        account_id: config.contracts.dclv2,
        method_name: 'quote',
        args_base64: btoa(JSON.stringify(quoteParams))
      }
    };

    const response = await axios.post(config.rpcUrl, rpcPayload, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    
    if (data.error) {
      console.error(`❌ RPC 错误: ${data.error.message}`);
      return;
    }

    const resultBytes = data.result?.result;
    if (!resultBytes || resultBytes.length === 0) {
      console.log('❌ 未返回结果');
      return;
    }

    const resultString = String.fromCharCode(...resultBytes);
    const quoteResult = JSON.parse(resultString);
    
    if (quoteResult.amount && quoteResult.amount !== '0') {
      console.log(`✅ 成功: ${quoteResult.amount} ${outputToken === "wrap.near" ? "NEAR" : "USDT"}`);
    } else {
      console.log(`❌ 返回 0 或无效金额: ${JSON.stringify(quoteResult)}`);
    }
    
  } catch (error: any) {
    console.error(`❌ 测试失败: ${error.message}`);
  }
}

/**
 * 测试池子信息查询
 */
async function testPoolInfo() {
  const config = getConfig();
  
  try {
    console.log('\n🔍 查询池子详细信息...');
    
    const poolId = "usdt.tether-token.near|wrap.near|2000";
    
    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: {
        request_type: 'call_function',
        finality: 'optimistic',
        account_id: config.contracts.dclv2,
        method_name: 'get_pool',
        args_base64: btoa(JSON.stringify({ pool_id: poolId }))
      }
    };

    const response = await axios.post(config.rpcUrl, rpcPayload, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    
    if (data.error) {
      console.error(`❌ 查询池子信息失败: ${data.error.message}`);
      return;
    }

    const resultBytes = data.result?.result;
    if (!resultBytes || resultBytes.length === 0) {
      console.log('❌ 未返回池子信息');
      return;
    }

    const resultString = String.fromCharCode(...resultBytes);
    const poolInfo = JSON.parse(resultString);
    
    console.log('📋 池子信息:');
    console.log(JSON.stringify(poolInfo, null, 2));
    
  } catch (error: any) {
    console.error(`❌ 查询失败: ${error.message}`);
  }
}

// 运行测试
async function main() {
  await testDifferentAmounts();
  await testPoolInfo();
}

if (require.main === module) {
  main().catch(console.error);
}
