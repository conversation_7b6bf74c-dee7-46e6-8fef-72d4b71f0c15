/**
 * 测试修复后的REF输出金额提取
 */

import axios from 'axios';

/**
 * 修复后的REF提取方法
 */
function extractOutputAmountFromResult(result: any, accountId: string): { humanReadable: string | null; wei: string | null } {
  try {
    console.log('🔍 开始提取输出金额（修复版）...');
    
    // 查找所有receipts中的ft_transfer事件
    const allReceipts = result.receipts_outcome || [];
    console.log(`📋 找到 ${allReceipts.length} 个receipts`);

    for (let i = 0; i < allReceipts.length; i++) {
      const receipt = allReceipts[i];
      const logs = receipt.outcome?.logs || [];

      for (let j = 0; j < logs.length; j++) {
        const log = logs[j];
        
        try {
          // 🔧 关键修复：首先查找EVENT_JSON日志
          if (log.includes('EVENT_JSON:')) {
            console.log(`   🔍 找到EVENT_JSON日志: ${log}`);
            const eventStr = log.split('EVENT_JSON:')[1];
            const event = JSON.parse(eventStr);

            // 查找ft_transfer事件
            if (event.standard === 'nep141' && event.event === 'ft_transfer') {
              const transferData = event.data?.[0];

              // 检查是否是转给我们账户的输出代币
              if (transferData &&
                  transferData.new_owner_id === accountId &&
                  transferData.amount) {

                const weiAmount = transferData.amount;
                console.log(`   ✅ 从EVENT_JSON提取实际输出: ${weiAmount} wei`);

                return {
                  wei: weiAmount,
                  humanReadable: null
                };
              }
            }
          } else {
            // 🔧 关键修复：查找普通Transfer日志格式
            const transferMatch = log.match(/Transfer (\d+) from ([\w\.-]+) to ([\w\.-]+)/);
            if (transferMatch) {
              const [, amount, from, to] = transferMatch;
              
              // 检查是否是转给我们账户的
              if (to === accountId) {
                console.log(`   ✅ 从普通日志提取实际输出: ${amount} wei (from ${from} to ${to})`);
                
                return {
                  wei: amount,
                  humanReadable: null
                };
              }
            }
          }
        } catch (parseError) {
          // 忽略解析错误，继续查找
          continue;
        }
      }
    }

    console.log('❌ 未找到匹配的transfer事件');
    return { humanReadable: null, wei: null };
  } catch (error) {
    console.error('❌ 提取输出金额失败:', error);
    return { humanReadable: null, wei: null };
  }
}

/**
 * 测试修复后的提取方法
 */
async function testFixedExtraction() {
  const txHash = '8BeGdw67i7QmMwaPdVZodx5Ed3tok3ZozajoRjgUcubA';
  const accountId = 'whatdoyoumean.near';
  
  console.log('🧪 测试修复后的REF输出金额提取');
  console.log('='.repeat(60));
  console.log(`交易哈希: ${txHash}`);
  console.log(`账户ID: ${accountId}`);
  console.log('='.repeat(60));

  try {
    // 查询交易状态
    const response = await axios.post('https://rpc.mainnet.near.org', {
      jsonrpc: '2.0',
      id: 'dontcare',
      method: 'tx',
      params: [txHash, accountId]
    });

    if (response.data.error) {
      console.error('❌ RPC错误:', response.data.error);
      return;
    }

    const txResult = response.data.result;

    // 测试修复后的提取方法
    console.log('\n🔧 测试修复后的提取方法:');
    console.log('='.repeat(50));
    const fixedResult = extractOutputAmountFromResult(txResult, accountId);
    
    console.log('\n📊 修复后的结果:');
    console.log(`   wei: ${fixedResult.wei}`);
    console.log(`   humanReadable: ${fixedResult.humanReadable}`);

    // 验证结果
    console.log('\n📊 验证结果:');
    console.log('='.repeat(50));
    console.log('预期输出: 1060173820656998309173701887367336 wei');
    
    if (fixedResult.wei) {
      const matches = fixedResult.wei === '1060173820656998309173701887367336';
      console.log(`实际输出: ${fixedResult.wei} wei`);
      console.log(`匹配预期: ${matches ? '✅ 完全匹配' : '❌ 不匹配'}`);
      
      if (matches) {
        console.log('\n🎉 修复成功！');
        console.log('✅ 现在可以正确提取REF交易的实际输出金额');
        console.log('✅ 第二步VEAX交易将使用正确的输入金额');
        console.log('✅ 套利交易精度问题已解决');
      } else {
        console.log('\n❌ 修复失败，需要进一步调试');
      }
    } else {
      console.log('❌ 修复后仍然无法提取金额');
    }

    // 模拟套利机器人的使用场景
    console.log('\n🤖 模拟套利机器人使用场景:');
    console.log('='.repeat(50));
    
    if (fixedResult.wei) {
      // 模拟第一步REF交易完成
      console.log('第一步REF交易:');
      console.log(`   实际输出: ${fixedResult.wei} wei`);
      
      // 模拟传递给第二步VEAX交易
      console.log('\n第二步VEAX交易:');
      console.log(`   输入金额: ${fixedResult.wei} wei`);
      console.log('   ✅ 使用真实的区块链输出作为输入');
      console.log('   ✅ 避免了精度损失');
      console.log('   ✅ 套利交易将更加准确');
      
      // 计算修复前后的差异
      const oldWrongAmount = '1059583155507499803153604415250318';
      const newCorrectAmount = fixedResult.wei;
      
      console.log('\n📊 修复前后对比:');
      console.log(`   修复前(错误): ${oldWrongAmount} wei`);
      console.log(`   修复后(正确): ${newCorrectAmount} wei`);
      
      const difference = BigInt(newCorrectAmount) - BigInt(oldWrongAmount);
      console.log(`   差异: ${difference.toString()} wei`);
      console.log(`   差异百分比: ${(Number(difference) / Number(newCorrectAmount) * 100).toFixed(6)}%`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始测试修复后的输出金额提取');
  
  await testFixedExtraction();
  
  console.log('\n🎉 测试完成!');
  console.log('REF交易输出金额提取已修复！');
}

// 运行测试
main();
