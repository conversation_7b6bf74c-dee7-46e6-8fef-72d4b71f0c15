/**
 * 探索DCL v2合约的可用方法
 * 
 * 研究是否可以获取池子状态信息进行本地计算
 */

import 'dotenv/config';
import axios from 'axios';
import { getConfig } from '../config/index';

/**
 * 探索DCL v2合约方法
 */
async function exploreDCLv2Methods() {
  console.log('🔍 探索DCL v2合约的可用方法');
  console.log('='.repeat(60));

  const config = getConfig();
  const rpcUrl = config.rpcUrl;
  const contractId = config.contracts.dclv2;

  // 测试不同的方法
  const methodsToTest = [
    'get_pool',
    'view_pool', 
    'pool_info',
    'get_pool_info',
    'pool_state',
    'get_pool_state',
    'pool_details',
    'get_pools',
    'list_pools_with_details',
    'get_pool_by_id'
  ];

  // 使用一个已知存在的池子ID进行测试
  const testPoolId = '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100';

  for (const method of methodsToTest) {
    console.log(`\n🧪 测试方法: ${method}`);
    await testContractMethod(rpcUrl, contractId, method, testPoolId);
  }
}

/**
 * 测试合约方法
 */
async function testContractMethod(rpcUrl: string, contractId: string, methodName: string, poolId: string) {
  try {
    // 尝试不同的参数格式
    const paramVariations = [
      { pool_id: poolId },
      { id: poolId },
      poolId,
      { pool: poolId },
      {}  // 无参数
    ];

    for (let i = 0; i < paramVariations.length; i++) {
      const params = paramVariations[i];
      
      try {
        const argsBase64 = Buffer.from(JSON.stringify(params)).toString('base64');
        
        const rpcPayload = {
          jsonrpc: '2.0',
          id: Date.now(),
          method: 'query',
          params: {
            request_type: 'call_function',
            finality: 'optimistic',
            account_id: contractId,
            method_name: methodName,
            args_base64: argsBase64
          }
        };

        const response = await axios.post(rpcUrl, rpcPayload, {
          timeout: 10000,
          headers: { 'Content-Type': 'application/json' }
        });

        const data = response.data;

        if (data.error) {
          if (i === 0) {
            console.log(`   ❌ 方法不存在或参数错误: ${data.error.message}`);
          }
          continue;
        }

        const resultBytes = data.result?.result;
        if (resultBytes && resultBytes.length > 0) {
          const resultString = String.fromCharCode(...resultBytes);
          const result = JSON.parse(resultString);
          
          console.log(`   ✅ 成功! 参数格式 ${i + 1}:`);
          console.log(`      参数: ${JSON.stringify(params)}`);
          console.log(`      结果类型: ${typeof result}`);
          console.log(`      结果键: ${typeof result === 'object' ? Object.keys(result).join(', ') : 'N/A'}`);
          
          // 显示部分结果
          if (typeof result === 'object') {
            const preview = JSON.stringify(result, null, 2).substring(0, 200);
            console.log(`      结果预览: ${preview}${preview.length >= 200 ? '...' : ''}`);
          }
          
          return result; // 找到有效方法就返回
        }

      } catch (error: any) {
        if (i === 0) {
          console.log(`   ❌ 调用失败: ${error.message}`);
        }
        continue;
      }
    }

  } catch (error: any) {
    console.log(`   ❌ 测试失败: ${error.message}`);
  }
}

/**
 * 分析list_pools返回的池子信息
 */
async function analyzePoolStructure() {
  console.log('\n📊 分析池子数据结构');
  console.log('='.repeat(60));

  try {
    const config = getConfig();
    const rpcUrl = config.rpcUrl;
    const contractId = config.contracts.dclv2;

    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: {
        request_type: 'call_function',
        finality: 'optimistic',
        account_id: contractId,
        method_name: 'list_pools',
        args_base64: Buffer.from(JSON.stringify({})).toString('base64')
      }
    };

    const response = await axios.post(rpcUrl, rpcPayload, {
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' }
    });

    const data = response.data;
    const resultBytes = data.result?.result;
    const resultString = String.fromCharCode(...resultBytes);
    const pools = JSON.parse(resultString);

    console.log(`📋 池子总数: ${pools.length}`);
    
    if (pools.length > 0) {
      const samplePool = pools[0];
      console.log(`\n🔍 样本池子结构:`);
      console.log(`池子ID: ${samplePool.pool_id || samplePool.id}`);
      console.log(`可用字段: ${Object.keys(samplePool).join(', ')}`);
      
      // 详细分析字段
      for (const [key, value] of Object.entries(samplePool)) {
        console.log(`   ${key}: ${typeof value} = ${JSON.stringify(value).substring(0, 100)}`);
      }

      // 分析是否包含价格和流动性信息
      console.log(`\n💰 关键信息分析:`);
      console.log(`   是否有流动性信息: ${samplePool.liquidity ? '✅' : '❌'}`);
      console.log(`   是否有价格信息: ${samplePool.current_price || samplePool.price ? '✅' : '❌'}`);
      console.log(`   是否有tick信息: ${samplePool.current_tick || samplePool.tick ? '✅' : '❌'}`);
      console.log(`   是否有储备信息: ${samplePool.reserve0 || samplePool.reserve1 ? '✅' : '❌'}`);
    }

  } catch (error: any) {
    console.error('❌ 分析失败:', error.message);
  }
}

/**
 * 测试获取单个池子的详细信息
 */
async function testGetSinglePoolInfo() {
  console.log('\n🎯 测试获取单个池子详细信息');
  console.log('='.repeat(60));

  const testPoolId = '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100';
  
  // 尝试可能的方法名
  const possibleMethods = [
    'get_pool',
    'pool_info', 
    'get_pool_info',
    'view_pool',
    'pool_details'
  ];

  for (const method of possibleMethods) {
    console.log(`\n🧪 尝试方法: ${method}`);
    const config = getConfig();
    const result = await testContractMethod(config.rpcUrl, config.contracts.dclv2, method, testPoolId);
    
    if (result) {
      console.log(`✅ 找到有效方法: ${method}`);
      
      // 分析返回的信息是否足够进行本地计算
      console.log(`\n📊 分析是否可用于本地计算:`);
      
      const hasLiquidity = result.liquidity || result.total_liquidity;
      const hasPrice = result.current_price || result.price || result.sqrt_price;
      const hasTick = result.current_tick || result.tick;
      const hasReserves = result.reserve0 || result.reserve1 || result.token0_balance || result.token1_balance;
      
      console.log(`   流动性信息: ${hasLiquidity ? '✅' : '❌'}`);
      console.log(`   价格信息: ${hasPrice ? '✅' : '❌'}`);
      console.log(`   Tick信息: ${hasTick ? '✅' : '❌'}`);
      console.log(`   储备信息: ${hasReserves ? '✅' : '❌'}`);
      
      if (hasLiquidity && hasPrice) {
        console.log(`🎉 该方法可能适用于本地计算!`);
      }
      
      break;
    }
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await analyzePoolStructure();
    await testGetSinglePoolInfo();
    await exploreDCLv2Methods();
    
    console.log('\n💡 优化建议:');
    console.log('1. 如果找到了获取池子状态的方法，可以实现本地计算');
    console.log('2. 定期批量更新池子状态，而不是每次查询');
    console.log('3. 使用集中流动性公式计算输出金额');
    console.log('4. 大幅减少RPC调用次数，提高性能');
    
  } catch (error) {
    console.error('❌ 探索失败:', error);
  }
}

// 运行探索
if (require.main === module) {
  main().catch(console.error);
}

export { exploreDCLv2Methods, analyzePoolStructure, testGetSinglePoolInfo };
