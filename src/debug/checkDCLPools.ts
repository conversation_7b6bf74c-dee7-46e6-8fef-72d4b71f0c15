import axios from 'axios';
import { getConfig } from '../config';

/**
 * 调试工具：检查 DCL v2 池子
 */
async function checkDCLPools() {
  const config = getConfig();
  
  try {
    console.log('🔍 查询 DCL v2 池子列表...');
    
    // 调用 list_pools 方法
    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: {
        request_type: 'call_function',
        finality: 'optimistic',
        account_id: config.contracts.dclv2,
        method_name: 'list_pools',
        args_base64: btoa(JSON.stringify({}))
      }
    };

    const response = await axios.post(config.rpcUrl, rpcPayload, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    
    if (data.error) {
      console.error('❌ RPC 错误:', data.error);
      return;
    }

    // 解析结果
    const resultBytes = data.result?.result;
    if (!resultBytes || resultBytes.length === 0) {
      console.log('❌ 未返回池子列表');
      return;
    }

    const resultString = String.fromCharCode(...resultBytes);
    const pools = JSON.parse(resultString);
    
    console.log(`✅ 找到 ${pools.length} 个 DCL v2 池子`);
    
    // 查找包含 NEAR 和 USDT 的池子
    const nearUsdtPools = pools.filter((pool: any) => {
      const poolId = pool.pool_id || pool.id;
      return poolId && (
        poolId.includes('wrap.near') && poolId.includes('usdt.tether-token.near')
      );
    });
    
    console.log(`\n🎯 NEAR/USDT 相关池子 (${nearUsdtPools.length} 个):`);
    nearUsdtPools.forEach((pool: any, index: number) => {
      console.log(`${index + 1}. ${pool.pool_id || pool.id}`);
      if (pool.liquidity) {
        console.log(`   流动性: ${pool.liquidity}`);
      }
    });
    
    // 查找包含 USDC 的池子
    const usdcPools = pools.filter((pool: any) => {
      const poolId = pool.pool_id || pool.id;
      return poolId && poolId.includes('17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1');
    });
    
    console.log(`\n💰 USDC 相关池子 (${usdcPools.length} 个):`);
    usdcPools.forEach((pool: any, index: number) => {
      console.log(`${index + 1}. ${pool.pool_id || pool.id}`);
      if (pool.liquidity) {
        console.log(`   流动性: ${pool.liquidity}`);
      }
    });
    
    // 显示前 10 个池子作为示例
    console.log(`\n📋 前 10 个池子示例:`);
    pools.slice(0, 10).forEach((pool: any, index: number) => {
      console.log(`${index + 1}. ${pool.pool_id || pool.id}`);
    });
    
  } catch (error: any) {
    console.error('❌ 查询失败:', error.message);
  }
}

/**
 * 测试特定池子的报价
 */
async function testSpecificPool(poolId: string, inputToken: string, outputToken: string, amount: string) {
  const config = getConfig();
  
  try {
    console.log(`\n🧪 测试池子: ${poolId}`);
    console.log(`📊 ${inputToken} → ${outputToken}, 金额: ${amount}`);
    
    const quoteParams = {
      pool_ids: [poolId],
      input_token: inputToken,
      output_token: outputToken,
      input_amount: amount
    };
    
    const rpcPayload = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'query',
      params: {
        request_type: 'call_function',
        finality: 'optimistic',
        account_id: config.contracts.dclv2,
        method_name: 'quote',
        args_base64: btoa(JSON.stringify(quoteParams))
      }
    };

    console.log('📤 发送参数:', JSON.stringify(quoteParams, null, 2));
    
    const response = await axios.post(config.rpcUrl, rpcPayload, {
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const data = response.data;
    
    if (data.error) {
      console.error('❌ RPC 错误:', data.error);
      return;
    }

    const resultBytes = data.result?.result;
    if (!resultBytes || resultBytes.length === 0) {
      console.log('❌ 未返回报价结果');
      return;
    }

    const resultString = String.fromCharCode(...resultBytes);
    const quoteResult = JSON.parse(resultString);
    
    console.log('✅ 报价结果:', quoteResult);
    
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行调试
async function main() {
  await checkDCLPools();
  
  // 测试一些特定的池子
  console.log('\n' + '='.repeat(60));
  console.log('测试特定池子报价');
  console.log('='.repeat(60));
  
  // 测试 NEAR/USDT 池子（如果存在）
  await testSpecificPool(
    'wrap.near|usdt.tether-token.near|2000',
    'wrap.near',
    'usdt.tether-token.near',
    '1000000000000000000000000000' // 1000 NEAR
  );
  
  // 测试 USDC/NEAR 池子（如果存在）
  await testSpecificPool(
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|2000',
    'wrap.near',
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    '1000000000000000000000000000' // 1000 NEAR
  );
}

if (require.main === module) {
  main().catch(console.error);
}
