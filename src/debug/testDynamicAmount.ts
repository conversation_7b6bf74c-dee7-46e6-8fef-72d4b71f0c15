/**
 * 动态金额功能测试脚本
 * 
 * 测试三档位动态金额管理器的功能：
 * 1. 初始化和获取当前金额
 * 2. 根据利润调整下次金额
 * 3. 处理无套利机会的情况
 * 4. 统计功能验证
 */

import 'dotenv/config';
import { dynamicAmountManager } from '../services/dynamicAmountManager';
import { tradingPairManager } from '../config/tradingPairs';

async function testDynamicAmountFeature() {
  console.log('🎯 开始测试动态金额功能...\n');

  // 获取启用动态金额的交易对
  const pairs = tradingPairManager.getEnabledPairs().filter(pair => pair.dynamicAmount?.enabled);
  
  if (pairs.length === 0) {
    console.log('❌ 没有找到启用动态金额的交易对');
    return;
  }

  console.log(`📊 找到 ${pairs.length} 个启用动态金额的交易对:`);
  pairs.forEach(pair => {
    console.log(`   - ${pair.id}: 低档${pair.dynamicAmount!.low} | 中档${pair.dynamicAmount!.medium} | 高档${pair.dynamicAmount!.high}`);
  });
  console.log('');

  // 测试每个交易对
  for (const pair of pairs) {
    console.log(`🔧 测试交易对: ${pair.id}`);
    
    // 1. 测试初始化和获取当前金额
    console.log('1️⃣ 测试初始化...');
    const initialAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   初始金额: ${initialAmount} NEAR (应该是低档: ${pair.dynamicAmount!.low})`);
    
    // 2. 测试渐进式调整逻辑
    console.log('2️⃣ 测试渐进式调整逻辑...');

    // 从低档开始，测试低利润（应该保持低档）
    console.log('   低档 + 低利润 (0.02):');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.02, pair);
    let currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (应该保持低档: ${pair.dynamicAmount!.low})`);

    // 低档 + 中等利润（应该升到中档）
    console.log('   低档 + 中等利润 (0.06):');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.06, pair);
    currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (应该升到中档: ${pair.dynamicAmount!.medium})`);

    // 中档 + 高利润（应该升到高档）
    console.log('   中档 + 高利润 (0.12):');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.12, pair);
    currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (应该升到高档: ${pair.dynamicAmount!.high})`);

    // 高档 + 中等利润（应该降到中档）
    console.log('   高档 + 中等利润 (0.06):');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.06, pair);
    currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (应该降到中档: ${pair.dynamicAmount!.medium})`);

    // 中档 + 低利润（应该降到低档）
    console.log('   中档 + 低利润 (0.02):');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.02, pair);
    currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (应该降到低档: ${pair.dynamicAmount!.low})`);

    // 3. 测试特殊情况：低档直接遇到高利润
    console.log('3️⃣ 测试特殊情况：低档 + 高利润...');
    console.log('   低档 + 高利润 (0.15) - 渐进式应该只升到中档:');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.15, pair);
    currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (渐进式：应该升到中档而非直接跳高档)`);

    // 4. 测试高档遇到低利润的渐进降档
    console.log('4️⃣ 测试高档渐进降档...');
    // 先升到高档
    dynamicAmountManager.adjustNextAmount(pair.id, 0.12, pair);
    console.log('   先升到高档...');
    // 然后低利润
    console.log('   高档 + 低利润 (0.02) - 渐进式应该只降到中档:');
    dynamicAmountManager.adjustNextAmount(pair.id, 0.02, pair);
    currentAmount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
    console.log(`   → ${currentAmount} NEAR (渐进式：应该降到中档而非直接跳低档)`);

    // 5. 测试渐进式调整的完整循环
    console.log('5️⃣ 测试完整的渐进式调整循环...');
    console.log('   模拟多次交易的档位变化:');

    const testProfits = [0.15, 0.08, 0.03, 0.12, 0.05, 0.01, 0.09];
    testProfits.forEach((profit, index) => {
      dynamicAmountManager.adjustNextAmount(pair.id, profit, pair);
      const amount = dynamicAmountManager.getCurrentAmount(pair.id, pair);
      console.log(`   交易${index + 1}: 利润${profit.toFixed(3)} → ${amount} NEAR`);
    });

    // 6. 显示统计信息
    console.log('6️⃣ 统计信息:');
    const stats = dynamicAmountManager.getStats(pair.id);
    if (stats) {
      console.log(`   总交易: ${stats.totalTrades}`);
      console.log(`   当前档位: ${stats.currentLevel}`);
      console.log(`   档位分布: 低档${stats.levelDistribution.low} | 中档${stats.levelDistribution.medium} | 高档${stats.levelDistribution.high}`);
      console.log(`   平均利润: ${stats.averageProfit.toFixed(4)} NEAR`);
    }
    
    console.log('');
  }

  // 显示整体统计摘要
  console.log('📈 整体统计摘要:');
  dynamicAmountManager.displayStatsSummary();
  
  console.log('\n✅ 动态金额功能测试完成!');
}

// 运行测试
if (require.main === module) {
  testDynamicAmountFeature().catch(error => {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  });
}

export { testDynamicAmountFeature };
