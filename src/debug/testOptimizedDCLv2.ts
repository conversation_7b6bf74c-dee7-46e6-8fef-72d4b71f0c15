/**
 * 测试优化后的DCL v2系统
 * 
 * 验证基于真实池子数据的精确查询机制
 */

import 'dotenv/config';
import { dclv2Contract } from '../services/dclv2Contract';
import { TOKENS } from '../config/tradingPairs';

/**
 * 测试优化后的DCL v2系统
 */
async function testOptimizedDCLv2() {
  console.log('🧪 测试优化后的DCL v2系统');
  console.log('='.repeat(60));

  // 测试不同的代币对
  const testPairs = [
    { tokenIn: TOKENS.NEAR, tokenOut: TOKENS.USDC, amount: '0.1' },
    { tokenIn: TOKENS.NEAR, tokenOut: TOKENS.USDT, amount: '0.1' },
    { tokenIn: TOKENS.USDC, tokenOut: TOKENS.NEAR, amount: '1' },
    { tokenIn: TOKENS.USDT, tokenOut: TOKENS.NEAR, amount: '1' },
    // 测试不存在的代币对
    { tokenIn: TOKENS.NEAR, tokenOut: { id: 'fake.token.near', symbol: 'FAKE', decimals: 18 }, amount: '0.1' }
  ];

  for (let i = 0; i < testPairs.length; i++) {
    const pair = testPairs[i];
    console.log(`\n${i + 1}️⃣ 测试 ${pair.tokenIn.symbol} → ${pair.tokenOut.symbol}`);
    console.log('-'.repeat(50));

    await testSinglePair(pair.tokenIn, pair.tokenOut, pair.amount);
  }

  console.log('\n✅ 优化后的DCL v2系统测试完成');
}

/**
 * 测试单个代币对
 */
async function testSinglePair(tokenIn: any, tokenOut: any, amount: string) {
  try {
    const startTime = Date.now();

    const quoteParams = {
      tokenIn,
      tokenOut,
      amountIn: amount,
      slippage: 0.005
    };

    console.log(`📊 获取报价: ${amount} ${tokenIn.symbol} → ${tokenOut.symbol}`);
    
    const quote = await dclv2Contract.getDCLv2Quote(quoteParams);
    
    const duration = Date.now() - startTime;
    console.log(`⏱️ 查询耗时: ${duration}ms`);

    if (quote) {
      console.log(`✅ 报价成功: ${quote.outputAmount} ${tokenOut.symbol}`);
      console.log(`🔧 系统: ${quote.system}`);
      console.log(`🏦 合约: ${quote.contractId}`);
      
      // 分析路径信息
      if (quote.route) {
        const route = quote.route as any;
        if (route.pool_ids && route.pool_ids.length > 0) {
          const poolId = route.pool_ids[0];
          const parts = poolId.split('|');
          if (parts.length === 3) {
            const [token1, token2, fee] = parts;
            const feePercent = (parseInt(fee) / 10000).toFixed(2);
            console.log(`📋 池子详情:`);
            console.log(`   池子ID: ${poolId}`);
            console.log(`   代币1: ${token1}`);
            console.log(`   代币2: ${token2}`);
            console.log(`   费率: ${feePercent}%`);
          }
        }
      }

      // 计算汇率
      const rate = parseFloat(quote.outputAmount) / parseFloat(amount);
      console.log(`💱 汇率: 1 ${tokenIn.symbol} = ${rate.toFixed(6)} ${tokenOut.symbol}`);

    } else {
      console.log(`❌ 报价失败: 没有找到可用的DCL v2池子`);
    }

  } catch (error) {
    console.error(`❌ 测试失败:`, error);
  }
}

/**
 * 测试池子缓存和索引性能
 */
async function testCachePerformance() {
  console.log('\n🚀 测试池子缓存和索引性能');
  console.log('='.repeat(60));

  const testPair = { tokenIn: TOKENS.NEAR, tokenOut: TOKENS.USDC, amount: '0.1' };

  // 第一次查询（会触发缓存加载）
  console.log('\n1️⃣ 第一次查询（加载缓存）...');
  const startTime1 = Date.now();
  await dclv2Contract.getDCLv2Quote({
    tokenIn: testPair.tokenIn,
    tokenOut: testPair.tokenOut,
    amountIn: testPair.amount,
    slippage: 0.005
  });
  const duration1 = Date.now() - startTime1;
  console.log(`⏱️ 第一次查询耗时: ${duration1}ms`);

  // 第二次查询（使用缓存）
  console.log('\n2️⃣ 第二次查询（使用缓存）...');
  const startTime2 = Date.now();
  await dclv2Contract.getDCLv2Quote({
    tokenIn: testPair.tokenIn,
    tokenOut: testPair.tokenOut,
    amountIn: testPair.amount,
    slippage: 0.005
  });
  const duration2 = Date.now() - startTime2;
  console.log(`⏱️ 第二次查询耗时: ${duration2}ms`);

  // 性能对比
  console.log('\n📊 性能对比:');
  console.log(`   第一次查询: ${duration1}ms`);
  console.log(`   第二次查询: ${duration2}ms`);
  if (duration1 > duration2) {
    const improvement = ((duration1 - duration2) / duration1 * 100).toFixed(1);
    console.log(`   性能提升: ${improvement}%`);
  }
}

/**
 * 测试池子存在性检查
 */
async function testPoolExistence() {
  console.log('\n🔍 测试池子存在性检查');
  console.log('='.repeat(60));

  const testPools = [
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|2000',
    'usdt.tether-token.near|wrap.near|100',
    'wrap.near|17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|100', // 错误顺序
    'fake.token.near|wrap.near|100' // 不存在的池子
  ];

  for (const poolId of testPools) {
    console.log(`\n🔍 检查池子: ${poolId}`);
    const exists = await dclv2Contract.isPoolExists(poolId);
    console.log(`   结果: ${exists ? '✅ 存在' : '❌ 不存在'}`);
  }
}

/**
 * 展示优化方案的优势
 */
function showOptimizationBenefits() {
  console.log('\n🎯 DCL v2优化方案的优势');
  console.log('='.repeat(60));
  
  console.log(`
✅ **精确性提升**：
   - 基于真实池子数据，不再盲目猜测
   - 精确的代币对匹配，避免无效查询
   - 消除"池子不存在但交易成功"的困惑

🚀 **性能优化**：
   - 池子列表缓存，减少重复网络请求
   - 代币对索引映射，O(1)查找复杂度
   - 并行查询多个费率等级，选择最优结果

🔒 **可靠性增强**：
   - 如果没有对应池子，直接跳过，避免无效尝试
   - 统一的池子数据源，确保数据一致性
   - 完整的错误处理，提高系统稳定性

📊 **智能选择**：
   - 比较所有相关池子的报价
   - 选择输出金额最大的池子
   - 支持多种费率等级的自动比较

🎯 **用户体验**：
   - 更快的报价响应时间
   - 更准确的价格信息
   - 更高的交易成功率
  `);
}

/**
 * 主函数
 */
async function main() {
  try {
    await testOptimizedDCLv2();
    await testCachePerformance();
    await testPoolExistence();
    showOptimizationBenefits();
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testOptimizedDCLv2, testCachePerformance, testPoolExistence };
