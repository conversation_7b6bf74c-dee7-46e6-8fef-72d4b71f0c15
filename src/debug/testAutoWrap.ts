/**
 * 测试自动包装功能
 * 
 * 功能：
 * 1. 检查当前NEAR和wNEAR余额
 * 2. 测试自动包装功能
 * 3. 验证包装后的余额
 */

import 'dotenv/config';
import NearWrapService from '../services/nearWrapService';

async function testAutoWrap() {
  console.log('🧪 测试NEAR自动包装功能');
  console.log('='.repeat(60));

  try {
    // 1. 初始化包装服务
    console.log('1️⃣ 初始化NEAR包装服务...');
    const wrapService = new NearWrapService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    await wrapService.initialize();
    console.log('✅ NEAR包装服务初始化完成');

    // 2. 获取初始余额
    console.log('\n2️⃣ 获取当前余额...');
    const initialBalance = await wrapService.getBalanceInfo();
    console.log(`💰 NEAR余额: ${initialBalance.nearBalance}`);
    console.log(`🔗 wNEAR余额: ${initialBalance.wNearBalance}`);
    console.log(`📊 总余额: ${initialBalance.totalBalance}`);

    // 3. 测试自动包装检查（需要少量wNEAR）
    console.log('\n3️⃣ 测试自动包装检查...');
    const requiredAmount = '0.1'; // 需要0.1 wNEAR
    console.log(`🎯 模拟交易需要: ${requiredAmount} wNEAR`);

    const wrapResult = await wrapService.checkAndWrapNear(requiredAmount, 20); // 20%缓冲

    if (wrapResult.success) {
      if (wrapResult.wrapped) {
        console.log(`✅ 自动包装成功: ${wrapResult.amount} NEAR → wNEAR`);
      } else {
        console.log(`✅ wNEAR余额充足，无需包装`);
      }
    } else {
      console.log(`❌ 自动包装失败: ${wrapResult.error}`);
    }

    // 4. 获取包装后余额
    console.log('\n4️⃣ 获取包装后余额...');
    const finalBalance = await wrapService.getBalanceInfo();
    console.log(`💰 NEAR余额: ${finalBalance.nearBalance}`);
    console.log(`🔗 wNEAR余额: ${finalBalance.wNearBalance}`);
    console.log(`📊 总余额: ${finalBalance.totalBalance}`);

    // 5. 计算变化
    console.log('\n5️⃣ 余额变化分析...');
    const nearChange = parseFloat(finalBalance.nearBalance) - parseFloat(initialBalance.nearBalance);
    const wNearChange = parseFloat(finalBalance.wNearBalance) - parseFloat(initialBalance.wNearBalance);
    
    console.log(`📈 NEAR变化: ${nearChange.toFixed(6)} (${nearChange >= 0 ? '+' : ''}${nearChange.toFixed(6)})`);
    console.log(`📈 wNEAR变化: ${wNearChange.toFixed(6)} (${wNearChange >= 0 ? '+' : ''}${wNearChange.toFixed(6)})`);

    if (wrapResult.wrapped) {
      console.log(`✅ 包装验证: ${Math.abs(nearChange).toFixed(6)} NEAR → ${wNearChange.toFixed(6)} wNEAR`);
      
      // 验证包装比例（应该接近1:1，考虑gas费用）
      const ratio = Math.abs(wNearChange / nearChange);
      if (ratio > 0.95 && ratio < 1.05) {
        console.log(`✅ 包装比例正常: 1:${ratio.toFixed(4)}`);
      } else {
        console.log(`⚠️ 包装比例异常: 1:${ratio.toFixed(4)}`);
      }
    }

    console.log('\n🎉 自动包装功能测试完成!');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
testAutoWrap().catch(console.error);
