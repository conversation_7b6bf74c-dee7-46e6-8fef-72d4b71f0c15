import { refQuoteService, TOKEN_METADATA } from '../index';

/**
 * 测试 DCL v2 工作的交易对
 */
async function testWorkingPairs() {
  console.log('🧪 测试 DCL v2 工作的交易对\n');

  // 测试 USDT → NEAR (已知工作)
  console.log('='.repeat(60));
  console.log('测试 1: 100 USDT → NEAR (DCL v2 应该工作)');
  console.log('='.repeat(60));
  
  try {
    const quote1 = await refQuoteService.getQuote({
      tokenIn: TOKEN_METADATA.USDT,
      tokenOut: TOKEN_METADATA.NEAR,
      amountIn: '100',
      slippage: 0.005
    });

    console.log('\n📋 报价详情:');
    console.log(JSON.stringify(refQuoteService.getQuoteDetails(quote1), null, 2));
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }

  // 测试 USDT → NEAR (更大金额)
  console.log('\n' + '='.repeat(60));
  console.log('测试 2: 1000 USDT → NEAR');
  console.log('='.repeat(60));
  
  try {
    const quote2 = await refQuoteService.getQuote({
      tokenIn: TOKEN_METADATA.USDT,
      tokenOut: TOKEN_METADATA.NEAR,
      amountIn: '1000',
      slippage: 0.005
    });

    console.log('\n📋 报价详情:');
    console.log(JSON.stringify(refQuoteService.getQuoteDetails(quote2), null, 2));
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }

  // 测试 USDT → NEAR (小金额)
  console.log('\n' + '='.repeat(60));
  console.log('测试 3: 10 USDT → NEAR');
  console.log('='.repeat(60));
  
  try {
    const quote3 = await refQuoteService.getQuote({
      tokenIn: TOKEN_METADATA.USDT,
      tokenOut: TOKEN_METADATA.NEAR,
      amountIn: '10',
      slippage: 0.005
    });

    console.log('\n📋 报价详情:');
    console.log(JSON.stringify(refQuoteService.getQuoteDetails(quote3), null, 2));
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }

  // 测试 USDC → NEAR
  console.log('\n' + '='.repeat(60));
  console.log('测试 4: 100 USDC → NEAR');
  console.log('='.repeat(60));
  
  try {
    const quote4 = await refQuoteService.getQuote({
      tokenIn: TOKEN_METADATA.USDC,
      tokenOut: TOKEN_METADATA.NEAR,
      amountIn: '100',
      slippage: 0.005
    });

    console.log('\n📋 报价详情:');
    console.log(JSON.stringify(refQuoteService.getQuoteDetails(quote4), null, 2));
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

if (require.main === module) {
  testWorkingPairs().catch(console.error);
}
