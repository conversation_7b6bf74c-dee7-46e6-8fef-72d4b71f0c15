/**
 * REF Finance 双系统交易测试
 * 
 * 测试V1 Smart Router和DCL v2两种交易系统
 */

import 'dotenv/config';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

function fromWei(amount: string, decimals: number): string {
  return new Big(amount).div(new Big(10).pow(decimals)).toString();
}

/**
 * 测试V1 Smart Router系统
 */
async function testV1System() {
  console.log('\n🔍 测试V1 Smart Router系统');
  console.log('='.repeat(50));

  try {
    // 获取V1报价（大额交易通常使用V1）
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDT,
      amountIn: '1000', // 1000 NEAR，大额交易
      slippage: 0.005
    };

    console.log(`📊 获取报价: ${quoteParams.amountIn} ${TOKENS.NEAR.symbol} → ${TOKENS.USDT.symbol}`);
    const quote = await refQuoteService.getBestQuote(quoteParams);

    if (quote.system !== 'V1') {
      console.log(`⚠️ 当前报价使用${quote.system}系统，不是V1系统`);
      console.log('💡 尝试更大金额或不同交易对来获取V1报价');
      return false;
    }

    console.log(`✅ 获得V1报价: ${quote.outputAmount} ${TOKENS.USDT.symbol}`);
    console.log(`🔧 合约地址: ${quote.contractId}`);

    // 分析V1路径结构
    if (quote.rawResponse?.result_data?.routes) {
      const routes = quote.rawResponse.result_data.routes;
      console.log(`📋 路径分析:`);
      console.log(`   路径数量: ${routes.length}`);
      
      routes.forEach((route: any, routeIndex: number) => {
        console.log(`   路径${routeIndex + 1}: ${route.pools.length}个池子`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`     池子${poolIndex + 1}: #${pool.pool_id} (${pool.token_in} → ${pool.token_out})`);
          if (pool.amount_in) {
            console.log(`       amount_in: ${pool.amount_in}`);
          }
        });
      });
    }

    // 计算交易参数
    const inputAmountWei = toWei(quoteParams.amountIn, TOKENS.NEAR.decimals);
    const slippage = 0.01; // 1%
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDT.decimals);

    console.log(`📊 交易参数:`);
    console.log(`   输入: ${quoteParams.amountIn} NEAR (${inputAmountWei} wei)`);
    console.log(`   预期输出: ${quote.outputAmount} USDT`);
    console.log(`   最小输出: ${minOutputAmount} USDT (${minOutputAmountWei} wei)`);
    console.log(`   滑点: ${slippage * 100}%`);

    console.log(`✅ V1系统测试准备完成`);
    console.log(`💡 要执行真实交易，请取消注释执行代码`);

    // 🔧 真实交易执行测试（小额测试）
    console.log('\n🧪 V1真实交易测试（小额）...');
    const testQuoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDT,
      amountIn: '0.01', // 0.01 NEAR，小额测试
      slippage: 0.005
    };

    const testQuote = await refQuoteService.getBestQuote(testQuoteParams);
    if (testQuote.system === 'V1') {
      const testInputAmountWei = toWei(testQuoteParams.amountIn, TOKENS.NEAR.decimals);
      const testExpectedOutput = parseFloat(testQuote.outputAmount);
      const testMinOutputAmount = (testExpectedOutput * (1 - 0.02)).toString(); // 2%滑点
      const testMinOutputAmountWei = toWei(testMinOutputAmount, TOKENS.USDT.decimals);

      console.log(`📊 小额测试参数:`);
      console.log(`   输入: ${testQuoteParams.amountIn} NEAR`);
      console.log(`   预期输出: ${testQuote.outputAmount} USDT`);
      console.log(`   最小输出: ${testMinOutputAmount} USDT`);
      console.log(`⚠️ 要执行真实交易，请设置 ENABLE_REAL_TRADING=true`);
    }

    return true;

  } catch (error) {
    console.error('❌ V1系统测试失败:', error);
    return false;
  }
}

/**
 * 测试DCL v2系统
 */
async function testDCLv2System() {
  console.log('\n🔍 测试DCL v2系统');
  console.log('='.repeat(50));

  try {
    // 获取DCL v2报价（小额交易通常使用DCL v2）
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '1', // 1 NEAR，小额交易
      slippage: 0.005
    };

    console.log(`📊 获取报价: ${quoteParams.amountIn} ${TOKENS.NEAR.symbol} → ${TOKENS.USDC.symbol}`);
    const quote = await refQuoteService.getBestQuote(quoteParams);

    if (quote.system !== 'DCL_V2') {
      console.log(`⚠️ 当前报价使用${quote.system}系统，不是DCL v2系统`);
      console.log('💡 尝试更小金额或不同交易对来获取DCL v2报价');
      return false;
    }

    console.log(`✅ 获得DCL v2报价: ${quote.outputAmount} ${TOKENS.USDC.symbol}`);
    console.log(`🔧 合约地址: ${quote.contractId}`);

    // 分析DCL v2池子信息
    if (quote.route) {
      const route = quote.route as any;
      console.log(`📋 池子分析:`);
      if (route.pool_ids && route.pool_ids.length > 0) {
        route.pool_ids.forEach((poolId: string, index: number) => {
          console.log(`   池子${index + 1}: ${poolId}`);
          const parts = poolId.split('|');
          if (parts.length === 3) {
            const [token1, token2, fee] = parts;
            const feePercent = (parseInt(fee) / 10000).toFixed(2);
            console.log(`     代币对: ${token1} / ${token2}`);
            console.log(`     费用: ${feePercent}%`);
          }
        });
      }
    }

    // 计算交易参数
    const inputAmountWei = toWei(quoteParams.amountIn, TOKENS.NEAR.decimals);
    const slippage = 0.01; // 1%
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDC.decimals);

    console.log(`📊 交易参数:`);
    console.log(`   输入: ${quoteParams.amountIn} NEAR (${inputAmountWei} wei)`);
    console.log(`   预期输出: ${quote.outputAmount} USDC`);
    console.log(`   最小输出: ${minOutputAmount} USDC (${minOutputAmountWei} wei)`);
    console.log(`   滑点: ${slippage * 100}%`);

    // 构建DCL v2池子ID
    const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`; // 0.01%费用池子
    console.log(`📋 池子ID: ${poolId}`);

    console.log(`✅ DCL v2系统测试准备完成`);
    console.log(`💡 要执行真实交易，请取消注释执行代码`);

    // 🔧 真实交易执行测试（小额测试）
    console.log('\n🧪 DCL v2真实交易测试（小额）...');
    const testQuoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.01', // 0.01 NEAR，小额测试
      slippage: 0.005
    };

    const testQuote = await refQuoteService.getBestQuote(testQuoteParams);
    if (testQuote.system === 'DCL_V2') {
      const testInputAmountWei = toWei(testQuoteParams.amountIn, TOKENS.NEAR.decimals);
      const testExpectedOutput = parseFloat(testQuote.outputAmount);
      const testMinOutputAmount = (testExpectedOutput * (1 - 0.02)).toString(); // 2%滑点
      const testMinOutputAmountWei = toWei(testMinOutputAmount, TOKENS.USDC.decimals);
      const testPoolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;

      console.log(`📊 小额测试参数:`);
      console.log(`   输入: ${testQuoteParams.amountIn} NEAR`);
      console.log(`   预期输出: ${testQuote.outputAmount} USDC`);
      console.log(`   最小输出: ${testMinOutputAmount} USDC`);
      console.log(`   池子ID: ${testPoolId}`);
      console.log(`⚠️ 要执行真实交易，请设置 ENABLE_REAL_TRADING=true`);
    }

    return true;

  } catch (error) {
    console.error('❌ DCL v2系统测试失败:', error);
    return false;
  }
}

/**
 * 主测试函数
 */
async function testBothRefSystems() {
  console.log('🚀 REF Finance 双系统交易测试');
  console.log('='.repeat(60));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请在.env文件中设置这些变量');
    return;
  }

  // 初始化执行服务
  const refExecution = new RefExecutionServiceCorrect(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    console.log('\n1️⃣ 初始化REF执行服务(正确版)...');
    await refExecution.initialize();

    // 测试两种系统
    const v1Success = await testV1System();
    const dclv2Success = await testDCLv2System();

    // 总结测试结果
    console.log('\n📊 测试结果总结');
    console.log('='.repeat(50));
    console.log(`V1 Smart Router: ${v1Success ? '✅ 准备就绪' : '❌ 测试失败'}`);
    console.log(`DCL v2 系统: ${dclv2Success ? '✅ 准备就绪' : '❌ 测试失败'}`);

    if (v1Success && dclv2Success) {
      console.log('\n🎉 两种REF交易系统都已准备就绪！');
      console.log('💡 要执行真实交易，请修改代码取消注释执行部分');
    } else {
      console.log('\n⚠️ 部分系统测试失败，请检查配置和网络连接');
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testBothRefSystems().catch(console.error);
}

export { testBothRefSystems, testV1System, testDCLv2System };
