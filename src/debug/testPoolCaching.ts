/**
 * 测试池子缓存优化
 * 
 * 验证池子预加载和缓存机制
 */

import 'dotenv/config';
import { dclv2Contract } from '../services/dclv2Contract';

/**
 * 测试池子缓存机制
 */
async function testPoolCaching() {
  console.log('🧪 测试DCL v2池子缓存机制');
  console.log('='.repeat(60));

  // 测试池子列表
  const testPools = [
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|2000',
    'usdt.tether-token.near|wrap.near|100',
    'usdt.tether-token.near|wrap.near|2000',
    'wrap.near|17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|100', // 错误顺序
    'fake.pool.near|wrap.near|100' // 不存在的池子
  ];

  console.log('\n1️⃣ 第一次查询（会触发池子列表加载）...');
  const startTime1 = Date.now();
  
  for (const poolId of testPools) {
    console.log(`\n🔍 检查池子: ${poolId}`);
    const exists = await dclv2Contract.isPoolExists(poolId);
    console.log(`   结果: ${exists ? '✅ 存在' : '❌ 不存在'}`);
  }
  
  const duration1 = Date.now() - startTime1;
  console.log(`\n⏱️ 第一次查询耗时: ${duration1}ms`);

  console.log('\n2️⃣ 第二次查询（使用缓存）...');
  const startTime2 = Date.now();
  
  for (const poolId of testPools) {
    console.log(`\n🔍 检查池子: ${poolId}`);
    const exists = await dclv2Contract.isPoolExists(poolId);
    console.log(`   结果: ${exists ? '✅ 存在' : '❌ 不存在'}`);
  }
  
  const duration2 = Date.now() - startTime2;
  console.log(`\n⏱️ 第二次查询耗时: ${duration2}ms`);

  console.log('\n📊 性能对比:');
  console.log(`   第一次查询: ${duration1}ms`);
  console.log(`   第二次查询: ${duration2}ms`);
  console.log(`   性能提升: ${((duration1 - duration2) / duration1 * 100).toFixed(1)}%`);

  // 测试报价获取
  console.log('\n3️⃣ 测试报价获取（使用缓存的池子）...');
  await testQuoteWithCaching();
}

/**
 * 测试使用缓存的报价获取
 */
async function testQuoteWithCaching() {
  const { TOKENS } = await import('../config/tradingPairs');
  
  const quoteParams = {
    tokenIn: TOKENS.NEAR,
    tokenOut: TOKENS.USDC,
    amountIn: '0.1',
    slippage: 0.005
  };

  console.log(`📊 获取报价: ${quoteParams.amountIn} NEAR → USDC`);
  
  const startTime = Date.now();
  const quote = await dclv2Contract.getDCLv2Quote(quoteParams);
  const duration = Date.now() - startTime;

  if (quote) {
    console.log(`✅ 报价成功: ${quote.outputAmount} USDC`);
    console.log(`⏱️ 报价耗时: ${duration}ms`);
    console.log(`🔧 使用的池子: ${JSON.stringify(quote.route)}`);
    
    // 验证使用的池子是否在缓存中
    const route = quote.route as any;
    if (route.pool_ids && route.pool_ids.length > 0) {
      const poolId = route.pool_ids[0];
      const exists = await dclv2Contract.isPoolExists(poolId);
      console.log(`🔍 池子验证: ${poolId} ${exists ? '✅ 在缓存中' : '❌ 不在缓存中'}`);
    }
  } else {
    console.log(`❌ 报价失败`);
  }
}

/**
 * 解释池子存在性检查的差异
 */
function explainPoolExistenceIssue() {
  console.log('\n📚 池子存在性检查差异解释');
  console.log('='.repeat(60));
  
  console.log(`
🔍 **问题分析：为什么池子检查说"不存在"但交易成功？**

1️⃣ **不同的查询方法**：
   - 池子验证使用: get_pool 方法
   - 交易执行使用: quote 方法
   - 这两个方法可能有不同的池子检查逻辑

2️⃣ **网络和缓存问题**：
   - RPC节点之间的数据同步延迟
   - 不同时间点的查询结果不一致
   - 本地缓存与远程状态不同步

3️⃣ **池子状态变化**：
   - 池子可能在短时间内被创建/删除
   - 流动性变化影响池子可见性
   - 合约升级导致的临时不一致

4️⃣ **解决方案（已实现）**：
   ✅ 使用 list_pools 预加载所有池子
   ✅ 本地缓存池子列表，避免重复查询
   ✅ 定时刷新缓存（5分钟）
   ✅ 快速池子存在性检查

5️⃣ **优势**：
   🚀 大幅提升查询性能
   🔒 减少网络请求，提高稳定性
   📊 统一的池子数据源
   ⚡ 快速的池子验证
  `);
}

/**
 * 主函数
 */
async function main() {
  try {
    await testPoolCaching();
    explainPoolExistenceIssue();
    console.log('\n✅ 池子缓存测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testPoolCaching, testQuoteWithCaching };
