/**
 * REF Finance & VEAX 综合诊断和修复工具
 * 
 * 诊断和修复所有已知的交易执行问题
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import VeaxExecutionService from '../services/veaxExecutionService';
import { refQuoteService } from '../services/refQuoteService';
import VeaxQuoteService from '../services/veaxQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

interface DiagnosisResult {
  component: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  details?: any;
  fix?: string;
}

/**
 * 综合诊断工具
 */
class ComprehensiveDiagnosis {
  private results: DiagnosisResult[] = [];
  private refExecution?: RefExecutionServiceFixed;
  private veaxExecution?: VeaxExecutionService;

  /**
   * 运行完整诊断
   */
  async runFullDiagnosis(): Promise<DiagnosisResult[]> {
    console.log('🔍 开始REF Finance & VEAX 综合诊断');
    console.log('='.repeat(60));

    // 1. 环境配置检查
    await this.checkEnvironmentConfig();

    // 2. 服务初始化检查
    await this.checkServiceInitialization();

    // 3. 报价功能检查
    await this.checkQuotingServices();

    // 4. REF Finance执行检查
    await this.checkRefExecution();

    // 5. VEAX执行检查
    await this.checkVeaxExecution();

    // 6. 生成修复建议
    this.generateFixRecommendations();

    return this.results;
  }

  /**
   * 检查环境配置
   */
  private async checkEnvironmentConfig(): Promise<void> {
    console.log('\n1️⃣ 检查环境配置...');

    const configValidation = validateExecutionConfig();
    if (configValidation.valid) {
      this.addResult('环境配置', 'success', '所有必要的环境变量已配置');
    } else {
      this.addResult('环境配置', 'error', 
        `缺少环境变量: ${configValidation.missing.join(', ')}`,
        { missing: configValidation.missing },
        '请在.env文件中设置缺少的环境变量'
      );
    }
  }

  /**
   * 检查服务初始化
   */
  private async checkServiceInitialization(): Promise<void> {
    console.log('\n2️⃣ 检查服务初始化...');

    try {
      // 初始化REF执行服务（修复版本）
      this.refExecution = new RefExecutionServiceFixed(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.refExecution.initialize();
      this.addResult('REF执行服务', 'success', 'REF执行服务(修复版)初始化成功');

      // 初始化VEAX执行服务
      this.veaxExecution = new VeaxExecutionService(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.veaxExecution.initialize();
      this.addResult('VEAX执行服务', 'success', 'VEAX执行服务初始化成功');

    } catch (error: any) {
      this.addResult('服务初始化', 'error', 
        `服务初始化失败: ${error.message}`,
        { error: error.message },
        '检查账户ID和私钥是否正确'
      );
    }
  }

  /**
   * 检查报价服务
   */
  private async checkQuotingServices(): Promise<void> {
    console.log('\n3️⃣ 检查报价服务...');

    try {
      // 测试REF Finance报价
      const refQuote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDT,
        amountIn: '1',
        slippage: 0.005
      });
      this.addResult('REF报价服务', 'success', 
        `REF报价正常: 1 NEAR → ${refQuote.outputAmount} USDT (${refQuote.system})`
      );

      // 测试VEAX报价
      const veaxQuote = await VeaxQuoteService.getQuote(
        TOKENS.NEAR.id,
        TOKENS.USDT.id,
        '1'
      );
      if (veaxQuote.success) {
        this.addResult('VEAX报价服务', 'success', 
          `VEAX报价正常: 1 NEAR → ${veaxQuote.outputAmount} USDT`
        );
      } else {
        this.addResult('VEAX报价服务', 'error', 
          `VEAX报价失败: ${veaxQuote.error}`,
          { error: veaxQuote.error },
          '检查网络连接和VEAX服务状态'
        );
      }

    } catch (error: any) {
      this.addResult('报价服务', 'error', 
        `报价服务测试失败: ${error.message}`,
        { error: error.message },
        '检查网络连接和API服务状态'
      );
    }
  }

  /**
   * 检查REF Finance执行
   */
  private async checkRefExecution(): Promise<void> {
    console.log('\n4️⃣ 检查REF Finance执行...');

    if (!this.refExecution) {
      this.addResult('REF执行检查', 'error', 'REF执行服务未初始化');
      return;
    }

    try {
      // 获取报价用于测试
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDC,
        amountIn: '0.1',
        slippage: 0.005
      });

      // 检查修复版本的关键特性
      this.checkRefFixedVersion(quote);

    } catch (error: any) {
      this.addResult('REF执行检查', 'error', 
        `REF执行检查失败: ${error.message}`,
        { error: error.message }
      );
    }
  }

  /**
   * 检查修复版本的关键特性
   */
  private checkRefFixedVersion(quote: any): void {
    // 检查是否使用修复版本
    const isFixedVersion = this.refExecution instanceof RefExecutionServiceFixed;
    if (isFixedVersion) {
      this.addResult('REF修复版本', 'success', '正在使用修复版本的REF执行服务');
    } else {
      this.addResult('REF修复版本', 'error', 
        '未使用修复版本的REF执行服务',
        {},
        '请使用RefExecutionServiceFixed而不是RefExecutionService'
      );
    }

    // 检查交易构建逻辑
    if (quote.system === 'V1' && quote.rawResponse) {
      const routeData = quote.rawResponse.result_data;
      const hasMultiplePools = routeData.routes.some((route: any) => route.pools.length > 1);
      
      if (hasMultiplePools) {
        this.addResult('V1路径处理', 'success', 
          `检测到复杂路径，修复版本能正确处理 ${routeData.routes.length} 条路径`
        );
      }

      // 检查pool_id类型
      const firstPool = routeData.routes[0]?.pools[0];
      if (firstPool && typeof firstPool.pool_id === 'number') {
        this.addResult('pool_id类型', 'success', 'pool_id类型正确（数字）');
      } else {
        this.addResult('pool_id类型', 'warning', 
          'pool_id可能需要类型转换',
          {},
          '确保pool_id被转换为数字类型'
        );
      }
    }
  }

  /**
   * 检查VEAX执行
   */
  private async checkVeaxExecution(): Promise<void> {
    console.log('\n5️⃣ 检查VEAX执行...');

    if (!this.veaxExecution) {
      this.addResult('VEAX执行检查', 'error', 'VEAX执行服务未初始化');
      return;
    }

    try {
      // 检查账户余额
      const balance = await this.veaxExecution.checkAccountBalance();
      const wNearBalance = await this.veaxExecution.checkWNearBalance();

      if (balance.balanceNear < 0.1) {
        this.addResult('NEAR余额', 'warning', 
          `NEAR余额较低: ${balance.balanceNear.toFixed(6)} NEAR`,
          { balance: balance.balanceNear },
          '建议保持至少0.1 NEAR用于支付gas费'
        );
      } else {
        this.addResult('NEAR余额', 'success', 
          `NEAR余额充足: ${balance.balanceNear.toFixed(6)} NEAR`
        );
      }

      if (wNearBalance.balanceWNear < 0.01) {
        this.addResult('wNEAR余额', 'warning', 
          `wNEAR余额较低: ${wNearBalance.balanceWNear.toFixed(6)} wNEAR`,
          { balance: wNearBalance.balanceWNear },
          '需要wNEAR进行VEAX交易，请在REF Finance包装一些NEAR'
        );
      } else {
        this.addResult('wNEAR余额', 'success', 
          `wNEAR余额充足: ${wNearBalance.balanceWNear.toFixed(6)} wNEAR`
        );
      }

      // 检查用户注册状态
      const userStatus = await this.veaxExecution.checkUserRegistration();
      if (userStatus.isRegistered) {
        this.addResult('VEAX用户注册', 'success', '用户已在VEAX注册');
      } else {
        this.addResult('VEAX用户注册', 'warning', 
          '用户未在VEAX注册',
          {},
          '首次使用VEAX需要注册用户'
        );
      }

      // 检查关键代币注册状态
      const tokenChecks = [
        { token: TOKENS.NEAR.id, symbol: 'NEAR' },
        { token: TOKENS.USDC.id, symbol: 'USDC' },
        { token: TOKENS.USDT.id, symbol: 'USDT' }
      ];

      for (const { token, symbol } of tokenChecks) {
        const tokenStatus = await this.veaxExecution.checkTokenRegistration(token);
        if (tokenStatus.isRegistered) {
          this.addResult(`${symbol}代币注册`, 'success', `${symbol}代币已注册`);
        } else {
          this.addResult(`${symbol}代币注册`, 'warning', 
            `${symbol}代币未注册`,
            {},
            `需要在${symbol}代币合约中注册存储`
          );
        }
      }

    } catch (error: any) {
      this.addResult('VEAX执行检查', 'error', 
        `VEAX执行检查失败: ${error.message}`,
        { error: error.message }
      );
    }
  }

  /**
   * 生成修复建议
   */
  private generateFixRecommendations(): void {
    console.log('\n6️⃣ 生成修复建议...');

    const errors = this.results.filter(r => r.status === 'error');
    const warnings = this.results.filter(r => r.status === 'warning');

    if (errors.length === 0 && warnings.length === 0) {
      this.addResult('系统状态', 'success', '所有组件运行正常，可以进行交易');
    } else {
      const priority = errors.length > 0 ? 'error' : 'warning';
      this.addResult('系统状态', priority, 
        `发现 ${errors.length} 个错误和 ${warnings.length} 个警告`,
        { errors: errors.length, warnings: warnings.length },
        '请按照修复建议解决问题'
      );
    }
  }

  /**
   * 添加诊断结果
   */
  private addResult(
    component: string, 
    status: 'success' | 'warning' | 'error', 
    message: string,
    details?: any,
    fix?: string
  ): void {
    this.results.push({ component, status, message, details, fix });
    
    const icon = status === 'success' ? '✅' : status === 'warning' ? '⚠️' : '❌';
    console.log(`${icon} ${component}: ${message}`);
    if (fix) {
      console.log(`   💡 修复建议: ${fix}`);
    }
  }

  /**
   * 打印诊断报告
   */
  printReport(): void {
    console.log('\n📋 诊断报告总结');
    console.log('='.repeat(60));

    const successCount = this.results.filter(r => r.status === 'success').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;
    const errorCount = this.results.filter(r => r.status === 'error').length;

    console.log(`✅ 成功: ${successCount} 项`);
    console.log(`⚠️ 警告: ${warningCount} 项`);
    console.log(`❌ 错误: ${errorCount} 项`);

    if (errorCount > 0) {
      console.log('\n🔧 需要立即修复的问题:');
      this.results
        .filter(r => r.status === 'error')
        .forEach(r => {
          console.log(`   ❌ ${r.component}: ${r.message}`);
          if (r.fix) console.log(`      💡 ${r.fix}`);
        });
    }

    if (warningCount > 0) {
      console.log('\n⚠️ 建议改进的问题:');
      this.results
        .filter(r => r.status === 'warning')
        .forEach(r => {
          console.log(`   ⚠️ ${r.component}: ${r.message}`);
          if (r.fix) console.log(`      💡 ${r.fix}`);
        });
    }

    console.log('\n🎯 下一步行动:');
    if (errorCount > 0) {
      console.log('1. 首先解决所有错误问题');
      console.log('2. 然后处理警告问题');
      console.log('3. 重新运行诊断确认修复');
    } else if (warningCount > 0) {
      console.log('1. 处理警告问题以获得最佳性能');
      console.log('2. 可以尝试小额交易测试');
    } else {
      console.log('1. 系统状态良好，可以进行交易');
      console.log('2. 建议先用小额进行测试');
    }
  }
}

/**
 * 运行综合诊断
 */
async function runComprehensiveDiagnosis() {
  const diagnosis = new ComprehensiveDiagnosis();
  await diagnosis.runFullDiagnosis();
  diagnosis.printReport();
}

// 运行诊断
if (require.main === module) {
  runComprehensiveDiagnosis().catch(console.error);
}

export { ComprehensiveDiagnosis, runComprehensiveDiagnosis };
