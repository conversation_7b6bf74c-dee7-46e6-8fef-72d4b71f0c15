/**
 * REF Finance 交易执行测试
 *
 * 测试V1和DCL v2系统的交易执行功能
 */

import 'dotenv/config';
import RefExecutionService from '../services/refExecutionService';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';

/**
 * REF Finance 执行测试
 */
async function testRefExecution() {
  console.log('🚀 开始REF Finance交易执行测试');

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请在.env文件中设置这些变量');
    return;
  }

  const refExecution = new RefExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化REF执行服务...');
    await refExecution.initialize();

    // 2. 获取报价
    console.log('\n2️⃣ 获取REF Finance报价...');
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1', // 1 NEAR
      slippage: 0.005
    };

    const quote = await refQuoteService.getBestQuote(quoteParams);
    console.log(`📊 获得报价: ${quote.outputAmount} ${TOKENS.USDC.symbol}`);
    console.log(`🔧 使用系统: ${quote.system}`);

    // 3. 计算最小输出金额（考虑滑点）
    const slippage = 0.01; // 1%
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    console.log(`📉 最小输出金额: ${minOutputAmount} ${TOKENS.USDC.symbol}`);

    // 4. 执行交易（注意：这是真实交易，请谨慎！）
    console.log('\n3️⃣ 准备执行交易...');
    console.log('⚠️ 这将执行真实的交易！请确认你想要继续。');
    console.log('💡 如果只是测试，请注释掉下面的执行代码。');

    // 取消注释下面的代码来执行真实交易
    /*
    const inputAmountWei = (parseFloat(quoteParams.amountIn) * Math.pow(10, TOKENS.NEAR.decimals)).toString();
    const minOutputAmountWei = (parseFloat(minOutputAmount) * Math.pow(10, TOKENS.USDC.decimals)).toString();

    let executionResult;
    if (quote.system === 'V1') {
      executionResult = await refExecution.executeV1Swap(
        quote,
        inputAmountWei,
        minOutputAmountWei,
        slippage
      );
    } else if (quote.system === 'DCL_V2') {
      // 对于DCL v2，需要额外的参数
      const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`; // 假设使用0.01%费率
      executionResult = await refExecution.executeDCLv2Swap(
        quote,
        inputAmountWei,
        minOutputAmountWei,
        poolId,
        TOKENS.USDC.id
      );
    }

    if (executionResult?.success) {
      console.log(`✅ 交易执行成功!`);
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 输出金额: ${executionResult.outputAmount}`);
    } else {
      console.log(`❌ 交易执行失败: ${executionResult?.error}`);
    }
    */

    console.log('\n✅ REF Finance执行测试完成');
    console.log('💡 要执行真实交易，请取消注释执行代码并提供正确的账户信息');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 测试V1系统交易构建
 */
async function testV1TransactionBuilding() {
  console.log('\n🔧 测试V1交易构建逻辑...');

  // 模拟Smart Router响应
  const mockSmartRouterResponse = {
    result_code: 0,
    result_data: {
      amount_out: "2234567890",
      routes: [
        {
          pools: [
            {
              pool_id: 4179,
              token_in: "wrap.near",
              token_out: "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1",
              amount_in: "1000000000000000000000000000"
            },
            {
              pool_id: 5515,
              token_in: "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1",
              token_out: "usdt.tether-token.near",
              amount_in: "2234567890"
            }
          ]
        }
      ]
    }
  };

  const mockQuote = {
    system: 'V1' as const,
    contractId: 'v2.ref-finance.near',
    outputAmount: '2.234567890',
    inputAmount: '1',
    rawResponse: mockSmartRouterResponse
  };

  console.log('📋 模拟交易参数构建:');
  console.log(`输入: ${mockQuote.inputAmount} NEAR`);
  console.log(`输出: ${mockQuote.outputAmount} USDT`);
  console.log(`路径: ${mockSmartRouterResponse.result_data.routes[0].pools.length} 个池子`);

  // 这里可以测试交易构建逻辑，但不执行真实交易
  console.log('✅ V1交易构建测试完成');
}

/**
 * 测试DCL v2系统交易构建
 */
async function testDCLv2TransactionBuilding() {
  console.log('\n🔧 测试DCL v2交易构建逻辑...');

  const mockDCLQuote = {
    system: 'DCL_V2' as const,
    contractId: 'dclv2.ref-labs.near',
    outputAmount: '2.234567890',
    inputAmount: '1'
  };

  const poolId = "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100";
  const outputToken = "17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1";

  console.log('📋 模拟DCL v2交易参数:');
  console.log(`池子ID: ${poolId}`);
  console.log(`输出代币: ${outputToken}`);
  console.log(`输入: ${mockDCLQuote.inputAmount} NEAR`);
  console.log(`输出: ${mockDCLQuote.outputAmount} USDC`);

  console.log('✅ DCL v2交易构建测试完成');
}

// 运行测试
if (require.main === module) {
  Promise.all([
    testRefExecution(),
    testV1TransactionBuilding(),
    testDCLv2TransactionBuilding()
  ]).catch(console.error);
}

export { testRefExecution, testV1TransactionBuilding, testDCLv2TransactionBuilding };
