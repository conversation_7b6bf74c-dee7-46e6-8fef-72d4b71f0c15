/**
 * VEAX 注册诊断工具
 * 
 * 专门诊断VEAX代币注册问题，逐步检查每个环节
 */

import 'dotenv/config';
import VeaxExecutionService from '../services/veaxExecutionService';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import { TOKENS } from '../config/tradingPairs';

/**
 * 逐步诊断VEAX注册问题
 */
async function diagnoseRegistration() {
  console.log('🔍 VEAX 注册诊断工具 - 逐步检查');
  console.log('='.repeat(50));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    return;
  }

  console.log(`✅ 环境变量配置正确`);
  console.log(`📋 账户: ${EXECUTION_CONFIG.ACCOUNT_ID}`);
  console.log(`🌐 网络: ${EXECUTION_CONFIG.NETWORK_ID}`);

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 步骤1: 初始化服务
    console.log('\n1️⃣ 初始化VEAX服务...');
    await veaxExecution.initialize();
    console.log('✅ 服务初始化成功');

    // 步骤2: 检查账户余额
    console.log('\n2️⃣ 检查账户余额...');
    const balanceInfo = await veaxExecution.checkAccountBalance();
    const wNearBalance = await veaxExecution.checkWNearBalance();
    
    console.log(`💳 NEAR余额: ${balanceInfo.balanceNear.toFixed(6)} NEAR`);
    console.log(`🔄 wNEAR余额: ${wNearBalance.balanceWNear.toFixed(6)} wNEAR`);
    
    if (balanceInfo.balanceNear < 1) {
      console.log('⚠️ NEAR余额不足，可能无法支付交易费用');
      return;
    }
    
    if (wNearBalance.balanceWNear < 0.1) {
      console.log('⚠️ wNEAR余额不足，无法进行测试交易');
      console.log('💡 请先在 https://app.ref.finance 包装一些NEAR为wNEAR');
      return;
    }

    // 步骤3: 检查用户在VEAX的注册状态
    console.log('\n3️⃣ 检查用户VEAX注册状态...');
    const userStatus = await veaxExecution.checkUserRegistration();
    
    if (userStatus.isRegistered) {
      console.log('✅ 用户已在VEAX注册');
      if (userStatus.storageBalance) {
        console.log(`💰 存储余额: ${userStatus.storageBalance.available} / ${userStatus.storageBalance.total}`);
      }
    } else {
      console.log('❌ 用户未在VEAX注册');
      console.log('🔄 正在注册用户...');
      
      const registerResult = await veaxExecution.registerUser();
      if (registerResult.success) {
        console.log(`✅ 用户注册成功: ${registerResult.transactionHash}`);
      } else {
        console.log(`❌ 用户注册失败: ${registerResult.error}`);
        return;
      }
    }

    // 步骤4: 检查代币注册状态
    console.log('\n4️⃣ 检查代币注册状态...');
    
    const testTokens = [
      { id: TOKENS.NEAR.id, symbol: 'NEAR' },
      { id: TOKENS.USDC.id, symbol: 'USDC' }
    ];

    for (const token of testTokens) {
      console.log(`\n🪙 检查 ${token.symbol} (${token.id}):`);

      // 检查代币合约注册
      const tokenStatus = await veaxExecution.checkTokenRegistration(token.id);

      if (tokenStatus.isRegistered) {
        console.log(`   ✅ ${token.symbol} 已在代币合约注册`);
        console.log(`   💰 存储余额: ${tokenStatus.balance || '0'}`);
      } else {
        console.log(`   ❌ ${token.symbol} 未在代币合约注册`);
        console.log(`   🔄 正在注册 ${token.symbol}...`);

        const registerResult = await veaxExecution.registerToken(token.id);
        if (registerResult.success) {
          console.log(`   ✅ ${token.symbol} 代币合约注册成功: ${registerResult.transactionHash}`);
        } else {
          console.log(`   ❌ ${token.symbol} 代币合约注册失败: ${registerResult.error}`);
        }
      }

      // 检查VEAX合约注册
      const veaxTokenStatus = await veaxExecution.checkTokenRegistrationInVeax(token.id);

      if (veaxTokenStatus.isRegistered) {
        console.log(`   ✅ ${token.symbol} 已在VEAX合约注册`);
      } else {
        console.log(`   ❌ ${token.symbol} 未在VEAX合约注册`);
        console.log(`   🔄 正在VEAX合约中注册 ${token.symbol}...`);

        const registerResult = await veaxExecution.registerTokensInVeax([token.id]);
        if (registerResult.success) {
          console.log(`   ✅ ${token.symbol} VEAX合约注册成功: ${registerResult.transactionHash}`);
        } else {
          console.log(`   ❌ ${token.symbol} VEAX合约注册失败: ${registerResult.error}`);
        }
      }
    }

    console.log('\n✅ 注册诊断完成');
    console.log('\n📋 下一步建议:');
    console.log('1. 如果所有注册都成功，可以尝试执行小额测试交易');
    console.log('2. 如果仍然失败，问题可能在VEAX合约端');
    console.log('3. 可以尝试不同的代币对或减少交易金额');

  } catch (error) {
    console.error('❌ 诊断过程中发生错误:', error);
    
    // 提供详细的错误信息
    if (error instanceof Error) {
      console.error('错误详情:', error.message);
      if (error.stack) {
        console.error('错误堆栈:', error.stack);
      }
    }
  }
}

/**
 * 测试特定代币对的注册
 */
async function testSpecificTokenPair(tokenInId: string, tokenOutId: string) {
  console.log(`\n🧪 测试特定代币对注册: ${tokenInId} → ${tokenOutId}`);
  
  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    await veaxExecution.initialize();

    // 检查两个代币的注册状态
    const tokenInStatus = await veaxExecution.checkTokenRegistration(tokenInId);
    const tokenOutStatus = await veaxExecution.checkTokenRegistration(tokenOutId);

    console.log(`输入代币注册状态: ${tokenInStatus.isRegistered ? '✅' : '❌'}`);
    console.log(`输出代币注册状态: ${tokenOutStatus.isRegistered ? '✅' : '❌'}`);

    // 如果需要，进行注册
    if (!tokenInStatus.isRegistered) {
      console.log('🔄 注册输入代币...');
      const result = await veaxExecution.registerToken(tokenInId);
      console.log(`结果: ${result.success ? '✅' : '❌'} ${result.error || result.transactionHash}`);
    }

    if (!tokenOutStatus.isRegistered) {
      console.log('🔄 注册输出代币...');
      const result = await veaxExecution.registerToken(tokenOutId);
      console.log(`结果: ${result.success ? '✅' : '❌'} ${result.error || result.transactionHash}`);
    }

  } catch (error) {
    console.error('❌ 特定代币对测试失败:', error);
  }
}

// 运行诊断
if (require.main === module) {
  diagnoseRegistration().catch(console.error);
}

export { diagnoseRegistration, testSpecificTokenPair };
