/**
 * 测试优化后的套利流程
 * 
 * 优化点：
 * 1. 移除不必要的等待时间
 * 2. 直接使用区块链返回的wei格式数量
 * 3. 从交易receipt中提取实际输出金额
 */

import 'dotenv/config';
import ArbitrageBot from '../arbitrageBot';
import NearWrapService from '../services/nearWrapService';

async function testOptimizedArbitrage() {
  console.log('🚀 测试优化后的套利流程');
  console.log('='.repeat(60));
  console.log('⚡ 优化点:');
  console.log('   1. 移除不必要的等待时间');
  console.log('   2. 直接使用区块链返回的wei格式数量');
  console.log('   3. 从交易receipt中提取实际输出金额');
  console.log('💰 测试金额: 0.1 NEAR');
  console.log('🔄 路径: NEAR → USDT (VEAX) → NEAR (REF)');
  console.log('='.repeat(60));

  const startTime = Date.now();

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化服务...');
    
    const wrapService = new NearWrapService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    const arbitrageBot = new ArbitrageBot();
    
    // 初始化套利机器人的执行服务
    await (arbitrageBot as any).refExecutionService.initialize();
    await (arbitrageBot as any).veaxExecutionService.initialize();
    await (arbitrageBot as any).nearWrapService.initialize();

    // 2. 检查初始余额
    console.log('\n2️⃣ 检查初始余额...');
    const initialBalance = await wrapService.getBalanceInfo();
    console.log(`💰 NEAR余额: ${initialBalance.nearBalance}`);
    console.log(`🔗 wNEAR余额: ${initialBalance.wNearBalance}`);

    // 3. 定义测试代币
    const tokenA = {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    };

    const tokenB = {
      id: 'usdt.tether-token.near',
      symbol: 'USDT',
      decimals: 6
    };

    const testAmount = '0.1';

    // 4. 记录第一步开始时间
    const step1StartTime = Date.now();
    console.log('\n3️⃣ 执行第一步：VEAX交易 (NEAR → USDT)...');
    console.log(`🚀 开始VEAX交易: ${testAmount} ${tokenA.symbol} → ${tokenB.symbol}`);

    const step1Result = await (arbitrageBot as any).executeVEAXTrade(
      tokenA,
      tokenB,
      testAmount
    );

    const step1EndTime = Date.now();
    const step1Duration = step1EndTime - step1StartTime;

    if (!step1Result.success) {
      throw new Error(`第一步VEAX交易失败: ${step1Result.error}`);
    }

    console.log(`✅ 第一步VEAX交易成功: ${step1Result.outputAmount} ${tokenB.symbol}`);
    console.log(`⏱️ 第一步耗时: ${step1Duration}ms`);
    console.log(`📊 交易哈希: ${step1Result.txHash}`);

    // 5. 立即执行第二步（无等待）
    const step2StartTime = Date.now();
    console.log('\n4️⃣ 立即执行第二步：REF交易 (USDT → NEAR)...');
    console.log(`🚀 开始REF交易: ${step1Result.outputAmount} ${tokenB.symbol} → ${tokenA.symbol}`);
    console.log(`📊 使用第一步的实际输出: ${step1Result.outputAmount}`);

    const step2Result = await (arbitrageBot as any).executeREFTrade(
      tokenB,
      tokenA,
      step1Result.outputAmount
    );

    const step2EndTime = Date.now();
    const step2Duration = step2EndTime - step2StartTime;

    if (!step2Result.success) {
      console.log(`❌ 第二步REF交易失败: ${step2Result.error}`);
      console.log(`⚠️ 注意：第一步VEAX交易已成功，您现在持有 ${step1Result.outputAmount} ${tokenB.symbol}`);
      throw new Error(`第二步REF交易失败: ${step2Result.error}`);
    }

    console.log(`✅ 第二步REF交易成功: ${step2Result.outputAmount} ${tokenA.symbol}`);
    console.log(`⏱️ 第二步耗时: ${step2Duration}ms`);
    console.log(`📊 交易哈希: ${step2Result.txHash}`);

    // 6. 计算总耗时和结果
    const totalEndTime = Date.now();
    const totalDuration = totalEndTime - startTime;
    const tradingDuration = step1Duration + step2Duration;

    console.log('\n5️⃣ 性能分析...');
    console.log(`⏱️ 总耗时: ${totalDuration}ms`);
    console.log(`⏱️ 纯交易耗时: ${tradingDuration}ms`);
    console.log(`⏱️ 初始化耗时: ${totalDuration - tradingDuration}ms`);
    console.log(`⚡ 第一步→第二步间隔: 0ms (立即执行)`);

    // 7. 计算套利结果
    console.log('\n6️⃣ 套利结果分析...');
    const inputAmount = parseFloat(testAmount);
    
    // 检查step2Result.outputAmount的格式
    let outputAmount: number;
    if (typeof step2Result.outputAmount === 'string' && /^\d+$/.test(step2Result.outputAmount)) {
      // 如果是wei格式，转换为人类可读格式
      outputAmount = parseFloat(step2Result.outputAmount) / Math.pow(10, tokenA.decimals);
      console.log(`🔄 检测到wei格式输出: ${step2Result.outputAmount} wei → ${outputAmount.toFixed(6)} ${tokenA.symbol}`);
    } else {
      // 如果已经是人类可读格式
      outputAmount = parseFloat(step2Result.outputAmount!);
      console.log(`📊 使用人类可读格式输出: ${outputAmount.toFixed(6)} ${tokenA.symbol}`);
    }
    
    const actualProfit = outputAmount - inputAmount;
    const profitRate = (actualProfit / inputAmount) * 100;

    console.log(`📊 套利结果:`);
    console.log(`   输入金额: ${inputAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   输出金额: ${outputAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   实际利润: ${actualProfit.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   利润率: ${profitRate.toFixed(4)}%`);

    if (actualProfit > 0) {
      console.log(`🎉 套利成功! 获得利润 ${actualProfit.toFixed(6)} ${tokenA.symbol}`);
    } else {
      console.log(`📉 套利亏损: ${Math.abs(actualProfit).toFixed(6)} ${tokenA.symbol}`);
    }

    // 8. 检查最终余额
    console.log('\n7️⃣ 检查最终余额...');
    const finalBalance = await wrapService.getBalanceInfo();
    console.log(`💰 NEAR余额: ${finalBalance.nearBalance}`);
    console.log(`🔗 wNEAR余额: ${finalBalance.wNearBalance}`);

    // 9. 性能对比
    console.log('\n8️⃣ 性能优化效果...');
    console.log(`⚡ 优化前: ~10秒 (包含5秒等待)`);
    console.log(`⚡ 优化后: ${(tradingDuration / 1000).toFixed(2)}秒 (无等待)`);
    console.log(`🚀 性能提升: ~${((10000 - tradingDuration) / 10000 * 100).toFixed(1)}%`);

    console.log('\n🎉 优化后的套利流程测试完成!');
    console.log('✅ 移除了不必要的等待时间');
    console.log('✅ 直接使用区块链返回的实际数量');
    console.log('✅ 大幅提升了交易执行速度');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    console.log('\n💡 可能的原因:');
    console.log('   1. 网络连接问题');
    console.log('   2. 账户余额不足');
    console.log('   3. 市场价格变化导致交易失败');
    console.log('   4. 合约调用错误');
    
    process.exit(1);
  }
}

// 运行测试
testOptimizedArbitrage().catch(console.error);
