/**
 * 测试VEAX-REF套利监控系统
 * 
 * 验证所有交易对的套利监控是否正常工作
 */

import 'dotenv/config';
import ArbitrageMonitorService from '../services/arbitrageMonitor';
import { tradingPairManager } from '../config/tradingPairs';

/**
 * 测试套利监控系统
 */
async function testArbitrageMonitor() {
  console.log('🧪 测试VEAX-REF套利监控系统');
  console.log('='.repeat(60));

  const monitor = new ArbitrageMonitorService();

  // 显示配置的交易对
  const allPairs = tradingPairManager.getAllPairs();
  const enabledPairs = tradingPairManager.getEnabledPairs();
  
  console.log(`📊 交易对配置:`);
  console.log(`   总交易对: ${allPairs.length}`);
  console.log(`   启用交易对: ${enabledPairs.length}`);
  
  console.log(`\n📋 启用的交易对列表:`);
  enabledPairs.forEach((pair, index) => {
    // 🔧 修复显示逻辑：正确显示动态金额和固定金额
    let amountDisplay: string;
    if (pair.dynamicAmount?.enabled) {
      amountDisplay = `动态(${pair.dynamicAmount.low}-${pair.dynamicAmount.high})`;
    } else if (pair.tradeAmount) {
      amountDisplay = pair.tradeAmount;
    } else {
      amountDisplay = '未配置';
    }
    console.log(`   ${index + 1}. ${pair.id}: ${amountDisplay} ${pair.tokenA.symbol} → ${pair.tokenB.symbol} (间隔: ${pair.checkInterval}ms)`);
  });

  // 测试单次检查
  console.log(`\n🔍 测试单次套利检查 (前3个交易对):`);
  console.log('-'.repeat(60));

  const testPairs = enabledPairs.slice(0, 3); // 只测试前3个交易对
  
  for (let i = 0; i < testPairs.length; i++) {
    const pair = testPairs[i];
    console.log(`\n${i + 1}️⃣ 测试交易对: ${pair.id}`);
    console.log(`   基础代币: ${pair.tokenA.symbol} (${pair.tokenA.id})`);
    console.log(`   报价代币: ${pair.tokenB.symbol} (${pair.tokenB.id})`);
    console.log(`   交易金额: ${pair.tradeAmount} ${pair.tokenA.symbol}`);
    
    try {
      // 使用反射调用私有方法进行测试
      await (monitor as any).checkArbitrageOpportunity(pair);
    } catch (error) {
      console.error(`❌ 测试失败:`, error);
    }
  }

  // 显示监控统计
  console.log(`\n📊 监控统计:`);
  const stats = monitor.getStats();
  console.log(`   总检查次数: ${stats.totalChecks}`);
  console.log(`   发现机会: ${stats.opportunitiesFound}`);
  console.log(`   成功交易: ${stats.successfulTrades}`);
  console.log(`   总利润: ${stats.totalProfit} NEAR`);
  console.log(`   平均检查时间: ${stats.averageCheckTime.toFixed(2)}ms`);
  console.log(`   错误次数: ${stats.errors}`);

  // 显示最近的套利机会
  const recentOpportunities = monitor.getRecentOpportunities(5);
  if (recentOpportunities.length > 0) {
    console.log(`\n💰 最近发现的套利机会:`);
    recentOpportunities.forEach((opp, index) => {
      console.log(`   ${index + 1}. ${opp.pairId} (${opp.direction})`);
      console.log(`      利润: ${opp.profit.absoluteProfit} NEAR (${opp.profit.profitPercentage.toFixed(2)}%)`);
      console.log(`      置信度: ${opp.confidence}`);
      console.log(`      时间: ${new Date(opp.timestamp).toLocaleString()}`);
    });
  } else {
    console.log(`\n⚠️ 暂未发现套利机会`);
  }
}

/**
 * 测试短期监控运行
 */
async function testShortTermMonitoring() {
  console.log('\n🚀 测试短期监控运行 (30秒)');
  console.log('='.repeat(60));

  const monitor = new ArbitrageMonitorService();

  // 临时只启用1-2个交易对进行测试
  const allPairs = tradingPairManager.getAllPairs();
  
  // 禁用所有交易对
  allPairs.forEach(pair => {
    tradingPairManager.togglePair(pair.id, false);
  });

  // 只启用NEAR-USDC和NEAR-USDT进行测试
  tradingPairManager.togglePair('NEAR-USDC', true);
  tradingPairManager.togglePair('NEAR-USDT', true);

  console.log(`📊 测试配置: 只监控 NEAR-USDC 和 NEAR-USDT`);

  // 开始监控
  await monitor.startMonitoring();

  // 运行30秒
  console.log(`⏰ 监控运行30秒...`);
  await new Promise(resolve => setTimeout(resolve, 30000));

  // 停止监控
  monitor.stopMonitoring();

  // 显示结果
  console.log(`\n📊 30秒监控结果:`);
  const stats = monitor.getStats();
  console.log(`   总检查次数: ${stats.totalChecks}`);
  console.log(`   发现机会: ${stats.opportunitiesFound}`);
  console.log(`   平均检查时间: ${stats.averageCheckTime.toFixed(2)}ms`);
  console.log(`   错误次数: ${stats.errors}`);

  const recentOpportunities = monitor.getRecentOpportunities(10);
  if (recentOpportunities.length > 0) {
    console.log(`\n💰 发现的套利机会:`);
    recentOpportunities.forEach((opp, index) => {
      console.log(`   ${index + 1}. ${opp.pairId} (${opp.direction})`);
      console.log(`      步骤1: ${opp.step1.inputAmount} ${opp.step1.inputToken.split('.')[0]} → ${opp.step1.outputAmount} ${opp.step1.outputToken.split('.')[0]} (${opp.step1.dex})`);
      console.log(`      步骤2: ${opp.step2.inputAmount} ${opp.step2.inputToken.split('.')[0]} → ${opp.step2.outputAmount} ${opp.step2.outputToken.split('.')[0]} (${opp.step2.dex})`);
      console.log(`      利润: ${opp.profit.absoluteProfit} NEAR (${opp.profit.profitPercentage.toFixed(2)}%)`);
      console.log(`      净利润: ${opp.profit.netProfit} NEAR`);
      console.log(`      置信度: ${opp.confidence}`);
      console.log(`      时间: ${new Date(opp.timestamp).toLocaleString()}`);
      console.log('');
    });
  } else {
    console.log(`\n⚠️ 30秒内未发现套利机会`);
  }

  // 恢复原始配置
  console.log(`\n🔄 恢复原始交易对配置...`);
  allPairs.forEach(pair => {
    tradingPairManager.togglePair(pair.id, true);
  });
}

/**
 * 测试套利配置
 */
function testArbitrageConfig() {
  console.log('\n⚙️ 测试套利配置');
  console.log('='.repeat(60));

  const config = tradingPairManager.getArbitrageConfig();
  console.log(`📋 当前套利配置:`);
  console.log(`   最小利润阈值: ${config.minProfitThreshold}%`);
  console.log(`   最大滑点: ${config.maxSlippage * 100}%`);
  console.log(`   最大价格影响: ${config.maxPriceImpact * 100}%`);
  console.log(`   Gas费用缓冲: ${config.gasBuffer * 100}%`);
  console.log(`   自动执行: ${config.enabled ? '启用' : '禁用'}`);

  // 测试配置更新
  console.log(`\n🔧 测试配置更新...`);
  tradingPairManager.updateArbitrageConfig({
    minProfitThreshold: 1.0,  // 提高到1%
    enabled: false            // 确保禁用自动执行
  });

  const updatedConfig = tradingPairManager.getArbitrageConfig();
  console.log(`✅ 更新后配置:`);
  console.log(`   最小利润阈值: ${updatedConfig.minProfitThreshold}%`);
  console.log(`   自动执行: ${updatedConfig.enabled ? '启用' : '禁用'}`);
}

/**
 * 显示系统状态
 */
function showSystemStatus() {
  console.log('\n📊 系统状态总览');
  console.log('='.repeat(60));

  const allPairs = tradingPairManager.getAllPairs();
  const enabledPairs = tradingPairManager.getEnabledPairs();
  
  console.log(`🔧 交易对状态:`);
  console.log(`   总数: ${allPairs.length}`);
  console.log(`   启用: ${enabledPairs.length}`);
  console.log(`   禁用: ${allPairs.length - enabledPairs.length}`);

  console.log(`\n💰 代币类型分布:`);
  const tokenTypes = new Set();
  allPairs.forEach(pair => {
    tokenTypes.add(pair.tokenB.symbol);
  });
  console.log(`   监控的报价代币: ${Array.from(tokenTypes).join(', ')}`);

  console.log(`\n⏰ 检查间隔分布:`);
  const intervals = new Map();
  allPairs.forEach(pair => {
    const interval = pair.checkInterval;
    intervals.set(interval, (intervals.get(interval) || 0) + 1);
  });
  intervals.forEach((count, interval) => {
    console.log(`   ${interval}ms: ${count} 个交易对`);
  });
}

/**
 * 主函数
 */
async function main() {
  try {
    showSystemStatus();
    testArbitrageConfig();
    await testArbitrageMonitor();
    
    // 询问是否运行短期监控测试
    console.log('\n❓ 是否运行30秒短期监控测试？');
    console.log('   这将实际启动监控系统并运行30秒');
    console.log('   按 Ctrl+C 可以随时停止');
    
    // 等待3秒，如果没有中断就继续
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    await testShortTermMonitoring();
    
    console.log('\n✅ 套利监控系统测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { testArbitrageMonitor, testShortTermMonitoring };
