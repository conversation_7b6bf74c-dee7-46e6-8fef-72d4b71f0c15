/**
 * DCL v2 池子问题修复
 * 
 * 专门修复"E403: pool not exist"问题
 */

import 'dotenv/config';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

/**
 * 验证并修复DCL v2池子问题
 */
async function fixDCLv2Pools() {
  console.log('🔧 DCL v2 池子问题修复');
  console.log('='.repeat(60));

  // 1. 验证已知存在的池子
  console.log('\n1️⃣ 验证已知存在的池子...');
  const knownPools = [
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|2000',
    'usdt.tether-token.near|wrap.near|100',
    'usdt.tether-token.near|wrap.near|2000'
  ];

  for (const poolId of knownPools) {
    console.log(`\n🔍 测试池子: ${poolId}`);
    const isValid = await testPoolExists(poolId);
    console.log(`   ${isValid ? '✅' : '❌'} 池子${isValid ? '存在' : '不存在'}`);
  }

  // 2. 测试DCL v2报价获取
  console.log('\n2️⃣ 测试DCL v2报价获取...');
  await testDCLv2Quote();

  // 3. 测试修复后的DCL v2交易
  console.log('\n3️⃣ 测试修复后的DCL v2交易...');
  await testFixedDCLv2Transaction();
}

/**
 * 测试池子是否存在
 */
async function testPoolExists(poolId: string): Promise<boolean> {
  try {
    const response = await fetch('https://rpc.mainnet.near.org', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 'dontcare',
        method: 'query',
        params: {
          request_type: 'call_function',
          finality: 'final',
          account_id: 'dclv2.ref-labs.near',
          method_name: 'get_pool',
          args_base64: Buffer.from(JSON.stringify({
            pool_id: poolId
          })).toString('base64')
        }
      })
    });

    const result: any = await response.json();
    return !!(result.result && result.result.result);
  } catch (error) {
    return false;
  }
}

/**
 * 测试DCL v2报价获取
 */
async function testDCLv2Quote() {
  try {
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1',
      slippage: 0.005
    };

    console.log(`📊 获取DCL v2报价: ${quoteParams.amountIn} NEAR → USDC`);

    // 🔧 强制测试DCL v2
    const { dclv2Contract } = await import('../services/dclv2Contract');
    const dclQuote = await dclv2Contract.getDCLv2Quote(quoteParams);

    if (dclQuote) {
      console.log(`✅ 直接DCL v2报价成功: ${dclQuote.outputAmount} USDC`);
      console.log(`🔧 使用的池子: ${JSON.stringify(dclQuote.route)}`);
      return dclQuote;
    } else {
      console.log(`❌ 直接DCL v2报价失败`);
    }

    // 备用：通过报价服务获取
    const quote = await refQuoteService.getBestQuote(quoteParams);

    if (quote.system === 'DCL_V2') {
      console.log(`✅ 成功获得DCL v2报价: ${quote.outputAmount} USDC`);
      console.log(`🔧 使用的池子: ${JSON.stringify(quote.route)}`);
      
      // 检查池子ID格式
      if (quote.route && (quote.route as any).pool_ids) {
        const poolIds = (quote.route as any).pool_ids;
        console.log(`📋 池子ID列表:`);
        poolIds.forEach((poolId: string, index: number) => {
          console.log(`   ${index + 1}. ${poolId}`);
          const parts = poolId.split('|');
          if (parts.length === 3) {
            const [token1, token2, fee] = parts;
            console.log(`      代币1: ${token1}`);
            console.log(`      代币2: ${token2}`);
            console.log(`      费用: ${fee} (${(parseInt(fee) / 10000).toFixed(2)}%)`);
          }
        });
      }
      
      return quote;
    } else {
      console.log(`⚠️ 获得${quote.system}报价，不是DCL v2`);
      return null;
    }
  } catch (error) {
    console.error('❌ DCL v2报价获取失败:', error);
    return null;
  }
}

/**
 * 测试修复后的DCL v2交易
 */
async function testFixedDCLv2Transaction() {
  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    return;
  }

  const refExecution = new RefExecutionServiceCorrect(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 初始化服务
    console.log('🔧 初始化REF执行服务...');
    await refExecution.initialize();

    // 获取DCL v2报价
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1',
      slippage: 0.005
    };

    console.log(`📊 强制获取DCL v2报价: ${quoteParams.amountIn} NEAR → USDC`);

    // 🔧 强制使用DCL v2
    const { dclv2Contract } = await import('../services/dclv2Contract');
    const quote = await dclv2Contract.getDCLv2Quote(quoteParams);

    if (!quote || quote.system !== 'DCL_V2') {
      console.log(`⚠️ 无法获得DCL v2报价，当前报价系统: ${quote?.system || 'null'}`);
      return;
    }

    console.log(`✅ 获得DCL v2报价: ${quote.outputAmount} USDC`);

    // 使用报价中的正确池子ID
    const route = quote.route as any;
    const correctPoolId = route.pool_ids[0];
    console.log(`🔧 使用正确的池子ID: ${correctPoolId}`);

    // 计算交易参数
    const inputAmountWei = toWei(quoteParams.amountIn, TOKENS.NEAR.decimals);
    const slippage = 0.02; // 2%滑点
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDC.decimals);

    console.log(`📊 交易参数:`);
    console.log(`   输入: ${quoteParams.amountIn} NEAR (${inputAmountWei} wei)`);
    console.log(`   预期输出: ${quote.outputAmount} USDC`);
    console.log(`   最小输出: ${minOutputAmount} USDC (${minOutputAmountWei} wei)`);
    console.log(`   池子ID: ${correctPoolId}`);

    // 执行真实交易（如果启用）
    if (process.env.ENABLE_REAL_TRADING === 'true') {
      console.log('\n🚀 执行修复后的DCL v2交易...');
      
      const result = await refExecution.executeDCLv2Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        correctPoolId,
        TOKENS.USDC.id
      );

      if (result.success) {
        console.log(`✅ DCL v2交易修复成功!`);
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log(`📊 输入金额: ${result.inputAmount}`);
        console.log(`📊 输出金额: ${result.outputAmount}`);
        
        // 计算实际汇率
        const actualRate = parseFloat(result.outputAmount || '0') / parseFloat(quoteParams.amountIn);
        console.log(`💱 实际汇率: 1 NEAR = ${actualRate.toFixed(6)} USDC`);
      } else {
        console.log(`❌ DCL v2交易仍然失败: ${result.error}`);
        
        // 分析失败原因
        if (result.error?.includes('E403')) {
          console.log('🔍 E403错误分析:');
          console.log(`   使用的池子ID: ${correctPoolId}`);
          console.log(`   建议检查池子ID格式和代币顺序`);
        }
      }
    } else {
      console.log('💡 模拟模式：交易参数已验证，未执行真实交易');
      console.log('💡 要执行真实交易，请设置 ENABLE_REAL_TRADING=true');
    }

  } catch (error) {
    console.error('❌ DCL v2交易测试失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    await fixDCLv2Pools();
    console.log('\n✅ DCL v2池子修复测试完成');
  } catch (error) {
    console.error('❌ 修复测试失败:', error);
  }
}

// 运行修复测试
if (require.main === module) {
  main().catch(console.error);
}

export { fixDCLv2Pools, testPoolExists, testDCLv2Quote };
