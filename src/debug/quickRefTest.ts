/**
 * 快速REF修复验证测试
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';

/**
 * 快速测试修复版本
 */
async function quickRefTest() {
  console.log('🚀 快速REF修复验证测试');
  console.log('='.repeat(50));

  // 验证环境变量
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 环境变量配置不完整');
    return;
  }

  const refExecution = new RefExecutionServiceFixed(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化
    console.log('\n1️⃣ 初始化服务...');
    await refExecution.initialize();

    // 2. 获取报价
    console.log('\n2️⃣ 获取报价...');
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1',
      slippage: 0.005
    };

    const quote = await refQuoteService.getBestQuote(quoteParams);
    console.log(`📊 报价: ${quote.outputAmount} USDC (${quote.system})`);

    // 3. 测试交易构建（不执行）
    console.log('\n3️⃣ 测试交易构建...');
    
    if (quote.system === 'V1' && quote.rawResponse) {
      console.log('🔧 测试V1交易构建...');
      
      // 模拟构建过程
      const routeData = quote.rawResponse.result_data;
      const routes = routeData.routes || [];
      
      console.log(`📋 路径数量: ${routes.length}`);
      
      routes.forEach((route: any, routeIndex: number) => {
        console.log(`路径${routeIndex + 1}:`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          // 🔧 关键测试：确保pool_id是数字类型
          const poolId = parseInt(pool.pool_id.toString());
          console.log(`  池子${poolIndex + 1}: ${poolId} (类型: ${typeof poolId})`);
          
          if (typeof poolId !== 'number') {
            console.error(`❌ pool_id类型错误: ${typeof poolId}`);
          } else {
            console.log(`  ✅ pool_id类型正确: number`);
          }
        });
      });

      // 测试JSON序列化
      const testAction = {
        pool_id: parseInt(routes[0].pools[0].pool_id.toString()),
        token_in: routes[0].pools[0].token_in,
        token_out: routes[0].pools[0].token_out,
        amount_out: "0",
        min_amount_out: "0"
      };

      const serialized = JSON.stringify(testAction);
      console.log('\n🔍 序列化测试:');
      console.log(`原始: pool_id = ${testAction.pool_id} (${typeof testAction.pool_id})`);
      console.log(`序列化: ${serialized}`);
      
      const parsed = JSON.parse(serialized);
      console.log(`反序列化: pool_id = ${parsed.pool_id} (${typeof parsed.pool_id})`);
      
      if (typeof parsed.pool_id === 'number') {
        console.log('✅ JSON序列化保持数字类型');
      } else {
        console.log('❌ JSON序列化转换为字符串类型');
      }

    } else if (quote.system === 'DCL_V2') {
      console.log('🔧 测试DCL v2交易构建...');
      
      const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
      console.log(`池子ID: ${poolId}`);
      
      const swapParams = {
        pool_ids: [poolId],
        output_token: TOKENS.USDC.id,
        min_output_amount: "220000",
        skip_unwrap_near: true
      };

      const msg = { Swap: swapParams };
      console.log('DCL v2消息:', JSON.stringify(msg, null, 2));
      
      console.log('\n💰 DCL v2成本分析:');
      console.log('修复前: attachedDeposit = 200 NEAR (❌ 过高)');
      console.log('修复后: attachedDeposit = 1 yoctoNEAR (✅ 正确)');
    }

    // 4. 总结修复点
    console.log('\n4️⃣ 修复总结:');
    console.log('✅ 修复1: pool_id确保为数字类型');
    console.log('✅ 修复2: DCL v2 attachedDeposit降低到1 yoctoNEAR');
    console.log('✅ 修复3: 正确调用输入代币合约的ft_transfer_call');
    console.log('✅ 修复4: 完整处理Smart Router的多路径结构');

    console.log('\n🎯 下一步建议:');
    console.log('1. 使用小额资金测试V1交易');
    console.log('2. 使用小额资金测试DCL v2交易');
    console.log('3. 验证交易成功后替换原服务');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 显示修复对比
 */
function showFixComparison() {
  console.log('\n📊 修复前后对比:');
  console.log('='.repeat(50));
  
  console.log('\n🔧 问题1: pool_id类型');
  console.log('修复前: "pool_id": "5470" (字符串)');
  console.log('修复后: "pool_id": 5470 (数字)');
  
  console.log('\n🔧 问题2: DCL v2成本');
  console.log('修复前: attachedDeposit: 200 NEAR');
  console.log('修复后: attachedDeposit: 1 yoctoNEAR');
  
  console.log('\n🔧 问题3: 合约调用');
  console.log('修复前: refContract.ft_transfer_call()');
  console.log('修复后: inputTokenContract.ft_transfer_call()');
  
  console.log('\n🔧 问题4: 错误信息');
  console.log('修复前: E28: Illegal msg in ft_transfer_call');
  console.log('修复后: 消息格式正确，合约能解析');
}

// 运行测试
if (require.main === module) {
  Promise.all([
    quickRefTest(),
    showFixComparison()
  ]).catch(console.error);
}

export { quickRefTest };
