/**
 * VEAX 诊断工具
 * 
 * 诊断VEAX交易失败的原因
 */

import 'dotenv/config';
import VeaxExecutionService from '../services/veaxExecutionService';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';

async function diagnoseVeax() {
  console.log('🔍 VEAX 诊断工具');

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量');
    return;
  }

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    await veaxExecution.initialize();

    console.log('\n📊 诊断结果:');
    console.log('1. ✅ 交易格式正确 (与示例一致)');
    console.log('2. ✅ 余额充足 (5.6+ wNEAR)');
    console.log('3. ✅ 用户已注册VEAX');
    console.log('4. ✅ 代币已注册 (钱包层面)');
    console.log('5. ❌ 交易失败: Error -*********');

    console.log('\n🤔 可能的原因:');
    console.log('1. VEAX合约内部状态问题');
    console.log('2. 特定代币对暂时不可用');
    console.log('3. 流动性不足');
    console.log('4. VEAX合约维护中');

    console.log('\n💡 建议:');
    console.log('1. 检查VEAX官网是否有维护公告');
    console.log('2. 尝试其他代币对 (如 NEAR-USDC)');
    console.log('3. 减少交易金额');
    console.log('4. 等待一段时间后重试');

    console.log('\n📋 技术细节:');
    console.log('- 错误发生在 wrap.near 合约中');
    console.log('- 错误代码: -*********');
    console.log('- 资金自动退回，无损失');
    console.log('- 交易格式与成功示例完全一致');

    console.log('\n🎯 结论:');
    console.log('代码实现正确，问题可能在VEAX合约端。');
    console.log('建议联系VEAX团队或查看官方文档。');

  } catch (error) {
    console.error('❌ 诊断失败:', error);
  }
}

// 运行诊断
if (require.main === module) {
  diagnoseVeax().catch(console.error);
}

export { diagnoseVeax };
