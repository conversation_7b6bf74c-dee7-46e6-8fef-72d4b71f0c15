/**
 * 测试正确版本的REF Finance执行服务
 * 
 * 基于官方SDK文档的正确实现测试
 */

import 'dotenv/config';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 正确版本测试器
 */
class CorrectRefExecutionTest {
  private refExecution?: RefExecutionServiceCorrect;

  /**
   * 运行完整测试
   */
  async runTest(): Promise<void> {
    console.log('🧪 REF Finance 正确版本执行测试');
    console.log('='.repeat(50));

    // 1. 验证环境
    if (!this.validateEnvironment()) {
      return;
    }

    // 2. 初始化服务
    await this.initializeService();

    // 3. 对比分析
    await this.compareImplementations();

    // 4. 测试正确版本
    await this.testCorrectImplementation();

    console.log('\n✅ 正确版本测试完成');
  }

  /**
   * 验证环境
   */
  private validateEnvironment(): boolean {
    console.log('\n1️⃣ 验证环境配置...');

    const configValidation = validateExecutionConfig();
    if (!configValidation.valid) {
      console.error('❌ 缺少必要的环境变量:');
      configValidation.missing.forEach(key => console.error(`   - ${key}`));
      return false;
    }

    console.log('✅ 环境配置验证通过');
    return true;
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    console.log('\n2️⃣ 初始化正确版本服务...');

    try {
      this.refExecution = new RefExecutionServiceCorrect(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.refExecution.initialize();
      console.log('✅ REF执行服务(正确版)初始化成功');
    } catch (error: any) {
      console.error('❌ 服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 对比不同实现版本
   */
  private async compareImplementations(): Promise<void> {
    console.log('\n3️⃣ 对比不同实现版本...');

    console.log('📊 实现版本对比:');
    console.log('='.repeat(40));

    console.log('\n❌ 原版本问题:');
    console.log('1. 调用错误的合约: v2.ref-finance.near.ft_transfer_call()');
    console.log('2. pool_id类型错误: 字符串类型');
    console.log('3. 交易构建不完整: 简单处理');

    console.log('\n⚠️ 修复版本问题:');
    console.log('1. ✅ 调用正确: inputToken.ft_transfer_call()');
    console.log('2. ✅ pool_id正确: 数字类型');
    console.log('3. ❌ msg格式错误: 包含skip_unwrap_near');
    console.log('4. ❌ amount_in设置错误: 多个池子都设置');

    console.log('\n✅ 正确版本改进:');
    console.log('1. ✅ 调用正确: inputToken.ft_transfer_call()');
    console.log('2. ✅ pool_id正确: 数字类型');
    console.log('3. ✅ msg格式正确: 按照官方SDK格式');
    console.log('4. ✅ amount_in设置正确: 只有第一个池子设置');
    console.log('5. ✅ min_amount_out设置正确: 只有最后一个池子设置');

    console.log('\n🔍 关键差异分析:');
    console.log('官方SDK格式: {"force":0,"actions":[...]}');
    console.log('修复版格式: {"force":0,"actions":[...],"skip_unwrap_near":false}');
    console.log('💡 skip_unwrap_near可能导致E76错误');
  }

  /**
   * 测试正确实现
   */
  private async testCorrectImplementation(): Promise<void> {
    console.log('\n4️⃣ 测试正确实现...');

    try {
      // 获取V1报价
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDC,
        amountIn: '0.01', // 0.01 NEAR 小额测试
        slippage: 0.01 // 1% 滑点
      });

      console.log(`📊 获取报价: ${quote.system} 系统`);
      console.log(`💰 预期输出: ${quote.outputAmount} USDC`);

      if (quote.system !== 'V1') {
        console.log('ℹ️ 当前最佳报价不是V1系统，跳过V1测试');
        return;
      }

      // 计算交易参数
      const inputAmount = '0.01';
      const inputAmountWei = this.toWei(inputAmount, TOKENS.NEAR.decimals);
      const minOutputAmount = new Big(quote.outputAmount).times(0.99).toString(); // 1%滑点
      const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.USDC.decimals);

      console.log(`🔢 输入金额: ${inputAmountWei} (${inputAmount} NEAR)`);
      console.log(`🔢 最小输出: ${minOutputAmountWei} (${minOutputAmount} USDC)`);

      // 分析报价结构
      this.analyzeQuoteStructure(quote);

      // 显示正确的交易构建过程
      this.showCorrectTransactionBuilding(quote, minOutputAmountWei);

      console.log('\n⚠️ 准备执行真实交易...');
      console.log('💡 这将花费真实的NEAR，确认继续？');
      console.log('倒计时: 5秒...');

      // 等待5秒
      for (let i = 5; i > 0; i--) {
        console.log(`倒计时: ${i}...`);
        await this.sleep(1000);
      }

      if (!this.refExecution) {
        throw new Error('REF执行服务未初始化');
      }

      // 执行真实交易
      console.log('\n🚀 执行V1交易(正确版)...');
      const result = await this.refExecution.executeV1Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        0.01
      );

      if (result.success) {
        console.log('🎉 V1交易成功!');
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log(`💰 输入金额: ${result.inputAmount}`);
        console.log(`💰 输出金额: ${result.outputAmount}`);
        console.log('\n✅ 正确版本修复成功！');
      } else {
        console.log('❌ V1交易失败:', result.error);
        this.analyzeError(result.error);
      }

    } catch (error: any) {
      console.error('❌ 正确版本测试失败:', error.message);
      this.analyzeError(error.message);
    }
  }

  /**
   * 分析报价结构
   */
  private analyzeQuoteStructure(quote: any): void {
    console.log('\n🔍 分析报价结构:');
    console.log(`系统: ${quote.system}`);
    console.log(`输出金额: ${quote.outputAmount}`);

    if (quote.rawResponse?.result_data?.routes) {
      const routes = quote.rawResponse.result_data.routes;
      console.log(`路径数量: ${routes.length}`);

      routes.forEach((route: any, routeIndex: number) => {
        console.log(`路径 ${routeIndex + 1}:`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`  池子 ${poolIndex + 1}:`);
          console.log(`    pool_id: ${pool.pool_id} (${typeof pool.pool_id})`);
          console.log(`    token_in: ${pool.token_in}`);
          console.log(`    token_out: ${pool.token_out}`);
          if (pool.amount_in) {
            console.log(`    amount_in: ${pool.amount_in}`);
          }
        });
      });
    }
  }

  /**
   * 显示正确的交易构建过程
   */
  private showCorrectTransactionBuilding(quote: any, minOutputAmount: string): void {
    console.log('\n🏗️ 正确的交易构建过程:');

    if (quote.rawResponse?.result_data?.routes) {
      const routes = quote.rawResponse.result_data.routes;
      const actions: any[] = [];

      routes.forEach((route: any, routeIndex: number) => {
        route.pools.forEach((pool: any, poolIndex: number) => {
          const action: any = {
            pool_id: parseInt(pool.pool_id.toString()),
            token_in: pool.token_in,
            token_out: pool.token_out,
            min_amount_out: "0"
          };

          // 只有第一个池子设置amount_in
          if (routeIndex === 0 && poolIndex === 0 && pool.amount_in) {
            action.amount_in = pool.amount_in;
          }

          // 只有最后一个池子设置min_amount_out
          if (routeIndex === routes.length - 1 && poolIndex === route.pools.length - 1) {
            action.min_amount_out = minOutputAmount;
          }

          actions.push(action);
        });
      });

      const correctMsg = {
        force: 0,
        actions: actions
      };

      console.log('✅ 正确的msg格式:');
      console.log(JSON.stringify(correctMsg, null, 2));
    }
  }

  /**
   * 分析错误
   */
  private analyzeError(error?: string): void {
    if (!error) return;

    console.log('\n🔍 错误分析:');
    
    if (error.includes('E76: invalid params')) {
      console.log('❌ E76错误: 参数无效');
      console.log('💡 可能原因: msg格式不正确或参数设置错误');
    }
    
    if (error.includes('ft_transfer_call')) {
      console.log('❌ ft_transfer_call调用错误');
      console.log('💡 可能原因: 调用了错误的合约');
    }
    
    if (error.includes('pool_id')) {
      console.log('❌ pool_id相关错误');
      console.log('💡 可能原因: pool_id类型或格式错误');
    }
  }

  /**
   * 精度转换工具
   */
  private toWei(amount: string, decimals: number): string {
    return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 运行正确版本测试
 */
async function runCorrectRefExecutionTest() {
  const tester = new CorrectRefExecutionTest();
  await tester.runTest();
}

// 运行测试
if (require.main === module) {
  runCorrectRefExecutionTest().catch(console.error);
}

export { CorrectRefExecutionTest, runCorrectRefExecutionTest };
