/**
 * 测试真实交易：0.1 NEAR → USDT
 */

import 'dotenv/config';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';
import { v1SmartRouter } from '../services/v1SmartRouter';
import { COMMON_TOKENS } from '../config';

/**
 * 测试0.1 NEAR → USDT的真实交易
 */
async function testRealTrade() {
  console.log('🚀 测试真实交易：0.1 NEAR → USDT');
  console.log('='.repeat(60));

  try {
    // 1. 获取Smart Router报价
    console.log('\n📊 步骤1：获取Smart Router报价');
    const quote = await v1SmartRouter.getV1Quote({
      tokenIn: { id: COMMON_TOKENS.NEAR, decimals: 24, symbol: 'NEAR', name: 'NEAR' },
      tokenOut: { id: COMMON_TOKENS.USDT, decimals: 6, symbol: 'USDT', name: 'USDT' },
      amountIn: '0.1',
      slippage: 0.005
    });

    if (!quote || !quote.rawResponse) {
      console.log('❌ 未获取到报价');
      return;
    }

    console.log('✅ 获取到报价');
    const routes = quote.rawResponse.result_data.routes;
    console.log(`📋 路径数量: ${routes.length}`);

    // 2. 分析Smart Router返回的pools
    console.log('\n📊 步骤2：分析Smart Router返回的pools');
    routes.forEach((route: any, routeIndex: number) => {
      console.log(`\n🛤️  路径 ${routeIndex + 1}:`);
      console.log(`   路径分配金额: ${route.amount_in}`);
      console.log(`   预期输出: ${route.amount_out}`);
      console.log(`   最小输出: ${route.min_amount_out}`);
      
      route.pools.forEach((pool: any, poolIndex: number) => {
        console.log(`\n   池子 ${poolIndex + 1} (ID: ${pool.pool_id}):`);
        console.log(`     token_in: ${pool.token_in}`);
        console.log(`     token_out: ${pool.token_out}`);
        console.log(`     amount_in: ${pool.amount_in || '0'} ${pool.amount_in === '0' ? '(链式中间步骤)' : '(Smart Router分配)'}`);
        console.log(`     min_amount_out: ${pool.min_amount_out || '0'}`);
        console.log(`     ✅ 这就是一个完整的action!`);
      });
    });

    // 3. 构建actions（使用我们修复后的逻辑）
    console.log('\n📊 步骤3：构建actions（修复后的逻辑）');
    const actions = routes.flatMap((route: any) => 
      route.pools.map((pool: any) => ({
        pool_id: parseInt(pool.pool_id),
        token_in: pool.token_in,
        token_out: pool.token_out,
        amount_in: pool.amount_in === "0" ? undefined : pool.amount_in,
        min_amount_out: pool.min_amount_out || "0"
      }))
    );

    console.log(`✅ 构建了 ${actions.length} 个actions:`);
    actions.forEach((action: any, index: number) => {
      console.log(`\n   Action ${index + 1}:`);
      console.log(`     pool_id: ${action.pool_id}`);
      console.log(`     token_in: ${action.token_in}`);
      console.log(`     token_out: ${action.token_out}`);
      console.log(`     amount_in: ${action.amount_in || '未设置'}`);
      console.log(`     min_amount_out: ${action.min_amount_out}`);
    });

    // 4. 构建完整的交易消息
    console.log('\n📊 步骤4：构建完整的交易消息');
    const totalInputAmount = quote.rawResponse.result_data.amount_in;
    const msg = {
      force: 0,
      actions: actions
    };

    console.log(`📋 ft_transfer_call参数:`);
    console.log(`   sender_id: ${process.env.NEAR_ACCOUNT_ID}`);
    console.log(`   amount: ${totalInputAmount} (总输入金额)`);
    console.log(`   msg: ${JSON.stringify(msg, null, 2)}`);

    // 5. 执行真实交易
    console.log('\n📊 步骤5：执行真实交易');
    console.log('⚠️ 即将执行真实交易，使用0.1 NEAR');
    
    const refService = new RefExecutionServiceCorrect(
      process.env.NEAR_ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY!,
      'mainnet'
    );

    await refService.initialize();

    // 计算最小输出（考虑滑点）
    const expectedOutput = parseFloat(quote.rawResponse.result_data.amount_out);
    const minOutput = Math.floor(expectedOutput * (1 - 0.005)).toString(); // 0.5%滑点

    console.log(`💱 交易参数:`);
    console.log(`   输入: 0.1 NEAR (${totalInputAmount} wei)`);
    console.log(`   预期输出: ${expectedOutput} USDT`);
    console.log(`   最小输出: ${minOutput} USDT`);

    const result = await refService.executeV1Swap(
      quote,
      COMMON_TOKENS.NEAR,
      totalInputAmount,
      minOutput,
      0.005
    );

    console.log('\n📊 交易结果:');
    if (result.success) {
      console.log('✅ 交易成功!');
      console.log(`   交易哈希: ${result.transactionHash}`);
      console.log(`   输出金额: ${result.outputAmount} USDT`);
      console.log(`   输出金额(wei): ${result.outputAmountWei}`);
    } else {
      console.log('❌ 交易失败:');
      console.log(`   错误: ${result.error}`);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 REF Finance Smart Router 真实交易测试');
  console.log('测试交易：0.1 NEAR → USDT');
  console.log('='.repeat(80));
  
  await testRealTrade();
  
  console.log('\n🎉 测试完成!');
}

// 运行测试
main().catch(console.error);
