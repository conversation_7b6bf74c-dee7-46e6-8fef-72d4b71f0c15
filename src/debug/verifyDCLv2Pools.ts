/**
 * DCL v2 池子验证工具
 * 
 * 验证DCL v2池子是否存在，并获取正确的池子ID
 */

import 'dotenv/config';
import { TOKENS } from '../config/tradingPairs';

/**
 * 验证DCL v2池子
 */
async function verifyDCLv2Pools() {
  console.log('🔍 DCL v2 池子验证');
  console.log('='.repeat(50));

  const tokenPairs = [
    { tokenA: TOKENS.NEAR, tokenB: TOKENS.USDC },
    { tokenA: TOKENS.NEAR, tokenB: TOKENS.USDT },
  ];

  const feeLevels = [100, 400, 2000, 10000]; // 基点

  for (const pair of tokenPairs) {
    console.log(`\n📊 验证交易对: ${pair.tokenA.symbol}-${pair.tokenB.symbol}`);
    
    for (const fee of feeLevels) {
      const poolId = `${pair.tokenA.id}|${pair.tokenB.id}|${fee}`;
      console.log(`\n🔧 测试池子: ${poolId}`);
      
      try {
        // 尝试获取池子信息
        const response = await fetch('https://rpc.mainnet.near.org', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 'dontcare',
            method: 'query',
            params: {
              request_type: 'call_function',
              finality: 'final',
              account_id: 'dclv2.ref-labs.near',
              method_name: 'get_pool',
              args_base64: Buffer.from(JSON.stringify({
                pool_id: poolId
              })).toString('base64')
            }
          })
        });

        const result: any = await response.json();

        if (result.result && result.result.result) {
          const poolData = JSON.parse(Buffer.from(result.result.result).toString());
          console.log(`   ✅ 池子存在`);
          console.log(`   📊 流动性: ${poolData.total_liquidity || 'N/A'}`);
          console.log(`   📊 费率: ${fee / 100}%`);
        } else {
          console.log(`   ❌ 池子不存在`);
        }
      } catch (error) {
        console.log(`   ❌ 查询失败: ${error}`);
      }
    }
  }

  // 测试反向池子ID
  console.log('\n🔄 测试反向池子ID:');
  for (const pair of tokenPairs) {
    for (const fee of feeLevels) {
      const reversePoolId = `${pair.tokenB.id}|${pair.tokenA.id}|${fee}`;
      console.log(`\n🔧 测试反向池子: ${reversePoolId}`);
      
      try {
        const response = await fetch('https://rpc.mainnet.near.org', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 'dontcare',
            method: 'query',
            params: {
              request_type: 'call_function',
              finality: 'final',
              account_id: 'dclv2.ref-labs.near',
              method_name: 'get_pool',
              args_base64: Buffer.from(JSON.stringify({
                pool_id: reversePoolId
              })).toString('base64')
            }
          })
        });

        const result: any = await response.json();

        if (result.result && result.result.result) {
          console.log(`   ✅ 反向池子存在`);
          break; // 找到一个就够了
        } else {
          console.log(`   ❌ 反向池子不存在`);
        }
      } catch (error) {
        console.log(`   ❌ 查询失败: ${error}`);
      }
    }
  }
}

/**
 * 获取所有DCL v2池子列表
 */
async function getAllDCLv2Pools() {
  console.log('\n📋 获取所有DCL v2池子列表:');
  console.log('='.repeat(50));

  try {
    const response = await fetch('https://rpc.mainnet.near.org', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        id: 'dontcare',
        method: 'query',
        params: {
          request_type: 'call_function',
          finality: 'final',
          account_id: 'dclv2.ref-labs.near',
          method_name: 'list_pools',
          args_base64: Buffer.from(JSON.stringify({
            from_index: 0,
            limit: 100
          })).toString('base64')
        }
      })
    });

    const result: any = await response.json();

    if (result.result && result.result.result) {
      const pools = JSON.parse(Buffer.from(result.result.result).toString());
      console.log(`📊 找到 ${pools.length} 个DCL v2池子:`);
      
      // 过滤包含NEAR和USDC的池子
      const relevantPools = pools.filter((pool: any) => {
        const poolId = pool.pool_id || pool.id;
        return poolId && (
          (poolId.includes('wrap.near') && poolId.includes('17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1')) ||
          (poolId.includes('wrap.near') && poolId.includes('usdt.tether-token.near'))
        );
      });

      console.log(`\n🎯 相关池子 (NEAR-USDC/USDT):`);
      relevantPools.forEach((pool: any, index: number) => {
        const poolId = pool.pool_id || pool.id;
        console.log(`   ${index + 1}. ${poolId}`);
        if (pool.total_liquidity) {
          console.log(`      流动性: ${pool.total_liquidity}`);
        }
      });

      if (relevantPools.length === 0) {
        console.log('   ❌ 没有找到相关的NEAR-USDC/USDT池子');
        
        console.log('\n📋 所有池子列表:');
        pools.slice(0, 10).forEach((pool: any, index: number) => {
          const poolId = pool.pool_id || pool.id;
          console.log(`   ${index + 1}. ${poolId}`);
        });
        
        if (pools.length > 10) {
          console.log(`   ... 还有 ${pools.length - 10} 个池子`);
        }
      }
    } else {
      console.log('❌ 无法获取池子列表');
    }
  } catch (error) {
    console.error('❌ 获取池子列表失败:', error);
  }
}

/**
 * 建议使用V1系统
 */
function suggestV1Alternative() {
  console.log('\n💡 建议解决方案:');
  console.log('='.repeat(50));
  
  console.log('🔧 由于DCL v2池子问题，建议:');
  console.log('1. ✅ 优先使用V1系统进行NEAR-USDC交易');
  console.log('2. ✅ V1系统有更好的流动性和稳定性');
  console.log('3. ✅ V1系统支持多跳路径，覆盖更多交易对');
  
  console.log('\n🎯 V1交易测试建议:');
  console.log('1. 强制使用V1系统获取报价');
  console.log('2. 测试修复后的V1交易执行');
  console.log('3. 验证pool_id数字类型修复');
  
  console.log('\n📋 修复后的V1交易应该能够成功执行');
}

// 运行验证
if (require.main === module) {
  Promise.all([
    verifyDCLv2Pools(),
    getAllDCLv2Pools(),
    suggestV1Alternative()
  ]).catch(console.error);
}

export { verifyDCLv2Pools, getAllDCLv2Pools };
