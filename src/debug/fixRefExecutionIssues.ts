/**
 * REF Finance 执行问题修复工具
 * 
 * 专门修复REF Finance交易执行中的滑点失败和DCL v2错误问题
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * REF Finance 修复工具
 */
class RefExecutionFixer {
  private refExecution?: RefExecutionServiceFixed;

  /**
   * 运行完整修复流程
   */
  async runFix(): Promise<void> {
    console.log('🔧 REF Finance 执行问题修复工具');
    console.log('='.repeat(50));

    // 1. 验证环境配置
    if (!this.validateEnvironment()) {
      return;
    }

    // 2. 初始化修复版本服务
    await this.initializeFixedService();

    // 3. 测试V1系统修复
    await this.testV1SystemFix();

    // 4. 测试DCL v2系统修复
    await this.testDCLv2SystemFix();

    // 5. 执行小额真实交易测试
    await this.executeSmallRealTrade();

    console.log('\n✅ REF Finance 修复流程完成');
  }

  /**
   * 验证环境配置
   */
  private validateEnvironment(): boolean {
    console.log('\n1️⃣ 验证环境配置...');

    const configValidation = validateExecutionConfig();
    if (!configValidation.valid) {
      console.error('❌ 缺少必要的环境变量:');
      configValidation.missing.forEach(key => console.error(`   - ${key}`));
      console.error('💡 请在.env文件中设置这些变量');
      return false;
    }

    console.log('✅ 环境配置验证通过');
    return true;
  }

  /**
   * 初始化修复版本服务
   */
  private async initializeFixedService(): Promise<void> {
    console.log('\n2️⃣ 初始化修复版本服务...');

    try {
      this.refExecution = new RefExecutionServiceFixed(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.refExecution.initialize();
      console.log('✅ REF执行服务(修复版)初始化成功');
    } catch (error: any) {
      console.error('❌ 服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 测试V1系统修复
   */
  private async testV1SystemFix(): Promise<void> {
    console.log('\n3️⃣ 测试V1系统修复...');

    try {
      // 获取V1报价
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDC,
        amountIn: '0.1',
        slippage: 0.005
      });

      if (quote.system !== 'V1') {
        console.log('ℹ️ 当前最佳报价不是V1系统，跳过V1测试');
        return;
      }

      console.log(`📊 V1报价: 0.1 NEAR → ${quote.outputAmount} USDC`);

      // 分析修复要点
      this.analyzeV1Fixes(quote);

      // 构建正确的交易参数
      this.buildCorrectV1Transaction(quote);

    } catch (error: any) {
      console.error('❌ V1系统测试失败:', error.message);
    }
  }

  /**
   * 分析V1修复要点
   */
  private analyzeV1Fixes(quote: any): void {
    console.log('\n🔍 V1系统修复要点分析:');

    if (quote.rawResponse?.result_data?.routes) {
      const routes = quote.rawResponse.result_data.routes;
      console.log(`   📋 路径数量: ${routes.length}`);

      routes.forEach((route: any, routeIndex: number) => {
        console.log(`   路径 ${routeIndex + 1}:`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`     池子 ${poolIndex + 1}: ${pool.pool_id} (${typeof pool.pool_id})`);
          
          // 检查pool_id类型
          if (typeof pool.pool_id !== 'number') {
            console.log(`     ⚠️ pool_id类型错误，需要转换为数字`);
          } else {
            console.log(`     ✅ pool_id类型正确`);
          }

          // 检查amount_in分配
          if (pool.amount_in) {
            console.log(`     💰 amount_in: ${pool.amount_in}`);
          }
        });
      });
    }

    console.log('\n🔧 关键修复点:');
    console.log('   1. ✅ 调用输入代币合约的ft_transfer_call');
    console.log('   2. ✅ REF合约作为receiver_id');
    console.log('   3. ✅ pool_id强制转换为数字类型');
    console.log('   4. ✅ 正确处理多路径结构');
    console.log('   5. ✅ 只在需要时设置amount_in');
  }

  /**
   * 构建正确的V1交易
   */
  private buildCorrectV1Transaction(quote: any): void {
    console.log('\n🏗️ 构建正确的V1交易:');

    const inputAmount = '0.1';
    const inputAmountWei = this.toWei(inputAmount, TOKENS.NEAR.decimals);
    const minOutputAmount = (parseFloat(quote.outputAmount) * 0.99).toString(); // 1%滑点
    const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.USDC.decimals);

    console.log('📋 正确的交易参数:');
    console.log(`   合约调用: ${TOKENS.NEAR.id}.ft_transfer_call()`);
    console.log(`   参数:`);
    console.log(`     receiver_id: "v2.ref-finance.near"`);
    console.log(`     amount: "${inputAmountWei}"`);
    console.log(`     msg: {包含正确构建的actions}`);
    console.log(`   附加存款: 1 yoctoNEAR`);
    console.log(`   Gas: 300 TGas`);

    console.log('\n❌ 原版本错误:');
    console.log('   - 调用: v2.ref-finance.near.ft_transfer_call()');
    console.log('   - pool_id: 字符串类型');
    console.log('   - 简单的单路径处理');

    console.log('\n✅ 修复版本正确:');
    console.log(`   - 调用: ${TOKENS.NEAR.id}.ft_transfer_call()`);
    console.log('   - pool_id: 数字类型');
    console.log('   - 完整的多路径处理');
  }

  /**
   * 测试DCL v2系统修复
   */
  private async testDCLv2SystemFix(): Promise<void> {
    console.log('\n4️⃣ 测试DCL v2系统修复...');

    try {
      // 强制获取DCL v2报价
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.USDT,
        tokenOut: TOKENS.NEAR,
        amountIn: '100',
        slippage: 0.005
      });

      if (quote.system !== 'DCL_V2') {
        console.log('ℹ️ 当前最佳报价不是DCL v2系统，跳过DCL v2测试');
        return;
      }

      console.log(`📊 DCL v2报价: 100 USDT → ${quote.outputAmount} NEAR`);

      // 分析DCL v2修复要点
      this.analyzeDCLv2Fixes();

      // 构建正确的DCL v2交易
      this.buildCorrectDCLv2Transaction(quote);

    } catch (error: any) {
      console.error('❌ DCL v2系统测试失败:', error.message);
    }
  }

  /**
   * 分析DCL v2修复要点
   */
  private analyzeDCLv2Fixes(): void {
    console.log('\n🔍 DCL v2系统修复要点分析:');

    console.log('🔧 关键修复点:');
    console.log('   1. ✅ 调用输入代币合约的ft_transfer_call');
    console.log('   2. ✅ DCL v2合约作为receiver_id');
    console.log('   3. ✅ 使用更高的attachedDeposit (0.2 NEAR)');
    console.log('   4. ✅ 正确的池子ID格式');
    console.log('   5. ✅ 分别查询每个池子');

    console.log('\n❌ 原版本问题:');
    console.log('   - attachedDeposit: 1 yoctoNEAR (太少)');
    console.log('   - 批量查询池子 (不支持)');
    console.log('   - 错误的合约调用方式');

    console.log('\n✅ 修复版本改进:');
    console.log('   - attachedDeposit: 0.2 NEAR (足够)');
    console.log('   - 分别查询每个池子');
    console.log('   - 正确的合约调用方式');
  }

  /**
   * 构建正确的DCL v2交易
   */
  private buildCorrectDCLv2Transaction(quote: any): void {
    console.log('\n🏗️ 构建正确的DCL v2交易:');

    const poolId = `${TOKENS.USDT.id}|${TOKENS.NEAR.id}|100`;
    const inputAmount = '100';
    const inputAmountWei = this.toWei(inputAmount, TOKENS.USDT.decimals);
    const minOutputAmount = (parseFloat(quote.outputAmount) * 0.99).toString();
    const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.NEAR.decimals);

    console.log('📋 正确的交易参数:');
    console.log(`   合约调用: ${TOKENS.USDT.id}.ft_transfer_call()`);
    console.log(`   参数:`);
    console.log(`     receiver_id: "dclv2.ref-labs.near"`);
    console.log(`     amount: "${inputAmountWei}"`);
    console.log(`     msg: {"Swap": {"pool_ids": ["${poolId}"], ...}}`);
    console.log(`   附加存款: 0.2 NEAR`);
    console.log(`   Gas: 300 TGas`);
  }

  /**
   * 执行小额真实交易测试
   */
  private async executeSmallRealTrade(): Promise<void> {
    console.log('\n5️⃣ 小额真实交易测试...');
    console.log('⚠️ 这将执行真实的小额交易进行测试');
    console.log('💡 如果不想执行真实交易，请按 Ctrl+C 退出');

    // 等待用户确认
    console.log('\n倒计时: 5...');
    await this.sleep(1000);
    console.log('倒计时: 4...');
    await this.sleep(1000);
    console.log('倒计时: 3...');
    await this.sleep(1000);
    console.log('倒计时: 2...');
    await this.sleep(1000);
    console.log('倒计时: 1...');
    await this.sleep(1000);

    console.log('\n💡 真实交易测试已跳过');
    console.log('🔧 要执行真实交易，请手动取消注释相关代码');

    /*
    // 取消注释以执行真实交易
    try {
      if (!this.refExecution) {
        throw new Error('REF执行服务未初始化');
      }

      // 获取小额报价
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDC,
        amountIn: '0.01', // 0.01 NEAR 小额测试
        slippage: 0.01 // 1% 滑点
      });

      const inputAmountWei = this.toWei('0.01', TOKENS.NEAR.decimals);
      const minOutputAmount = (parseFloat(quote.outputAmount) * 0.99).toString();
      const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.USDC.decimals);

      console.log(`🚀 执行小额测试交易: 0.01 NEAR → ${quote.outputAmount} USDC`);

      let result;
      if (quote.system === 'V1') {
        result = await this.refExecution.executeV1Swap(
          quote,
          TOKENS.NEAR.id,
          inputAmountWei,
          minOutputAmountWei,
          0.01
        );
      } else if (quote.system === 'DCL_V2') {
        const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
        result = await this.refExecution.executeDCLv2Swap(
          quote,
          TOKENS.NEAR.id,
          inputAmountWei,
          minOutputAmountWei,
          poolId,
          TOKENS.USDC.id
        );
      }

      if (result?.success) {
        console.log('✅ 小额测试交易成功!');
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log('🎉 REF Finance 执行问题已修复!');
      } else {
        console.log('❌ 小额测试交易失败:', result?.error);
      }

    } catch (error: any) {
      console.error('❌ 真实交易测试失败:', error.message);
    }
    */
  }

  /**
   * 精度转换工具
   */
  private toWei(amount: string, decimals: number): string {
    return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 运行REF Finance修复
 */
async function runRefExecutionFix() {
  const fixer = new RefExecutionFixer();
  await fixer.runFix();
}

// 运行修复
if (require.main === module) {
  runRefExecutionFix().catch(console.error);
}

export { RefExecutionFixer, runRefExecutionFix };
