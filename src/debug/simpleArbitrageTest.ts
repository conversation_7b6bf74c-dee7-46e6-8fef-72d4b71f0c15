/**
 * 批量套利查询测试
 *
 * 一次性测试所有配置的交易对
 */

import ArbitrageMonitorService from '../services/arbitrageMonitor';
import { tradingPairManager } from '../config/tradingPairs';

/**
 * 批量套利查询测试
 */
async function simpleArbitrageTest() {
  console.log('🚀 开始批量套利查询测试');

  const monitor = new ArbitrageMonitorService();

  try {
    // 1. 获取所有交易对配置
    const allPairs = tradingPairManager.getAllPairs();
    console.log(`\n1️⃣ 获取所有交易对配置 (共${allPairs.length}个)`);

    allPairs.forEach((pair, index) => {
      // 🔧 修复显示逻辑：正确显示动态金额和固定金额
      let amountDisplay: string;
      if (pair.dynamicAmount?.enabled) {
        amountDisplay = `动态(${pair.dynamicAmount.low}-${pair.dynamicAmount.high})`;
      } else if (pair.tradeAmount) {
        amountDisplay = pair.tradeAmount;
      } else {
        amountDisplay = '未配置';
      }
      console.log(`${index + 1}. ${pair.id}: ${amountDisplay} ${pair.tokenA.symbol} → ${pair.tokenB.symbol}`);
    });

    // 2. 并行执行所有套利检查
    console.log('\n2️⃣ 开始并行检查所有套利机会...');

    const checkMethod = (monitor as any).checkArbitrageOpportunity.bind(monitor);

    // 创建所有检查任务
    const checkTasks = allPairs.map(async (pair, index) => {
      try {
        console.log(`[${index + 1}/${allPairs.length}] 开始检查 ${pair.id}`);
        await checkMethod(pair);
        console.log(`✅ ${pair.id} 检查完成`);
      } catch (error) {
        console.log(`⚠️ ${pair.id} 检查失败，已忽略`);
      }
    });

    // 并行执行所有检查
    await Promise.all(checkTasks);

    // 3. 显示总体结果
    console.log('\n3️⃣ 批量查询完成');
    const stats = monitor.getStats();
    console.log(`   总检查次数: ${stats.totalChecks}`);
    console.log(`   发现机会数: ${stats.opportunitiesFound}`);
    console.log(`   平均检查时间: ${stats.averageCheckTime.toFixed(2)}ms`);
    console.log(`   错误次数: ${stats.errors}`);

    // 4. 显示所有套利机会
    const opportunities = monitor.getRecentOpportunities(10);
    if (opportunities.length > 0) {
      console.log('\n💰 发现的套利机会:');
      opportunities.forEach((opp, index) => {
        console.log(`${index + 1}. ${opp.pairId} (${opp.direction})`);
        console.log(`   利润: ${opp.profit.profitPercentage.toFixed(2)}%`);
        console.log(`   置信度: ${opp.confidence}`);
      });
    } else {
      console.log('\n📊 暂无套利机会发现');
    }

    console.log('\n✅ 批量测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  simpleArbitrageTest().catch(console.error);
}

export { simpleArbitrageTest };
