/**
 * VEAX报价服务测试
 * 
 * 测试VEAX DEX的报价获取功能
 */

import { VeaxQuoteService } from '../services/veaxQuoteService';

// 常用代币地址
const TOKENS = {
  NEAR: 'wrap.near',
  USDC: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
  USDT: 'usdt.tether-token.near'
};

/**
 * 测试单个报价
 */
async function testSingleQuote() {
  console.log('\n=== 测试单个VEAX报价 ===');
  
  try {
    // 测试 1000 NEAR -> USDC
    const result = await VeaxQuoteService.getQuote(
      TOKENS.NEAR,
      TOKENS.USDC,
      '1000'
    );

    console.log('报价结果:', {
      输入: '1000 NEAR',
      输出: `${result.outputAmount} USDC`,
      价格影响: `${result.priceImpact}`,
      手续费: `${result.fee}`,
      池子存在: result.poolExists,
      成功: result.success,
      错误: result.error
    });

    if (result.success) {
      const rate = parseFloat(result.outputAmount) / 1000;
      console.log(`汇率: 1 NEAR = ${rate.toFixed(6)} USDC`);
    }

  } catch (error) {
    console.error('测试失败:', error);
  }
}

/**
 * 测试批量报价
 */
async function testBatchQuotes() {
  console.log('\n=== 测试批量VEAX报价 ===');
  
  try {
    const quotes = [
      {
        tokenA: TOKENS.NEAR,
        tokenB: TOKENS.USDC,
        amountA: '100'
      },
      {
        tokenA: TOKENS.NEAR,
        tokenB: TOKENS.USDT,
        amountA: '100'
      },
      {
        tokenA: TOKENS.USDC,
        tokenB: TOKENS.NEAR,
        amountA: '100'
      }
    ];

    const results = await VeaxQuoteService.getBatchQuotes(quotes);

    console.log('\n批量报价结果:');
    results.forEach((result, index) => {
      const quote = quotes[index];
      console.log(`${index + 1}. ${quote.amountA} ${quote.tokenA.slice(-4)} -> ${result.outputAmount} ${quote.tokenB.slice(-4)}`);
      console.log(`   成功: ${result.success}, 池子存在: ${result.poolExists}`);
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });

  } catch (error) {
    console.error('批量测试失败:', error);
  }
}

/**
 * 测试池子存在性检查
 */
async function testPoolExists() {
  console.log('\n=== 测试池子存在性检查 ===');
  
  try {
    const pairs = [
      { tokenA: TOKENS.NEAR, tokenB: TOKENS.USDC, name: 'NEAR/USDC' },
      { tokenA: TOKENS.NEAR, tokenB: TOKENS.USDT, name: 'NEAR/USDT' },
      { tokenA: TOKENS.USDC, tokenB: TOKENS.USDT, name: 'USDC/USDT' }
    ];

    console.log('检查交易对池子:');
    for (const pair of pairs) {
      const exists = await VeaxQuoteService.checkPoolExists(pair.tokenA, pair.tokenB);
      console.log(`${pair.name}: ${exists ? '✅ 存在' : '❌ 不存在'}`);
    }

  } catch (error) {
    console.error('池子检查失败:', error);
  }
}

/**
 * 测试不同数量的报价
 */
async function testDifferentAmounts() {
  console.log('\n=== 测试不同数量的报价 ===');
  
  try {
    const amounts = ['1', '10', '100', '1000'];
    
    console.log('NEAR -> USDC 不同数量报价:');
    for (const amount of amounts) {
      const result = await VeaxQuoteService.getQuote(
        TOKENS.NEAR,
        TOKENS.USDC,
        amount
      );

      if (result.success) {
        const rate = parseFloat(result.outputAmount) / parseFloat(amount);
        console.log(`${amount} NEAR -> ${result.outputAmount} USDC (汇率: ${rate.toFixed(6)})`);
      } else {
        console.log(`${amount} NEAR -> 失败: ${result.error}`);
      }
    }

  } catch (error) {
    console.error('数量测试失败:', error);
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🚀 开始VEAX报价服务测试');
  
  await testSingleQuote();
  await testBatchQuotes();
  await testPoolExists();
  await testDifferentAmounts();
  
  console.log('\n✅ VEAX报价服务测试完成');
}

// 运行测试
if (require.main === module) {
  main().catch(console.error);
}

export { main as testVeaxQuote };
