/**
 * VEAX 交易测试 (使用正确的格式)
 * 
 * 测试修复后的VEAX交易执行功能
 */

import 'dotenv/config';
import VeaxExecutionService from '../services/veaxExecutionService';
import VeaxQuoteService from '../services/veaxQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';

async function testVeaxSwap() {
  console.log('🚀 开始VEAX交易测试 (修复版)');

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请在.env文件中设置这些变量');
    return;
  }

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化VEAX执行服务...');
    await veaxExecution.initialize();

    // 2. 检查余额
    console.log('\n2️⃣ 检查账户余额...');
    const balanceInfo = await veaxExecution.checkAccountBalance();
    const wNearBalanceInfo = await veaxExecution.checkWNearBalance();
    
    console.log(`💳 NEAR余额: ${balanceInfo.balanceNear.toFixed(6)} NEAR`);
    console.log(`🔄 wNEAR余额: ${wNearBalanceInfo.balanceWNear.toFixed(6)} wNEAR`);
    console.log(`🔢 wNEAR原始余额: ${wNearBalanceInfo.balance} wei`);

    if (balanceInfo.balanceNear < 1) {
      console.log('❌ NEAR余额不足，无法支付交易费用');
      return;
    }

    // 检查实际可用的wNEAR余额
    const availableWNearWei = BigInt(wNearBalanceInfo.balance);
    const requiredWNearWei = BigInt('100000000000000000000000'); // 0.1 wNEAR (修复：24位小数)

    console.log(`📊 需要wNEAR: ${requiredWNearWei.toString()} wei (0.1 wNEAR)`);
    console.log(`📊 可用wNEAR: ${availableWNearWei.toString()} wei (${wNearBalanceInfo.balanceWNear.toFixed(6)} wNEAR)`);

    if (availableWNearWei < requiredWNearWei) {
      console.log('❌ wNEAR余额不足，无法进行0.1 wNEAR的交易');

      // 计算实际可以交易的最大金额
      const maxTradeWei = availableWNearWei * BigInt(95) / BigInt(100); // 留5%作为缓冲
      const maxTradeWNear = Number(maxTradeWei) / Math.pow(10, 24);
      console.log(`💡 建议交易金额: ${maxTradeWNear.toFixed(6)} wNEAR (${maxTradeWei.toString()} wei)`);
      return;
    } else {
      console.log('✅ wNEAR余额充足，可以进行交易');
    }

    // 3. 获取报价 (改用USDT，因为示例中使用的是USDT)
    console.log('\n3️⃣ 获取VEAX报价...');
    const quoteResult = await VeaxQuoteService.getQuote(
      TOKENS.NEAR.id,
      TOKENS.USDT.id, // 改用USDT
      '0.1', // 0.1 wNEAR
      0.005
    );

    if (!quoteResult.success) {
      console.log(`❌ 获取报价失败: ${quoteResult.error}`);
      return;
    }

    console.log(`📊 报价: 0.1 wNEAR → ${quoteResult.outputAmount} USDT`);
    console.log(`💥 价格影响: ${quoteResult.priceImpact}`);

    // 4. 计算交易参数 (使用BigInt避免精度问题)
    console.log('\n4️⃣ 准备交易参数...');
    const inputAmountWei = '100000000000000000000000'; // 0.1 wNEAR in wei (修复：24位小数)
    const slippage = 0.01; // 1%

    // 使用BigInt进行精确计算
    const outputAmountBigInt = BigInt(quoteResult.outputAmount);
    const slippageProtection = BigInt(99); // 99% (1 - 1% slippage)
    const hundred = BigInt(100);

    // 计算最小输出金额：outputAmount * 99 / 100
    const minAmountOutWei = ((outputAmountBigInt * slippageProtection) / hundred).toString();

    // 用于显示的浮点数版本
    const minOutputAmountForDisplay = parseFloat(minAmountOutWei) / Math.pow(10, TOKENS.USDT.decimals);

    console.log(`📋 交易参数:`);
    console.log(`  输入: ${inputAmountWei} wei (0.1 wNEAR)`);
    console.log(`  最小输出: ${minAmountOutWei} wei (${minOutputAmountForDisplay.toFixed(6)} USDT)`);
    console.log(`  滑点: ${slippage * 100}%`);

    // 5. 显示将要使用的交易格式
    console.log('\n5️⃣ 交易格式预览...');
    const swapMsg = [
      "Deposit",
      {
        "SwapExactIn": {
          "token_in": TOKENS.NEAR.id,
          "token_out": TOKENS.USDT.id,
          "amount": inputAmountWei,
          "amount_limit": minAmountOutWei
        }
      },
      {
        "Withdraw": [TOKENS.NEAR.id, "0", null]
      },
      {
        "Withdraw": [TOKENS.USDT.id, "0", null]
      }
    ];

    console.log('📋 VEAX交易消息:');
    console.log(JSON.stringify(swapMsg, null, 2));

    // 5.5. 检查VEAX注册状态
    console.log('\n5️⃣.5 检查VEAX注册状态...');
    const userStatus = await veaxExecution.checkUserRegistration();
    console.log(`👤 VEAX用户状态: ${userStatus.isRegistered ? '已注册' : '未注册'}`);

    const tokenInStatus = await veaxExecution.checkTokenRegistration(TOKENS.NEAR.id);
    const tokenOutStatus = await veaxExecution.checkTokenRegistration(TOKENS.USDT.id);
    console.log(`🪙 ${TOKENS.NEAR.symbol} 注册状态: ${tokenInStatus.isRegistered ? '已注册' : '未注册'}`);
    console.log(`🪙 ${TOKENS.USDT.symbol} 注册状态: ${tokenOutStatus.isRegistered ? '已注册' : '未注册'}`);

    // 尝试在VEAX合约中注册代币
    console.log('\n🔧 尝试在VEAX合约中注册代币...');
    try {
      const veaxTokenInResult = await veaxExecution.registerTokenInVeax(TOKENS.NEAR.id);
      console.log(`🪙 VEAX中${TOKENS.NEAR.symbol}注册: ${veaxTokenInResult.success ? '成功' : '失败'}`);

      const veaxTokenOutResult = await veaxExecution.registerTokenInVeax(TOKENS.USDT.id);
      console.log(`🪙 VEAX中${TOKENS.USDT.symbol}注册: ${veaxTokenOutResult.success ? '成功' : '失败'}`);
    } catch (error) {
      console.log('ℹ️ VEAX代币注册失败，可能方法不存在或已注册');
    }

    // 6. 执行交易 (注释掉以避免意外执行)
    console.log('\n6️⃣ 准备执行交易...');
    console.log('⚠️ 这将执行真实的交易！');
    console.log('💡 取消注释下面的代码来执行真实交易');

    
    console.log('🚀 执行交易...');
    const executionResult = await veaxExecution.executeSwap(
      TOKENS.NEAR.id,
      TOKENS.USDT.id,
      inputAmountWei,
      minAmountOutWei
    );

    if (executionResult.success) {
      console.log(`✅ 交易执行成功!`);
      console.log(`🔗 交易哈希: ${executionResult.transactionHash}`);
      console.log(`📊 输入金额: ${executionResult.amountIn}`);
      console.log(`📊 输出金额: ${executionResult.amountOut}`);
    } else {
      console.log(`❌ 交易执行失败: ${executionResult.error}`);
    }
    

    console.log('\n✅ VEAX交易测试完成');
    console.log('💡 要执行真实交易，请取消注释执行代码');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  testVeaxSwap().catch(console.error);
}

export { testVeaxSwap };
