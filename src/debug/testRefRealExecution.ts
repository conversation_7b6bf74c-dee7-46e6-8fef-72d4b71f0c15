/**
 * REF Finance 真实交易执行测试
 * 
 * 测试REF Finance的真实交易执行功能
 * ⚠️ 这会执行真实交易，请谨慎使用！
 */

import 'dotenv/config';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 精度转换工具
 */
function toWei(amount: string, decimals: number): string {
  return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
}

function fromWei(amount: string, decimals: number): string {
  return new Big(amount).div(new Big(10).pow(decimals)).toString();
}

/**
 * 执行真实的REF Finance交易测试
 */
async function executeRealRefTrade() {
  console.log('🚀 REF Finance 真实交易执行测试');
  console.log('⚠️ 这将执行真实的区块链交易！');
  console.log('='.repeat(60));

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请在.env文件中设置这些变量');
    return;
  }

  // 检查是否启用真实交易
  if (process.env.ENABLE_REAL_TRADING !== 'true') {
    console.log('⚠️ 真实交易未启用');
    console.log('💡 要执行真实交易，请在.env文件中设置 ENABLE_REAL_TRADING=true');
    console.log('💡 当前将只进行模拟测试');
  }

  const refExecution = new RefExecutionServiceCorrect(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化REF执行服务...');
    await refExecution.initialize();

    // 2. 测试DCL v2交易（小额）
    console.log('\n2️⃣ 测试DCL v2交易（小额）...');
    await testDCLv2RealTrade(refExecution);

    // 3. 测试V1交易（如果能获得V1报价）
    console.log('\n3️⃣ 测试V1交易（如果可用）...');
    await testV1RealTrade(refExecution);

    console.log('\n✅ REF Finance真实交易测试完成');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 测试DCL v2真实交易
 */
async function testDCLv2RealTrade(refExecution: RefExecutionServiceCorrect) {
  try {
    // 获取DCL v2报价
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDC,
      amountIn: '0.1', // 0.1 NEAR，按用户要求
      slippage: 0.005
    };

    console.log(`📊 获取DCL v2报价: ${quoteParams.amountIn} ${TOKENS.NEAR.symbol} → ${TOKENS.USDC.symbol}`);
    const quote = await refQuoteService.getBestQuote(quoteParams);

    if (quote.system !== 'DCL_V2') {
      console.log(`⚠️ 当前报价使用${quote.system}系统，跳过DCL v2测试`);
      return;
    }

    console.log(`✅ 获得DCL v2报价: ${quote.outputAmount} ${TOKENS.USDC.symbol}`);

    // 计算交易参数
    const inputAmountWei = toWei(quoteParams.amountIn, TOKENS.NEAR.decimals);
    const slippage = 0.02; // 2%滑点，更宽松
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDC.decimals);
    // 🔧 修复：使用正确的池子ID顺序（USDC在前，NEAR在后）
    const poolId = `${TOKENS.USDC.id}|${TOKENS.NEAR.id}|100`;

    console.log(`📊 交易参数:`);
    console.log(`   输入: ${quoteParams.amountIn} NEAR (${inputAmountWei} wei)`);
    console.log(`   预期输出: ${quote.outputAmount} USDC`);
    console.log(`   最小输出: ${minOutputAmount} USDC (${minOutputAmountWei} wei)`);
    console.log(`   滑点: ${slippage * 100}%`);
    console.log(`   池子ID: ${poolId}`);

    // 执行真实交易
    if (process.env.ENABLE_REAL_TRADING === 'true') {
      console.log('\n🚀 执行DCL v2真实交易...');
      
      const result = await refExecution.executeDCLv2Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        poolId,
        TOKENS.USDC.id
      );

      if (result.success) {
        console.log(`✅ DCL v2交易成功!`);
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log(`📊 输入金额: ${result.inputAmount}`);
        console.log(`📊 输出金额: ${result.outputAmount}`);
        
        // 计算实际汇率
        const actualRate = parseFloat(result.outputAmount || '0') / parseFloat(quoteParams.amountIn);
        console.log(`💱 实际汇率: 1 NEAR = ${actualRate.toFixed(6)} USDC`);
      } else {
        console.log(`❌ DCL v2交易失败: ${result.error}`);
      }
    } else {
      console.log('💡 模拟模式：交易参数已验证，未执行真实交易');
    }

  } catch (error) {
    console.error('❌ DCL v2交易测试失败:', error);
  }
}

/**
 * 测试V1真实交易
 */
async function testV1RealTrade(refExecution: RefExecutionServiceCorrect) {
  try {
    // 尝试获取V1报价
    const quoteParams = {
      tokenIn: TOKENS.NEAR,
      tokenOut: TOKENS.USDT,
      amountIn: '0.1', // 0.1 NEAR，按用户要求
      slippage: 0.005
    };

    console.log(`📊 获取V1报价: ${quoteParams.amountIn} ${TOKENS.NEAR.symbol} → ${TOKENS.USDT.symbol}`);
    const quote = await refQuoteService.getBestQuote(quoteParams);

    if (quote.system !== 'V1') {
      console.log(`⚠️ 当前报价使用${quote.system}系统，跳过V1测试`);
      return;
    }

    console.log(`✅ 获得V1报价: ${quote.outputAmount} ${TOKENS.USDT.symbol}`);

    // 计算交易参数
    const inputAmountWei = toWei(quoteParams.amountIn, TOKENS.NEAR.decimals);
    const slippage = 0.02; // 2%滑点，更宽松
    const expectedOutput = parseFloat(quote.outputAmount);
    const minOutputAmount = (expectedOutput * (1 - slippage)).toString();
    const minOutputAmountWei = toWei(minOutputAmount, TOKENS.USDT.decimals);

    console.log(`📊 交易参数:`);
    console.log(`   输入: ${quoteParams.amountIn} NEAR (${inputAmountWei} wei)`);
    console.log(`   预期输出: ${quote.outputAmount} USDT`);
    console.log(`   最小输出: ${minOutputAmount} USDT (${minOutputAmountWei} wei)`);
    console.log(`   滑点: ${slippage * 100}%`);

    // 执行真实交易
    if (process.env.ENABLE_REAL_TRADING === 'true') {
      console.log('\n🚀 执行V1真实交易...');
      
      const result = await refExecution.executeV1Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        slippage
      );

      if (result.success) {
        console.log(`✅ V1交易成功!`);
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log(`📊 输入金额: ${result.inputAmount}`);
        console.log(`📊 输出金额: ${result.outputAmount}`);
        
        // 计算实际汇率
        const actualRate = parseFloat(result.outputAmount || '0') / parseFloat(quoteParams.amountIn);
        console.log(`💱 实际汇率: 1 NEAR = ${actualRate.toFixed(6)} USDT`);
      } else {
        console.log(`❌ V1交易失败: ${result.error}`);
      }
    } else {
      console.log('💡 模拟模式：交易参数已验证，未执行真实交易');
    }

  } catch (error) {
    console.error('❌ V1交易测试失败:', error);
  }
}

/**
 * 安全提醒
 */
function showSafetyReminder() {
  console.log('\n⚠️ 安全提醒:');
  console.log('1. 这是真实的区块链交易，会消耗真实的代币');
  console.log('2. 请确保账户有足够的NEAR支付gas费用');
  console.log('3. 建议先用小额测试');
  console.log('4. 交易不可逆，请谨慎操作');
  console.log('5. 设置合理的滑点容忍度');
}

// 运行测试
if (require.main === module) {
  showSafetyReminder();
  executeRealRefTrade().catch(console.error);
}

export { executeRealRefTrade, testDCLv2RealTrade, testV1RealTrade };
