/**
 * 快速池子检查工具
 */

import 'dotenv/config';

/**
 * 检查DCL v2池子是否存在
 */
async function quickPoolCheck() {
  console.log('🔍 快速DCL v2池子检查');
  console.log('='.repeat(50));

  // 测试的池子ID
  const testPools = [
    'wrap.near|17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|100',
    'wrap.near|17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|400',
    'wrap.near|17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|2000',
    '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1|wrap.near|100',
    'wrap.near|usdt.tether-token.near|100',
    'wrap.near|usdt.tether-token.near|400',
  ];

  for (const poolId of testPools) {
    console.log(`\n🔧 测试池子: ${poolId.slice(0, 50)}...`);
    
    try {
      const response = await fetch('https://rpc.mainnet.near.org', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          id: 'test',
          method: 'query',
          params: {
            request_type: 'call_function',
            finality: 'final',
            account_id: 'dclv2.ref-labs.near',
            method_name: 'get_pool',
            args_base64: Buffer.from(JSON.stringify({ pool_id: poolId })).toString('base64')
          }
        })
      });

      const result: any = await response.json();
      
      if (result.result?.result) {
        try {
          const poolData = JSON.parse(Buffer.from(result.result.result).toString());
          console.log(`   ✅ 池子存在 - 流动性: ${poolData.total_liquidity || 'N/A'}`);
        } catch {
          console.log(`   ✅ 池子存在 (解析数据失败)`);
        }
      } else if (result.error) {
        console.log(`   ❌ 池子不存在 - ${result.error.message || 'Unknown error'}`);
      } else {
        console.log(`   ❌ 池子不存在`);
      }
    } catch (error) {
      console.log(`   ❌ 查询失败: ${error}`);
    }
  }
}

/**
 * 分析V1滑点问题
 */
function analyzeV1SlippageIssue() {
  console.log('\n🔍 V1滑点问题分析');
  console.log('='.repeat(50));

  console.log('\n📊 交易数据分析:');
  console.log('输入: 100000000000000000000000 wei = 0.1 NEAR');
  console.log('预期输出: 0.225173 USDC');
  console.log('实际第一步输出: 225093 microUSDT = 0.225093 USDT');
  console.log('设置的最小输出: 200000 microUSDC = 0.2 USDC');

  console.log('\n🔧 问题分析:');
  console.log('1. 交易路径: NEAR → USDT → USDC');
  console.log('2. 第一步成功: 0.1 NEAR → 0.225093 USDT');
  console.log('3. 第二步失败: 滑点检查失败');
  console.log('4. 可能原因: USDT → USDC 的汇率变化');

  console.log('\n💡 解决方案:');
  console.log('1. 使用更宽松的滑点设置 (3-5%)');
  console.log('2. 根据实际报价动态计算最小输出');
  console.log('3. 考虑使用直接的 NEAR → USDC 路径');
  console.log('4. 在测试时使用更小的金额');

  console.log('\n🎯 建议的滑点设置:');
  console.log('测试环境: 5-10%');
  console.log('生产环境: 1-3%');
  console.log('高波动期: 3-5%');
}

/**
 * 建议的修复方案
 */
function suggestFixes() {
  console.log('\n🔧 建议的修复方案');
  console.log('='.repeat(50));

  console.log('\n1️⃣ DCL v2池子问题:');
  console.log('   - 大部分测试的池子可能不存在');
  console.log('   - 需要获取真实的池子列表');
  console.log('   - 建议优先使用V1系统');

  console.log('\n2️⃣ V1滑点问题:');
  console.log('   - 当前滑点设置过于严格');
  console.log('   - 需要动态计算最小输出金额');
  console.log('   - 建议使用报价金额的95%作为最小输出');

  console.log('\n3️⃣ 立即可行的解决方案:');
  console.log('   ✅ V1系统消息格式问题已完全解决');
  console.log('   ✅ 可以通过调整滑点参数立即使用');
  console.log('   ✅ DCL v2需要进一步研究池子列表');

  console.log('\n4️⃣ 推荐的测试步骤:');
  console.log('   1. 使用V1系统 + 宽松滑点进行测试');
  console.log('   2. 验证小额交易成功');
  console.log('   3. 逐步收紧滑点设置');
  console.log('   4. 研究DCL v2的真实池子列表');
}

// 运行检查
if (require.main === module) {
  Promise.all([
    quickPoolCheck(),
    analyzeV1SlippageIssue(),
    suggestFixes()
  ]).catch(console.error);
}

export { quickPoolCheck };
