/**
 * 真实交易测试
 * 
 * 执行真实的小额交易来验证修复效果
 */

import 'dotenv/config';
import RefExecutionServiceFixed from '../services/refExecutionServiceFixed';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * 真实交易测试器
 */
class RealTradeTest {
  private refExecution?: RefExecutionServiceFixed;

  /**
   * 运行真实交易测试
   */
  async runRealTradeTest(): Promise<void> {
    console.log('🧪 REF Finance 真实交易测试');
    console.log('='.repeat(50));

    // 1. 验证环境
    if (!this.validateEnvironment()) {
      return;
    }

    // 2. 初始化服务
    await this.initializeService();

    // 3. 测试小额V1交易
    await this.testV1RealTrade();

    // 4. 测试小额DCL v2交易
    await this.testDCLv2RealTrade();

    console.log('\n✅ 真实交易测试完成');
  }

  /**
   * 验证环境
   */
  private validateEnvironment(): boolean {
    console.log('\n1️⃣ 验证环境配置...');

    const configValidation = validateExecutionConfig();
    if (!configValidation.valid) {
      console.error('❌ 缺少必要的环境变量:');
      configValidation.missing.forEach(key => console.error(`   - ${key}`));
      return false;
    }

    console.log('✅ 环境配置验证通过');
    return true;
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    console.log('\n2️⃣ 初始化REF执行服务...');

    try {
      this.refExecution = new RefExecutionServiceFixed(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.refExecution.initialize();
      console.log('✅ REF执行服务初始化成功');
    } catch (error: any) {
      console.error('❌ 服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 测试V1真实交易
   */
  private async testV1RealTrade(): Promise<void> {
    console.log('\n3️⃣ 测试V1真实交易...');

    try {
      // 获取V1报价
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDC,
        amountIn: '0.01', // 0.01 NEAR 小额测试
        slippage: 0.01 // 1% 滑点
      });

      console.log(`📊 获取报价: ${quote.system} 系统`);
      console.log(`💰 预期输出: ${quote.outputAmount} USDC`);

      if (quote.system !== 'V1') {
        console.log('ℹ️ 当前最佳报价不是V1系统，跳过V1测试');
        return;
      }

      // 计算交易参数
      const inputAmount = '0.01';
      const inputAmountWei = this.toWei(inputAmount, TOKENS.NEAR.decimals);
      const minOutputAmount = new Big(quote.outputAmount).times(0.99).toString(); // 1%滑点
      const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.USDC.decimals);

      console.log(`🔢 输入金额: ${inputAmountWei} (${inputAmount} NEAR)`);
      console.log(`🔢 最小输出: ${minOutputAmountWei} (${minOutputAmount} USDC)`);

      // 详细分析报价结构
      this.analyzeQuoteStructure(quote);

      console.log('\n⚠️ 准备执行真实交易...');
      console.log('💡 这将花费真实的NEAR，确认继续？');
      console.log('倒计时: 5秒...');

      // 等待5秒
      for (let i = 5; i > 0; i--) {
        console.log(`倒计时: ${i}...`);
        await this.sleep(1000);
      }

      if (!this.refExecution) {
        throw new Error('REF执行服务未初始化');
      }

      // 执行真实交易
      console.log('\n🚀 执行V1交易...');
      const result = await this.refExecution.executeV1Swap(
        quote,
        TOKENS.NEAR.id,
        inputAmountWei,
        minOutputAmountWei,
        0.01
      );

      if (result.success) {
        console.log('✅ V1交易成功!');
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log(`💰 输入金额: ${result.inputAmount}`);
        console.log(`💰 输出金额: ${result.outputAmount}`);
      } else {
        console.log('❌ V1交易失败:', result.error);
        this.analyzeError(result.error);
      }

    } catch (error: any) {
      console.error('❌ V1交易测试失败:', error.message);
      this.analyzeError(error.message);
    }
  }

  /**
   * 测试DCL v2真实交易
   */
  private async testDCLv2RealTrade(): Promise<void> {
    console.log('\n4️⃣ 测试DCL v2真实交易...');

    try {
      // 获取DCL v2报价
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.USDT,
        tokenOut: TOKENS.NEAR,
        amountIn: '10', // 10 USDT 小额测试
        slippage: 0.01 // 1% 滑点
      });

      console.log(`📊 获取报价: ${quote.system} 系统`);
      console.log(`💰 预期输出: ${quote.outputAmount} NEAR`);

      if (quote.system !== 'DCL_V2') {
        console.log('ℹ️ 当前最佳报价不是DCL v2系统，跳过DCL v2测试');
        return;
      }

      // 计算交易参数
      const inputAmount = '10';
      const inputAmountWei = this.toWei(inputAmount, TOKENS.USDT.decimals);
      const minOutputAmount = new Big(quote.outputAmount).times(0.99).toString(); // 1%滑点
      const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.NEAR.decimals);
      const poolId = `${TOKENS.USDT.id}|${TOKENS.NEAR.id}|100`;

      console.log(`🔢 输入金额: ${inputAmountWei} (${inputAmount} USDT)`);
      console.log(`🔢 最小输出: ${minOutputAmountWei} (${minOutputAmount} NEAR)`);
      console.log(`🏊 池子ID: ${poolId}`);

      console.log('\n⚠️ 准备执行真实交易...');
      console.log('💡 这将花费真实的USDT，确认继续？');
      console.log('倒计时: 5秒...');

      // 等待5秒
      for (let i = 5; i > 0; i--) {
        console.log(`倒计时: ${i}...`);
        await this.sleep(1000);
      }

      if (!this.refExecution) {
        throw new Error('REF执行服务未初始化');
      }

      // 执行真实交易
      console.log('\n🚀 执行DCL v2交易...');
      const result = await this.refExecution.executeDCLv2Swap(
        quote,
        TOKENS.USDT.id,
        inputAmountWei,
        minOutputAmountWei,
        poolId,
        TOKENS.NEAR.id
      );

      if (result.success) {
        console.log('✅ DCL v2交易成功!');
        console.log(`🔗 交易哈希: ${result.transactionHash}`);
        console.log(`💰 输入金额: ${result.inputAmount}`);
        console.log(`💰 输出金额: ${result.outputAmount}`);
      } else {
        console.log('❌ DCL v2交易失败:', result.error);
        this.analyzeError(result.error);
      }

    } catch (error: any) {
      console.error('❌ DCL v2交易测试失败:', error.message);
      this.analyzeError(error.message);
    }
  }

  /**
   * 分析报价结构
   */
  private analyzeQuoteStructure(quote: any): void {
    console.log('\n🔍 分析报价结构:');
    console.log(`系统: ${quote.system}`);
    console.log(`输出金额: ${quote.outputAmount}`);

    if (quote.rawResponse?.result_data?.routes) {
      const routes = quote.rawResponse.result_data.routes;
      console.log(`路径数量: ${routes.length}`);

      routes.forEach((route: any, routeIndex: number) => {
        console.log(`路径 ${routeIndex + 1}:`);
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`  池子 ${poolIndex + 1}:`);
          console.log(`    pool_id: ${pool.pool_id} (${typeof pool.pool_id})`);
          console.log(`    token_in: ${pool.token_in}`);
          console.log(`    token_out: ${pool.token_out}`);
          if (pool.amount_in) {
            console.log(`    amount_in: ${pool.amount_in}`);
          }
        });
      });
    }
  }

  /**
   * 分析错误
   */
  private analyzeError(error?: string): void {
    if (!error) return;

    console.log('\n🔍 错误分析:');
    
    if (error.includes('ft_transfer_call')) {
      console.log('❌ ft_transfer_call调用错误');
      console.log('💡 可能原因: 调用了错误的合约');
    }
    
    if (error.includes('pool_id')) {
      console.log('❌ pool_id相关错误');
      console.log('💡 可能原因: pool_id类型或格式错误');
    }
    
    if (error.includes('slippage') || error.includes('滑点')) {
      console.log('❌ 滑点相关错误');
      console.log('💡 可能原因: 滑点设置过低或市场波动');
    }
    
    if (error.includes('balance') || error.includes('余额')) {
      console.log('❌ 余额不足错误');
      console.log('💡 可能原因: 账户余额不足');
    }

    if (error.includes('gas')) {
      console.log('❌ Gas相关错误');
      console.log('💡 可能原因: Gas不足或Gas价格设置错误');
    }
  }

  /**
   * 精度转换工具
   */
  private toWei(amount: string, decimals: number): string {
    return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 运行真实交易测试
 */
async function runRealTradeTest() {
  const tester = new RealTradeTest();
  await tester.runRealTradeTest();
}

// 运行测试
if (require.main === module) {
  runRealTradeTest().catch(console.error);
}

export { RealTradeTest, runRealTradeTest };
