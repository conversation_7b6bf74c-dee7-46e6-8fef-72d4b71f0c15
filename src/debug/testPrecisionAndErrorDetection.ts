/**
 * 测试精度修复和错误检测
 * 验证NEAR官方转换和FunctionCallError检测
 */

import 'dotenv/config';
import { parseNearAmount, formatNearAmount } from 'near-api-js/lib/utils/format';

/**
 * 精确的wei转换（避免浮点数精度问题）
 */
function toWei(amount: string, decimals: number): string {
  const [integer, decimal = ''] = amount.split('.');
  const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals);
  return integer + paddedDecimal;
}

/**
 * 测试精度转换
 */
function testPrecisionConversion() {
  console.log('🧪 测试精度转换修复');
  console.log('='.repeat(50));

  // 测试NEAR转换
  console.log('\n1️⃣ NEAR代币转换测试:');
  
  const nearAmounts = ['5', '5.0', '5.000000000000000000000000'];
  
  nearAmounts.forEach(amount => {
    console.log(`\n📊 测试金额: ${amount} NEAR`);
    
    // 旧方法（有精度问题）
    const oldMethod = BigInt(Math.floor(parseFloat(amount) * Math.pow(10, 24))).toString();
    
    // 新方法（NEAR官方）
    const newMethod = parseNearAmount(amount) || '0';
    
    console.log(`   旧方法: ${oldMethod}`);
    console.log(`   新方法: ${newMethod}`);
    console.log(`   差异: ${BigInt(newMethod) - BigInt(oldMethod)} yoctoNEAR`);
    console.log(`   相等: ${oldMethod === newMethod ? '✅' : '❌'}`);
  });

  // 测试其他代币转换
  console.log('\n2️⃣ 其他代币转换测试:');
  
  const tokenTests = [
    { amount: '1000.123456', decimals: 6, symbol: 'USDT' },
    { amount: '1937.368710116586490889', decimals: 18, symbol: 'PURGE' },
    { amount: '0.5', decimals: 6, symbol: 'USDC' }
  ];
  
  tokenTests.forEach(test => {
    console.log(`\n📊 测试: ${test.amount} ${test.symbol} (${test.decimals} decimals)`);
    
    // 旧方法（有精度问题）
    const oldMethod = BigInt(Math.floor(parseFloat(test.amount) * Math.pow(10, test.decimals))).toString();
    
    // 新方法（字符串精确计算）
    const newMethod = toWei(test.amount, test.decimals);
    
    console.log(`   旧方法: ${oldMethod}`);
    console.log(`   新方法: ${newMethod}`);
    console.log(`   差异: ${BigInt(newMethod) - BigInt(oldMethod)}`);
    console.log(`   相等: ${oldMethod === newMethod ? '✅' : '❌'}`);
  });
}

/**
 * 测试错误检测逻辑
 */
function testErrorDetection() {
  console.log('\n\n🧪 测试错误检测逻辑');
  console.log('='.repeat(50));

  // 模拟不同类型的交易结果
  const testCases = [
    {
      name: '成功交易',
      result: {
        receipts_outcome: [
          {
            outcome: {
              status: { SuccessValue: '' }
            }
          }
        ]
      },
      expected: { success: true }
    },
    {
      name: 'E22错误 - 存款不足',
      result: {
        receipts_outcome: [
          {
            outcome: {
              status: {
                Failure: {
                  ActionError: {
                    kind: {
                      FunctionCallError: {
                        ExecutionError: "Smart contract panicked: panicked at 'E22: not enough tokens in deposit', ref-exchange/src/account_deposit.rs:163:17"
                      }
                    }
                  }
                }
              }
            }
          }
        ]
      },
      expected: { success: false, error: 'REF合约存款不足 (E22)' }
    },
    {
      name: 'E76错误 - 滑点过大',
      result: {
        receipts_outcome: [
          {
            outcome: {
              status: {
                Failure: {
                  ActionError: {
                    kind: {
                      FunctionCallError: {
                        ExecutionError: "Smart contract panicked: E76: slippage error"
                      }
                    }
                  }
                }
              }
            }
          }
        ]
      },
      expected: { success: false, error: 'REF交易滑点过大 (E76)' }
    }
  ];

  // 模拟checkTransactionSuccess方法
  function checkTransactionSuccess(result: any): { success: boolean; error?: string } {
    try {
      const allReceipts = result.receipts_outcome || [];
      
      for (const receipt of allReceipts) {
        const status = receipt.outcome?.status;
        
        if (status && status.Failure) {
          const failure = status.Failure;
          
          if (failure.ActionError) {
            const actionError = failure.ActionError;
            
            if (actionError.kind && actionError.kind.FunctionCallError) {
              const functionCallError = actionError.kind.FunctionCallError;
              
              if (functionCallError.ExecutionError) {
                const errorMessage = functionCallError.ExecutionError;
                
                if (errorMessage.includes('E22: not enough tokens in deposit')) {
                  return { success: false, error: 'REF合约存款不足 (E22)' };
                } else if (errorMessage.includes('E76')) {
                  return { success: false, error: 'REF交易滑点过大 (E76)' };
                } else {
                  return { success: false, error: `REF合约错误: ${errorMessage}` };
                }
              }
            }
          }
        }
      }
      
      return { success: true };
    } catch (error) {
      return { success: false, error: '无法检查交易状态' };
    }
  }

  // 测试每个案例
  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}️⃣ 测试: ${testCase.name}`);
    
    const result = checkTransactionSuccess(testCase.result);
    
    console.log(`   期望: success=${testCase.expected.success}, error="${testCase.expected.error || 'none'}"`);
    console.log(`   实际: success=${result.success}, error="${result.error || 'none'}"`);
    
    const successMatch = result.success === testCase.expected.success;
    const errorMatch = result.error === testCase.expected.error;
    
    console.log(`   结果: ${successMatch && errorMatch ? '✅ 通过' : '❌ 失败'}`);
  });
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试精度修复和错误检测');
  console.log('='.repeat(60));
  
  try {
    // 测试精度转换
    testPrecisionConversion();
    
    // 测试错误检测
    testErrorDetection();
    
    console.log('\n\n🎉 所有测试完成!');
    console.log('='.repeat(60));
    console.log('✅ 修复内容:');
    console.log('   1. 使用parseNearAmount()进行NEAR精确转换');
    console.log('   2. 使用字符串计算进行其他代币精确转换');
    console.log('   3. 添加FunctionCallError检测逻辑');
    console.log('   4. 区分E22、E76等具体错误类型');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
runTests().catch(console.error);
