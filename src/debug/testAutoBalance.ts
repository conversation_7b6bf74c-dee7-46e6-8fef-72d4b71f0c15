/**
 * 测试自动余额管理功能
 */

import 'dotenv/config';
import { AutoBalanceManager } from '../services/autoBalanceManager';

/**
 * 测试自动余额管理
 */
async function testAutoBalanceManager() {
  console.log('🧪 测试自动余额管理功能');
  console.log('='.repeat(60));

  // 模拟配置
  const config = {
    enabled: true,
    checkInterval: 5 * 1000,  // 5秒检查一次（测试用）
    minNearBalance: 1.0,      // 最小1 NEAR
    unwrapAmount: 1.0,        // 解包1 wNEAR
    reserveAmount: 0.5        // 预留0.5 wNEAR
  };

  // 模拟交易状态
  let isExecutingTrade = false;

  try {
    // 创建自动余额管理器
    const autoBalanceManager = new AutoBalanceManager(
      process.env.NEAR_ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY!,
      config,
      () => isExecutingTrade,
      'mainnet'
    );

    // 初始化
    await autoBalanceManager.initialize();

    console.log('\n📊 初始状态检查:');
    await autoBalanceManager.manualCheck();

    console.log('\n🚀 启动自动余额管理 (5秒间隔)...');
    autoBalanceManager.start();

    // 运行30秒
    console.log('⏳ 运行30秒进行测试...');
    
    // 模拟交易状态变化
    setTimeout(() => {
      console.log('\n🔒 模拟开始交易...');
      isExecutingTrade = true;
    }, 10000);

    setTimeout(() => {
      console.log('\n🔓 模拟交易完成...');
      isExecutingTrade = false;
    }, 20000);

    // 30秒后停止
    setTimeout(() => {
      console.log('\n🛑 停止自动余额管理...');
      autoBalanceManager.stop();
      
      // 显示最终状态
      const status = autoBalanceManager.getStatus();
      console.log('\n📊 最终状态:', status);
      
      console.log('\n✅ 测试完成!');
      process.exit(0);
    }, 30000);

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

/**
 * 测试配置验证
 */
function testConfigValidation() {
  console.log('\n🔧 测试配置验证:');
  
  const testConfigs = [
    {
      name: '正常配置',
      config: {
        enabled: true,
        checkInterval: 30 * 60 * 1000,
        minNearBalance: 1.0,
        unwrapAmount: 1.0,
        reserveAmount: 0.5
      },
      expected: '✅ 有效'
    },
    {
      name: '解包数量过大',
      config: {
        enabled: true,
        checkInterval: 30 * 60 * 1000,
        minNearBalance: 1.0,
        unwrapAmount: 10.0,
        reserveAmount: 0.5
      },
      expected: '⚠️ 需要检查wNEAR余额'
    },
    {
      name: '检查间隔过短',
      config: {
        enabled: true,
        checkInterval: 1000,  // 1秒
        minNearBalance: 1.0,
        unwrapAmount: 1.0,
        reserveAmount: 0.5
      },
      expected: '⚠️ 检查间隔可能过短'
    }
  ];

  testConfigs.forEach(test => {
    console.log(`\n   ${test.name}:`);
    console.log(`     检查间隔: ${test.config.checkInterval / 1000 / 60} 分钟`);
    console.log(`     最小余额: ${test.config.minNearBalance} NEAR`);
    console.log(`     解包数量: ${test.config.unwrapAmount} wNEAR`);
    console.log(`     预留数量: ${test.config.reserveAmount} wNEAR`);
    console.log(`     评估: ${test.expected}`);
  });
}

/**
 * 测试余额计算逻辑
 */
function testBalanceCalculation() {
  console.log('\n🧮 测试余额计算逻辑:');

  const scenarios = [
    {
      name: '余额充足',
      nearBalance: 2.5,
      wNearBalance: 5.0,
      minNearBalance: 1.0,
      unwrapAmount: 1.0,
      reserveAmount: 0.5,
      expected: '无需操作'
    },
    {
      name: 'NEAR不足，wNEAR充足',
      nearBalance: 0.5,
      wNearBalance: 3.0,
      minNearBalance: 1.0,
      unwrapAmount: 1.0,
      reserveAmount: 0.5,
      expected: '执行解包'
    },
    {
      name: 'NEAR不足，wNEAR不足',
      nearBalance: 0.5,
      wNearBalance: 1.0,
      minNearBalance: 1.0,
      unwrapAmount: 1.0,
      reserveAmount: 0.5,
      expected: '无法解包'
    }
  ];

  scenarios.forEach(scenario => {
    console.log(`\n   ${scenario.name}:`);
    console.log(`     NEAR余额: ${scenario.nearBalance}`);
    console.log(`     wNEAR余额: ${scenario.wNearBalance}`);
    console.log(`     最小阈值: ${scenario.minNearBalance}`);
    
    const needsUnwrap = scenario.nearBalance < scenario.minNearBalance;
    const canUnwrap = scenario.wNearBalance >= (scenario.unwrapAmount + scenario.reserveAmount);
    
    let action = '无需操作';
    if (needsUnwrap) {
      action = canUnwrap ? '执行解包' : '无法解包';
    }
    
    console.log(`     需要解包: ${needsUnwrap ? '是' : '否'}`);
    console.log(`     可以解包: ${canUnwrap ? '是' : '否'}`);
    console.log(`     操作: ${action}`);
    console.log(`     预期: ${scenario.expected}`);
    console.log(`     匹配: ${action === scenario.expected ? '✅' : '❌'}`);
  });
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始测试自动余额管理功能');
  console.log('='.repeat(80));
  
  try {
    // 测试配置验证
    testConfigValidation();
    
    // 测试余额计算逻辑
    testBalanceCalculation();
    
    // 测试实际功能（需要有效的账户信息）
    if (process.env.NEAR_ACCOUNT_ID && process.env.NEAR_PRIVATE_KEY) {
      await testAutoBalanceManager();
    } else {
      console.log('\n⚠️ 跳过实际功能测试（缺少账户信息）');
      console.log('   设置 NEAR_ACCOUNT_ID 和 NEAR_PRIVATE_KEY 环境变量以运行完整测试');
      
      console.log('\n🎉 配置和逻辑测试完成!');
      console.log('='.repeat(80));
      console.log('✅ 配置验证通过');
      console.log('✅ 余额计算逻辑正确');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
runTests().catch(console.error);
