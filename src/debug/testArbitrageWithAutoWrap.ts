/**
 * 测试带自动包装功能的完整套利流程
 * 
 * 功能：
 * 1. 检查初始余额
 * 2. 如果wNEAR不足，自动包装NEAR
 * 3. 执行完整的VEAX→REF套利流程
 * 4. 验证最终结果
 */

import 'dotenv/config';
import ArbitrageBot from '../arbitrageBot';
import NearWrapService from '../services/nearWrapService';

async function testArbitrageWithAutoWrap() {
  console.log('🧪 测试带自动包装的完整套利流程');
  console.log('='.repeat(60));
  console.log('⚠️ 这将执行真实的区块链交易！');
  console.log('💰 测试金额: 0.1 NEAR');
  console.log('🔄 路径: NEAR → USDT (VEAX) → NEAR (REF)');
  console.log('🔧 包含自动wNEAR包装功能');
  console.log('='.repeat(60));

  try {
    // 1. 初始化服务
    console.log('\n1️⃣ 初始化服务...');
    
    const wrapService = new NearWrapService(
      process.env.NEAR_ACCOUNT_ID || process.env.ACCOUNT_ID!,
      process.env.NEAR_PRIVATE_KEY || process.env.PRIVATE_KEY!,
      'mainnet'
    );

    const arbitrageBot = new ArbitrageBot();

    // 初始化套利机器人的执行服务
    console.log('🔧 初始化套利机器人执行服务...');
    await (arbitrageBot as any).refExecutionService.initialize();
    await (arbitrageBot as any).veaxExecutionService.initialize();
    await (arbitrageBot as any).nearWrapService.initialize();
    console.log('✅ 套利机器人执行服务初始化完成');

    // 2. 检查初始余额
    console.log('\n2️⃣ 检查初始余额...');
    const initialBalance = await wrapService.getBalanceInfo();
    console.log(`💰 NEAR余额: ${initialBalance.nearBalance}`);
    console.log(`🔗 wNEAR余额: ${initialBalance.wNearBalance}`);
    console.log(`📊 总余额: ${initialBalance.totalBalance}`);

    // 3. 检查是否需要包装
    const testAmount = '0.1';
    console.log(`\n3️⃣ 检查是否需要包装 ${testAmount} NEAR...`);
    
    const currentWNear = parseFloat(initialBalance.wNearBalance);
    const required = parseFloat(testAmount);
    
    if (currentWNear < required) {
      console.log(`⚠️ wNEAR余额不足: 当前 ${currentWNear.toFixed(6)}, 需要 ${required.toFixed(6)}`);
      console.log(`🔄 将自动包装NEAR...`);
    } else {
      console.log(`✅ wNEAR余额充足: ${currentWNear.toFixed(6)} >= ${required.toFixed(6)}`);
    }

    // 4. 定义测试代币
    const tokenA = {
      id: 'wrap.near',
      symbol: 'NEAR',
      decimals: 24
    };

    const tokenB = {
      id: 'usdt.tether-token.near',
      symbol: 'USDT',
      decimals: 6
    };

    console.log(`\n📋 测试参数:`);
    console.log(`   输入代币: ${tokenA.symbol} (${tokenA.id})`);
    console.log(`   中间代币: ${tokenB.symbol} (${tokenB.id})`);
    console.log(`   测试金额: ${testAmount} ${tokenA.symbol}`);
    console.log(`   套利方向: VEAX → REF`);

    // 5. 创建强制套利机会（模拟发现套利机会）
    console.log('\n4️⃣ 创建测试套利机会...');
    
    const opportunity = {
      direction: 'VEAX_TO_REF' as const,
      pair: {
        id: 'NEAR-USDT',
        tokenA,
        tokenB,
        tradeAmount: testAmount
      },
      inputAmount: testAmount,
      intermediateAmount: '0.22', // 预估中间金额
      finalAmount: '0.101', // 预估最终金额
      profit: 0.001 // 预估利润
    };

    console.log(`✅ 套利机会已创建:`);
    console.log(`   方向: ${opportunity.direction}`);
    console.log(`   交易对: ${opportunity.pair.id}`);
    console.log(`   预期利润: ${opportunity.profit.toFixed(6)} NEAR`);

    // 6. 执行第一步：VEAX交易 (包含自动包装)
    console.log('\n5️⃣ 执行第一步：VEAX交易 (NEAR → USDT)...');
    console.log(`🚀 开始VEAX交易: ${testAmount} ${tokenA.symbol} → ${tokenB.symbol}`);
    console.log(`🔧 自动包装功能已启用`);

    const step1Result = await (arbitrageBot as any).executeVEAXTrade(
      tokenA,
      tokenB,
      testAmount
    );

    if (!step1Result.success) {
      console.log(`❌ 第一步VEAX交易失败: ${step1Result.error}`);
      throw new Error(`第一步VEAX交易失败: ${step1Result.error}`);
    }

    console.log(`✅ 第一步VEAX交易成功: ${step1Result.outputAmount} ${tokenB.symbol}`);
    console.log(`📊 交易哈希: ${step1Result.txHash}`);

    // 7. 交易已完成，立即进行下一步
    console.log('\n✅ 第一步交易完成，立即执行第二步...');

    // 8. 执行第二步：REF交易 (USDT → NEAR)
    console.log('\n6️⃣ 执行第二步：REF交易 (USDT → NEAR)...');
    console.log(`🚀 开始REF交易: ${step1Result.outputAmount} ${tokenB.symbol} → ${tokenA.symbol}`);

    const step2Result = await (arbitrageBot as any).executeREFTrade(
      tokenB,
      tokenA,
      step1Result.outputAmount
    );

    if (!step2Result.success) {
      console.log(`❌ 第二步REF交易失败: ${step2Result.error}`);
      console.log(`⚠️ 注意：第一步VEAX交易已成功，您现在持有 ${step1Result.outputAmount} ${tokenB.symbol}`);
      throw new Error(`第二步REF交易失败: ${step2Result.error}`);
    }

    console.log(`✅ 第二步REF交易成功: ${step2Result.outputAmount} ${tokenA.symbol}`);
    console.log(`📊 交易哈希: ${step2Result.txHash}`);

    // 9. 计算最终结果
    console.log('\n7️⃣ 计算套利结果...');
    const inputAmount = parseFloat(testAmount);
    const outputAmount = parseFloat(step2Result.outputAmount!);
    const actualProfit = outputAmount - inputAmount;
    const profitRate = (actualProfit / inputAmount) * 100;

    console.log(`📊 套利结果分析:`);
    console.log(`   输入金额: ${inputAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   输出金额: ${outputAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   实际利润: ${actualProfit.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   利润率: ${profitRate.toFixed(4)}%`);

    if (actualProfit > 0) {
      console.log(`🎉 套利成功! 获得利润 ${actualProfit.toFixed(6)} ${tokenA.symbol}`);
    } else {
      console.log(`📉 套利亏损: ${Math.abs(actualProfit).toFixed(6)} ${tokenA.symbol}`);
    }

    // 10. 检查最终余额
    console.log('\n8️⃣ 检查最终余额...');
    const finalBalance = await wrapService.getBalanceInfo();
    console.log(`💰 NEAR余额: ${finalBalance.nearBalance}`);
    console.log(`🔗 wNEAR余额: ${finalBalance.wNearBalance}`);
    console.log(`📊 总余额: ${finalBalance.totalBalance}`);

    // 11. 余额变化分析
    console.log('\n9️⃣ 余额变化分析...');
    const totalChange = parseFloat(finalBalance.totalBalance) - parseFloat(initialBalance.totalBalance);
    console.log(`📈 总余额变化: ${totalChange.toFixed(6)} NEAR`);
    
    if (Math.abs(totalChange - actualProfit) < 0.001) {
      console.log(`✅ 余额变化与计算利润一致`);
    } else {
      console.log(`⚠️ 余额变化与计算利润不一致，可能包含gas费用`);
    }

    console.log('\n🎉 带自动包装的完整套利流程测试完成!');
    console.log('✅ 自动包装功能正常工作');
    console.log('✅ 完整套利流程执行成功');

  } catch (error) {
    console.error('\n❌ 测试失败:', error);
    console.log('\n💡 可能的原因:');
    console.log('   1. 网络连接问题');
    console.log('   2. 账户余额不足');
    console.log('   3. 市场价格变化导致交易失败');
    console.log('   4. 合约调用错误');
    
    process.exit(1);
  }
}

// 运行测试
testArbitrageWithAutoWrap().catch(console.error);
