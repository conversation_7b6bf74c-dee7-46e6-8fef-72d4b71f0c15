/**
 * 测试skip_unwrap_near参数的影响
 * 
 * 比较包含和不包含skip_unwrap_near参数的交易结果
 */

import 'dotenv/config';
import RefExecutionServiceWithSkipUnwrap from '../services/refExecutionServiceWithSkipUnwrap';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';
import { refQuoteService } from '../services/refQuoteService';
import { TOKENS } from '../config/tradingPairs';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import Big from 'big.js';

/**
 * skip_unwrap_near参数测试器
 */
class SkipUnwrapNearTest {
  private refExecutionWithSkip?: RefExecutionServiceWithSkipUnwrap;
  private refExecutionCorrect?: RefExecutionServiceCorrect;

  /**
   * 运行完整测试
   */
  async runTest(): Promise<void> {
    console.log('🧪 skip_unwrap_near 参数影响测试');
    console.log('='.repeat(50));

    // 1. 验证环境
    if (!this.validateEnvironment()) {
      return;
    }

    // 2. 初始化服务
    await this.initializeServices();

    // 3. 获取测试报价
    const quote = await this.getTestQuote();
    if (!quote) return;

    // 4. 测试不同的skip_unwrap_near设置
    await this.testDifferentSkipUnwrapSettings(quote);

    console.log('\n✅ skip_unwrap_near 测试完成');
  }

  /**
   * 验证环境
   */
  private validateEnvironment(): boolean {
    console.log('\n1️⃣ 验证环境配置...');

    const configValidation = validateExecutionConfig();
    if (!configValidation.valid) {
      console.error('❌ 缺少必要的环境变量:');
      configValidation.missing.forEach(key => console.error(`   - ${key}`));
      return false;
    }

    console.log('✅ 环境配置验证通过');
    return true;
  }

  /**
   * 初始化服务
   */
  private async initializeServices(): Promise<void> {
    console.log('\n2️⃣ 初始化测试服务...');

    try {
      // 初始化带skip_unwrap_near的服务
      this.refExecutionWithSkip = new RefExecutionServiceWithSkipUnwrap(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.refExecutionWithSkip.initialize();
      console.log('✅ 带skip_unwrap_near的服务初始化成功');

      // 初始化正确版本服务
      this.refExecutionCorrect = new RefExecutionServiceCorrect(
        EXECUTION_CONFIG.ACCOUNT_ID,
        EXECUTION_CONFIG.PRIVATE_KEY,
        EXECUTION_CONFIG.NETWORK_ID
      );
      await this.refExecutionCorrect.initialize();
      console.log('✅ 正确版本服务初始化成功');

    } catch (error: any) {
      console.error('❌ 服务初始化失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取测试报价
   */
  private async getTestQuote(): Promise<any> {
    console.log('\n3️⃣ 获取测试报价...');

    try {
      const quote = await refQuoteService.getBestQuote({
        tokenIn: TOKENS.NEAR,
        tokenOut: TOKENS.USDC,
        amountIn: '0.01', // 0.01 NEAR 小额测试
        slippage: 0.01 // 1% 滑点
      });

      console.log(`📊 获取报价: ${quote.system} 系统`);
      console.log(`💰 预期输出: ${quote.outputAmount} USDC`);

      if (quote.system !== 'V1') {
        console.log('ℹ️ 当前最佳报价不是V1系统，跳过测试');
        return null;
      }

      return quote;
    } catch (error: any) {
      console.error('❌ 获取报价失败:', error.message);
      return null;
    }
  }

  /**
   * 测试不同的skip_unwrap_near设置
   */
  private async testDifferentSkipUnwrapSettings(quote: any): Promise<void> {
    console.log('\n4️⃣ 测试不同的skip_unwrap_near设置...');

    const inputAmount = '0.01';
    const inputAmountWei = this.toWei(inputAmount, TOKENS.NEAR.decimals);
    const minOutputAmount = new Big(quote.outputAmount).times(0.99).toString();
    const minOutputAmountWei = this.toWei(minOutputAmount, TOKENS.USDC.decimals);

    console.log(`🔢 输入金额: ${inputAmountWei} (${inputAmount} NEAR)`);
    console.log(`🔢 最小输出: ${minOutputAmountWei} (${minOutputAmount} USDC)`);

    const testCases = [
      {
        name: '不包含skip_unwrap_near',
        service: this.refExecutionCorrect,
        method: 'executeV1Swap',
        params: [quote, TOKENS.NEAR.id, inputAmountWei, minOutputAmountWei, 0.01]
      },
      {
        name: 'skip_unwrap_near: false',
        service: this.refExecutionWithSkip,
        method: 'executeV1Swap',
        params: [quote, TOKENS.NEAR.id, inputAmountWei, minOutputAmountWei, 0.01, false]
      },
      {
        name: 'skip_unwrap_near: true',
        service: this.refExecutionWithSkip,
        method: 'executeV1Swap',
        params: [quote, TOKENS.NEAR.id, inputAmountWei, minOutputAmountWei, 0.01, true]
      }
    ];

    const results: any[] = [];

    for (const testCase of testCases) {
      console.log(`\n📋 测试: ${testCase.name}`);
      console.log('⚠️ 准备执行真实交易...');
      console.log('倒计时: 3秒...');

      // 短暂倒计时
      for (let i = 3; i > 0; i--) {
        console.log(`倒计时: ${i}...`);
        await this.sleep(1000);
      }

      try {
        console.log(`🚀 执行交易: ${testCase.name}`);
        
        const result = await (testCase.service as any)[testCase.method](...testCase.params);
        
        if (result.success) {
          console.log(`✅ ${testCase.name} - 交易成功!`);
          console.log(`🔗 交易哈希: ${result.transactionHash}`);
          results.push({
            name: testCase.name,
            success: true,
            transactionHash: result.transactionHash,
            error: null
          });
        } else {
          console.log(`❌ ${testCase.name} - 交易失败: ${result.error}`);
          results.push({
            name: testCase.name,
            success: false,
            transactionHash: null,
            error: result.error
          });
        }
      } catch (error: any) {
        console.log(`❌ ${testCase.name} - 异常: ${error.message}`);
        results.push({
          name: testCase.name,
          success: false,
          transactionHash: null,
          error: error.message
        });
      }

      // 等待一下避免过快的请求
      console.log('⏳ 等待5秒后进行下一个测试...');
      await this.sleep(5000);
    }

    // 显示测试结果总结
    this.showTestSummary(results);
  }

  /**
   * 显示测试结果总结
   */
  private showTestSummary(results: any[]): void {
    console.log('\n📊 测试结果总结');
    console.log('='.repeat(50));

    results.forEach((result, index) => {
      const status = result.success ? '✅ 成功' : '❌ 失败';
      console.log(`${index + 1}. ${result.name}: ${status}`);
      
      if (result.success) {
        console.log(`   🔗 交易哈希: ${result.transactionHash}`);
      } else {
        console.log(`   ❌ 错误: ${result.error}`);
      }
    });

    console.log('\n🎯 结论:');
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    if (successCount === totalCount) {
      console.log('✅ 所有版本都成功！skip_unwrap_near参数不影响交易结果');
    } else if (successCount > 0) {
      console.log(`⚠️ ${successCount}/${totalCount} 个版本成功`);
      console.log('💡 某些skip_unwrap_near设置可能有问题');
    } else {
      console.log('❌ 所有版本都失败，可能有其他问题');
    }

    // 分析哪个版本最好
    const successfulVersions = results.filter(r => r.success);
    if (successfulVersions.length > 0) {
      console.log('\n🏆 推荐使用:');
      successfulVersions.forEach(version => {
        console.log(`   ✅ ${version.name}`);
      });
    }
  }

  /**
   * 精度转换工具
   */
  private toWei(amount: string, decimals: number): string {
    return new Big(amount).times(new Big(10).pow(decimals)).toFixed(0);
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 运行skip_unwrap_near测试
 */
async function runSkipUnwrapNearTest() {
  const tester = new SkipUnwrapNearTest();
  await tester.runTest();
}

// 运行测试
if (require.main === module) {
  runSkipUnwrapNearTest().catch(console.error);
}

export { SkipUnwrapNearTest, runSkipUnwrapNearTest };
