/**
 * 测试VEAX→REF完整流程：10 NEAR → BLACKDRAGON → NEAR
 */

import 'dotenv/config';
import { VeaxQuoteService } from '../services/veaxQuoteService';
import { v1SmartRouter } from '../services/v1SmartRouter';
import RefExecutionServiceCorrect from '../services/refExecutionServiceCorrect';

/**
 * 测试完整的VEAX→REF流程
 */
async function testVeaxToRefFlow() {
  console.log('🧪 测试VEAX→REF完整流程');
  console.log('='.repeat(80));
  console.log('流程: 10 NEAR → BLACKDRAGON (VEAX) → NEAR (REF)');
  console.log('='.repeat(80));

  // 代币配置
  const nearToken = {
    id: 'wrap.near',
    decimals: 24,
    symbol: 'NEAR',
    name: 'NEAR'
  };

  const blackdragonToken = {
    id: 'blackdragon.tkn.near',
    decimals: 24,
    symbol: 'BLACKDRAGON',
    name: 'BLAC<PERSON>DRAGON'
  };

  try {
    // 步骤1: 获取VEAX报价 (10 NEAR → BLACKDRAGON)
    console.log('\n📊 步骤1: 获取VEAX报价 (10 NEAR → BLACKDRAGON)');
    console.log('-'.repeat(60));
    
    const veaxResult = await VeaxQuoteService.getQuote(
      nearToken.id,
      blackdragonToken.id,
      '10'  // 10 NEAR (人类可读格式)
    );

    if (!veaxResult.success) {
      console.log('❌ VEAX报价失败:', veaxResult.error);
      return;
    }

    console.log(`✅ VEAX报价成功:`);
    console.log(`   输入: 10 NEAR`);
    console.log(`   输出: ${veaxResult.outputAmount} BLACKDRAGON`);
    console.log(`   格式: ${typeof veaxResult.outputAmount} (${veaxResult.outputAmount.includes('.') ? '人类可读' : 'wei格式'})`);
    console.log(`   长度: ${veaxResult.outputAmount.length} 字符`);

    // 步骤2: 使用VEAX输出作为REF输入 (BLACKDRAGON → NEAR)
    console.log('\n📊 步骤2: 使用VEAX输出作为REF输入 (BLACKDRAGON → NEAR)');
    console.log('-'.repeat(60));
    
    const veaxOutput = veaxResult.outputAmount;
    console.log(`🔄 传递给REF的输入: ${veaxOutput} BLACKDRAGON`);

    const refResult = await v1SmartRouter.getV1Quote({
      tokenIn: blackdragonToken,
      tokenOut: nearToken,
      amountIn: veaxOutput,  // 直接使用VEAX的输出
      slippage: 0.005
    });

    if (!refResult) {
      console.log('❌ REF报价失败');
      return;
    }

    console.log(`✅ REF报价成功:`);
    console.log(`   输入: ${veaxOutput} BLACKDRAGON`);
    console.log(`   输出: ${refResult.outputAmount} NEAR`);
    console.log(`   格式: ${typeof refResult.outputAmount} (${refResult.outputAmount.includes('.') ? '人类可读' : 'wei格式'})`);

    // 步骤3: 分析REF Smart Router返回的actions
    console.log('\n📊 步骤3: 分析REF Smart Router返回的actions');
    console.log('-'.repeat(60));

    if (refResult.rawResponse && refResult.rawResponse.result_data) {
      const routes = refResult.rawResponse.result_data.routes;
      console.log(`📋 路径数量: ${routes.length}`);

      routes.forEach((route: any, routeIndex: number) => {
        console.log(`\n🛤️  路径 ${routeIndex + 1}:`);
        console.log(`   路径分配金额: ${route.amount_in}`);
        console.log(`   预期输出: ${route.amount_out}`);
        console.log(`   最小输出: ${route.min_amount_out}`);
        
        route.pools.forEach((pool: any, poolIndex: number) => {
          console.log(`\n   池子 ${poolIndex + 1} (ID: ${pool.pool_id}):`);
          console.log(`     token_in: ${pool.token_in}`);
          console.log(`     token_out: ${pool.token_out}`);
          console.log(`     amount_in: ${pool.amount_in || '0'}`);
          console.log(`     min_amount_out: ${pool.min_amount_out || '0'}`);
        });
      });

      // 步骤4: 构建最终的actions
      console.log('\n📊 步骤4: 构建最终的actions');
      console.log('-'.repeat(60));

      const actions = routes.flatMap((route: any) => 
        route.pools.map((pool: any) => ({
          pool_id: parseInt(pool.pool_id),
          token_in: pool.token_in,
          token_out: pool.token_out,
          amount_in: pool.amount_in === "0" ? undefined : pool.amount_in,
          min_amount_out: pool.min_amount_out || "0"
        }))
      );

      console.log(`✅ 构建了 ${actions.length} 个actions:`);
      actions.forEach((action: any, index: number) => {
        console.log(`\n   Action ${index + 1}:`);
        console.log(`     pool_id: ${action.pool_id}`);
        console.log(`     token_in: ${action.token_in}`);
        console.log(`     token_out: ${action.token_out}`);
        console.log(`     amount_in: ${action.amount_in || '未设置'}`);
        console.log(`     min_amount_out: ${action.min_amount_out}`);
      });

      // 步骤5: 构建完整的交易消息
      console.log('\n📊 步骤5: 构建完整的交易消息');
      console.log('-'.repeat(60));

      const totalInputAmount = refResult.rawResponse.result_data.amount_in;
      const msg = {
        force: 0,
        actions: actions
      };

      console.log(`📋 ft_transfer_call参数:`);
      console.log(`   receiver_id: "v2.ref-finance.near"`);
      console.log(`   amount: "${totalInputAmount}" (总输入金额)`);
      console.log(`   msg: ${JSON.stringify(msg, null, 2)}`);

      // 步骤6: 精度验证
      console.log('\n📊 步骤6: 精度验证');
      console.log('-'.repeat(60));

      console.log(`🔍 精度验证:`);
      console.log(`   VEAX输出: ${veaxOutput}`);
      console.log(`   REF输入: ${veaxOutput}`);
      console.log(`   REF amount_in: ${totalInputAmount}`);
      console.log(`   Action amount_in: ${actions[0]?.amount_in || '未设置'}`);

      // 检查精度是否保持
      const inputMatches = totalInputAmount === actions[0]?.amount_in;
      console.log(`   总输入与Action匹配: ${inputMatches ? '✅' : '❌'}`);

      if (!inputMatches) {
        console.log(`   ⚠️ 精度不匹配！`);
        console.log(`   预期: ${totalInputAmount}`);
        console.log(`   实际: ${actions[0]?.amount_in}`);
      }

      // 步骤7: 套利利润计算
      console.log('\n📊 步骤7: 套利利润计算');
      console.log('-'.repeat(60));

      const inputAmount = 10; // 10 NEAR
      const outputAmount = parseFloat(refResult.outputAmount);
      const profit = outputAmount - inputAmount;

      console.log(`💰 套利分析:`);
      console.log(`   输入: ${inputAmount} NEAR`);
      console.log(`   输出: ${outputAmount} NEAR`);
      console.log(`   利润: ${profit.toFixed(6)} NEAR`);
      console.log(`   利润率: ${((profit / inputAmount) * 100).toFixed(4)}%`);

    } else {
      console.log('❌ 无法获取REF Smart Router的详细响应');
    }

  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始测试VEAX→REF完整流程');
  console.log('测试代币: NEAR → BLACKDRAGON → NEAR');
  console.log('测试金额: 10 NEAR');
  
  await testVeaxToRefFlow();
  
  console.log('\n🎉 测试完成!');
}

// 运行测试
main().catch(console.error);
