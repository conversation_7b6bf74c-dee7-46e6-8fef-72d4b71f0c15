import ArbitrageBot from '../arbitrageBot';
import { TOKENS } from '../config/tradingPairs';
import { VeaxQuoteService } from '../services/veaxQuoteService';
import { refQuoteService } from '../services/refQuoteService';

/**
 * 强制触发VEAX→REF套利交易测试
 * 测试金额：0.1 NEAR → USDT → NEAR
 * 不考虑盈利，纯粹测试交易执行
 */
async function testForceVeaxArbitrage() {
  console.log('🧪 强制触发VEAX→REF套利交易测试');
  console.log('============================================================');
  console.log('⚠️ 这将执行真实的区块链交易！');
  console.log('💰 测试金额: 0.1 NEAR');
  console.log('🔄 路径: NEAR → USDT (VEAX) → NEAR (REF)');
  console.log('============================================================\n');

  try {
    // 初始化套利机器人
    console.log('1️⃣ 初始化套利机器人...');
    const arbitrageBot = new ArbitrageBot();
    await arbitrageBot.start();
    console.log('✅ 套利机器人初始化完成\n');

    // 测试参数
    const testAmount = '0.1'; // 0.1 NEAR (人类可读格式)
    const tokenA = TOKENS.NEAR;
    const tokenB = TOKENS.USDT;

    console.log('📋 测试参数:');
    console.log(`   输入代币: ${tokenA.symbol} (${tokenA.id})`);
    console.log(`   中间代币: ${tokenB.symbol} (${tokenB.id})`);
    console.log(`   测试金额: ${testAmount} ${tokenA.symbol}`);
    console.log(`   套利方向: VEAX → REF\n`);

    // 2. 获取第一步VEAX报价 (NEAR → USDT)
    console.log('2️⃣ 获取第一步VEAX报价 (NEAR → USDT)...');
    const veaxQuote = await VeaxQuoteService.getQuote(
      tokenA.id,
      tokenB.id,
      testAmount
    );

    if (!veaxQuote.success) {
      throw new Error(`VEAX报价失败: ${veaxQuote.error}`);
    }

    console.log(`✅ VEAX报价成功:`);
    console.log(`   ${testAmount} ${tokenA.symbol} → ${veaxQuote.outputAmount} ${tokenB.symbol}`);
    console.log(`   价格影响: ${veaxQuote.priceImpact}`);
    console.log(`   手续费: ${veaxQuote.fee}\n`);

    // 3. 获取第二步REF报价 (USDT → NEAR)
    console.log('3️⃣ 获取第二步REF报价 (USDT → NEAR)...');
    const refQuote = await refQuoteService.getQuote({
      tokenIn: tokenB,
      tokenOut: tokenA,
      amountIn: veaxQuote.outputAmount
    });

    if (!refQuote) {
      throw new Error('REF报价失败');
    }

    console.log(`✅ REF报价成功:`);
    console.log(`   ${veaxQuote.outputAmount} ${tokenB.symbol} → ${refQuote.outputAmount} ${tokenA.symbol}`);
    console.log(`   系统: ${refQuote.system}`);
    if (refQuote.poolId) {
      console.log(`   池子ID: ${refQuote.poolId}`);
    }
    console.log('');

    // 4. 计算预期结果
    const inputAmount = parseFloat(testAmount);
    const finalAmount = parseFloat(refQuote.outputAmount);
    const difference = finalAmount - inputAmount;
    const percentageChange = ((finalAmount - inputAmount) / inputAmount) * 100;

    console.log('📊 预期套利结果:');
    console.log(`   输入: ${inputAmount} ${tokenA.symbol}`);
    console.log(`   输出: ${finalAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   差额: ${difference > 0 ? '+' : ''}${difference.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   变化: ${percentageChange > 0 ? '+' : ''}${percentageChange.toFixed(4)}%`);
    console.log('');

    // 5. 确认执行
    console.log('⚠️ 准备执行真实交易...');
    console.log('💡 这将消耗真实的代币和Gas费用');
    console.log('🔄 执行步骤:');
    console.log(`   1. VEAX交易: ${testAmount} ${tokenA.symbol} → ${veaxQuote.outputAmount} ${tokenB.symbol}`);
    console.log(`   2. REF交易: ${veaxQuote.outputAmount} ${tokenB.symbol} → ${refQuote.outputAmount} ${tokenA.symbol}`);
    console.log('');

    // 6. 创建强制套利机会
    console.log('4️⃣ 创建强制套利机会...');
    const forceOpportunity = {
      direction: 'VEAX_TO_REF' as const,
      pair: {
        id: 'NEAR-USDT',
        tokenA: tokenA,
        tokenB: tokenB,
        enabled: true,
        tradeAmount: testAmount,
        checkInterval: 1000
      },
      inputAmount: testAmount,
      intermediateAmount: veaxQuote.outputAmount,
      finalAmount: refQuote.outputAmount,
      profit: difference
    };

    console.log(`✅ 套利机会已创建:`);
    console.log(`   方向: ${forceOpportunity.direction}`);
    console.log(`   交易对: ${forceOpportunity.pair.id}`);
    console.log(`   预期利润: ${forceOpportunity.profit.toFixed(6)} ${tokenA.symbol}\n`);

    // 7. 执行第一步：VEAX交易 (NEAR → USDT)
    console.log('5️⃣ 执行第一步：VEAX交易 (NEAR → USDT)...');
    console.log(`🚀 开始VEAX交易: ${testAmount} ${tokenA.symbol} → ${tokenB.symbol}`);
    
    const step1Result = await (arbitrageBot as any).executeVEAXTrade(
      tokenA,
      tokenB,
      testAmount
    );

    if (!step1Result.success) {
      throw new Error(`第一步VEAX交易失败: ${step1Result.error}`);
    }

    console.log(`✅ 第一步VEAX交易成功!`);
    console.log(`🔗 交易哈希: ${step1Result.txHash}`);
    console.log(`📊 实际输出: ${step1Result.outputAmount} ${tokenB.symbol}`);
    console.log(`💰 预期输出: ${veaxQuote.outputAmount} ${tokenB.symbol}`);
    console.log('');

    // 8. 等待一下确保交易确认
    console.log('⏳ 等待交易确认...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    // 9. 执行第二步：REF交易 (USDT → NEAR) - 带重试机制
    console.log('6️⃣ 执行第二步：REF交易 (USDT → NEAR)...');
    console.log(`🚀 开始REF交易: ${step1Result.outputAmount} ${tokenB.symbol} → ${tokenA.symbol}`);

    let step2Result;
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        console.log(`🔄 尝试第 ${retryCount + 1} 次 REF交易...`);
        step2Result = await (arbitrageBot as any).executeREFTrade(
          tokenB,
          tokenA,
          step1Result.outputAmount
        );

        if (step2Result.success) {
          break; // 成功则跳出重试循环
        } else {
          throw new Error(step2Result.error);
        }
      } catch (error: any) {
        retryCount++;
        console.log(`❌ 第 ${retryCount} 次尝试失败: ${error.message}`);

        if (retryCount < maxRetries) {
          console.log(`⏳ 等待 ${retryCount * 5} 秒后重试...`);
          await new Promise(resolve => setTimeout(resolve, retryCount * 5000));
        }
      }
    }

    if (!step2Result || !step2Result.success) {
      console.log(`❌ 所有重试都失败了，第二步REF交易最终失败`);
      console.log(`⚠️ 注意：第一步VEAX交易已成功，您现在持有 ${step1Result.outputAmount} ${tokenB.symbol}`);
      console.log(`💡 建议：手动在REF Finance上将 ${tokenB.symbol} 兑换回 ${tokenA.symbol}`);
      throw new Error(`第二步REF交易失败，已重试 ${maxRetries} 次`);
    }

    console.log(`✅ 第二步REF交易成功!`);
    console.log(`🔗 交易哈希: ${step2Result.txHash}`);
    console.log(`📊 最终输出: ${step2Result.outputAmount} ${tokenA.symbol}`);
    console.log('');

    // 10. 计算实际结果
    const actualFinalAmount = parseFloat(step2Result.outputAmount || '0');
    const actualDifference = actualFinalAmount - inputAmount;
    const actualPercentageChange = ((actualFinalAmount - inputAmount) / inputAmount) * 100;

    console.log('🎉 套利交易完成!');
    console.log('============================================================');
    console.log('📊 最终结果:');
    console.log(`   输入金额: ${inputAmount} ${tokenA.symbol}`);
    console.log(`   最终金额: ${actualFinalAmount.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   实际差额: ${actualDifference > 0 ? '+' : ''}${actualDifference.toFixed(6)} ${tokenA.symbol}`);
    console.log(`   实际变化: ${actualPercentageChange > 0 ? '+' : ''}${actualPercentageChange.toFixed(4)}%`);
    console.log('');
    console.log('🔗 交易哈希:');
    console.log(`   VEAX交易: ${step1Result.txHash}`);
    console.log(`   REF交易: ${step2Result.txHash}`);
    console.log('');
    
    if (actualDifference > 0) {
      console.log(`💰 恭喜！获得利润: ${actualDifference.toFixed(6)} ${tokenA.symbol}`);
    } else {
      console.log(`📉 损失: ${Math.abs(actualDifference).toFixed(6)} ${tokenA.symbol}`);
    }

    console.log('\n✅ VEAX→REF套利交易测试完成！');

  } catch (error: any) {
    console.error('\n❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testForceVeaxArbitrage().catch(console.error);
}

export { testForceVeaxArbitrage };
