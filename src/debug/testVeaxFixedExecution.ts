import { VeaxQuoteService } from '../services/veaxQuoteService';
import { TOKENS } from '../config/tradingPairs';

/**
 * 测试修复后的VEAX交易执行
 * 验证amount_limit参数格式是否正确
 */
async function testVeaxFixedExecution() {
  console.log('🧪 测试修复后的VEAX交易执行');
  console.log('============================================================');

  try {
    console.log('✅ 准备测试VEAX报价服务');

    // 测试参数
    const tokenIn = TOKENS.NEAR;
    const tokenOut = TOKENS.USDT;
    const amount = '1000000000000000000'; // 0.000001 NEAR in wei (非常小的金额)

    console.log('\n📋 测试参数:');
    console.log(`   输入代币: ${tokenIn.symbol} (${tokenIn.id})`);
    console.log(`   输出代币: ${tokenOut.symbol} (${tokenOut.id})`);
    console.log(`   交易金额: ${amount} wei (0.000001 NEAR)`);

    // 1. 获取VEAX报价
    console.log('\n1️⃣ 获取VEAX报价...');
    const veaxQuote = await VeaxQuoteService.getQuote(
      tokenIn.id,
      tokenOut.id,
      amount
    );

    if (!veaxQuote.success) {
      throw new Error(`VEAX报价失败: ${veaxQuote.error}`);
    }

    console.log(`✅ VEAX报价成功:`);
    console.log(`   输出金额: ${veaxQuote.outputAmount} ${tokenOut.symbol} (小数格式)`);
    console.log(`   价格影响: ${veaxQuote.priceImpact}`);
    console.log(`   手续费: ${veaxQuote.fee}`);

    // 2. 测试金额转换逻辑
    console.log('\n2️⃣ 测试金额转换逻辑...');
    
    // 模拟套利机器人中的转换逻辑
    const outputAmountFloat = parseFloat(veaxQuote.outputAmount);
    const outputTokenDecimals = tokenOut.decimals;
    const outputAmountWei = Math.floor(outputAmountFloat * Math.pow(10, outputTokenDecimals)).toString();
    
    console.log(`📊 转换过程:`);
    console.log(`   原始输出 (小数): ${veaxQuote.outputAmount}`);
    console.log(`   浮点数值: ${outputAmountFloat}`);
    console.log(`   代币精度: ${outputTokenDecimals}`);
    console.log(`   转换为wei: ${outputAmountWei}`);

    // 计算最小输出金额
    const outputAmountBigInt = BigInt(outputAmountWei);
    const slippageProtection = BigInt(99); // 99%
    const hundred = BigInt(100);
    const minOutputAmountBigInt = (outputAmountBigInt * slippageProtection) / hundred;
    const minOutputAmount = minOutputAmountBigInt.toString();

    console.log(`📉 滑点保护计算:`);
    console.log(`   输出金额 (BigInt): ${outputAmountBigInt}`);
    console.log(`   滑点保护 (99%): ${slippageProtection}`);
    console.log(`   最小输出 (BigInt): ${minOutputAmountBigInt}`);
    console.log(`   最小输出 (字符串): ${minOutputAmount}`);

    // 3. 验证消息格式
    console.log('\n3️⃣ 验证VEAX交易消息格式...');
    
    const swapMsg = [
      "Deposit",
      {
        "SwapExactIn": {
          "token_in": tokenIn.id,
          "token_out": tokenOut.id,
          "amount": amount,
          "amount_limit": minOutputAmount // 这应该是整数字符串
        }
      },
      {
        "Withdraw": [tokenIn.id, "0", null]
      },
      {
        "Withdraw": [tokenOut.id, "0", null]
      }
    ];

    console.log(`✅ 交易消息格式:`);
    console.log(JSON.stringify(swapMsg, null, 2));

    // 4. 验证amount_limit是否为整数字符串
    const swapExactIn = swapMsg[1] as { SwapExactIn: { token_in: string; token_out: string; amount: string; amount_limit: string; } };
    const amountLimit = swapExactIn.SwapExactIn.amount_limit;
    const isInteger = /^\d+$/.test(amountLimit);
    
    console.log('\n4️⃣ 验证amount_limit格式...');
    console.log(`   amount_limit值: ${amountLimit}`);
    console.log(`   是否为整数字符串: ${isInteger ? '✅ 是' : '❌ 否'}`);
    console.log(`   字符串长度: ${amountLimit.length}`);
    console.log(`   包含小数点: ${amountLimit.includes('.') ? '❌ 是' : '✅ 否'}`);

    if (!isInteger) {
      throw new Error('amount_limit不是整数字符串格式！');
    }

    // 5. 格式验证总结
    console.log('\n5️⃣ 格式验证总结...');
    console.log('✅ 所有格式检查通过！');

    console.log('\n✅ 测试完成 - VEAX交易格式修复验证成功！');
    console.log('🔧 修复要点:');
    console.log('   1. VEAX API返回小数格式的outputAmount');
    console.log('   2. 需要转换为wei格式的整数字符串');
    console.log('   3. amount_limit必须是整数字符串，不能包含小数点');
    console.log('   4. 使用BigInt进行精确计算避免精度问题');

  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
    console.error('详细错误:', error);
  }
}

// 运行测试
if (require.main === module) {
  testVeaxFixedExecution().catch(console.error);
}

export { testVeaxFixedExecution };
