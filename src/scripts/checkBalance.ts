/**
 * 账户余额检查工具
 * 
 * 检查NEAR账户余额和建议
 */

import 'dotenv/config';
import { EXECUTION_CONFIG, validateExecutionConfig } from '../config/executionConfig';
import VeaxExecutionService from '../services/veaxExecutionService';

async function checkAccountBalance() {
  console.log('💰 NEAR账户余额检查工具');
  console.log('');

  // 验证环境变量配置
  const configValidation = validateExecutionConfig();
  if (!configValidation.valid) {
    console.error('❌ 缺少必要的环境变量:');
    configValidation.missing.forEach(key => console.error(`   - ${key}`));
    console.error('💡 请运行 npm run setup:env 设置环境变量');
    return;
  }

  const veaxExecution = new VeaxExecutionService(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );

  try {
    console.log(`🔍 检查账户: ${EXECUTION_CONFIG.ACCOUNT_ID}`);
    console.log(`🌐 网络: ${EXECUTION_CONFIG.NETWORK_ID}`);
    console.log('');

    // 初始化服务
    await veaxExecution.initialize();

    // 获取余额信息
    const balanceInfo = await veaxExecution.checkAccountBalance();
    const wNearBalanceInfo = await veaxExecution.checkWNearBalance();

    console.log('📊 账户状态:');
    console.log(`💳 NEAR余额: ${balanceInfo.balanceNear.toFixed(6)} NEAR (用于支付费用)`);
    console.log(`🔄 wNEAR余额: ${wNearBalanceInfo.balanceWNear.toFixed(6)} wNEAR (用于交易)`);
    console.log(`🔢 原始NEAR: ${balanceInfo.balance} yoctoNEAR`);
    console.log(`🔢 原始wNEAR: ${wNearBalanceInfo.balance} yocto`);
    console.log('');

    // 余额建议
    console.log('💡 余额建议:');

    // 检查NEAR余额（用于费用）
    if (balanceInfo.balanceNear >= 2) {
      console.log('✅ NEAR余额充足，可以支付交易费用');
    } else if (balanceInfo.balanceNear >= 1) {
      console.log('⚠️ NEAR余额较低，建议谨慎测试');
    } else {
      console.log('❌ NEAR余额不足，无法支付交易费用');
    }

    // 检查wNEAR余额（用于交易）
    if (wNearBalanceInfo.balanceWNear >= 1) {
      console.log('✅ wNEAR余额充足，可以进行交易');
    } else if (wNearBalanceInfo.balanceWNear > 0) {
      console.log('⚠️ wNEAR余额较低，只能进行小额交易');
    } else {
      console.log('❌ 没有wNEAR余额，需要先将NEAR包装为wNEAR');
    }

    console.log('');
    console.log('📋 操作成本估算:');
    console.log('💰 NEAR费用 (支付gas和存储):');
    console.log('  - VEAX用户注册: ~0.00125 NEAR');
    console.log('  - 代币注册: ~0.00125 NEAR/代币');
    console.log('  - 交易执行gas: ~0.01-0.1 NEAR');
    console.log('  - 建议最低NEAR: 2-3 NEAR');
    console.log('');
    console.log('🔄 wNEAR余额 (用于交易):');
    console.log('  - 交易金额: 根据策略而定');
    console.log('  - 套利测试: 建议1-10 wNEAR');
    console.log('  - 如何获得: 将NEAR包装为wNEAR');
    console.log('');

    // 检查用户注册状态
    console.log('🔍 检查VEAX注册状态...');
    const userStatus = await veaxExecution.checkUserRegistration();
    console.log(`👤 VEAX用户状态: ${userStatus.isRegistered ? '已注册' : '未注册'}`);

    if (userStatus.isRegistered && userStatus.storageBalance) {
      console.log(`📦 存储余额: ${userStatus.storageBalance.available} / ${userStatus.storageBalance.total}`);
    }

    console.log('');
    console.log('🎯 下一步建议:');

    if (balanceInfo.balanceNear < 2) {
      console.log('1. 向账户转入更多NEAR (用于支付费用)');
      console.log('2. 建议转入至少2-3 NEAR');
    } else if (wNearBalanceInfo.balanceWNear < 1) {
      console.log('1. NEAR余额充足，但需要wNEAR用于交易');
      console.log('2. 将部分NEAR包装为wNEAR:');
      console.log('   - 访问 https://app.ref.finance');
      console.log('   - 或使用NEAR钱包的wrap功能');
      console.log('   - 建议包装1-5 NEAR为wNEAR');
    } else {
      console.log('1. ✅ 余额充足，可以开始测试');
      console.log('2. 运行: npm run test:veax-execution');
      console.log('3. 运行: npm run test:ref-execution');
      console.log('4. 运行: npm run test:arbitrage');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  }
}

// 运行检查
if (require.main === module) {
  checkAccountBalance().catch(console.error);
}

export { checkAccountBalance };
