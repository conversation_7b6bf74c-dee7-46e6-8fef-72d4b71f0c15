/**
 * 环境变量设置脚本
 * 
 * 帮助用户快速设置.env文件
 */

import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt: string): Promise<string> {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupEnvironment() {
  console.log('🚀 欢迎使用REF-VEAX套利机器人环境设置');
  console.log('');

  try {
    // 检查是否已存在.env文件
    const envPath = path.join(process.cwd(), '.env');
    const envExists = fs.existsSync(envPath);

    if (envExists) {
      console.log('⚠️ 发现已存在的.env文件');
      const overwrite = await question('是否要覆盖现有配置？(y/N): ');
      if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
        console.log('✅ 保持现有配置不变');
        rl.close();
        return;
      }
    }

    console.log('');
    console.log('📝 请提供以下信息:');
    console.log('');

    // 获取NEAR账户信息
    const accountId = await question('NEAR账户ID (例: your-account.near): ');
    if (!accountId) {
      console.log('❌ 账户ID不能为空');
      rl.close();
      return;
    }

    const privateKey = await question('NEAR私钥 (例: ed25519:xxx): ');
    if (!privateKey) {
      console.log('❌ 私钥不能为空');
      rl.close();
      return;
    }

    const networkId = await question('网络ID (mainnet/testnet) [mainnet]: ') || 'mainnet';

    // 安全配置
    console.log('');
    console.log('🔒 安全配置:');
    const enableTrading = await question('启用真实交易？(y/N) [N]: ');
    const maxTradeAmount = await question('最大交易金额(NEAR) [10]: ') || '10';
    const requireConfirmation = await question('需要交易确认？(Y/n) [Y]: ');

    // 生成.env内容
    const envContent = `# REF Finance & VEAX 套利机器人配置

# NEAR 网络配置
NEAR_NETWORK=mainnet
NEAR_RPC_URL=https://rpc.mainnet.near.org

# REF Finance 配置
REF_SMART_ROUTER_URL=https://smartrouter.ref.finance
REF_V1_CONTRACT=v2.ref-finance.near
REF_DCLV2_CONTRACT=dclv2.ref-labs.near

# 请求配置
REQUEST_TIMEOUT=5000
DEFAULT_SLIPPAGE=0.005

# 日志配置
LOG_LEVEL=info
DEBUG_MODE=false

# NEAR账户配置 (交易执行需要)
NEAR_ACCOUNT_ID=${accountId}
NEAR_PRIVATE_KEY=${privateKey}
NEAR_NETWORK_ID=${networkId}

# 交易安全配置
ENABLE_REAL_TRADING=${enableTrading.toLowerCase() === 'y' || enableTrading.toLowerCase() === 'yes' ? 'true' : 'false'}
MAX_TRADE_AMOUNT_NEAR=${maxTradeAmount}
REQUIRE_CONFIRMATION=${requireConfirmation.toLowerCase() === 'n' || requireConfirmation.toLowerCase() === 'no' ? 'false' : 'true'}
`;

    // 写入.env文件
    fs.writeFileSync(envPath, envContent);

    console.log('');
    console.log('✅ 环境配置已保存到.env文件');
    console.log('');
    console.log('🎯 下一步:');
    console.log('1. 运行套利测试: npm run test:arbitrage');
    console.log('2. 测试REF执行: npm run test:ref-execution');
    console.log('3. 测试VEAX执行: npm run test:veax-execution');
    console.log('');
    console.log('⚠️ 重要提醒:');
    console.log('- 请妥善保管你的私钥');
    console.log('- 建议先在小额资金上测试');
    console.log('- .env文件已添加到.gitignore，不会被提交到代码库');

  } catch (error) {
    console.error('❌ 设置过程中出现错误:', error);
  } finally {
    rl.close();
  }
}

// 运行设置
if (require.main === module) {
  setupEnvironment().catch(console.error);
}

export { setupEnvironment };
