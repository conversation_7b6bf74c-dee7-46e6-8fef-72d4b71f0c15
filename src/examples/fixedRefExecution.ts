
/**
 * 修复后的REF Finance使用示例
 */

import RefExecutionServiceFixed from './services/refExecutionServiceFixed';
import { refQuoteService } from './services/refQuoteService';
import { TOKENS } from './config/tradingPairs';
import { EXECUTION_CONFIG } from './config/executionConfig';
import Big from 'big.js';

async function executeRefTradeFixed() {
  // 1. 使用修复版本的服务
  const refExecution = new RefExecutionServiceFixed(
    EXECUTION_CONFIG.ACCOUNT_ID,
    EXECUTION_CONFIG.PRIVATE_KEY,
    EXECUTION_CONFIG.NETWORK_ID
  );
  
  await refExecution.initialize();

  // 2. 获取报价
  const quote = await refQuoteService.getBestQuote({
    tokenIn: TOKENS.NEAR,
    tokenOut: TOKENS.USDC,
    amountIn: '0.1',
    slippage: 0.005
  });

  // 3. 计算交易参数
  const inputAmountWei = new Big('0.1')
    .times(new Big(10).pow(TOKENS.NEAR.decimals))
    .toFixed(0);
  
  const minOutputAmount = new Big(quote.outputAmount)
    .times(0.99) // 1% 滑点
    .toString();
  
  const minOutputAmountWei = new Big(minOutputAmount)
    .times(new Big(10).pow(TOKENS.USDC.decimals))
    .toFixed(0);

  // 4. 执行交易（修复版本）
  let result;
  if (quote.system === 'V1') {
    result = await refExecution.executeV1Swap(
      quote,
      TOKENS.NEAR.id, // 关键：输入代币ID
      inputAmountWei,
      minOutputAmountWei,
      0.01
    );
  } else if (quote.system === 'DCL_V2') {
    const poolId = `${TOKENS.NEAR.id}|${TOKENS.USDC.id}|100`;
    result = await refExecution.executeDCLv2Swap(
      quote,
      TOKENS.NEAR.id, // 关键：输入代币ID
      inputAmountWei,
      minOutputAmountWei,
      poolId,
      TOKENS.USDC.id
    );
  }

  if (result?.success) {
    console.log('✅ 交易成功:', result.transactionHash);
  } else {
    console.log('❌ 交易失败:', result?.error);
  }
}
