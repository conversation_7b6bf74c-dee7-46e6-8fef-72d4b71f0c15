/**
 * 三档位动态金额系统测试示例
 * 
 * 功能：验证动态金额管理器的核心逻辑
 */

import { dynamicAmountManager } from '../services/dynamicAmountManager';
import { TradingPairConfig, TOKENS } from '../config/tradingPairs';

// 测试用的交易对配置
const testPair: TradingPairConfig = {
  id: 'TEST-NEAR-USDC',
  tokenA: TOKENS.NEAR,
  tokenB: TOKENS.USDC,
  enabled: true,
  dynamicAmount: {
    enabled: true,
    low: '3',      // 低档 3 NEAR
    medium: '8',   // 中档 8 NEAR
    high: '15'     // 高档 15 NEAR
  },
  checkInterval: 1000,
  description: '测试用NEAR-USDC交易对'
};

/**
 * 测试动态金额调整逻辑
 */
async function testDynamicAmountAdjustment() {
  console.log('🧪 开始测试三档位动态金额系统\n');

  // 测试场景1: 初始查询
  console.log('📋 测试场景1: 初始查询');
  const initialAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
  console.log(`   初始金额: ${initialAmount} NEAR (应该是低档: 3 NEAR)`);
  console.log(`   当前档位: ${dynamicAmountManager.getCurrentLevel(testPair.id)}\n`);

  // 测试场景2: 低利润交易 (保持低档)
  console.log('📋 测试场景2: 低利润交易 (0.025 NEAR)');
  dynamicAmountManager.adjustNextAmount(testPair.id, 0.025, testPair);
  const lowProfitAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
  console.log(`   调整后金额: ${lowProfitAmount} NEAR (应该保持低档: 3 NEAR)`);
  console.log(`   当前档位: ${dynamicAmountManager.getCurrentLevel(testPair.id)}\n`);

  // 测试场景3: 中等利润交易 (升到中档)
  console.log('📋 测试场景3: 中等利润交易 (0.075 NEAR)');
  dynamicAmountManager.adjustNextAmount(testPair.id, 0.075, testPair);
  const mediumProfitAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
  console.log(`   调整后金额: ${mediumProfitAmount} NEAR (应该升到中档: 8 NEAR)`);
  console.log(`   当前档位: ${dynamicAmountManager.getCurrentLevel(testPair.id)}\n`);

  // 测试场景4: 高利润交易 (升到高档)
  console.log('📋 测试场景4: 高利润交易 (0.15 NEAR)');
  dynamicAmountManager.adjustNextAmount(testPair.id, 0.15, testPair);
  const highProfitAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
  console.log(`   调整后金额: ${highProfitAmount} NEAR (应该升到高档: 15 NEAR)`);
  console.log(`   当前档位: ${dynamicAmountManager.getCurrentLevel(testPair.id)}\n`);

  // 测试场景5: 利润下降 (降到中档)
  console.log('📋 测试场景5: 利润下降 (0.06 NEAR)');
  dynamicAmountManager.adjustNextAmount(testPair.id, 0.06, testPair);
  const decreasedProfitAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
  console.log(`   调整后金额: ${decreasedProfitAmount} NEAR (应该降到中档: 8 NEAR)`);
  console.log(`   当前档位: ${dynamicAmountManager.getCurrentLevel(testPair.id)}\n`);

  // 测试场景6: 连续无机会 (重置到低档)
  console.log('📋 测试场景6: 连续无机会处理');
  for (let i = 1; i <= 12; i++) {
    dynamicAmountManager.handleNoOpportunity(testPair.id, testPair);
    if (i === 10) {
      console.log(`   第${i}次无机会: 应该触发重置`);
    }
  }
  const resetAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
  console.log(`   重置后金额: ${resetAmount} NEAR (应该重置到低档: 3 NEAR)`);
  console.log(`   当前档位: ${dynamicAmountManager.getCurrentLevel(testPair.id)}\n`);

  // 显示统计信息
  console.log('📊 最终统计信息:');
  dynamicAmountManager.displayStatsSummary();
}

/**
 * 测试模拟交易场景
 */
async function testSimulatedTradingScenario() {
  console.log('\n🎮 模拟真实交易场景\n');

  const scenarios = [
    { profit: 0.02, description: '小利润交易' },
    { profit: 0.08, description: '中等利润交易' },
    { profit: 0.12, description: '高利润交易' },
    { profit: 0.045, description: '利润回落' },
    { profit: 0.15, description: '再次高利润' },
    { profit: 0.03, description: '利润大幅下降' }
  ];

  for (let i = 0; i < scenarios.length; i++) {
    const scenario = scenarios[i];
    const currentAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
    const currentLevel = dynamicAmountManager.getCurrentLevel(testPair.id);
    
    console.log(`🔄 交易 ${i + 1}: ${scenario.description}`);
    console.log(`   查询金额: ${currentAmount} NEAR (${currentLevel}档)`);
    console.log(`   实际利润: ${scenario.profit} NEAR`);
    
    // 调整下次金额
    dynamicAmountManager.adjustNextAmount(testPair.id, scenario.profit, testPair);
    
    const nextAmount = dynamicAmountManager.getCurrentAmount(testPair.id, testPair);
    const nextLevel = dynamicAmountManager.getCurrentLevel(testPair.id);
    console.log(`   下次金额: ${nextAmount} NEAR (${nextLevel}档)\n`);
    
    // 模拟交易间隔
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  // 最终统计
  console.log('📈 模拟交易完成，最终统计:');
  dynamicAmountManager.displayStatsSummary();
}

/**
 * 主测试函数
 */
async function main() {
  try {
    await testDynamicAmountAdjustment();
    await testSimulatedTradingScenario();
    
    console.log('\n✅ 所有测试完成！');
    console.log('\n💡 测试结论:');
    console.log('   - 动态金额调整逻辑正常工作');
    console.log('   - 档位切换符合预期');
    console.log('   - 统计信息正确记录');
    console.log('   - 无机会重置机制有效');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
if (require.main === module) {
  main();
}

export { testDynamicAmountAdjustment, testSimulatedTradingScenario };
