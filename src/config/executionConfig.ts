/**
 * 交易执行配置
 * 
 * 从环境变量读取敏感信息
 */

export const EXECUTION_CONFIG = {
  // NEAR账户配置 (从环境变量读取)
  ACCOUNT_ID: process.env.NEAR_ACCOUNT_ID || '',
  PRIVATE_KEY: process.env.NEAR_PRIVATE_KEY || '',
  NETWORK_ID: process.env.NEAR_NETWORK_ID || 'mainnet',

  // 交易配置
  DEFAULT_SLIPPAGE: 0.01, // 1%
  MAX_GAS: '***************', // 300 TGas
  
  // REF Finance配置
  REF_FINANCE: {
    V1_CONTRACT: 'v2.ref-finance.near',
    DCL_V2_CONTRACT: 'dclv2.ref-labs.near',
    DEFAULT_DEPOSIT: '1', // 1 yoctoNEAR for V1
    DCL_V2_DEPOSIT: '200000000000000000000000000' // 0.2 NEAR for DCL v2
  },

  // VEAX配置
  VEAX: {
    CONTRACT: 'veax.near',
    STORAGE_DEPOSIT: '5000000000000000000000000', // 0.005 NEAR
    TOKEN_REGISTER_DEPOSIT: '2840000000000000000000' // ~0.00284 NEAR
  },

  // 安全配置
  SAFETY: {
    ENABLE_REAL_TRADING: process.env.ENABLE_REAL_TRADING === 'true', // 默认false
    MAX_TRADE_AMOUNT_NEAR: process.env.MAX_TRADE_AMOUNT_NEAR || '10', // 最大交易金额限制
    REQUIRE_CONFIRMATION: process.env.REQUIRE_CONFIRMATION !== 'false' // 默认true
  }
};

// 验证必要的环境变量
export function validateExecutionConfig(): { valid: boolean; missing: string[] } {
  const missing: string[] = [];
  
  if (!EXECUTION_CONFIG.ACCOUNT_ID) missing.push('NEAR_ACCOUNT_ID');
  if (!EXECUTION_CONFIG.PRIVATE_KEY) missing.push('NEAR_PRIVATE_KEY');
  
  return {
    valid: missing.length === 0,
    missing
  };
}

// 导出类型
export type ExecutionConfig = typeof EXECUTION_CONFIG;
