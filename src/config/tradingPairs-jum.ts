/**
 * 交易对配置文件
 * 
 * 用于配置套利机器人监控的交易对
 * 支持动态添加、删除和修改交易对
 */

// 代币元数据接口
export interface TokenInfo {
  id: string;           // 代币合约地址
  symbol: string;       // 代币符号
  name: string;         // 代币名称
  decimals: number;     // 精度
  icon?: string;        // 图标URL
}

// 交易对配置接口
export interface TradingPairConfig {
  id: string;           // 交易对唯一标识
  tokenA: TokenInfo;    // 基础代币
  tokenB: TokenInfo;    // 报价代币
  enabled: boolean;     // 是否启用监控
  tradeAmount: string;  // 固定交易金额
  checkInterval: number; // 检查间隔（毫秒）
  description?: string; // 描述
}

// 自动余额管理配置接口
export interface AutoBalanceConfig {
  enabled: boolean;              // 是否启用自动余额管理
  checkInterval: number;         // 检查间隔（毫秒）
  minNearBalance: number;        // 最小NEAR余额阈值
  unwrapAmount: number;          // 自动解包数量
  reserveAmount: number;         // 预留wNEAR数量（保留用于交易）
}

// 套利配置接口
export interface ArbitrageConfig {
  minProfitThreshold: number;    // 最小利润阈值（NEAR绝对数量）
  maxSlippage: number;           // 最大滑点容忍度
  maxPriceImpact: number;        // 最大价格影响
  gasBuffer: number;             // Gas费用缓冲（百分比）
  enabled: boolean;              // 是否启用套利执行
  monitoringInterval: number;    // 监控间隔（毫秒）
  statusDisplayInterval: number; // 状态显示间隔（检查次数）
  debugMode: boolean;            // 调试模式（显示所有监控日志）
  verboseLogging: boolean;       // 详细日志（显示所有报价信息）
  autoBalanceManagement: AutoBalanceConfig; // 自动余额管理配置
}

/**
 * 常用代币定义
 */
export const TOKENS: Record<string, TokenInfo> = {
  NEAR: {
    id: 'wrap.near',
    symbol: 'NEAR',
    name: 'NEAR Protocol',
    decimals: 24,
    icon: 'https://assets.coingecko.com/coins/images/10365/small/near.jpg'
  },
  
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png'
  },

  USDC_e: {
    id: 'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near',
    symbol: 'USDC.e',
    name: 'USDC.e',
    decimals: 6,
  },
  
  USDT: {
    id: 'usdt.tether-token.near',
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png'
  },
  
  USDT_e: {
    id: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
    symbol: 'USDT.e',
    name: 'USDT.e',
    decimals: 6,
  },

  AURORA: {
    id: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
    symbol: 'AURORA',
    name: 'Aurora',
    decimals: 18,
  },
  
  WBTC: {
    id: '2260fac5e5542a773aa44fbcfedf7c193bc2c599.factory.bridge.near',
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    decimals: 8,
    icon: 'https://assets.coingecko.com/coins/images/7598/small/wrapped_bitcoin_wbtc.png'
  },

  BLACKDRAGON:{
    id: 'blackdragon.tkn.near',
    symbol: 'BLACKDRAGON',
    name: 'BLACKDRAGON',
    decimals: 24,
  },

  SHITZU:{
    id: 'token.0xshitzu.near',
    symbol: 'SHITZU',
    name: 'SHITZU',
    decimals: 18,
  },

  PURGE:{
    id: 'purge-558.meme-cooking.near',
    symbol: 'PURGE',
    name: 'PURGE',
    decimals: 18,
  },
  
  GEAR:{
    id: 'gear.enleap.near',
    symbol: 'GEAR',
    name: 'GEAR',
    decimals: 18,
  },

  DOGSHIT:{
    id: 'dogshit-1408.meme-cooking.near',
    symbol: 'DOGSHIT',
    name: 'DOGSHIT',
    decimals: 18,
  },

  LONK:{
    id: 'token.lonkingnearbackto2024.near',
    symbol: 'LONK',
    name: 'LONK',
    decimals: 8,
  },

  NEKO:{
    id: 'ftv2.nekotoken.near',
    symbol: 'NEKO',
    name: 'NEKO',
    decimals: 24,
  },

  nBTC:{
    id: 'nbtc.bridge.near',
    symbol: 'nBTC',
    name: 'nBTC',
    decimals: 8,
  },

  mpDAO:{
    id: 'mpdao-token.near',
    symbol: 'mpDAO',
    name: 'mpDAO',
    decimals: 6,
  },

};

/**
 * 默认交易对配置
 */
export const DEFAULT_TRADING_PAIRS: TradingPairConfig[] = [

  {
    id: 'NEAR-PURGE',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.PURGE,
    enabled: true,
    tradeAmount: '5',   // 固定100 NEAR
    checkInterval: 500,
  },

  {
    id: 'NEAR-PURGE',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.PURGE,
    enabled: true,
    tradeAmount: '3',   // 固定100 NEAR
    checkInterval: 500,
  },

    {
    id: 'NEAR-PURGE',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.PURGE,
    enabled: true,
    tradeAmount: '7',   // 固定100 NEAR
    checkInterval: 500,
  },
  
];

/**
 * 默认套利配置
 *
 * 🔧 调试模式使用说明：
 * - debugMode: true  = 显示所有监控日志（包括错误和失败的报价）
 * - verboseLogging: true = 显示所有交易对的报价信息
 * - 两者都为 false = 只显示重要信息（套利机会和状态）
 */
export const DEFAULT_ARBITRAGE_CONFIG: ArbitrageConfig = {
  minProfitThreshold: 0.004,  // 最小0.015 NEAR利润
  maxSlippage: 0.01,          // 最大1%滑点
  maxPriceImpact: 0.01,       // 最大5%价格影响
  gasBuffer: 0.1,             // 10% Gas费用缓冲
  enabled: true,              // 启用自动执行
  monitoringInterval: 500,   // 1秒监控间隔
  statusDisplayInterval: 30,  // 每10次检查显示一次状态
  debugMode: false,           // 🔧 调试模式：true=显示所有监控日志，false=只显示重要信息
  verboseLogging: false,      // 🔧 详细日志：true=显示所有报价，false=只显示有意义的报价

  // 🔧 自动余额管理配置
  autoBalanceManagement: {
    enabled: true,            // 是否启用自动余额管理
    checkInterval: 30 * 60 * 1000,  // 检查间隔：30分钟
    minNearBalance: 1.0,      // 最小NEAR余额阈值（低于此值时自动解包）
    unwrapAmount: 1.0,        // 自动解包数量
    reserveAmount: 5.5        // 预留wNEAR数量（保留用于交易，不会被解包）
  }
};

/**
 * 交易对管理类
 */
export class TradingPairManager {
  private pairs: Map<string, TradingPairConfig> = new Map();
  private arbitrageConfig: ArbitrageConfig;

  constructor(
    initialPairs: TradingPairConfig[] = DEFAULT_TRADING_PAIRS,
    arbitrageConfig: ArbitrageConfig = DEFAULT_ARBITRAGE_CONFIG
  ) {
    // 初始化交易对
    initialPairs.forEach(pair => {
      this.pairs.set(pair.id, pair);
    });
    
    this.arbitrageConfig = { ...arbitrageConfig };
  }

  /**
   * 获取所有交易对
   */
  getAllPairs(): TradingPairConfig[] {
    return Array.from(this.pairs.values());
  }

  /**
   * 获取启用的交易对
   */
  getEnabledPairs(): TradingPairConfig[] {
    return this.getAllPairs().filter(pair => pair.enabled);
  }

  /**
   * 获取特定交易对
   */
  getPair(id: string): TradingPairConfig | undefined {
    return this.pairs.get(id);
  }

  /**
   * 添加交易对
   */
  addPair(pair: TradingPairConfig): void {
    this.pairs.set(pair.id, pair);
    console.log(`✅ 添加交易对: ${pair.id}`);
  }

  /**
   * 更新交易对
   */
  updatePair(id: string, updates: Partial<TradingPairConfig>): boolean {
    const existing = this.pairs.get(id);
    if (!existing) {
      console.error(`❌ 交易对不存在: ${id}`);
      return false;
    }

    const updated = { ...existing, ...updates };
    this.pairs.set(id, updated);
    console.log(`✅ 更新交易对: ${id}`);
    return true;
  }

  /**
   * 删除交易对
   */
  removePair(id: string): boolean {
    const deleted = this.pairs.delete(id);
    if (deleted) {
      console.log(`✅ 删除交易对: ${id}`);
    } else {
      console.error(`❌ 交易对不存在: ${id}`);
    }
    return deleted;
  }

  /**
   * 启用/禁用交易对
   */
  togglePair(id: string, enabled?: boolean): boolean {
    const pair = this.pairs.get(id);
    if (!pair) {
      console.error(`❌ 交易对不存在: ${id}`);
      return false;
    }

    pair.enabled = enabled !== undefined ? enabled : !pair.enabled;
    console.log(`✅ ${pair.enabled ? '启用' : '禁用'}交易对: ${id}`);
    return true;
  }

  /**
   * 获取套利配置
   */
  getArbitrageConfig(): ArbitrageConfig {
    return { ...this.arbitrageConfig };
  }

  /**
   * 更新套利配置
   */
  updateArbitrageConfig(updates: Partial<ArbitrageConfig>): void {
    this.arbitrageConfig = { ...this.arbitrageConfig, ...updates };
    console.log('✅ 更新套利配置');
  }

  /**
   * 导出配置为JSON
   */
  exportConfig(): string {
    return JSON.stringify({
      pairs: this.getAllPairs(),
      arbitrageConfig: this.arbitrageConfig
    }, null, 2);
  }

  /**
   * 从JSON导入配置
   */
  importConfig(jsonConfig: string): boolean {
    try {
      const config = JSON.parse(jsonConfig);
      
      if (config.pairs && Array.isArray(config.pairs)) {
        this.pairs.clear();
        config.pairs.forEach((pair: TradingPairConfig) => {
          this.pairs.set(pair.id, pair);
        });
      }
      
      if (config.arbitrageConfig) {
        this.arbitrageConfig = { ...config.arbitrageConfig };
      }
      
      console.log('✅ 配置导入成功');
      return true;
    } catch (error) {
      console.error('❌ 配置导入失败:', error);
      return false;
    }
  }
}

/**
 * 全局交易对管理器实例
 */
export const tradingPairManager = new TradingPairManager();

/**
 * 导出常用函数
 */
export function createTradingPair(
  tokenASymbol: string,
  tokenBSymbol: string,
  options: Partial<TradingPairConfig> = {}
): TradingPairConfig {
  const tokenA = TOKENS[tokenASymbol];
  const tokenB = TOKENS[tokenBSymbol];

  if (!tokenA || !tokenB) {
    throw new Error(`代币不存在: ${tokenASymbol} 或 ${tokenBSymbol}`);
  }

  const id = `${tokenASymbol}-${tokenBSymbol}`;

  return {
    id,
    tokenA,
    tokenB,
    enabled: true,
    tradeAmount: '100',   // 默认固定100个基础代币
    checkInterval: 1000,
    description: `${tokenASymbol}/${tokenBSymbol} 交易对`,
    ...options
  };
}

export default tradingPairManager;
