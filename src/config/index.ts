import { RefConfig } from '../types';

/**
 * REF Finance 配置
 */
export const REF_CONFIG: RefConfig = {
  networkId: 'mainnet',
  rpcUrl: process.env.NEAR_RPC_URL || 'https://free.rpc.fastnear.com',
  smartRouterUrl: process.env.REF_SMART_ROUTER_URL || 'https://smartrouter.ref.finance',
  contracts: {
    v1: process.env.REF_V1_CONTRACT || 'v2.ref-finance.near',
    dclv2: process.env.REF_DCLV2_CONTRACT || 'dclv2.ref-labs.near'
  }
};

/**
 * 测试网配置
 */
export const REF_CONFIG_TESTNET: RefConfig = {
  networkId: 'testnet',
  rpcUrl: process.env.NEAR_RPC_URL || 'https://test.rpc.fastnear.com',
  smartRouterUrl: 'https://smartroutertest.refburrow.top',
  contracts: {
    v1: 'ref-finance-101.testnet',
    dclv2: 'dclv2.ref-dev.testnet'
  }
};

/**
 * DCL v2 费用等级
 */
export const DCL_V2_FEE_LEVELS = [100, 400, 2000, 10000]; // 0.01%, 0.04%, 0.2%, 1%

/**
 * 默认配置
 */
export const DEFAULT_SLIPPAGE = 0.005; // 0.5%
export const DEFAULT_PATH_DEEP = 3;    // 最大路径深度
export const REQUEST_TIMEOUT = 30000;  // 30秒超时 (增加以避免网络超时)

/**
 * 常用代币地址
 */
export const COMMON_TOKENS = {
  NEAR: 'wrap.near',
  USDT: 'usdt.tether-token.near',
  USDC: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
  REF: 'token.v2.ref-finance.near',
  AURORA: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near'
};

/**
 * 获取配置
 */
export function getConfig(network: 'mainnet' | 'testnet' = 'mainnet'): RefConfig {
  return network === 'testnet' ? REF_CONFIG_TESTNET : REF_CONFIG;
}
