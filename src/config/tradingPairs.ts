/**
 * 交易对配置文件
 * 
 * 用于配置套利机器人监控的交易对
 * 支持动态添加、删除和修改交易对
 */

// 代币元数据接口
export interface TokenInfo {
  id: string;           // 代币合约地址
  symbol: string;       // 代币符号
  name: string;         // 代币名称
  decimals: number;     // 精度
  icon?: string;        // 图标URL
}

// 三档位动态金额配置接口
export interface DynamicAmountConfig {
  enabled: boolean;     // 是否启用动态金额
  low: string;         // 低档金额 (利润 >= 0.012 NEAR)
  medium: string;      // 中档金额 (利润 >= 0.052 NEAR)
  high: string;        // 高档金额 (利润 >= 0.1 NEAR)
}

// 交易对配置接口
export interface TradingPairConfig {
  id: string;           // 交易对唯一标识
  tokenA: TokenInfo;    // 基础代币
  tokenB: TokenInfo;    // 报价代币
  enabled: boolean;     // 是否启用监控

  // 交易金额配置（向后兼容）
  tradeAmount?: string;          // 固定交易金额（可选，用于向后兼容）
  dynamicAmount?: DynamicAmountConfig; // 动态金额配置（可选）

  description?: string; // 描述
}

// 自动余额管理配置接口
export interface AutoBalanceConfig {
  enabled: boolean;              // 是否启用自动余额管理
  checkInterval: number;         // 检查间隔（毫秒）
  minNearBalance: number;        // 最小NEAR余额阈值
  unwrapAmount: number;          // 自动解包数量
  reserveAmount: number;         // 预留wNEAR数量（保留用于交易）
}

// 套利配置接口
export interface ArbitrageConfig {
  minProfitThreshold: number;    // 最小利润阈值（NEAR绝对数量）
  maxSlippage: number;           // 最大滑点容忍度
  maxPriceImpact: number;        // 最大价格影响
  gasBuffer: number;             // Gas费用缓冲（百分比）
  enabled: boolean;              // 是否启用套利执行
  monitoringInterval: number;    // 监控间隔（毫秒）
  statusDisplayInterval: number; // 状态显示间隔（检查次数）
  debugMode: boolean;            // 调试模式（显示所有监控日志）
  verboseLogging: boolean;       // 详细日志（显示所有报价信息）
  autoBalanceManagement: AutoBalanceConfig; // 自动余额管理配置
}

/**
 * 常用代币定义
 */
export const TOKENS: Record<string, TokenInfo> = {
  NEAR: {
    id: 'wrap.near',
    symbol: 'NEAR',
    name: 'NEAR Protocol',
    decimals: 24,
    icon: 'https://assets.coingecko.com/coins/images/10365/small/near.jpg'
  },
  
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    symbol: 'USDC',
    name: 'USD Coin',
    decimals: 6,
    icon: 'https://assets.coingecko.com/coins/images/6319/small/USD_Coin_icon.png'
  },

  USDC_e: {
    id: 'a0b86991c6218b36c1d19d4a2e9eb0ce3606eb48.factory.bridge.near',
    symbol: 'USDC.e',
    name: 'USDC.e',
    decimals: 6,
  },
  
  USDT: {
    id: 'usdt.tether-token.near',
    symbol: 'USDT',
    name: 'Tether USD',
    decimals: 6,
    icon: 'https://assets.coingecko.com/coins/images/325/small/Tether.png'
  },
  
  USDT_e: {
    id: 'dac17f958d2ee523a2206206994597c13d831ec7.factory.bridge.near',
    symbol: 'USDT.e',
    name: 'USDT.e',
    decimals: 6,
  },

  AURORA: {
    id: 'aaaaaa20d9e0e2461697782ef11675f668207961.factory.bridge.near',
    symbol: 'AURORA',
    name: 'Aurora',
    decimals: 18,
  },
  
  WBTC: {
    id: '2260fac5e5542a773aa44fbcfedf7c193bc2c599.factory.bridge.near',
    symbol: 'WBTC',
    name: 'Wrapped Bitcoin',
    decimals: 8,
    icon: 'https://assets.coingecko.com/coins/images/7598/small/wrapped_bitcoin_wbtc.png'
  },

  BLACKDRAGON:{
    id: 'blackdragon.tkn.near',
    symbol: 'BLACKDRAGON',
    name: 'BLACKDRAGON',
    decimals: 24,
  },

  SHITZU:{
    id: 'token.0xshitzu.near',
    symbol: 'SHITZU',
    name: 'SHITZU',
    decimals: 18,
  },

  PURGE:{
    id: 'purge-558.meme-cooking.near',
    symbol: 'PURGE',
    name: 'PURGE',
    decimals: 18,
  },
  
  GEAR:{
    id: 'gear.enleap.near',
    symbol: 'GEAR',
    name: 'GEAR',
    decimals: 18,
  },

  DOGSHIT:{
    id: 'dogshit-1408.meme-cooking.near',
    symbol: 'DOGSHIT',
    name: 'DOGSHIT',
    decimals: 18,
  },

  LONK:{
    id: 'token.lonkingnearbackto2024.near',
    symbol: 'LONK',
    name: 'LONK',
    decimals: 8,
  },

  NEKO:{
    id: 'ftv2.nekotoken.near',
    symbol: 'NEKO',
    name: 'NEKO',
    decimals: 24,
  },

  nBTC:{
    id: 'nbtc.bridge.near',
    symbol: 'nBTC',
    name: 'nBTC',
    decimals: 8,
  },

  mpDAO:{
    id: 'mpdao-token.near',
    symbol: 'mpDAO',
    name: 'mpDAO',
    decimals: 6,
  },

  RHEA:{
    id: 'token.rhealab.near',
    symbol: 'RHEA',
    name: 'RHEA',
    decimals: 18,
  },

};

/**
 * 默认交易对配置
 */
export const DEFAULT_TRADING_PAIRS: TradingPairConfig[] = [
  {
    id: 'NEAR-USDC',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDC,
    enabled: true,
    // tradeAmount: '3',   // 固定3 NEAR
    // 🎯 启用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '3',      // 基础 3 NEAR (利润 >= 0.012)
      medium: '5',   // 中档 8 NEAR (利润 >= 0.052)
      high: '8'     // 高档 15 NEAR (利润 >= 0.1)
    },
    description: 'NEAR-USDC套利交易对 - 三档位动态金额'
  },

  {
    id: 'NEAR-USDC_e',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDC_e,
    enabled: true,
    tradeAmount: '0.5',   // 固定100 NEAR
  },

  {
    id: 'NEAR-USDT',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDT,
    enabled: true,
    // tradeAmount: '12',   // 固定100 NEAR
    // 使用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '12',      // 基础 3 NEAR (利润 >= 0.012)
      medium: '18',   // 中档 8 NEAR (利润 >= 0.052)
      high: '24'     // 高档 15 NEAR (利润 >= 0.1)
    },
  },

  {
    id: 'NEAR-USDT_e',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.USDT_e,
    enabled: true,
    tradeAmount: '1',   // 固定100 NEAR
  },

  {
    id: 'NEAR-BLACKDRAGON',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.BLACKDRAGON,
    enabled: true,
    // tradeAmount: '5',   // 固定5 NEAR
    // 🎯 启用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '5',      // 基础 5 NEAR
      medium: '10',  // 中档 10 NEAR
      high: '15'     // 高档 18 NEAR
    },
  },

  {
    id: 'NEAR-SHITZU',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.SHITZU,
    enabled: true,
    // tradeAmount: '5',   // 固定100 NEAR
    // 🎯 启用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '5',      // 基础 5 NEAR
      medium: '10',  // 中档 10 NEAR
      high: '15'     // 高档 18 NEAR
    },
  },

  {
    id: 'NEAR-PURGE',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.PURGE,
    enabled: true,
    tradeAmount: '8',   // 固定100 NEAR
  },

  // {
  //   id: 'NEAR-PURGE2',
  //   tokenA: TOKENS.NEAR,
  //   tokenB: TOKENS.PURGE,
  //   enabled: true,
  //   tradeAmount: '8',   // 固定100 NEAR
  //   checkInterval: 1000,
  // },
  
  {
    id: 'NEAR-GEAR',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.GEAR,
    enabled: true,
    tradeAmount: '5',   // 固定100 NEAR
  },

  {
    id: 'NEAR-DOGSHIT',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.DOGSHIT,
    enabled: true,
    // tradeAmount: '5',   // 固定100 NEAR
    // 🎯 启用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '5',      // 基础 5 NEAR
      medium: '10',  // 中档 10 NEAR
      high: '15'     // 高档 18 NEAR
    },
  },

  {
    id: 'NEAR-LONK',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.LONK,
    enabled: true,
    tradeAmount: '2',   // 固定100 NEAR
    // 🎯 启用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '2',      // 基础 5 NEAR
      medium: '5',  // 中档 10 NEAR
      high: '10'     // 高档 18 NEAR
    },
  },

  {
    id: 'NEAR-mpDAO',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.mpDAO,
    enabled: true,
    tradeAmount: '1',   // 固定100 NEAR
  },

  {
    id: 'NEAR-NEKO',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.NEKO,
    enabled: true,
    tradeAmount: '4',   // 固定100 NEAR
  },

  {
    id: 'NEAR-nBTC',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.nBTC,
    enabled: true,
    tradeAmount: '2',   // 固定100 NEAR
  },

  {
    id: 'NEAR-AURORA',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.AURORA,
    enabled: true,
    tradeAmount: '0.5',   // 固定100 NEAR
  },

  {
    id: 'NEAR-RHEA',
    tokenA: TOKENS.NEAR,
    tokenB: TOKENS.RHEA,
    enabled: true,
    // tradeAmount: '12',   // 固定100 NEAR
    // 使用三档位动态金额
    dynamicAmount: {
      enabled: true,
      low: '0.05',      // 基础 3 NEAR (利润 >= 0.012)
      medium: '0.08',   // 中档 8 NEAR (利润 >= 0.052)
      high: '1'     // 高档 15 NEAR (利润 >= 0.1)
    },
  },
];

/**
 * 默认套利配置
 *
 * 🔧 调试模式使用说明：
 * - debugMode: true  = 显示所有监控日志（包括错误和失败的报价）
 * - verboseLogging: true = 显示所有交易对的报价信息
 * - 两者都为 false = 只显示重要信息（套利机会和状态）
 */
export const DEFAULT_ARBITRAGE_CONFIG: ArbitrageConfig = {
  minProfitThreshold: 0.012,  // 最小0.015 NEAR利润
  maxSlippage: 0.005,          // 最大1%滑点
  maxPriceImpact: 0.01,       // 最大5%价格影响
  gasBuffer: 0.1,             // 10% Gas费用缓冲
  enabled: true,              // 启用自动执行
  monitoringInterval: 3000,   // 3秒监控间隔
  statusDisplayInterval: 30,  // 每10次检查显示一次状态
  debugMode: false,           // 🔧 调试模式：true=显示所有监控日志，false=只显示重要信息
  verboseLogging: false,      // 🔧 详细日志：true=显示所有报价，false=只显示有意义的报价

  // 🔧 自动余额管理配置
  autoBalanceManagement: {
    enabled: true,            // 是否启用自动余额管理
    checkInterval: 30 * 60 * 1000,  // 检查间隔：30分钟
    minNearBalance: 1.0,      // 最小NEAR余额阈值（低于此值时自动解包）
    unwrapAmount: 1.0,        // 自动解包数量
    reserveAmount: 5.5        // 预留wNEAR数量（保留用于交易，不会被解包）
  }
};

/**
 * 交易对管理类
 */
export class TradingPairManager {
  private pairs: Map<string, TradingPairConfig> = new Map();
  private arbitrageConfig: ArbitrageConfig;

  constructor(
    initialPairs: TradingPairConfig[] = DEFAULT_TRADING_PAIRS,
    arbitrageConfig: ArbitrageConfig = DEFAULT_ARBITRAGE_CONFIG
  ) {
    // 初始化交易对
    initialPairs.forEach(pair => {
      this.pairs.set(pair.id, pair);
    });
    
    this.arbitrageConfig = { ...arbitrageConfig };
  }

  /**
   * 获取所有交易对
   */
  getAllPairs(): TradingPairConfig[] {
    return Array.from(this.pairs.values());
  }

  /**
   * 获取启用的交易对
   */
  getEnabledPairs(): TradingPairConfig[] {
    return this.getAllPairs().filter(pair => pair.enabled);
  }

  /**
   * 获取特定交易对
   */
  getPair(id: string): TradingPairConfig | undefined {
    return this.pairs.get(id);
  }

  /**
   * 添加交易对
   */
  addPair(pair: TradingPairConfig): void {
    this.pairs.set(pair.id, pair);
    console.log(`✅ 添加交易对: ${pair.id}`);
  }

  /**
   * 更新交易对
   */
  updatePair(id: string, updates: Partial<TradingPairConfig>): boolean {
    const existing = this.pairs.get(id);
    if (!existing) {
      console.error(`❌ 交易对不存在: ${id}`);
      return false;
    }

    const updated = { ...existing, ...updates };
    this.pairs.set(id, updated);
    console.log(`✅ 更新交易对: ${id}`);
    return true;
  }

  /**
   * 删除交易对
   */
  removePair(id: string): boolean {
    const deleted = this.pairs.delete(id);
    if (deleted) {
      console.log(`✅ 删除交易对: ${id}`);
    } else {
      console.error(`❌ 交易对不存在: ${id}`);
    }
    return deleted;
  }

  /**
   * 启用/禁用交易对
   */
  togglePair(id: string, enabled?: boolean): boolean {
    const pair = this.pairs.get(id);
    if (!pair) {
      console.error(`❌ 交易对不存在: ${id}`);
      return false;
    }

    pair.enabled = enabled !== undefined ? enabled : !pair.enabled;
    console.log(`✅ ${pair.enabled ? '启用' : '禁用'}交易对: ${id}`);
    return true;
  }

  /**
   * 获取套利配置
   */
  getArbitrageConfig(): ArbitrageConfig {
    return { ...this.arbitrageConfig };
  }

  /**
   * 更新套利配置
   */
  updateArbitrageConfig(updates: Partial<ArbitrageConfig>): void {
    this.arbitrageConfig = { ...this.arbitrageConfig, ...updates };
    console.log('✅ 更新套利配置');
  }

  /**
   * 导出配置为JSON
   */
  exportConfig(): string {
    return JSON.stringify({
      pairs: this.getAllPairs(),
      arbitrageConfig: this.arbitrageConfig
    }, null, 2);
  }

  /**
   * 从JSON导入配置
   */
  importConfig(jsonConfig: string): boolean {
    try {
      const config = JSON.parse(jsonConfig);
      
      if (config.pairs && Array.isArray(config.pairs)) {
        this.pairs.clear();
        config.pairs.forEach((pair: TradingPairConfig) => {
          this.pairs.set(pair.id, pair);
        });
      }
      
      if (config.arbitrageConfig) {
        this.arbitrageConfig = { ...config.arbitrageConfig };
      }
      
      console.log('✅ 配置导入成功');
      return true;
    } catch (error) {
      console.error('❌ 配置导入失败:', error);
      return false;
    }
  }
}

/**
 * 全局交易对管理器实例
 */
export const tradingPairManager = new TradingPairManager();

/**
 * 导出常用函数
 */
export function createTradingPair(
  tokenASymbol: string,
  tokenBSymbol: string,
  options: Partial<TradingPairConfig> = {}
): TradingPairConfig {
  const tokenA = TOKENS[tokenASymbol];
  const tokenB = TOKENS[tokenBSymbol];

  if (!tokenA || !tokenB) {
    throw new Error(`代币不存在: ${tokenASymbol} 或 ${tokenBSymbol}`);
  }

  const id = `${tokenASymbol}-${tokenBSymbol}`;

  return {
    id,
    tokenA,
    tokenB,
    enabled: true,
    tradeAmount: '100',   // 默认固定100个基础代币
    description: `${tokenASymbol}/${tokenBSymbol} 交易对`,
    ...options
  };
}

export default tradingPairManager;
