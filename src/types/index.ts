/**
 * REF Finance 报价系统类型定义
 */

// 代币元数据
export interface TokenMetadata {
  id: string;
  name: string;
  symbol: string;
  decimals: number;
  icon?: string;
}

// 交易结果接口（修复精度问题）
export interface TransactionResult {
  success: boolean;
  transactionHash?: string;
  error?: string;
  gasUsed?: string;
  outputAmount?: string;      // 人类可读格式
  outputAmountWei?: string;   // wei格式（精确，新增）
  inputAmount?: string;       // 人类可读格式
  inputAmountWei?: string;    // wei格式（精确，新增）
}

// V1 Smart Router API 相关类型
export interface SmartRouterResponse {
  result_code: number;
  result_data: {
    routes: V1Route[];
    amount_out: string;
    contract_out: string;
  };
}

export interface V1Route {
  pools: V1Pool[];
  amount_out: string;
  percent?: number;
}

export interface V1Pool {
  pool_id: number;  // V1 池子使用数字 ID
  token_in: string;
  token_out: string;
  amount_in?: string;
  min_amount_out: string;
}

// DCL v2 合约调用相关类型
export interface DCLv2QuoteParams {
  pool_ids: string[];  // DCL v2 池子 ID 格式：token1|token2|fee
  input_token: string;
  output_token: string;
  input_amount: string;
  tag?: string;
}

export interface DCLv2QuoteResponse {
  amount: string;
  tag?: string;
}

// 统一报价结果
export interface QuoteResult {
  system: 'V1' | 'DCL_V2' | 'DCL_V2_LOCAL';
  contractId: string;
  outputAmount: string;
  inputAmount: string;
  priceImpact?: string;
  fee?: string;
  poolId?: string; // 用于DCL v2池子ID
  route?: V1Route | DCLv2QuoteParams;
  rawResponse: any;
}

// 报价查询参数
export interface QuoteParams {
  tokenIn: TokenMetadata;
  tokenOut: TokenMetadata;
  amountIn: string;  // 可读格式，如 "1000"
  slippage?: number; // 滑点容忍度，如 0.005 表示 0.5%
}

// 错误类型
export interface QuoteError {
  system: 'V1' | 'DCL_V2' | 'BOTH';
  message: string;
  originalError?: any;
}

// NEAR RPC 调用参数
export interface NearRPCParams {
  request_type: 'call_function';
  finality: 'optimistic' | 'final';
  account_id: string;
  method_name: string;
  args_base64: string;
}

// 配置类型
export interface RefConfig {
  networkId: 'mainnet' | 'testnet';
  rpcUrl: string;
  smartRouterUrl: string;
  contracts: {
    v1: string;      // v2.ref-finance.near
    dclv2: string;   // dclv2.ref-labs.near
  };
}
