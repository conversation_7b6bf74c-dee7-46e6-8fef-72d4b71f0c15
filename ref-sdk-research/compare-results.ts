/**
 * REF SDK vs 现有API 对比测试
 * 
 * 目的：对比REF官方SDK和我们现有API的报价结果，验证差异
 */

import 'dotenv/config';
import path from 'path';

// REF SDK 导入
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos
} from '@ref-finance/ref-sdk';

// 现有项目API导入 (需要调整路径)
import { refQuoteService } from '../src/services/refQuoteService';
import { TokenMetadata } from '../src/types';

// 代币定义（与test-current-api.ts保持一致）
const TOKEN_METADATA: Record<string, TokenMetadata> = {
  NEAR: {
    id: 'wrap.near',
    name: 'Wrapped NEAR',
    symbol: 'NEAR',
    decimals: 24
  },
  USDT: {
    id: 'usdt.tether-token.near',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6
  },
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    name: 'USD Coin',
    symbol: 'USDC',
    decimals: 6
  },
  REF: {
    id: 'token.v2.ref-finance.near',
    name: 'Ref Finance Token',
    symbol: 'REF',
    decimals: 18
  }
};

// 初始化REF SDK
init_env('mainnet');

/**
 * 对比测试结果接口
 */
interface ComparisonResult {
  testCase: string;
  sdkResult: {
    success: boolean;
    output?: string;
    time?: number;
    error?: string;
    paths?: string;
  };
  apiResult: {
    success: boolean;
    output?: string;
    time?: number;
    error?: string;
    system?: string;
  };
  difference?: {
    absolute: string;
    percentage: string;
    significant: boolean;
  };
}

/**
 * 使用REF SDK获取报价
 */
async function getSDKQuote(tokenIn: any, tokenOut: any, amount: string) {
  try {
    const startTime = Date.now();
    
    // 获取池子数据（可以缓存以提高性能）
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();
    
    const swapTodos = await estimateSwap({
      tokenIn,
      tokenOut,
      amountIn: amount,
      simplePools,
      options: {
        enableSmartRouting: true,
        stablePools: [...unRatedPools, ...ratedPools]
      }
    });

    const output = getExpectedOutputFromSwapTodos(swapTodos, tokenOut.id);
    const time = Date.now() - startTime;
    
    // 构建路径信息
    const paths = swapTodos.map((step, index) => 
      `Pool#${step.pool.id}`
    ).join(' → ');

    return {
      success: true,
      output: output.toString(),
      time,
      paths
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      time: 0
    };
  }
}

/**
 * 使用现有API获取报价
 */
async function getAPIQuote(tokenIn: any, tokenOut: any, amount: string) {
  try {
    const startTime = Date.now();
    
    const quote = await refQuoteService.getQuote({
      tokenIn,
      tokenOut,
      amountIn: amount,
      slippage: 0.005
    });

    const time = Date.now() - startTime;

    return {
      success: true,
      output: quote.outputAmount,
      time,
      system: quote.system
    };

  } catch (error: any) {
    return {
      success: false,
      error: error.message,
      time: 0
    };
  }
}

/**
 * 计算差异
 */
function calculateDifference(sdkOutput: string, apiOutput: string) {
  try {
    const sdk = parseFloat(sdkOutput);
    const api = parseFloat(apiOutput);
    
    const absolute = Math.abs(sdk - api);
    const percentage = (absolute / Math.max(sdk, api)) * 100;
    
    return {
      absolute: absolute.toFixed(6),
      percentage: percentage.toFixed(4) + '%',
      significant: percentage > 0.1 // 超过0.1%认为是显著差异
    };
  } catch {
    return {
      absolute: 'N/A',
      percentage: 'N/A',
      significant: true
    };
  }
}

/**
 * 执行对比测试
 */
async function runComparisonTest() {
  console.log('🔍 REF SDK vs 现有API 对比测试');
  console.log('='.repeat(80));

  // 测试用例
  const testCases = [
    { 
      tokenIn: TOKEN_METADATA.NEAR, 
      tokenOut: TOKEN_METADATA.USDT, 
      amount: '1',
      desc: '1 NEAR → USDT'
    },
    { 
      tokenIn: TOKEN_METADATA.NEAR, 
      tokenOut: TOKEN_METADATA.USDC, 
      amount: '1',
      desc: '1 NEAR → USDC'
    },
    { 
      tokenIn: TOKEN_METADATA.NEAR, 
      tokenOut: TOKEN_METADATA.USDT, 
      amount: '10',
      desc: '10 NEAR → USDT'
    },
    { 
      tokenIn: TOKEN_METADATA.USDT, 
      tokenOut: TOKEN_METADATA.NEAR, 
      amount: '100',
      desc: '100 USDT → NEAR'
    },
    { 
      tokenIn: TOKEN_METADATA.USDC, 
      tokenOut: TOKEN_METADATA.NEAR, 
      amount: '100',
      desc: '100 USDC → NEAR'
    }
  ];

  const results: ComparisonResult[] = [];

  for (const testCase of testCases) {
    console.log(`\n📊 测试: ${testCase.desc}`);
    console.log('-'.repeat(50));

    // 获取SDK代币元数据
    const [sdkTokenIn, sdkTokenOut] = await Promise.all([
      ftGetTokenMetadata(testCase.tokenIn.id),
      ftGetTokenMetadata(testCase.tokenOut.id)
    ]);

    // 并行获取两种报价
    const [sdkResult, apiResult] = await Promise.all([
      getSDKQuote(sdkTokenIn, sdkTokenOut, testCase.amount),
      getAPIQuote(testCase.tokenIn, testCase.tokenOut, testCase.amount)
    ]);

    console.log(`REF SDK: ${sdkResult.success ? 
      `✅ ${sdkResult.output} (${sdkResult.time}ms)` : 
      `❌ ${sdkResult.error}`
    }`);
    
    console.log(`现有API: ${apiResult.success ? 
      `✅ ${apiResult.output} (${apiResult.time}ms, ${apiResult.system})` : 
      `❌ ${apiResult.error}`
    }`);

    // 计算差异
    let difference;
    if (sdkResult.success && apiResult.success && sdkResult.output && apiResult.output) {
      difference = calculateDifference(sdkResult.output, apiResult.output);
      console.log(`差异: ${difference.absolute} (${difference.percentage}) ${
        difference.significant ? '⚠️ 显著' : '✅ 可接受'
      }`);
    }

    // 显示路径信息
    if (sdkResult.paths) {
      console.log(`SDK路径: ${sdkResult.paths}`);
    }

    results.push({
      testCase: testCase.desc,
      sdkResult,
      apiResult,
      difference
    });
  }

  return results;
}

/**
 * 生成对比报告
 */
function generateReport(results: ComparisonResult[]) {
  console.log('\n📋 对比测试报告');
  console.log('='.repeat(80));

  let totalTests = results.length;
  let bothSuccessful = 0;
  let significantDifferences = 0;
  let sdkFaster = 0;
  let apiFaster = 0;

  console.log('\n| 测试用例 | SDK结果 | API结果 | 差异 | 速度对比 |');
  console.log('|----------|---------|---------|------|----------|');

  results.forEach(result => {
    const sdkStatus = result.sdkResult.success ? '✅' : '❌';
    const apiStatus = result.apiResult.success ? '✅' : '❌';
    
    if (result.sdkResult.success && result.apiResult.success) {
      bothSuccessful++;
      
      if (result.difference?.significant) {
        significantDifferences++;
      }

      if (result.sdkResult.time! < result.apiResult.time!) {
        sdkFaster++;
      } else {
        apiFaster++;
      }
    }

    const diffText = result.difference ? 
      `${result.difference.percentage} ${result.difference.significant ? '⚠️' : '✅'}` : 
      'N/A';

    const speedText = result.sdkResult.time && result.apiResult.time ?
      `SDK:${result.sdkResult.time}ms API:${result.apiResult.time}ms` :
      'N/A';

    console.log(`| ${result.testCase} | ${sdkStatus} | ${apiStatus} | ${diffText} | ${speedText} |`);
  });

  console.log('\n📊 统计摘要:');
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   双方成功: ${bothSuccessful}/${totalTests} (${(bothSuccessful/totalTests*100).toFixed(1)}%)`);
  console.log(`   显著差异: ${significantDifferences}/${bothSuccessful} (${bothSuccessful > 0 ? (significantDifferences/bothSuccessful*100).toFixed(1) : 0}%)`);
  console.log(`   速度对比: SDK更快 ${sdkFaster}次, API更快 ${apiFaster}次`);

  // 结论
  console.log('\n🎯 结论:');
  if (significantDifferences === 0) {
    console.log('   ✅ REF SDK和现有API结果基本一致，可以考虑迁移');
  } else if (significantDifferences <= bothSuccessful * 0.2) {
    console.log('   ⚠️ 存在少量差异，需要进一步分析原因');
  } else {
    console.log('   ❌ 存在较多差异，建议继续使用现有API');
  }

  if (sdkFaster > apiFaster) {
    console.log('   🚀 REF SDK在速度上有优势');
  } else {
    console.log('   🚀 现有API在速度上有优势');
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const results = await runComparisonTest();
    generateReport(results);
    
    console.log('\n🎉 对比测试完成!');
    console.log('基于以上结果，您可以决定是否使用REF SDK');
    
  } catch (error: any) {
    console.error('❌ 对比测试失败:', error.message);
  }
}

// 运行对比测试
main().catch(console.error);
