# REF Finance SDK 研究项目

## 🎯 研究目的

验证REF官方SDK与我们现有API的差异，评估是否值得迁移到官方SDK。

## 📁 文件说明

- `package.json` - 独立的依赖管理，包含REF SDK
- `test-ref-sdk.ts` - REF SDK功能测试
- `compare-results.ts` - SDK vs API对比测试
- `README.md` - 本文档

## 🚀 快速开始

### 1. 安装依赖

```bash
cd ref-sdk-research
npm install
```

### 2. 配置环境

确保项目根目录的 `.env` 文件包含必要的配置：

```env
NEAR_NETWORK=mainnet
NEAR_RPC_URL=https://free.rpc.fastnear.com
ACCOUNT_ID=your-account.near
PRIVATE_KEY=your-private-key
```

### 3. 运行测试

```bash
# 测试REF SDK基本功能
npm run test:sdk

# 对比SDK和API结果
npm run test:compare

# 运行所有测试
npm run test:all
```

## 🧪 测试内容

### REF SDK 功能测试 (`test-ref-sdk.ts`)

1. **代币元数据获取** - 验证SDK能否正确获取代币信息
2. **池子数据获取** - 测试获取所有池子数据的性能
3. **报价功能测试** - 测试不同交易对的报价
4. **稳定性测试** - 连续查询验证结果一致性

### 对比测试 (`compare-results.ts`)

1. **并行对比** - 同时调用SDK和现有API
2. **结果差异分析** - 计算输出金额的绝对差异和百分比差异
3. **性能对比** - 比较响应时间
4. **路径分析** - 对比交易路径选择

## 📊 预期测试结果

### 成功指标
- ✅ SDK能正常获取报价
- ✅ 结果与现有API差异小于0.1%
- ✅ 响应时间可接受
- ✅ 结果稳定一致

### 风险指标
- ❌ 报价差异超过0.5%
- ❌ 频繁出现错误
- ❌ 响应时间过长
- ❌ 结果不稳定

## 🔍 已知问题记录

基于项目历史，REF SDK可能存在以下问题：

1. **路径不一致** - SDK返回的路径与实际执行不匹配
2. **精度损失** - 金额转换过程中的精度问题
3. **交易构建错误** - 生成的交易参数格式不正确
4. **amount_in分配问题** - 多池子路径中的金额分配错误

## 📝 测试记录

### 测试日期: [待填写]

#### REF SDK 基础测试结果:
- [ ] 代币元数据获取: 成功/失败
- [ ] 池子数据获取: 成功/失败 (耗时: ___ms)
- [ ] 报价功能: 成功/失败
- [ ] 稳定性: 一致/不一致

#### 对比测试结果:
- [ ] 1 NEAR → USDT: 差异 ___%
- [ ] 1 NEAR → USDC: 差异 ___%
- [ ] 10 NEAR → USDT: 差异 ___%
- [ ] 100 USDT → NEAR: 差异 ___%
- [ ] 100 USDC → NEAR: 差异 ___%

#### 性能对比:
- [ ] SDK平均响应时间: ___ms
- [ ] API平均响应时间: ___ms
- [ ] 速度优势: SDK/API

#### 最终结论:
- [ ] 推荐迁移到REF SDK
- [ ] 继续使用现有API
- [ ] 需要进一步测试

## 🎯 决策标准

### 迁移到REF SDK的条件:
1. 报价差异 < 0.1%
2. 响应时间 < 现有API的2倍
3. 稳定性测试100%一致
4. 无严重错误

### 继续使用现有API的条件:
1. 报价差异 > 0.5%
2. 频繁出现错误
3. 响应时间过长
4. 结果不稳定

## 📚 参考资料

- [REF Finance SDK GitHub](https://github.com/ref-finance/ref-sdk)
- [REF Finance 官方文档](https://guide.ref.finance/)
- [项目主要文档](../REF_Finance_完整开发指南.md)
- [套利总结文档](../RayNear套利总结.md)
