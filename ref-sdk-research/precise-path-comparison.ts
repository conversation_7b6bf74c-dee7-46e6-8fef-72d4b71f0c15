/**
 * 精确路径对比测试
 * 
 * 目的：
 * 1. 零误差验证 - 完全一致性检查
 * 2. 路径对比 - SDK vs API 路径是否完全一致
 * 3. 使用正确RPC - https://rpc.shitzuapes.xyz
 * 4. 探究REF SDK正确用法
 */

import 'dotenv/config';
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos,
  instantSwap
} from '@ref-finance/ref-sdk';

// 现有项目API导入
import { refQuoteService } from '../src/services/refQuoteService';
import { v1SmartRouter } from '../src/services/v1SmartRouter';
import { dclv2Contract } from '../src/services/dclv2Contract';
import { TokenMetadata } from '../src/types';

// 设置正确的RPC
process.env.NEAR_RPC_URL = 'https://rpc.shitzuapes.xyz';

// 初始化REF SDK环境，使用正确的RPC
init_env('mainnet', '', 'https://rpc.shitzuapes.xyz');

// 代币定义
const TOKENS: Record<string, TokenMetadata> = {
  NEAR: {
    id: 'wrap.near',
    name: 'Wrapped NEAR',
    symbol: 'NEAR',
    decimals: 24
  },
  USDT: {
    id: 'usdt.tether-token.near',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6
  },
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    name: 'USD Coin',
    symbol: 'USDC',
    decimals: 6
  }
};

/**
 * 路径对比结果接口
 */
interface PathComparisonResult {
  testCase: string;
  sdkPath: string[];
  apiPath: string;
  sdkOutput: string;
  apiOutput: string;
  pathsMatch: boolean;
  outputsMatch: boolean;
  exactMatch: boolean;
  sdkTime: number;
  apiTime: number;
}

/**
 * 获取REF SDK的详细路径信息
 */
async function getSDKDetailedPath(tokenIn: any, tokenOut: any, amount: string) {
  try {
    console.log(`🔍 REF SDK查询: ${amount} ${tokenIn.symbol} → ${tokenOut.symbol}`);
    
    const startTime = Date.now();
    
    // 获取池子数据
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();
    console.log(`   📦 池子数据获取完成: ${simplePools.length} simple, ${unRatedPools.length + ratedPools.length} stable`);
    
    // 获取交换路径
    const swapTodos = await estimateSwap({
      tokenIn,
      tokenOut,
      amountIn: amount,
      simplePools,
      options: {
        enableSmartRouting: true,
        stablePools: [...unRatedPools, ...ratedPools]
      }
    });
    
    const output = getExpectedOutputFromSwapTodos(swapTodos, tokenOut.id);
    const time = Date.now() - startTime;
    
    // 提取详细路径信息
    const pathDetails = swapTodos.map((step, index) => {
      return {
        step: index + 1,
        poolId: step.pool.id,
        inputToken: step.inputToken,
        outputToken: step.outputToken,
        inputAmount: step.pool.partialAmountIn || 'N/A',
        outputAmount: step.estimate,
        poolType: step.pool.pool_kind
      };
    });
    
    console.log(`   ✅ SDK路径 (${time}ms):`);
    pathDetails.forEach(detail => {
      console.log(`      步骤${detail.step}: Pool#${detail.poolId} (${detail.poolType})`);
      console.log(`         ${detail.inputToken} → ${detail.outputToken}`);
      console.log(`         输入: ${detail.inputAmount}, 输出: ${detail.outputAmount}`);
    });
    
    return {
      success: true,
      output: output.toString(),
      time,
      pathDetails,
      swapTodos
    };
    
  } catch (error: any) {
    console.log(`   ❌ SDK失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      time: 0
    };
  }
}

/**
 * 获取现有API的详细路径信息
 */
async function getAPIDetailedPath(tokenIn: TokenMetadata, tokenOut: TokenMetadata, amount: string) {
  try {
    console.log(`🔍 现有API查询: ${amount} ${tokenIn.symbol} → ${tokenOut.symbol}`);
    
    const startTime = Date.now();
    
    // 并行调用V1和DCL v2
    const [v1Result, dclResult] = await Promise.allSettled([
      v1SmartRouter.getV1Quote({
        tokenIn,
        tokenOut,
        amountIn: amount,
        slippage: 0.005
      }),
      dclv2Contract.getDCLv2Quote({
        tokenIn,
        tokenOut,
        amountIn: amount,
        slippage: 0.005
      })
    ]);
    
    const time = Date.now() - startTime;
    
    // 分析结果
    let bestResult: any = null;
    let system = '';
    let pathInfo = '';
    
    if (v1Result.status === 'fulfilled' && v1Result.value) {
      bestResult = v1Result.value;
      system = 'V1';
      const routeInfo = (bestResult as any).route || {};
      pathInfo = `V1 Smart Router (${routeInfo.pools?.length || 0} pools)`;
    }

    if (dclResult.status === 'fulfilled' && dclResult.value) {
      if (!bestResult || parseFloat(dclResult.value.outputAmount) > parseFloat(bestResult.outputAmount)) {
        bestResult = dclResult.value;
        system = 'DCL_V2';
        pathInfo = `DCL v2 Direct (Pool: ${(bestResult as any).poolId || 'N/A'})`;
      }
    }
    
    if (!bestResult) {
      throw new Error('所有系统都失败了');
    }
    
    console.log(`   ✅ API路径 (${time}ms, ${system}):`);
    console.log(`      ${pathInfo}`);
    console.log(`      输出: ${bestResult.outputAmount} ${tokenOut.symbol}`);

    return {
      success: true,
      output: bestResult.outputAmount,
      time,
      system,
      pathInfo,
      route: (bestResult as any).route || {},
      poolId: (bestResult as any).poolId
    };
    
  } catch (error: any) {
    console.log(`   ❌ API失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      time: 0
    };
  }
}

/**
 * 精确对比两个输出值
 */
function exactOutputComparison(sdkOutput: string, apiOutput: string): boolean {
  // 转换为相同精度进行比较
  try {
    const sdk = parseFloat(sdkOutput);
    const api = parseFloat(apiOutput);
    
    // 计算相对差异
    const diff = Math.abs(sdk - api);
    const relativeDiff = diff / Math.max(sdk, api);
    
    console.log(`   🔍 精确对比:`);
    console.log(`      SDK: ${sdkOutput}`);
    console.log(`      API: ${apiOutput}`);
    console.log(`      差异: ${diff.toFixed(10)} (${(relativeDiff * 100).toFixed(6)}%)`);
    
    // 零误差要求：差异必须小于0.000001%
    const isExactMatch = relativeDiff < 0.00000001;
    console.log(`      精确匹配: ${isExactMatch ? '✅ 是' : '❌ 否'}`);
    
    return isExactMatch;
  } catch {
    return false;
  }
}

/**
 * 路径匹配分析
 */
function analyzePathMatch(sdkResult: any, apiResult: any): boolean {
  if (!sdkResult.success || !apiResult.success) {
    return false;
  }
  
  console.log(`   🛣️ 路径匹配分析:`);
  
  // SDK路径
  const sdkPools = sdkResult.pathDetails?.map((detail: any) => detail.poolId) || [];
  console.log(`      SDK路径: ${sdkPools.join(' → ')}`);
  
  // API路径
  let apiPools: string[] = [];
  if (apiResult.system === 'V1') {
    // V1系统的route中包含pools
    const route = apiResult.route || {};
    apiPools = route.pools?.map((pool: any) => pool.pool_id?.toString() || 'N/A') || [];
  } else if (apiResult.system === 'DCL_V2') {
    // DCL v2系统直接使用poolId
    apiPools = apiResult.poolId ? [apiResult.poolId] : [];
  }
  console.log(`      API路径: ${apiPools.join(' → ')}`);
  
  // 路径匹配检查
  const pathsMatch = JSON.stringify(sdkPools) === JSON.stringify(apiPools);
  console.log(`      路径匹配: ${pathsMatch ? '✅ 是' : '❌ 否'}`);
  
  return pathsMatch;
}

/**
 * 执行精确路径对比测试
 */
async function runPrecisePathComparison() {
  console.log('🎯 精确路径对比测试');
  console.log('='.repeat(80));
  console.log(`🌐 使用RPC: ${process.env.NEAR_RPC_URL}`);
  console.log('📋 零误差要求: 输出差异必须 < 0.000001%');
  console.log('🛣️ 路径要求: SDK和API路径必须完全一致');
  console.log('');
  
  const testCases = [
    { tokenIn: TOKENS.NEAR, tokenOut: TOKENS.USDT, amount: '1', desc: '1 NEAR → USDT' },
    { tokenIn: TOKENS.NEAR, tokenOut: TOKENS.USDC, amount: '1', desc: '1 NEAR → USDC' },
    { tokenIn: TOKENS.USDT, tokenOut: TOKENS.NEAR, amount: '100', desc: '100 USDT → NEAR' }
  ];
  
  const results: PathComparisonResult[] = [];
  
  for (const testCase of testCases) {
    console.log(`\n📊 测试: ${testCase.desc}`);
    console.log('-'.repeat(50));
    
    // 获取SDK代币元数据
    const [sdkTokenIn, sdkTokenOut] = await Promise.all([
      ftGetTokenMetadata(testCase.tokenIn.id),
      ftGetTokenMetadata(testCase.tokenOut.id)
    ]);
    
    // 并行获取两种结果
    const [sdkResult, apiResult] = await Promise.all([
      getSDKDetailedPath(sdkTokenIn, sdkTokenOut, testCase.amount),
      getAPIDetailedPath(testCase.tokenIn, testCase.tokenOut, testCase.amount)
    ]);
    
    if (sdkResult.success && apiResult.success) {
      // 精确对比
      const outputsMatch = exactOutputComparison(sdkResult.output || '', apiResult.output || '');
      const pathsMatch = analyzePathMatch(sdkResult, apiResult);
      const exactMatch = outputsMatch && pathsMatch;

      console.log(`\n   🎯 最终结果:`);
      console.log(`      输出匹配: ${outputsMatch ? '✅' : '❌'}`);
      console.log(`      路径匹配: ${pathsMatch ? '✅' : '❌'}`);
      console.log(`      完全匹配: ${exactMatch ? '✅ 通过' : '❌ 失败'}`);

      results.push({
        testCase: testCase.desc,
        sdkPath: sdkResult.pathDetails?.map((d: any) => d.poolId) || [],
        apiPath: apiResult.pathInfo || 'N/A',
        sdkOutput: sdkResult.output || 'N/A',
        apiOutput: apiResult.output || 'N/A',
        pathsMatch,
        outputsMatch,
        exactMatch,
        sdkTime: sdkResult.time || 0,
        apiTime: apiResult.time || 0
      });
    } else {
      console.log(`   ❌ 测试失败 - SDK: ${sdkResult.success}, API: ${apiResult.success}`);
    }
  }
  
  return results;
}

/**
 * 生成精确对比报告
 */
function generatePreciseReport(results: PathComparisonResult[]) {
  console.log('\n📋 精确对比报告');
  console.log('='.repeat(80));
  
  const totalTests = results.length;
  const exactMatches = results.filter(r => r.exactMatch).length;
  const pathMatches = results.filter(r => r.pathsMatch).length;
  const outputMatches = results.filter(r => r.outputsMatch).length;
  
  console.log('\n| 测试用例 | 路径匹配 | 输出匹配 | 完全匹配 | 速度对比 |');
  console.log('|----------|----------|----------|----------|----------|');
  
  results.forEach(result => {
    const pathIcon = result.pathsMatch ? '✅' : '❌';
    const outputIcon = result.outputsMatch ? '✅' : '❌';
    const exactIcon = result.exactMatch ? '✅' : '❌';
    const speedText = `SDK:${result.sdkTime}ms API:${result.apiTime}ms`;
    
    console.log(`| ${result.testCase} | ${pathIcon} | ${outputIcon} | ${exactIcon} | ${speedText} |`);
  });
  
  console.log('\n📊 统计摘要:');
  console.log(`   总测试数: ${totalTests}`);
  console.log(`   路径匹配: ${pathMatches}/${totalTests} (${(pathMatches/totalTests*100).toFixed(1)}%)`);
  console.log(`   输出匹配: ${outputMatches}/${totalTests} (${(outputMatches/totalTests*100).toFixed(1)}%)`);
  console.log(`   完全匹配: ${exactMatches}/${totalTests} (${(exactMatches/totalTests*100).toFixed(1)}%)`);
  
  console.log('\n🎯 零误差验证结果:');
  if (exactMatches === totalTests) {
    console.log('   ✅ 所有测试完全匹配，REF SDK可以使用');
  } else {
    console.log('   ❌ 存在不匹配，不建议使用REF SDK');
    console.log('   📋 不匹配的测试:');
    results.filter(r => !r.exactMatch).forEach(result => {
      console.log(`      - ${result.testCase}: 路径${result.pathsMatch?'✅':'❌'} 输出${result.outputsMatch?'✅':'❌'}`);
    });
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    const results = await runPrecisePathComparison();
    generatePreciseReport(results);
    
    console.log('\n🎉 精确路径对比测试完成!');
    
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
main().catch(console.error);
