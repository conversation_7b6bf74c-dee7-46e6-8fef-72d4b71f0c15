/**
 * 现有API性能和稳定性测试
 * 
 * 目的：在REF SDK安装问题解决前，先测试现有API的表现
 */

import 'dotenv/config';
import path from 'path';

// 现有项目API导入
import { refQuoteService } from '../src/services/refQuoteService';
import { TokenMetadata } from '../src/types';

// 代币定义（基于项目中的EXTENDED_TOKENS）
const TOKEN_METADATA: Record<string, TokenMetadata> = {
  NEAR: {
    id: 'wrap.near',
    name: 'Wrapped NEAR',
    symbol: 'NEAR',
    decimals: 24
  },
  USDT: {
    id: 'usdt.tether-token.near',
    name: 'Tether USD',
    symbol: 'USDT',
    decimals: 6
  },
  USDC: {
    id: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
    name: 'USD Coin',
    symbol: 'USDC',
    decimals: 6
  },
  REF: {
    id: 'token.v2.ref-finance.near',
    name: 'Ref Finance Token',
    symbol: 'REF',
    decimals: 18
  }
};

/**
 * 测试结果接口
 */
interface TestResult {
  testCase: string;
  success: boolean;
  output?: string;
  time: number;
  error?: string;
  system?: string;
}

/**
 * 测试现有API的报价功能
 */
async function testCurrentAPI() {
  console.log('🧪 测试现有REF API性能和稳定性');
  console.log('='.repeat(60));

  // 测试用例
  const testCases = [
    { 
      tokenIn: TOKEN_METADATA.NEAR, 
      tokenOut: TOKEN_METADATA.USDT, 
      amount: '1',
      desc: '1 NEAR → USDT'
    },
    { 
      tokenIn: TOKEN_METADATA.NEAR, 
      tokenOut: TOKEN_METADATA.USDC, 
      amount: '1',
      desc: '1 NEAR → USDC'
    },
    { 
      tokenIn: TOKEN_METADATA.NEAR, 
      tokenOut: TOKEN_METADATA.USDT, 
      amount: '10',
      desc: '10 NEAR → USDT'
    },
    { 
      tokenIn: TOKEN_METADATA.USDT, 
      tokenOut: TOKEN_METADATA.NEAR, 
      amount: '100',
      desc: '100 USDT → NEAR'
    },
    { 
      tokenIn: TOKEN_METADATA.USDC, 
      tokenOut: TOKEN_METADATA.NEAR, 
      amount: '100',
      desc: '100 USDC → NEAR'
    }
  ];

  const results: TestResult[] = [];

  console.log('\n📊 单次测试结果:');
  console.log('-'.repeat(50));

  for (const testCase of testCases) {
    console.log(`\n测试: ${testCase.desc}`);
    
    try {
      const startTime = Date.now();
      
      const quote = await refQuoteService.getQuote({
        tokenIn: testCase.tokenIn,
        tokenOut: testCase.tokenOut,
        amountIn: testCase.amount,
        slippage: 0.005
      });

      const time = Date.now() - startTime;

      console.log(`✅ 成功: ${quote.outputAmount} ${testCase.tokenOut.symbol} (${time}ms, ${quote.system})`);
      
      results.push({
        testCase: testCase.desc,
        success: true,
        output: quote.outputAmount,
        time,
        system: quote.system
      });

    } catch (error: any) {
      const time = Date.now() - Date.now();
      console.log(`❌ 失败: ${error.message}`);
      
      results.push({
        testCase: testCase.desc,
        success: false,
        time,
        error: error.message
      });
    }
  }

  return results;
}

/**
 * 测试API稳定性 - 连续查询同一个交易对
 */
async function testAPIStability() {
  console.log('\n🔄 API稳定性测试 (连续10次查询 1 NEAR → USDT)');
  console.log('='.repeat(60));

  const results: string[] = [];
  const times: number[] = [];

  for (let i = 0; i < 10; i++) {
    try {
      const startTime = Date.now();
      
      const quote = await refQuoteService.getQuote({
        tokenIn: TOKEN_METADATA.NEAR,
        tokenOut: TOKEN_METADATA.USDT,
        amountIn: '1',
        slippage: 0.005
      });

      const time = Date.now() - startTime;
      times.push(time);
      results.push(quote.outputAmount);
      
      console.log(`第${i + 1}次: ${quote.outputAmount} USDT (${time}ms, ${quote.system})`);

    } catch (error: any) {
      console.log(`第${i + 1}次: ❌ 失败 - ${error.message}`);
      results.push('ERROR');
      times.push(0);
    }
  }

  // 分析结果
  console.log('\n📊 稳定性分析:');
  const successfulResults = results.filter(r => r !== 'ERROR');
  const uniqueResults = [...new Set(successfulResults)];
  
  console.log(`   成功查询: ${successfulResults.length}/10`);
  console.log(`   唯一结果: ${uniqueResults.length}`);
  console.log(`   一致性: ${uniqueResults.length <= 1 ? '✅ 完全一致' : '⚠️ 存在差异'}`);

  if (uniqueResults.length > 1) {
    console.log(`   不同结果:`);
    uniqueResults.forEach((result, index) => {
      const count = successfulResults.filter(r => r === result).length;
      console.log(`     ${index + 1}. ${result} (${count}次)`);
    });
  }

  // 性能分析
  const validTimes = times.filter(t => t > 0);
  if (validTimes.length > 0) {
    const avgTime = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
    const minTime = Math.min(...validTimes);
    const maxTime = Math.max(...validTimes);
    
    console.log(`\n⏱️ 性能分析:`);
    console.log(`   平均响应时间: ${avgTime.toFixed(1)}ms`);
    console.log(`   最快响应: ${minTime}ms`);
    console.log(`   最慢响应: ${maxTime}ms`);
    console.log(`   时间稳定性: ${maxTime - minTime < avgTime * 0.5 ? '✅ 稳定' : '⚠️ 波动较大'}`);
  }

  return { results, times, uniqueResults };
}

/**
 * 测试并发性能
 */
async function testConcurrentPerformance() {
  console.log('\n🚀 并发性能测试 (5个并发查询)');
  console.log('='.repeat(60));

  const testCase = {
    tokenIn: TOKEN_METADATA.NEAR,
    tokenOut: TOKEN_METADATA.USDT,
    amountIn: '1',
    slippage: 0.005
  };

  const startTime = Date.now();
  
  try {
    const promises = Array(5).fill(null).map((_, index) => 
      refQuoteService.getQuote(testCase).then(result => ({
        index: index + 1,
        success: true,
        output: result.outputAmount,
        system: result.system
      })).catch(error => ({
        index: index + 1,
        success: false,
        error: error.message
      }))
    );

    const results = await Promise.all(promises);
    const totalTime = Date.now() - startTime;

    console.log(`总耗时: ${totalTime}ms`);
    console.log(`平均每个查询: ${(totalTime / 5).toFixed(1)}ms`);

    results.forEach(result => {
      if (result.success) {
        console.log(`查询${result.index}: ✅ ${(result as any).output} USDT (${(result as any).system})`);
      } else {
        console.log(`查询${result.index}: ❌ ${(result as any).error}`);
      }
    });

    const successCount = results.filter(r => r.success).length;
    console.log(`\n成功率: ${successCount}/5 (${(successCount/5*100).toFixed(1)}%)`);

    return results;

  } catch (error: any) {
    console.error(`❌ 并发测试失败: ${error.message}`);
    return [];
  }
}

/**
 * 生成测试报告
 */
function generateReport(
  basicResults: TestResult[], 
  stabilityData: any, 
  concurrentResults: any[]
) {
  console.log('\n📋 测试报告总结');
  console.log('='.repeat(80));

  // 基础功能测试
  const totalBasic = basicResults.length;
  const successBasic = basicResults.filter(r => r.success).length;
  const avgTimeBasic = basicResults
    .filter(r => r.success)
    .reduce((sum, r) => sum + r.time, 0) / successBasic;

  console.log(`\n🎯 基础功能测试:`);
  console.log(`   测试用例: ${totalBasic}`);
  console.log(`   成功率: ${successBasic}/${totalBasic} (${(successBasic/totalBasic*100).toFixed(1)}%)`);
  console.log(`   平均响应时间: ${avgTimeBasic.toFixed(1)}ms`);

  // 稳定性测试
  console.log(`\n🔄 稳定性测试:`);
  console.log(`   结果一致性: ${stabilityData.uniqueResults.length <= 1 ? '✅ 优秀' : '⚠️ 需关注'}`);
  console.log(`   成功率: ${stabilityData.results.filter((r: string) => r !== 'ERROR').length}/10`);

  // 并发性能测试
  const concurrentSuccess = concurrentResults.filter(r => r.success).length;
  console.log(`\n🚀 并发性能测试:`);
  console.log(`   并发成功率: ${concurrentSuccess}/5 (${(concurrentSuccess/5*100).toFixed(1)}%)`);

  // 总体评估
  console.log(`\n🏆 总体评估:`);
  if (successBasic === totalBasic && stabilityData.uniqueResults.length <= 1 && concurrentSuccess >= 4) {
    console.log(`   ✅ 现有API表现优秀，稳定可靠`);
    console.log(`   💡 建议：继续使用现有API，专注于其他优化`);
  } else if (successBasic >= totalBasic * 0.8) {
    console.log(`   ⚠️ 现有API基本可用，但有改进空间`);
    console.log(`   💡 建议：可以考虑测试REF SDK作为备选方案`);
  } else {
    console.log(`   ❌ 现有API存在问题，需要优化`);
    console.log(`   💡 建议：优先解决现有问题，然后考虑REF SDK`);
  }
}

/**
 * 主函数
 */
async function main() {
  try {
    console.log('🚀 现有API全面测试开始');
    console.log('='.repeat(80));
    
    // 执行各项测试
    const basicResults = await testCurrentAPI();
    const stabilityData = await testAPIStability();
    const concurrentResults = await testConcurrentPerformance();
    
    // 生成报告
    generateReport(basicResults, stabilityData, concurrentResults);
    
    console.log('\n🎉 测试完成!');
    console.log('基于以上结果，您可以决定是否需要进一步测试REF SDK');
    
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
main().catch(console.error);
