/**
 * 调试金额转换问题
 * 
 * 目的：
 * 1. 理解SDK的金额格式要求
 * 2. 修复本地API的金额转换问题
 * 3. 实现与官方API完全一致的结果
 */

import 'dotenv/config';
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos
} from '@ref-finance/ref-sdk';

// 设置正确的RPC
process.env.NEAR_RPC_URL = 'https://rpc.shitzuapes.xyz';
init_env('mainnet', '', 'https://rpc.shitzuapes.xyz');

/**
 * 调试金额转换和格式问题
 */
async function debugAmountConversion() {
  console.log('🔍 调试金额转换问题');
  console.log('='.repeat(80));
  
  try {
    // 获取代币元数据
    const [nearToken, usdtToken] = await Promise.all([
      ftGetTokenMetadata('wrap.near'),
      ftGetTokenMetadata('usdt.tether-token.near')
    ]);
    
    console.log('📊 代币信息:');
    console.log(`   NEAR: ${nearToken.symbol}, decimals: ${nearToken.decimals}`);
    console.log(`   USDT: ${usdtToken.symbol}, decimals: ${usdtToken.decimals}`);
    
    // 获取池子数据
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();
    
    // 测试不同的金额格式
    const amountTests = [
      { desc: '字符串"1"', amount: '1' },
      { desc: 'wei格式', amount: '1000000000000000000000000' },
      { desc: '数字1', amount: 1 },
      { desc: '浮点数1.0', amount: 1.0 },
      { desc: 'parseFloat("1")', amount: parseFloat('1') }
    ];
    
    console.log('\n🧪 测试不同金额格式:');
    console.log('-'.repeat(60));
    
    for (const test of amountTests) {
      console.log(`\n📋 测试: ${test.desc} (值: ${test.amount})`);
      
      try {
        const startTime = Date.now();
        
        const swapTodos = await estimateSwap({
          tokenIn: nearToken,
          tokenOut: usdtToken,
          amountIn: test.amount as any,
          simplePools,
          options: {
            enableSmartRouting: true,
            stablePools: [...unRatedPools, ...ratedPools]
          }
        });
        
        const output = getExpectedOutputFromSwapTodos(swapTodos, usdtToken.id);
        const time = Date.now() - startTime;
        
        console.log(`   ✅ 成功 (${time}ms)`);
        console.log(`   📈 输出: ${output} USDT`);
        console.log(`   🛣️ 路径: ${swapTodos.length}步`);
        
        // 显示路径详情
        swapTodos.forEach((step, index) => {
          console.log(`      步骤${index + 1}: Pool#${step.pool.id}`);
          console.log(`         输入金额: ${step.pool.partialAmountIn}`);
          console.log(`         输出金额: ${step.estimate}`);
        });
        
      } catch (error: any) {
        console.log(`   ❌ 失败: ${error.message}`);
      }
    }
    
    // 测试官方API格式模拟
    console.log('\n🌐 模拟官方API调用格式:');
    console.log('-'.repeat(60));
    
    // 这是官方API可能使用的格式
    const officialAPITests = [
      {
        desc: '官方API格式1',
        params: {
          tokenIn: nearToken,
          tokenOut: usdtToken,
          amountIn: '1',
          simplePools,
          options: {
            enableSmartRouting: false, // 可能不使用智能路由
            stablePools: []
          }
        }
      },
      {
        desc: '官方API格式2',
        params: {
          tokenIn: nearToken,
          tokenOut: usdtToken,
          amountIn: '1',
          simplePools,
          options: {
            enableSmartRouting: true,
            stablePools: [...unRatedPools, ...ratedPools],
            maxHops: 2 // 限制跳数
          }
        }
      }
    ];
    
    for (const test of officialAPITests) {
      console.log(`\n📋 测试: ${test.desc}`);
      
      try {
        const startTime = Date.now();
        
        const swapTodos = await estimateSwap(test.params as any);
        const output = getExpectedOutputFromSwapTodos(swapTodos, usdtToken.id);
        const time = Date.now() - startTime;
        
        console.log(`   ✅ 成功 (${time}ms)`);
        console.log(`   📈 输出: ${output} USDT`);
        console.log(`   🛣️ 路径: ${swapTodos.length}步`);
        
        // 构建类似官方API的响应格式
        const apiResponse = {
          outputAmount: output.toString(),
          route: {
            pools: swapTodos.map((step, index) => ({
              pool_id: step.pool.id,
              token_in: step.inputToken,
              token_out: step.outputToken,
              amount_in: step.pool.partialAmountIn,
              amount_out: step.estimate,
              fee: step.pool.fee
            }))
          }
        };
        
        console.log(`   📋 API格式响应:`);
        console.log(`      输出金额: ${apiResponse.outputAmount}`);
        console.log(`      路径池子: [${apiResponse.route.pools.map(p => p.pool_id).join(', ')}]`);
        
      } catch (error: any) {
        console.log(`   ❌ 失败: ${error.message}`);
      }
    }
    
    // 分析最佳实践
    console.log('\n💡 最佳实践分析:');
    console.log('-'.repeat(60));
    
    console.log('基于测试结果，官方API可能的实现方式:');
    console.log('1. 使用字符串"1"作为amountIn');
    console.log('2. 可能禁用智能路由或限制跳数');
    console.log('3. 可能只使用特定类型的池子');
    console.log('4. 输出格式需要特殊处理');
    
  } catch (error: any) {
    console.error('❌ 调试失败:', error.message);
  }
}

/**
 * 实现修正版本的本地API
 */
class CorrectedLocalAPI {
  private poolsCache: any = null;
  
  async findPath(params: {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    slippage?: number;
  }) {
    console.log('\n🔧 修正版本本地API');
    console.log(`   查询: ${params.amountIn} ${params.tokenIn} → ${params.tokenOut}`);
    
    try {
      // 获取池子数据
      if (!this.poolsCache) {
        this.poolsCache = await fetchAllPools();
      }
      
      // 获取代币元数据
      const [tokenIn, tokenOut] = await Promise.all([
        ftGetTokenMetadata(params.tokenIn),
        ftGetTokenMetadata(params.tokenOut)
      ]);
      
      // 转换输入金额 - 关键修复
      let amountIn: string;
      if (params.amountIn.length > 10) {
        // 如果是wei格式，转换为人类可读格式
        const factor = Math.pow(10, tokenIn.decimals);
        amountIn = (parseFloat(params.amountIn) / factor).toString();
      } else {
        // 如果是人类可读格式，直接使用
        amountIn = params.amountIn;
      }
      
      console.log(`   转换后金额: ${amountIn} ${tokenIn.symbol}`);
      
      // 使用修正的参数调用SDK
      const swapTodos = await estimateSwap({
        tokenIn,
        tokenOut,
        amountIn, // 使用修正后的金额
        simplePools: this.poolsCache.simplePools,
        options: {
          enableSmartRouting: false, // 先测试简单路由
          stablePools: []
        }
      });
      
      const output = getExpectedOutputFromSwapTodos(swapTodos, tokenOut.id);
      
      // 构建API格式响应
      const response = {
        outputAmount: output.toString(),
        route: {
          pools: swapTodos.map(step => ({
            pool_id: step.pool.id,
            token_in: step.inputToken,
            token_out: step.outputToken,
            amount_in: step.pool.partialAmountIn,
            amount_out: step.estimate
          }))
        }
      };
      
      console.log(`   ✅ 修正版本成功`);
      console.log(`   📈 输出: ${response.outputAmount} ${tokenOut.symbol}`);
      console.log(`   🛣️ 路径: ${response.route.pools.length}步`);
      
      return response;
      
    } catch (error: any) {
      console.error(`❌ 修正版本失败: ${error.message}`);
      throw error;
    }
  }
}

/**
 * 对比修正版本
 */
async function compareWithCorrectedAPI() {
  console.log('\n🔄 对比修正版本');
  console.log('='.repeat(80));
  
  const testParams = {
    tokenIn: 'wrap.near',
    tokenOut: 'usdt.tether-token.near',
    amountIn: '1000000000000000000000000', // 1 NEAR in wei
    slippage: 0.005
  };
  
  try {
    // 修正版本本地API
    const correctedAPI = new CorrectedLocalAPI();
    const correctedResult = await correctedAPI.findPath(testParams);
    
    // SDK直接调用（作为对照）
    const [tokenIn, tokenOut] = await Promise.all([
      ftGetTokenMetadata(testParams.tokenIn),
      ftGetTokenMetadata(testParams.tokenOut)
    ]);
    
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();
    
    const swapTodos = await estimateSwap({
      tokenIn,
      tokenOut,
      amountIn: '1', // 使用人类可读格式
      simplePools,
      options: {
        enableSmartRouting: false, // 使用相同配置
        stablePools: []
      }
    });
    
    const sdkOutput = getExpectedOutputFromSwapTodos(swapTodos, tokenOut.id);
    
    console.log('\n📊 最终对比:');
    console.log(`修正API输出: ${correctedResult.outputAmount} USDT`);
    console.log(`SDK直接输出: ${sdkOutput} USDT`);
    
    const diff = Math.abs(parseFloat(correctedResult.outputAmount) - parseFloat(sdkOutput.toString()));
    const diffPercent = (diff / Math.max(parseFloat(correctedResult.outputAmount), parseFloat(sdkOutput.toString()))) * 100;
    
    console.log(`差异: ${diff.toFixed(6)} USDT (${diffPercent.toFixed(4)}%)`);
    
    if (diffPercent < 0.01) {
      console.log('✅ 修正成功！本地API与SDK结果一致');
    } else {
      console.log('⚠️ 仍有差异，需要进一步调试');
    }
    
  } catch (error: any) {
    console.error('❌ 对比失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  await debugAmountConversion();
  await compareWithCorrectedAPI();
  
  console.log('\n🎉 调试完成!');
  console.log('现在我们理解了SDK的正确使用方式');
}

// 运行调试
main().catch(console.error);
