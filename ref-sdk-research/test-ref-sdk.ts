/**
 * REF Finance SDK 测试
 * 
 * 目的：测试REF官方SDK的报价功能，验证其准确性和可靠性
 */

import 'dotenv/config';
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos
} from '@ref-finance/ref-sdk';

// 初始化REF SDK环境
init_env('mainnet');

/**
 * 常用代币定义
 */
const TOKENS = {
  NEAR: 'wrap.near',
  USDT: 'usdt.tether-token.near',
  USDC: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
  REF: 'token.v2.ref-finance.near'
};

/**
 * 测试REF SDK报价功能
 */
async function testRefSDKQuote() {
  console.log('🧪 测试REF Finance SDK报价功能');
  console.log('='.repeat(60));

  try {
    // 1. 获取代币元数据
    console.log('\n1️⃣ 获取代币元数据...');
    const [nearToken, usdtToken, usdcToken] = await Promise.all([
      ftGetTokenMetadata(TOKENS.NEAR),
      ftGetTokenMetadata(TOKENS.USDT),
      ftGetTokenMetadata(TOKENS.USDC)
    ]);

    console.log(`✅ NEAR: ${nearToken.symbol} (${nearToken.decimals} decimals)`);
    console.log(`✅ USDT: ${usdtToken.symbol} (${usdtToken.decimals} decimals)`);
    console.log(`✅ USDC: ${usdcToken.symbol} (${usdcToken.decimals} decimals)`);

    // 2. 获取所有池子数据
    console.log('\n2️⃣ 获取池子数据...');
    const startTime = Date.now();
    const { ratedPools, unRatedPools, simplePools } = await fetchAllPools();
    const poolFetchTime = Date.now() - startTime;

    console.log(`✅ 获取池子完成 (${poolFetchTime}ms)`);
    console.log(`   Simple Pools: ${simplePools.length}`);
    console.log(`   Stable Pools: ${unRatedPools.length + ratedPools.length}`);

    // 3. 测试不同交易对的报价
    const testCases = [
      { tokenIn: nearToken, tokenOut: usdtToken, amount: '1', desc: '1 NEAR → USDT' },
      { tokenIn: nearToken, tokenOut: usdcToken, amount: '1', desc: '1 NEAR → USDC' },
      { tokenIn: nearToken, tokenOut: usdtToken, amount: '10', desc: '10 NEAR → USDT' },
      { tokenIn: usdtToken, tokenOut: nearToken, amount: '100', desc: '100 USDT → NEAR' },
      { tokenIn: usdcToken, tokenOut: nearToken, amount: '100', desc: '100 USDC → NEAR' }
    ];

    console.log('\n3️⃣ 测试报价功能...');
    
    for (const testCase of testCases) {
      console.log(`\n📊 测试: ${testCase.desc}`);
      
      try {
        const quoteStartTime = Date.now();
        
        // 使用REF SDK获取报价
        const swapTodos = await estimateSwap({
          tokenIn: testCase.tokenIn,
          tokenOut: testCase.tokenOut,
          amountIn: testCase.amount,
          simplePools,
          options: {
            enableSmartRouting: true,
            stablePools: [...unRatedPools, ...ratedPools]
          }
        });

        const quoteTime = Date.now() - quoteStartTime;
        
        // 获取预期输出
        const expectedOutput = getExpectedOutputFromSwapTodos(swapTodos, testCase.tokenOut.id);
        
        console.log(`   ✅ 报价成功 (${quoteTime}ms)`);
        console.log(`   📈 输出: ${expectedOutput} ${testCase.tokenOut.symbol}`);
        console.log(`   🛣️  路径: ${swapTodos.length} 步骤`);
        
        // 显示路径详情
        swapTodos.forEach((step, index) => {
          console.log(`      步骤${index + 1}: Pool#${step.pool.id} (${step.inputToken} → ${step.outputToken})`);
        });

      } catch (error: any) {
        console.log(`   ❌ 报价失败: ${error.message}`);
      }
    }

  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

/**
 * 测试REF SDK的稳定性
 */
async function testSDKStability() {
  console.log('\n🔄 测试SDK稳定性 (连续10次相同查询)');
  console.log('='.repeat(60));

  try {
    const nearToken = await ftGetTokenMetadata(TOKENS.NEAR);
    const usdtToken = await ftGetTokenMetadata(TOKENS.USDT);
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();

    const results: string[] = [];

    for (let i = 0; i < 10; i++) {
      try {
        const swapTodos = await estimateSwap({
          tokenIn: nearToken,
          tokenOut: usdtToken,
          amountIn: '1',
          simplePools,
          options: {
            enableSmartRouting: true,
            stablePools: [...unRatedPools, ...ratedPools]
          }
        });

        const output = getExpectedOutputFromSwapTodos(swapTodos, usdtToken.id);
        results.push(output.toString());
        console.log(`   第${i + 1}次: ${output} USDT`);

      } catch (error: any) {
        console.log(`   第${i + 1}次: ❌ 失败 - ${error.message}`);
        results.push('ERROR');
      }
    }

    // 分析结果一致性
    const uniqueResults = [...new Set(results)];
    console.log(`\n📊 结果分析:`);
    console.log(`   总查询次数: 10`);
    console.log(`   唯一结果数: ${uniqueResults.length}`);
    console.log(`   一致性: ${uniqueResults.length === 1 ? '✅ 完全一致' : '❌ 存在差异'}`);

    if (uniqueResults.length > 1) {
      console.log(`   不同结果:`);
      uniqueResults.forEach((result, index) => {
        const count = results.filter(r => r === result).length;
        console.log(`     ${index + 1}. ${result} (${count}次)`);
      });
    }

  } catch (error: any) {
    console.error('❌ 稳定性测试失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 REF Finance SDK 研究测试');
  console.log('='.repeat(80));
  
  await testRefSDKQuote();
  await testSDKStability();
  
  console.log('\n🎉 测试完成!');
  console.log('现在可以运行 compare-results.ts 来对比SDK和API的结果差异');
}

// 运行测试
main().catch(console.error);
