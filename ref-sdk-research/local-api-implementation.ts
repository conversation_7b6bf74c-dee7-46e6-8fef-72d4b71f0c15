/**
 * 本地API实现研究
 * 
 * 目的：
 * 1. 理解REF Finance API的实现原理
 * 2. 研究如何基于SDK构建本地API
 * 3. 分析SDK和API差异的根本原因
 * 4. 实现与官方API一致的本地版本
 */

import 'dotenv/config';
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos,
  instantSwap
} from '@ref-finance/ref-sdk';

// 设置正确的RPC
process.env.NEAR_RPC_URL = 'https://rpc.shitzuapes.xyz';
init_env('mainnet', '', 'https://rpc.shitzuapes.xyz');

/**
 * 分析REF Finance API的可能实现方式
 */
class LocalRefAPI {
  private poolsCache: any = null;
  private lastPoolUpdate: number = 0;
  private readonly POOL_CACHE_TTL = 300000; // 5分钟缓存

  /**
   * 模拟REF Finance API的findPath实现
   */
  async findPath(params: {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    slippage?: number;
  }) {
    console.log('🔧 本地API实现 - findPath');
    console.log(`   输入: ${params.amountIn} ${params.tokenIn} → ${params.tokenOut}`);
    
    try {
      // 1. 获取或更新池子数据
      await this.updatePoolsIfNeeded();
      
      // 2. 获取代币元数据
      const [tokenIn, tokenOut] = await Promise.all([
        ftGetTokenMetadata(params.tokenIn),
        ftGetTokenMetadata(params.tokenOut)
      ]);
      
      // 3. 尝试不同的路由策略
      const strategies = [
        { name: '直接路径', enableSmartRouting: false },
        { name: '智能路由', enableSmartRouting: true },
        { name: '仅V1池子', enableSmartRouting: true, onlyV1: true },
        { name: '仅稳定池', enableSmartRouting: true, onlyStable: true }
      ];
      
      const results = [];
      
      for (const strategy of strategies) {
        console.log(`\n   🧪 测试策略: ${strategy.name}`);
        
        try {
          const startTime = Date.now();
          
          let pools = this.poolsCache.simplePools;
          let stablePools = [...this.poolsCache.unRatedPools, ...this.poolsCache.ratedPools];
          
          if (strategy.onlyV1) {
            stablePools = [];
          } else if (strategy.onlyStable) {
            pools = [];
          }
          
          const swapTodos = await estimateSwap({
            tokenIn,
            tokenOut,
            amountIn: this.convertToSDKAmount(params.amountIn, tokenIn.decimals),
            simplePools: pools,
            options: {
              enableSmartRouting: strategy.enableSmartRouting,
              stablePools
            }
          });
          
          const output = getExpectedOutputFromSwapTodos(swapTodos, tokenOut.id);
          const time = Date.now() - startTime;
          
          // 构建路径信息（模拟API格式）
          const route = {
            pools: swapTodos.map((step, index) => ({
              pool_id: step.pool.id,
              token_in: step.inputToken,
              token_out: step.outputToken,
              amount_in: step.pool.partialAmountIn || params.amountIn,
              amount_out: step.estimate,
              fee: step.pool.fee || 0
            }))
          };
          
          results.push({
            strategy: strategy.name,
            outputAmount: this.convertFromSDKAmount(output.toString(), tokenOut.decimals),
            route,
            time,
            swapTodos
          });
          
          console.log(`      ✅ 成功: ${output} (${time}ms, ${swapTodos.length}步)`);
          
        } catch (error: any) {
          console.log(`      ❌ 失败: ${error.message}`);
          results.push({
            strategy: strategy.name,
            error: error.message
          });
        }
      }
      
      // 4. 选择最佳结果（模拟API的选择逻辑）
      const bestResult = this.selectBestRoute(results);
      
      console.log(`\n   🏆 选择策略: ${bestResult.strategy}`);
      console.log(`   📈 最终输出: ${bestResult.outputAmount}`);
      
      return bestResult;
      
    } catch (error: any) {
      console.error(`❌ 本地API失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新池子数据（如果需要）
   */
  private async updatePoolsIfNeeded() {
    if (!this.poolsCache || Date.now() - this.lastPoolUpdate > this.POOL_CACHE_TTL) {
      console.log('   📦 更新池子数据...');
      const startTime = Date.now();
      
      this.poolsCache = await fetchAllPools();
      this.lastPoolUpdate = Date.now();
      
      const updateTime = Date.now() - startTime;
      console.log(`   ✅ 池子数据更新完成 (${updateTime}ms)`);
      console.log(`      Simple Pools: ${this.poolsCache.simplePools.length}`);
      console.log(`      Stable Pools: ${this.poolsCache.unRatedPools.length + this.poolsCache.ratedPools.length}`);
    }
  }

  /**
   * 选择最佳路由（模拟API逻辑）
   */
  private selectBestRoute(results: any[]) {
    // 过滤掉失败的结果
    const validResults = results.filter(r => !r.error);
    
    if (validResults.length === 0) {
      throw new Error('所有路由策略都失败了');
    }
    
    // 选择输出最大的结果
    return validResults.reduce((best, current) => {
      const bestOutput = parseFloat(best.outputAmount || '0');
      const currentOutput = parseFloat(current.outputAmount || '0');
      return currentOutput > bestOutput ? current : best;
    });
  }

  /**
   * 转换金额格式（到SDK格式）
   */
  private convertToSDKAmount(amount: string, decimals: number): string {
    // 如果已经是wei格式，直接返回
    if (amount.length > 10) {
      return amount;
    }
    
    // 转换为wei格式
    const factor = Math.pow(10, decimals);
    return (parseFloat(amount) * factor).toString();
  }

  /**
   * 转换金额格式（从SDK格式）
   */
  private convertFromSDKAmount(amount: string, decimals: number): string {
    const factor = Math.pow(10, decimals);
    return (parseFloat(amount) / factor).toString();
  }

  /**
   * 构建交易（模拟API的instantSwap）
   */
  async buildTransaction(params: {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    slippage: number;
    accountId: string;
  }) {
    console.log('\n🔨 构建交易');
    
    try {
      // 1. 获取最佳路径
      const pathResult = await this.findPath({
        tokenIn: params.tokenIn,
        tokenOut: params.tokenOut,
        amountIn: params.amountIn,
        slippage: params.slippage
      });
      
      // 2. 获取代币元数据
      const [tokenIn, tokenOut] = await Promise.all([
        ftGetTokenMetadata(params.tokenIn),
        ftGetTokenMetadata(params.tokenOut)
      ]);
      
      // 3. 构建交易
      const transactions = await instantSwap({
        tokenIn,
        tokenOut,
        amountIn: this.convertToSDKAmount(params.amountIn, tokenIn.decimals),
        slippageTolerance: params.slippage,
        swapTodos: pathResult.swapTodos,
        AccountId: params.accountId
      });
      
      console.log(`   ✅ 交易构建成功: ${transactions.length}个交易`);
      
      return {
        transactions,
        expectedOutput: pathResult.outputAmount,
        route: pathResult.route
      };
      
    } catch (error: any) {
      console.error(`❌ 交易构建失败: ${error.message}`);
      throw error;
    }
  }
}

/**
 * 对比本地API和SDK的直接调用
 */
async function compareLocalAPIWithSDK() {
  console.log('🔍 本地API vs SDK直接调用对比');
  console.log('='.repeat(80));
  
  const testParams = {
    tokenIn: 'wrap.near',
    tokenOut: 'usdt.tether-token.near',
    amountIn: '1000000000000000000000000', // 1 NEAR in wei
    slippage: 0.005
  };
  
  try {
    // 1. 本地API实现
    console.log('\n1️⃣ 本地API实现测试');
    console.log('-'.repeat(50));
    
    const localAPI = new LocalRefAPI();
    const localResult = await localAPI.findPath(testParams);
    
    // 2. SDK直接调用
    console.log('\n2️⃣ SDK直接调用测试');
    console.log('-'.repeat(50));
    
    const [tokenIn, tokenOut] = await Promise.all([
      ftGetTokenMetadata(testParams.tokenIn),
      ftGetTokenMetadata(testParams.tokenOut)
    ]);
    
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();
    
    const swapTodos = await estimateSwap({
      tokenIn,
      tokenOut,
      amountIn: '1', // 使用人类可读格式
      simplePools,
      options: {
        enableSmartRouting: true,
        stablePools: [...unRatedPools, ...ratedPools]
      }
    });
    
    const sdkOutput = getExpectedOutputFromSwapTodos(swapTodos, tokenOut.id);
    
    console.log(`✅ SDK直接调用成功`);
    console.log(`   输出: ${sdkOutput} USDT`);
    console.log(`   路径: ${swapTodos.length}步`);
    
    // 3. 对比分析
    console.log('\n3️⃣ 对比分析');
    console.log('-'.repeat(50));
    
    console.log(`本地API输出: ${localResult.outputAmount} USDT`);
    console.log(`SDK直接输出: ${sdkOutput} USDT`);
    
    const diff = Math.abs(parseFloat(localResult.outputAmount) - parseFloat(sdkOutput.toString()));
    const diffPercent = (diff / Math.max(parseFloat(localResult.outputAmount), parseFloat(sdkOutput.toString()))) * 100;
    
    console.log(`差异: ${diff.toFixed(6)} USDT (${diffPercent.toFixed(4)}%)`);
    
    if (diffPercent < 0.01) {
      console.log('✅ 本地API实现成功，与SDK结果一致');
    } else {
      console.log('⚠️ 存在差异，需要进一步调试');
    }
    
  } catch (error: any) {
    console.error('❌ 对比测试失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 本地API实现研究');
  console.log('='.repeat(80));
  
  await compareLocalAPIWithSDK();
  
  console.log('\n🎉 本地API研究完成!');
  console.log('现在我们理解了如何基于SDK构建本地API');
}

// 运行研究
main().catch(console.error);
