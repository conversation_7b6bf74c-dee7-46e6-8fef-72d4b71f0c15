/**
 * REF SDK 深度分析
 * 
 * 目的：
 * 1. 理解REF SDK的具体执行流程
 * 2. 分析V1和V2的处理差异
 * 3. 探索代码复用的可能性
 * 4. 对比API和SDK的实现差异
 */

import 'dotenv/config';
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos,
  instantSwap,
  getPoolEstimate
} from '@ref-finance/ref-sdk';

// 设置正确的RPC
process.env.NEAR_RPC_URL = 'https://rpc.shitzuapes.xyz';
init_env('mainnet', '', 'https://rpc.shitzuapes.xyz');

/**
 * 分析REF SDK的执行流程
 */
async function analyzeSDKExecutionFlow() {
  console.log('🔍 REF SDK 执行流程深度分析');
  console.log('='.repeat(80));
  
  try {
    // 1. 获取代币元数据
    console.log('\n1️⃣ 代币元数据获取流程');
    console.log('-'.repeat(40));
    
    const nearToken = await ftGetTokenMetadata('wrap.near');
    const usdtToken = await ftGetTokenMetadata('usdt.tether-token.near');
    
    console.log('✅ 代币元数据获取成功');
    console.log(`   NEAR: ${nearToken.symbol} (${nearToken.decimals} decimals)`);
    console.log(`   USDT: ${usdtToken.symbol} (${usdtToken.decimals} decimals)`);
    
    // 2. 池子数据获取和分析
    console.log('\n2️⃣ 池子数据获取和分类');
    console.log('-'.repeat(40));
    
    const startTime = Date.now();
    const { ratedPools, unRatedPools, simplePools } = await fetchAllPools();
    const poolFetchTime = Date.now() - startTime;
    
    console.log(`✅ 池子数据获取完成 (${poolFetchTime}ms)`);
    console.log(`   Simple Pools (V1): ${simplePools.length}`);
    console.log(`   Stable Pools (V2): ${unRatedPools.length + ratedPools.length}`);
    console.log(`   - Unrated: ${unRatedPools.length}`);
    console.log(`   - Rated: ${ratedPools.length}`);
    
    // 分析池子类型
    console.log('\n📊 池子类型分析:');
    const poolTypes = new Map();
    simplePools.forEach((pool: any) => {
      const type = pool.pool_kind || 'SIMPLE_POOL';
      poolTypes.set(type, (poolTypes.get(type) || 0) + 1);
    });
    
    poolTypes.forEach((count, type) => {
      console.log(`   ${type}: ${count} 个池子`);
    });
    
    // 3. 智能路由算法分析
    console.log('\n3️⃣ 智能路由算法分析');
    console.log('-'.repeat(40));
    
    // 测试不同的路由选项
    const routingTests = [
      { enableSmartRouting: false, desc: '简单路由' },
      { enableSmartRouting: true, desc: '智能路由' }
    ];
    
    for (const test of routingTests) {
      console.log(`\n🧪 测试: ${test.desc}`);
      
      const routeStartTime = Date.now();
      const swapTodos = await estimateSwap({
        tokenIn: nearToken,
        tokenOut: usdtToken,
        amountIn: '1',
        simplePools,
        options: {
          enableSmartRouting: test.enableSmartRouting,
          stablePools: [...unRatedPools, ...ratedPools]
        }
      });
      const routeTime = Date.now() - routeStartTime;
      
      const output = getExpectedOutputFromSwapTodos(swapTodos, usdtToken.id);
      
      console.log(`   ⏱️ 路由计算时间: ${routeTime}ms`);
      console.log(`   📈 输出金额: ${output} USDT`);
      console.log(`   🛣️ 路径步骤: ${swapTodos.length}`);
      
      // 详细分析每个步骤
      swapTodos.forEach((step, index) => {
        console.log(`      步骤${index + 1}:`);
        console.log(`         池子ID: ${step.pool.id}`);
        console.log(`         池子类型: ${step.pool.pool_kind}`);
        console.log(`         输入代币: ${step.inputToken}`);
        console.log(`         输出代币: ${step.outputToken}`);
        console.log(`         输入金额: ${step.pool.partialAmountIn || 'N/A'}`);
        console.log(`         预期输出: ${step.estimate}`);
        
        // 分析池子详情
        if (step.pool.supplies) {
          const supplies = Object.entries(step.pool.supplies);
          console.log(`         池子流动性:`);
          supplies.forEach(([token, amount]) => {
            console.log(`           ${token}: ${amount}`);
          });
        }
      });
    }
    
    // 4. 单池子估算分析
    console.log('\n4️⃣ 单池子估算分析');
    console.log('-'.repeat(40));
    
    // 找到NEAR-USDT的直接池子
    const nearUsdtPools = simplePools.filter((pool: any) =>
      pool.tokenIds.includes('wrap.near') &&
      pool.tokenIds.includes('usdt.tether-token.near')
    );
    
    console.log(`🔍 找到 ${nearUsdtPools.length} 个NEAR-USDT直接池子`);
    
    for (const pool of nearUsdtPools.slice(0, 3)) { // 只测试前3个
      console.log(`\n📊 池子 #${pool.id} 分析:`);
      console.log(`   类型: ${pool.pool_kind}`);
      console.log(`   费率: ${pool.fee}bp`);
      console.log(`   流动性:`);
      
      Object.entries(pool.supplies).forEach(([token, amount]) => {
        const symbol = token.includes('wrap.near') ? 'NEAR' : 'USDT';
        console.log(`     ${symbol}: ${amount}`);
      });
      
      try {
        const poolEstimate = await getPoolEstimate({
          tokenIn: nearToken,
          tokenOut: usdtToken,
          amountIn: '1',
          pool: pool
        });
        
        console.log(`   单池估算: ${poolEstimate.estimate} USDT`);
      } catch (error: any) {
        console.log(`   单池估算失败: ${error.message}`);
      }
    }
    
    // 5. 交易构建分析
    console.log('\n5️⃣ 交易构建分析');
    console.log('-'.repeat(40));
    
    try {
      // 使用智能路由的结果构建交易
      const swapTodos = await estimateSwap({
        tokenIn: nearToken,
        tokenOut: usdtToken,
        amountIn: '1',
        simplePools,
        options: {
          enableSmartRouting: true,
          stablePools: [...unRatedPools, ...ratedPools]
        }
      });
      
      const transactions = await instantSwap({
        tokenIn: nearToken,
        tokenOut: usdtToken,
        amountIn: '1',
        slippageTolerance: 0.005,
        swapTodos,
        AccountId: 'test-account.near'
      });
      
      console.log(`✅ 交易构建成功`);
      console.log(`   交易数量: ${transactions.length}`);
      
      transactions.forEach((tx, index) => {
        console.log(`   交易${index + 1}:`);
        console.log(`     接收者: ${tx.receiverId}`);
        console.log(`     方法调用数: ${tx.functionCalls.length}`);
        
        tx.functionCalls.forEach((call, callIndex) => {
          console.log(`     调用${callIndex + 1}:`);
          console.log(`       方法: ${call.methodName}`);
          console.log(`       Gas: ${call.gas}`);
          console.log(`       附加金额: ${call.amount}`);
          
          if (call.args && (call.args as any).msg) {
            try {
              const msg = JSON.parse((call.args as any).msg);
              console.log(`       消息内容:`);
              console.log(`         强制执行: ${msg.force}`);
              console.log(`         动作数量: ${msg.actions?.length || 0}`);

              if (msg.actions) {
                msg.actions.forEach((action: any, actionIndex: number) => {
                  console.log(`         动作${actionIndex + 1}:`);
                  console.log(`           池子ID: ${action.pool_id}`);
                  console.log(`           输入代币: ${action.token_in}`);
                  console.log(`           输出代币: ${action.token_out}`);
                  console.log(`           输入金额: ${action.amount_in}`);
                  console.log(`           最小输出: ${action.min_amount_out}`);
                });
              }
            } catch {
              console.log(`       消息: ${(call.args as any).msg}`);
            }
          }
        });
      });
      
    } catch (error: any) {
      console.log(`❌ 交易构建失败: ${error.message}`);
    }
    
  } catch (error: any) {
    console.error('❌ 分析失败:', error.message);
  }
}

/**
 * 主函数
 */
async function main() {
  await analyzeSDKExecutionFlow();
  
  console.log('\n🎉 REF SDK 深度分析完成!');
  console.log('\n📋 关键发现将在分析后总结...');
}

// 运行分析
main().catch(console.error);
