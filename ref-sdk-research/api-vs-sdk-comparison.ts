/**
 * API vs SDK 对比分析
 * 
 * 目的：
 * 1. 对比API和SDK的具体实现差异
 * 2. 理解为什么会选择不同的路径
 * 3. 分析代码复用的可能性
 * 4. 探索优化方案
 */

import 'dotenv/config';
import axios from 'axios';

// REF SDK 导入
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos
} from '@ref-finance/ref-sdk';

// 设置正确的RPC
process.env.NEAR_RPC_URL = 'https://rpc.shitzuapes.xyz';
init_env('mainnet', '', 'https://rpc.shitzuapes.xyz');

/**
 * 直接调用REF Finance API (模拟现有实现)
 */
async function callRefFinanceAPI() {
  console.log('🌐 直接调用REF Finance API');
  console.log('-'.repeat(40));
  
  try {
    const startTime = Date.now();
    
    // V1 Smart Router API调用
    const v1Response = await axios.post('https://smartrouter.ref.finance/findPath', {
      tokenIn: 'wrap.near',
      tokenOut: 'usdt.tether-token.near',
      amountIn: '1000000000000000000000000', // 1 NEAR in wei
      slippage: 0.005
    });
    
    const v1Time = Date.now() - startTime;
    
    console.log(`✅ V1 API调用成功 (${v1Time}ms)`);
    console.log(`   输出金额: ${v1Response.data.outputAmount}`);
    console.log(`   路径信息:`);
    
    if (v1Response.data.route && v1Response.data.route.pools) {
      v1Response.data.route.pools.forEach((pool: any, index: number) => {
        console.log(`     步骤${index + 1}: Pool#${pool.pool_id} (${pool.token_in} → ${pool.token_out})`);
        console.log(`       输入: ${pool.amount_in}, 输出: ${pool.amount_out}`);
      });
    }
    
    return {
      success: true,
      output: v1Response.data.outputAmount,
      time: v1Time,
      route: v1Response.data.route,
      system: 'V1_API'
    };
    
  } catch (error: any) {
    console.log(`❌ API调用失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      time: 0
    };
  }
}

/**
 * 使用REF SDK获取报价
 */
async function callRefSDK() {
  console.log('🔧 使用REF SDK获取报价');
  console.log('-'.repeat(40));
  
  try {
    const startTime = Date.now();
    
    // 获取代币元数据
    const [nearToken, usdtToken] = await Promise.all([
      ftGetTokenMetadata('wrap.near'),
      ftGetTokenMetadata('usdt.tether-token.near')
    ]);
    
    // 获取池子数据
    const { simplePools, unRatedPools, ratedPools } = await fetchAllPools();
    
    // 智能路由
    const swapTodos = await estimateSwap({
      tokenIn: nearToken,
      tokenOut: usdtToken,
      amountIn: '1',
      simplePools,
      options: {
        enableSmartRouting: true,
        stablePools: [...unRatedPools, ...ratedPools]
      }
    });
    
    const output = getExpectedOutputFromSwapTodos(swapTodos, usdtToken.id);
    const sdkTime = Date.now() - startTime;
    
    console.log(`✅ SDK调用成功 (${sdkTime}ms)`);
    console.log(`   输出金额: ${output}`);
    console.log(`   路径信息:`);
    
    swapTodos.forEach((step, index) => {
      console.log(`     步骤${index + 1}: Pool#${step.pool.id} (${step.inputToken} → ${step.outputToken})`);
      console.log(`       输入: ${step.pool.partialAmountIn}, 输出: ${step.estimate}`);
    });
    
    return {
      success: true,
      output: output.toString(),
      time: sdkTime,
      swapTodos,
      system: 'SDK'
    };
    
  } catch (error: any) {
    console.log(`❌ SDK调用失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      time: 0
    };
  }
}

/**
 * 分析路径选择差异
 */
function analyzePathDifferences(apiResult: any, sdkResult: any) {
  console.log('\n🔍 路径选择差异分析');
  console.log('='.repeat(60));
  
  if (!apiResult.success || !sdkResult.success) {
    console.log('❌ 无法进行对比，因为有调用失败');
    return;
  }
  
  // API路径
  const apiPools = apiResult.route?.pools?.map((pool: any) => ({
    id: pool.pool_id,
    tokenIn: pool.token_in,
    tokenOut: pool.token_out,
    amountIn: pool.amount_in,
    amountOut: pool.amount_out
  })) || [];
  
  // SDK路径
  const sdkPools = sdkResult.swapTodos?.map((step: any) => ({
    id: step.pool.id,
    tokenIn: step.inputToken,
    tokenOut: step.outputToken,
    amountIn: step.pool.partialAmountIn,
    amountOut: step.estimate
  })) || [];
  
  console.log(`📊 路径对比:`);
  console.log(`   API路径长度: ${apiPools.length}`);
  console.log(`   SDK路径长度: ${sdkPools.length}`);
  
  console.log(`\n🌐 API选择的路径:`);
  apiPools.forEach((pool: any, index: number) => {
    console.log(`   步骤${index + 1}: Pool#${pool.id}`);
    console.log(`     ${pool.tokenIn} → ${pool.tokenOut}`);
    console.log(`     输入: ${pool.amountIn}, 输出: ${pool.amountOut}`);
  });

  console.log(`\n🔧 SDK选择的路径:`);
  sdkPools.forEach((pool: any, index: number) => {
    console.log(`   步骤${index + 1}: Pool#${pool.id}`);
    console.log(`     ${pool.tokenIn} → ${pool.tokenOut}`);
    console.log(`     输入: ${pool.amountIn}, 输出: ${pool.amountOut}`);
  });
  
  // 分析差异原因
  console.log(`\n🤔 差异原因分析:`);
  
  if (apiPools.length === 1 && sdkPools.length > 1) {
    console.log(`   ✅ API选择简单直接路径 (${apiPools.length}步)`);
    console.log(`   ⚠️ SDK选择复杂多步路径 (${sdkPools.length}步)`);
    console.log(`   💡 可能原因: SDK的智能路由过度优化`);
  } else if (apiPools.length > 1 && sdkPools.length === 1) {
    console.log(`   ⚠️ API选择复杂路径 (${apiPools.length}步)`);
    console.log(`   ✅ SDK选择简单路径 (${sdkPools.length}步)`);
    console.log(`   💡 可能原因: API考虑了更多因素`);
  } else {
    console.log(`   📊 两者路径复杂度相似`);
    
    // 检查是否使用相同的池子
    const apiPoolIds = apiPools.map((p: any) => p.id).sort();
    const sdkPoolIds = sdkPools.map((p: any) => p.id).sort();
    
    if (JSON.stringify(apiPoolIds) === JSON.stringify(sdkPoolIds)) {
      console.log(`   ✅ 使用相同的池子，但顺序可能不同`);
    } else {
      console.log(`   ❌ 使用完全不同的池子`);
      console.log(`   API池子: [${apiPoolIds.join(', ')}]`);
      console.log(`   SDK池子: [${sdkPoolIds.join(', ')}]`);
    }
  }
  
  // 输出金额对比
  const apiOutput = parseFloat(apiResult.output);
  const sdkOutput = parseFloat(sdkResult.output);
  const outputDiff = Math.abs(apiOutput - sdkOutput);
  const outputDiffPercent = (outputDiff / Math.max(apiOutput, sdkOutput)) * 100;
  
  console.log(`\n💰 输出金额对比:`);
  console.log(`   API输出: ${apiResult.output} USDT`);
  console.log(`   SDK输出: ${sdkResult.output} USDT`);
  console.log(`   差异: ${outputDiff.toFixed(6)} USDT (${outputDiffPercent.toFixed(4)}%)`);
  
  if (outputDiffPercent < 0.01) {
    console.log(`   ✅ 差异很小，可以接受`);
  } else if (outputDiffPercent < 0.1) {
    console.log(`   ⚠️ 差异较小，需要关注`);
  } else {
    console.log(`   ❌ 差异较大，不可接受`);
  }
  
  // 性能对比
  console.log(`\n⏱️ 性能对比:`);
  console.log(`   API响应时间: ${apiResult.time}ms`);
  console.log(`   SDK响应时间: ${sdkResult.time}ms`);
  console.log(`   速度优势: ${apiResult.time < sdkResult.time ? 'API' : 'SDK'} (${Math.abs(apiResult.time - sdkResult.time)}ms差距)`);
}

/**
 * 探索代码复用可能性
 */
function exploreCodeReuse(apiResult: any, sdkResult: any) {
  console.log('\n🔄 代码复用可能性分析');
  console.log('='.repeat(60));
  
  console.log(`📋 复用方案评估:`);
  
  // 方案1: 使用SDK的池子数据 + API的路径选择
  console.log(`\n1️⃣ 方案1: SDK池子数据 + API路径选择`);
  console.log(`   优势:`);
  console.log(`     - 获得SDK的完整池子数据`);
  console.log(`     - 保持API的简单路径选择`);
  console.log(`     - 可能提高报价准确性`);
  console.log(`   劣势:`);
  console.log(`     - 需要维护两套系统`);
  console.log(`     - 增加复杂度`);
  console.log(`     - 池子数据获取仍然很慢`);
  
  // 方案2: 使用API的路径 + SDK的计算精度
  console.log(`\n2️⃣ 方案2: API路径 + SDK计算精度`);
  console.log(`   优势:`);
  console.log(`     - 保持API的速度优势`);
  console.log(`     - 获得SDK的计算精度`);
  console.log(`     - 路径选择更可靠`);
  console.log(`   劣势:`);
  console.log(`     - 需要重新实现计算逻辑`);
  console.log(`     - 可能引入新的bug`);
  
  // 方案3: 完全使用API，优化现有实现
  console.log(`\n3️⃣ 方案3: 完全使用API，优化现有实现`);
  console.log(`   优势:`);
  console.log(`     - 保持现有稳定性`);
  console.log(`     - 专注于网络和缓存优化`);
  console.log(`     - 风险最小`);
  console.log(`   劣势:`);
  console.log(`     - 无法获得SDK的潜在优势`);
  console.log(`     - 依赖REF官方API稳定性`);
  
  // 推荐方案
  console.log(`\n🎯 推荐方案:`);
  
  const speedAdvantage = apiResult.time < sdkResult.time ? apiResult.time / sdkResult.time : sdkResult.time / apiResult.time;
  const outputDiffPercent = Math.abs(parseFloat(apiResult.output) - parseFloat(sdkResult.output)) / Math.max(parseFloat(apiResult.output), parseFloat(sdkResult.output)) * 100;
  
  if (speedAdvantage > 5 && outputDiffPercent < 0.5) {
    console.log(`   ✅ 推荐方案3: 继续使用API`);
    console.log(`   理由: API有显著速度优势且精度差异可接受`);
  } else if (outputDiffPercent > 1) {
    console.log(`   ⚠️ 推荐方案2: API路径 + SDK精度`);
    console.log(`   理由: 输出差异较大，需要提高精度`);
  } else {
    console.log(`   🤔 需要进一步测试决定`);
    console.log(`   理由: 各有优劣，需要更多数据`);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔍 API vs SDK 深度对比分析');
  console.log('='.repeat(80));
  
  try {
    // 并行调用API和SDK
    const [apiResult, sdkResult] = await Promise.all([
      callRefFinanceAPI(),
      callRefSDK()
    ]);
    
    // 分析差异
    analyzePathDifferences(apiResult, sdkResult);
    
    // 探索复用可能性
    exploreCodeReuse(apiResult, sdkResult);
    
    console.log('\n🎉 对比分析完成!');
    
  } catch (error: any) {
    console.error('❌ 分析失败:', error.message);
  }
}

// 运行分析
main().catch(console.error);
