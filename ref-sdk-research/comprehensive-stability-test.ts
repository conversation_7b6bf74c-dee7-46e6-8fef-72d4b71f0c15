/**
 * 全面稳定性测试
 * 
 * 目的：
 * 1. 多次测试验证本地API的稳定性和一致性
 * 2. 基于REF Finance官方测试计划进行验证
 * 3. 确保在不同条件下结果的可靠性
 * 4. 对比不同配置策略的表现
 */

import 'dotenv/config';
import { 
  init_env,
  estimateSwap,
  fetchAllPools,
  ftGetTokenMetadata,
  getExpectedOutputFromSwapTodos
} from '@ref-finance/ref-sdk';

// 设置正确的RPC
process.env.NEAR_RPC_URL = 'https://rpc.shitzuapes.xyz';
init_env('mainnet', '', 'https://rpc.shitzuapes.xyz');

/**
 * 测试配置接口
 */
interface TestConfig {
  name: string;
  enableSmartRouting: boolean;
  useStablePools: boolean;
  description: string;
}

/**
 * 测试结果接口
 */
interface TestResult {
  config: string;
  attempt: number;
  success: boolean;
  output?: string;
  time: number;
  pathLength?: number;
  pools?: string[];
  error?: string;
}

/**
 * 稳定性统计接口
 */
interface StabilityStats {
  config: string;
  totalAttempts: number;
  successCount: number;
  successRate: number;
  uniqueOutputs: number;
  avgTime: number;
  minTime: number;
  maxTime: number;
  mostCommonOutput: string;
  outputFrequency: Map<string, number>;
  isStable: boolean;
}

/**
 * 优化的本地API类
 */
class StableLocalAPI {
  private poolsCache: any = null;
  private lastUpdate: number = 0;
  private readonly CACHE_TTL = 300000; // 5分钟缓存

  /**
   * 获取报价（使用指定配置）
   */
  async getQuote(
    tokenIn: string,
    tokenOut: string,
    amountIn: string,
    config: TestConfig
  ) {
    try {
      // 更新池子数据
      await this.updatePoolsIfNeeded();
      
      // 获取代币元数据
      const [tokenInMeta, tokenOutMeta] = await Promise.all([
        ftGetTokenMetadata(tokenIn),
        ftGetTokenMetadata(tokenOut)
      ]);
      
      // 金额格式转换
      let processedAmount = amountIn;
      if (amountIn.length > 10) {
        const factor = Math.pow(10, tokenInMeta.decimals);
        processedAmount = (parseFloat(amountIn) / factor).toString();
      }
      
      // 配置池子
      let simplePools = this.poolsCache.simplePools;
      let stablePools = config.useStablePools ? 
        [...this.poolsCache.unRatedPools, ...this.poolsCache.ratedPools] : [];
      
      const startTime = Date.now();
      
      // 执行交换估算
      const swapTodos = await estimateSwap({
        tokenIn: tokenInMeta,
        tokenOut: tokenOutMeta,
        amountIn: processedAmount,
        simplePools,
        options: {
          enableSmartRouting: config.enableSmartRouting,
          stablePools
        }
      });
      
      const output = getExpectedOutputFromSwapTodos(swapTodos, tokenOutMeta.id);
      const time = Date.now() - startTime;
      
      return {
        success: true,
        output: output.toString(),
        time,
        pathLength: swapTodos.length,
        pools: swapTodos.map(step => step.pool.id.toString())
      };
      
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
        time: 0
      };
    }
  }

  private async updatePoolsIfNeeded() {
    if (!this.poolsCache || Date.now() - this.lastUpdate > this.CACHE_TTL) {
      console.log('   📦 更新池子数据...');
      this.poolsCache = await fetchAllPools();
      this.lastUpdate = Date.now();
      console.log(`   ✅ 池子数据更新完成`);
    }
  }
}

/**
 * 执行全面稳定性测试
 */
async function runComprehensiveStabilityTest() {
  console.log('🔍 全面稳定性测试');
  console.log('='.repeat(80));
  console.log('基于REF Finance官方测试计划进行多次验证');
  console.log('');

  // 测试配置（基于官方测试计划）
  const testConfigs: TestConfig[] = [
    {
      name: 'OFFICIAL_API_SIMULATION',
      enableSmartRouting: false,
      useStablePools: false,
      description: '模拟官方API配置（简单路由，仅V1池子）'
    },
    {
      name: 'SMART_ROUTING_V1_ONLY',
      enableSmartRouting: true,
      useStablePools: false,
      description: '智能路由，仅V1池子'
    },
    {
      name: 'SMART_ROUTING_ALL_POOLS',
      enableSmartRouting: true,
      useStablePools: true,
      description: '智能路由，包含所有池子'
    },
    {
      name: 'SIMPLE_ROUTING_ALL_POOLS',
      enableSmartRouting: false,
      useStablePools: true,
      description: '简单路由，包含所有池子'
    }
  ];

  // 测试用例
  const testCases = [
    {
      tokenIn: 'wrap.near',
      tokenOut: 'usdt.tether-token.near',
      amountIn: '1000000000000000000000000', // 1 NEAR in wei
      desc: '1 NEAR → USDT'
    },
    {
      tokenIn: 'wrap.near',
      tokenOut: '17208628f84f5d6ad33f0da3bbbeb27ffcb398eac501a31bd6ad2011e36133a1',
      amountIn: '1000000000000000000000000', // 1 NEAR in wei
      desc: '1 NEAR → USDC'
    }
  ];

  const localAPI = new StableLocalAPI();
  const allResults: TestResult[] = [];

  // 对每个测试用例和配置进行多次测试
  for (const testCase of testCases) {
    console.log(`\n📊 测试用例: ${testCase.desc}`);
    console.log('='.repeat(60));

    for (const config of testConfigs) {
      console.log(`\n🧪 配置: ${config.name}`);
      console.log(`   描述: ${config.description}`);
      console.log(`   执行20次测试...`);

      const configResults: TestResult[] = [];

      // 执行20次测试
      for (let i = 0; i < 20; i++) {
        const result = await localAPI.getQuote(
          testCase.tokenIn,
          testCase.tokenOut,
          testCase.amountIn,
          config
        );

        const testResult: TestResult = {
          config: config.name,
          attempt: i + 1,
          success: result.success,
          output: result.output,
          time: result.time,
          pathLength: result.pathLength,
          pools: result.pools,
          error: result.error
        };

        configResults.push(testResult);
        allResults.push(testResult);

        // 显示进度
        if ((i + 1) % 5 === 0) {
          const successCount = configResults.filter(r => r.success).length;
          console.log(`      进度: ${i + 1}/20, 成功率: ${successCount}/${i + 1}`);
        }
      }

      // 分析这个配置的结果
      const stats = analyzeConfigResults(configResults);
      displayConfigStats(stats);
    }
  }

  // 生成综合报告
  generateComprehensiveReport(allResults, testCases, testConfigs);
}

/**
 * 分析配置结果
 */
function analyzeConfigResults(results: TestResult[]): StabilityStats {
  const successfulResults = results.filter(r => r.success);
  const outputs = successfulResults.map(r => r.output!);
  const times = successfulResults.map(r => r.time);

  // 统计输出频率
  const outputFrequency = new Map<string, number>();
  outputs.forEach(output => {
    outputFrequency.set(output, (outputFrequency.get(output) || 0) + 1);
  });

  // 找到最常见的输出
  let mostCommonOutput = '';
  let maxFrequency = 0;
  outputFrequency.forEach((frequency, output) => {
    if (frequency > maxFrequency) {
      maxFrequency = frequency;
      mostCommonOutput = output;
    }
  });

  // 计算稳定性（如果最常见输出的频率超过90%，认为是稳定的）
  const isStable = maxFrequency >= successfulResults.length * 0.9;

  return {
    config: results[0].config,
    totalAttempts: results.length,
    successCount: successfulResults.length,
    successRate: successfulResults.length / results.length,
    uniqueOutputs: outputFrequency.size,
    avgTime: times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 0,
    minTime: times.length > 0 ? Math.min(...times) : 0,
    maxTime: times.length > 0 ? Math.max(...times) : 0,
    mostCommonOutput,
    outputFrequency,
    isStable
  };
}

/**
 * 显示配置统计
 */
function displayConfigStats(stats: StabilityStats) {
  console.log(`\n   📊 统计结果:`);
  console.log(`      成功率: ${stats.successCount}/${stats.totalAttempts} (${(stats.successRate * 100).toFixed(1)}%)`);
  console.log(`      唯一输出: ${stats.uniqueOutputs} 种`);
  console.log(`      稳定性: ${stats.isStable ? '✅ 稳定' : '❌ 不稳定'}`);
  console.log(`      最常见输出: ${stats.mostCommonOutput}`);
  console.log(`      平均响应时间: ${stats.avgTime.toFixed(1)}ms`);
  console.log(`      时间范围: ${stats.minTime}ms - ${stats.maxTime}ms`);

  if (stats.uniqueOutputs > 1) {
    console.log(`      输出分布:`);
    stats.outputFrequency.forEach((frequency, output) => {
      const percentage = (frequency / stats.successCount * 100).toFixed(1);
      console.log(`         ${output}: ${frequency}次 (${percentage}%)`);
    });
  }
}

/**
 * 生成综合报告
 */
function generateComprehensiveReport(
  allResults: TestResult[],
  testCases: any[],
  testConfigs: TestConfig[]
) {
  console.log('\n📋 综合稳定性报告');
  console.log('='.repeat(80));

  // 按配置分组统计
  const configStats = new Map<string, StabilityStats>();
  
  testConfigs.forEach(config => {
    const configResults = allResults.filter(r => r.config === config.name);
    if (configResults.length > 0) {
      const stats = analyzeConfigResults(configResults);
      configStats.set(config.name, stats);
    }
  });

  // 显示配置对比表
  console.log('\n| 配置 | 成功率 | 稳定性 | 唯一输出 | 平均时间 | 推荐度 |');
  console.log('|------|--------|--------|----------|----------|--------|');

  configStats.forEach((stats, configName) => {
    const successRate = `${(stats.successRate * 100).toFixed(1)}%`;
    const stability = stats.isStable ? '✅' : '❌';
    const avgTime = `${stats.avgTime.toFixed(0)}ms`;
    
    // 计算推荐度
    let recommendation = '⭐';
    if (stats.successRate >= 0.95 && stats.isStable && stats.avgTime < 1000) {
      recommendation = '⭐⭐⭐⭐⭐';
    } else if (stats.successRate >= 0.9 && stats.isStable) {
      recommendation = '⭐⭐⭐⭐';
    } else if (stats.successRate >= 0.8 && stats.isStable) {
      recommendation = '⭐⭐⭐';
    } else if (stats.successRate >= 0.7) {
      recommendation = '⭐⭐';
    }

    console.log(`| ${configName} | ${successRate} | ${stability} | ${stats.uniqueOutputs} | ${avgTime} | ${recommendation} |`);
  });

  // 最终建议
  console.log('\n🎯 最终建议:');
  
  const bestConfig = Array.from(configStats.entries())
    .sort((a, b) => {
      const scoreA = a[1].successRate * (a[1].isStable ? 1 : 0.5) * (1000 / Math.max(a[1].avgTime, 1));
      const scoreB = b[1].successRate * (b[1].isStable ? 1 : 0.5) * (1000 / Math.max(b[1].avgTime, 1));
      return scoreB - scoreA;
    })[0];

  if (bestConfig) {
    console.log(`   🏆 推荐配置: ${bestConfig[0]}`);
    console.log(`   📊 性能指标:`);
    console.log(`      - 成功率: ${(bestConfig[1].successRate * 100).toFixed(1)}%`);
    console.log(`      - 稳定性: ${bestConfig[1].isStable ? '优秀' : '需改进'}`);
    console.log(`      - 平均响应时间: ${bestConfig[1].avgTime.toFixed(1)}ms`);
    console.log(`      - 唯一输出数: ${bestConfig[1].uniqueOutputs}`);
  }

  // 与官方API对比的建议
  console.log('\n💡 实施建议:');
  console.log('   1. 使用推荐配置作为本地API的默认设置');
  console.log('   2. 实施渐进式替换策略，先在测试环境验证');
  console.log('   3. 添加监控机制，持续跟踪稳定性指标');
  console.log('   4. 保留官方API作为备份方案');
}

/**
 * 主函数
 */
async function main() {
  try {
    await runComprehensiveStabilityTest();
    
    console.log('\n🎉 全面稳定性测试完成!');
    console.log('现在您可以基于测试结果决定最佳的本地API配置');
    
  } catch (error: any) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
main().catch(console.error);
